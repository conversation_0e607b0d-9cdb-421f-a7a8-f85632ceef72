package com.clothingstore.model;

import java.util.ArrayList;
import java.util.List;

/**
 * Result class for inventory adjustment operations during refunds
 */
public class InventoryAdjustmentResult {
    private boolean success;
    private String errorMessage;
    private List<String> warnings;
    private List<InventoryAdjustmentDetail> adjustmentDetails;
    
    public InventoryAdjustmentResult() {
        this.warnings = new ArrayList<>();
        this.adjustmentDetails = new ArrayList<>();
    }
    
    public InventoryAdjustmentResult(boolean success, String errorMessage) {
        this();
        this.success = success;
        this.errorMessage = errorMessage;
    }
    
    public InventoryAdjustmentResult(boolean success, String errorMessage, List<InventoryAdjustmentDetail> adjustmentDetails) {
        this();
        this.success = success;
        this.errorMessage = errorMessage;
        this.adjustmentDetails = adjustmentDetails != null ? adjustmentDetails : new ArrayList<>();
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public void setWarnings(List<String> warnings) {
        this.warnings = warnings != null ? warnings : new ArrayList<>();
    }
    
    public void addWarning(String warning) {
        if (warning != null && !warning.trim().isEmpty()) {
            this.warnings.add(warning);
        }
    }
    
    public List<InventoryAdjustmentDetail> getAdjustmentDetails() {
        return adjustmentDetails;
    }
    
    public void setAdjustmentDetails(List<InventoryAdjustmentDetail> adjustmentDetails) {
        this.adjustmentDetails = adjustmentDetails != null ? adjustmentDetails : new ArrayList<>();
    }
    
    public void addAdjustmentDetail(InventoryAdjustmentDetail detail) {
        if (detail != null) {
            this.adjustmentDetails.add(detail);
        }
    }
    
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
    
    @Override
    public String toString() {
        return String.format("InventoryAdjustmentResult{success=%s, errorMessage='%s', warnings=%d, adjustments=%d}", 
                           success, errorMessage, warnings.size(), adjustmentDetails.size());
    }
    
    /**
     * Inner class to track individual inventory adjustment details
     */
    public static class InventoryAdjustmentDetail {
        private Long productId;
        private String productName;
        private int previousStock;
        private int adjustmentQuantity;
        private int newStock;
        private boolean successful;
        private String errorMessage;
        
        public InventoryAdjustmentDetail() {}
        
        public InventoryAdjustmentDetail(Long productId, String productName, int previousStock, 
                                       int adjustmentQuantity, int newStock, boolean successful) {
            this.productId = productId;
            this.productName = productName;
            this.previousStock = previousStock;
            this.adjustmentQuantity = adjustmentQuantity;
            this.newStock = newStock;
            this.successful = successful;
        }
        
        // Getters and Setters
        public Long getProductId() {
            return productId;
        }
        
        public void setProductId(Long productId) {
            this.productId = productId;
        }
        
        public String getProductName() {
            return productName;
        }
        
        public void setProductName(String productName) {
            this.productName = productName;
        }
        
        public int getPreviousStock() {
            return previousStock;
        }
        
        public void setPreviousStock(int previousStock) {
            this.previousStock = previousStock;
        }
        
        public int getAdjustmentQuantity() {
            return adjustmentQuantity;
        }
        
        public void setAdjustmentQuantity(int adjustmentQuantity) {
            this.adjustmentQuantity = adjustmentQuantity;
        }
        
        public int getNewStock() {
            return newStock;
        }
        
        public void setNewStock(int newStock) {
            this.newStock = newStock;
        }
        
        public boolean isSuccessful() {
            return successful;
        }
        
        public void setSuccessful(boolean successful) {
            this.successful = successful;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        @Override
        public String toString() {
            return String.format("InventoryAdjustmentDetail{productId=%d, productName='%s', previousStock=%d, adjustmentQuantity=%d, newStock=%d, successful=%s}", 
                               productId, productName, previousStock, adjustmentQuantity, newStock, successful);
        }
    }
}
