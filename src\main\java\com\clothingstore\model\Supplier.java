package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing a supplier
 */
public class Supplier {
    
    private Long id;
    private String supplierCode;
    private String companyName;
    private String contactPerson;
    private String email;
    private String phone;
    private String mobile;
    private String website;
    private String address;
    private String city;
    private String state;
    private String zipCode;
    private String country;
    private String taxId;
    private SupplierStatus status;
    private PaymentTerms paymentTerms;
    private int leadTimeDays;
    private BigDecimal minimumOrderAmount;
    private String currency;
    private String notes;
    private BigDecimal creditLimit;
    private BigDecimal currentBalance;
    private LocalDateTime lastOrderDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructors
    public Supplier() {
        this.status = SupplierStatus.ACTIVE;
        this.paymentTerms = PaymentTerms.NET_30;
        this.leadTimeDays = 7;
        this.currency = "USD";
        this.currentBalance = BigDecimal.ZERO;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public Supplier(String companyName, String contactPerson) {
        this();
        this.companyName = companyName;
        this.contactPerson = contactPerson;
        this.supplierCode = generateSupplierCode();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getSupplierCode() {
        return supplierCode;
    }
    
    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }
    
    public String getCompanyName() {
        return companyName;
    }
    
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    
    public String getContactPerson() {
        return contactPerson;
    }
    
    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getMobile() {
        return mobile;
    }
    
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    
    public String getWebsite() {
        return website;
    }
    
    public void setWebsite(String website) {
        this.website = website;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getState() {
        return state;
    }
    
    public void setState(String state) {
        this.state = state;
    }
    
    public String getZipCode() {
        return zipCode;
    }
    
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getTaxId() {
        return taxId;
    }
    
    public void setTaxId(String taxId) {
        this.taxId = taxId;
    }
    
    public SupplierStatus getStatus() {
        return status;
    }
    
    public void setStatus(SupplierStatus status) {
        this.status = status;
    }
    
    public PaymentTerms getPaymentTerms() {
        return paymentTerms;
    }
    
    public void setPaymentTerms(PaymentTerms paymentTerms) {
        this.paymentTerms = paymentTerms;
    }
    
    public int getLeadTimeDays() {
        return leadTimeDays;
    }
    
    public void setLeadTimeDays(int leadTimeDays) {
        this.leadTimeDays = leadTimeDays;
    }
    
    public BigDecimal getMinimumOrderAmount() {
        return minimumOrderAmount;
    }
    
    public void setMinimumOrderAmount(BigDecimal minimumOrderAmount) {
        this.minimumOrderAmount = minimumOrderAmount;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public BigDecimal getCreditLimit() {
        return creditLimit;
    }
    
    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }
    
    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }
    
    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }
    
    public LocalDateTime getLastOrderDate() {
        return lastOrderDate;
    }
    
    public void setLastOrderDate(LocalDateTime lastOrderDate) {
        this.lastOrderDate = lastOrderDate;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Utility methods
    public boolean isActive() {
        return status == SupplierStatus.ACTIVE;
    }
    
    public String getFullAddress() {
        StringBuilder addr = new StringBuilder();
        
        if (address != null && !address.trim().isEmpty()) {
            addr.append(address);
        }
        
        if (city != null && !city.trim().isEmpty()) {
            if (addr.length() > 0) addr.append(", ");
            addr.append(city);
        }
        
        if (state != null && !state.trim().isEmpty()) {
            if (addr.length() > 0) addr.append(", ");
            addr.append(state);
        }
        
        if (zipCode != null && !zipCode.trim().isEmpty()) {
            if (addr.length() > 0) addr.append(" ");
            addr.append(zipCode);
        }
        
        if (country != null && !country.trim().isEmpty()) {
            if (addr.length() > 0) addr.append(", ");
            addr.append(country);
        }
        
        return addr.toString();
    }
    
    public String getPrimaryContact() {
        if (email != null && !email.trim().isEmpty()) {
            return email;
        } else if (phone != null && !phone.trim().isEmpty()) {
            return phone;
        } else if (mobile != null && !mobile.trim().isEmpty()) {
            return mobile;
        }
        return "No contact info";
    }
    
    public BigDecimal getAvailableCredit() {
        if (creditLimit == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal balance = currentBalance != null ? currentBalance : BigDecimal.ZERO;
        return creditLimit.subtract(balance);
    }
    
    public boolean hasAvailableCredit(BigDecimal amount) {
        if (creditLimit == null || amount == null) {
            return true; // No credit limit set
        }
        
        return getAvailableCredit().compareTo(amount) >= 0;
    }
    
    private String generateSupplierCode() {
        return "SUP" + System.currentTimeMillis();
    }
    
    public String getDisplayName() {
        StringBuilder name = new StringBuilder();
        
        if (companyName != null && !companyName.trim().isEmpty()) {
            name.append(companyName);
        }
        
        if (contactPerson != null && !contactPerson.trim().isEmpty()) {
            if (name.length() > 0) {
                name.append(" (").append(contactPerson).append(")");
            } else {
                name.append(contactPerson);
            }
        }
        
        return name.length() > 0 ? name.toString() : "Unknown Supplier";
    }
    
    @Override
    public String toString() {
        return String.format("Supplier{id=%d, code='%s', company='%s'}", 
                id, supplierCode, companyName);
    }
}
