package com.clothingstore.model;

/**
 * Data model for sales report entries
 */
public class SalesReportData {
    private String date;
    private String description;
    private int quantity;
    private double amount;

    public SalesReportData() {}

    public SalesReportData(String date, String description, int quantity, double amount) {
        this.date = date;
        this.description = description;
        this.quantity = quantity;
        this.amount = amount;
    }

    // Getters and Setters
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return String.format("SalesReportData{date='%s', description='%s', quantity=%d, amount=%.2f}", 
                           date, description, quantity, amount);
    }
}
