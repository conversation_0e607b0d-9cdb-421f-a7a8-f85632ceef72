package com.clothingstore.test;

/**
 * Test to verify the minimal Inventory Movement Report loads correctly
 */
public class MinimalInventoryReportTest {

    public static void main(String[] args) {
        System.out.println("=== Minimal Inventory Movement Report Test ===");
        
        try {
            // Test 1: Verify minimal controller class exists
            System.out.println("1. Testing minimal controller class...");
            Class<?> controllerClass = Class.forName("com.clothingstore.view.InventoryMovementReportControllerMinimal");
            System.out.println("   PASSED: InventoryMovementReportControllerMinimal class found");
            
            // Test 2: Verify minimal FXML file exists
            System.out.println("\n2. Testing minimal FXML file...");
            java.net.URL fxmlUrl = MinimalInventoryReportTest.class.getResource("/fxml/InventoryMovementReportMinimal.fxml");
            if (fxmlUrl != null) {
                System.out.println("   PASSED: InventoryMovementReportMinimal.fxml found at: " + fxmlUrl);
            } else {
                System.out.println("   FAILED: InventoryMovementReportMinimal.fxml not found");
                return;
            }
            
            // Test 3: Verify MainWindowController has the updated method
            System.out.println("\n3. Testing MainWindowController integration...");
            Class<?> mainControllerClass = Class.forName("com.clothingstore.view.MainWindowController");
            
            // Check if showInventoryMovementReport method exists
            try {
                mainControllerClass.getDeclaredMethod("showInventoryMovementReport");
                System.out.println("   PASSED: showInventoryMovementReport method found in MainWindowController");
            } catch (NoSuchMethodException e) {
                System.out.println("   FAILED: showInventoryMovementReport method not found");
                return;
            }
            
            System.out.println("\n=== Test Results ===");
            System.out.println("PASSED: Minimal controller is available");
            System.out.println("PASSED: Minimal FXML file is in correct location");
            System.out.println("PASSED: MainWindow integration is updated");
            
            System.out.println("\n=== READY FOR TESTING ===");
            System.out.println("The minimal version should now load successfully!");
            System.out.println("Launch the application and try clicking the 'Inventory Movement' button.");
            System.out.println("You should see a simplified interface with working buttons.");
            
            System.out.println("\nIf it works, we can then upgrade to the full version.");
            System.out.println("If it still fails, we'll get more detailed error information.");
            
        } catch (ClassNotFoundException e) {
            System.err.println("FAILED: Required class not found: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("FAILED: Test error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
