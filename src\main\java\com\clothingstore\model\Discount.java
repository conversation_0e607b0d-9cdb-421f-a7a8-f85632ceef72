package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing a discount that can be applied to transactions or items
 */
public class Discount {
    
    private Long id;
    private String name;
    private String description;
    private DiscountType type;
    private BigDecimal value; // Percentage (0-100) or fixed amount
    private BigDecimal minimumAmount; // Minimum purchase amount to qualify
    private BigDecimal maximumDiscount; // Maximum discount amount (for percentage discounts)
    private int minimumQuantity; // Minimum quantity for bulk discounts
    private int buyQuantity; // For Buy X Get Y discounts
    private int getQuantity; // For Buy X Get Y discounts
    private String applicableCategories; // Comma-separated list of categories
    private String applicableProducts; // Comma-separated list of product SKUs
    private String customerGroups; // Comma-separated list of customer groups
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private boolean active;
    private boolean stackable; // Can be combined with other discounts
    private int usageLimit; // Maximum number of times this discount can be used
    private int usageCount; // Current usage count
    private String promoCode; // Optional promo code
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructors
    public Discount() {
        this.active = true;
        this.stackable = false;
        this.usageLimit = -1; // Unlimited by default
        this.usageCount = 0;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public Discount(String name, DiscountType type, BigDecimal value) {
        this();
        this.name = name;
        this.type = type;
        this.value = value;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public DiscountType getType() {
        return type;
    }
    
    public void setType(DiscountType type) {
        this.type = type;
    }
    
    public BigDecimal getValue() {
        return value;
    }
    
    public void setValue(BigDecimal value) {
        this.value = value;
    }
    
    public BigDecimal getMinimumAmount() {
        return minimumAmount;
    }
    
    public void setMinimumAmount(BigDecimal minimumAmount) {
        this.minimumAmount = minimumAmount;
    }
    
    public BigDecimal getMaximumDiscount() {
        return maximumDiscount;
    }
    
    public void setMaximumDiscount(BigDecimal maximumDiscount) {
        this.maximumDiscount = maximumDiscount;
    }
    
    public int getMinimumQuantity() {
        return minimumQuantity;
    }
    
    public void setMinimumQuantity(int minimumQuantity) {
        this.minimumQuantity = minimumQuantity;
    }
    
    public int getBuyQuantity() {
        return buyQuantity;
    }
    
    public void setBuyQuantity(int buyQuantity) {
        this.buyQuantity = buyQuantity;
    }
    
    public int getGetQuantity() {
        return getQuantity;
    }
    
    public void setGetQuantity(int getQuantity) {
        this.getQuantity = getQuantity;
    }
    
    public String getApplicableCategories() {
        return applicableCategories;
    }
    
    public void setApplicableCategories(String applicableCategories) {
        this.applicableCategories = applicableCategories;
    }
    
    public String getApplicableProducts() {
        return applicableProducts;
    }
    
    public void setApplicableProducts(String applicableProducts) {
        this.applicableProducts = applicableProducts;
    }
    
    public String getCustomerGroups() {
        return customerGroups;
    }
    
    public void setCustomerGroups(String customerGroups) {
        this.customerGroups = customerGroups;
    }
    
    public LocalDateTime getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }
    
    public LocalDateTime getEndDate() {
        return endDate;
    }
    
    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public boolean isStackable() {
        return stackable;
    }
    
    public void setStackable(boolean stackable) {
        this.stackable = stackable;
    }
    
    public int getUsageLimit() {
        return usageLimit;
    }
    
    public void setUsageLimit(int usageLimit) {
        this.usageLimit = usageLimit;
    }
    
    public int getUsageCount() {
        return usageCount;
    }
    
    public void setUsageCount(int usageCount) {
        this.usageCount = usageCount;
    }
    
    public String getPromoCode() {
        return promoCode;
    }
    
    public void setPromoCode(String promoCode) {
        this.promoCode = promoCode;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Utility methods
    public boolean isValid() {
        LocalDateTime now = LocalDateTime.now();
        
        if (!active) {
            return false;
        }
        
        if (startDate != null && now.isBefore(startDate)) {
            return false;
        }
        
        if (endDate != null && now.isAfter(endDate)) {
            return false;
        }
        
        if (usageLimit > 0 && usageCount >= usageLimit) {
            return false;
        }
        
        return true;
    }
    
    public boolean hasPromoCode() {
        return promoCode != null && !promoCode.trim().isEmpty();
    }
    
    public boolean isExpired() {
        return endDate != null && LocalDateTime.now().isAfter(endDate);
    }
    
    public boolean isUsageLimitReached() {
        return usageLimit > 0 && usageCount >= usageLimit;
    }
    
    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        sb.append(name);
        
        if (type == DiscountType.PERCENTAGE) {
            sb.append(" (").append(value).append("% off)");
        } else if (type == DiscountType.FIXED_AMOUNT) {
            sb.append(" ($").append(value).append(" off)");
        }
        
        if (hasPromoCode()) {
            sb.append(" - Code: ").append(promoCode);
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return String.format("Discount{id=%d, name='%s', type=%s, value=%s}", 
                id, name, type, value);
    }
}
