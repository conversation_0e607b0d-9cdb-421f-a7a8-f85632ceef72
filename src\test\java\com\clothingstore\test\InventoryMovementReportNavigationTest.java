package com.clothingstore.test;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Test class to verify the Inventory Movement Report navigation and UI integration
 * This test launches the Inventory Movement Report directly to verify FXML loading
 */
public class InventoryMovementReportNavigationTest extends Application {

    public static void main(String[] args) {
        System.out.println("=== Inventory Movement Report Navigation Test ===");
        System.out.println("Testing FXML loading and UI integration...");
        launch(args);
    }

    @Override
    public void start(Stage primaryStage) {
        try {
            System.out.println("Loading InventoryMovementReport.fxml...");
            
            // Load the FXML file
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/InventoryMovementReport.fxml"));
            VBox root = loader.load();
            
            System.out.println("✓ FXML loaded successfully");
            System.out.println("✓ Controller initialized");
            
            // Create and show the scene
            Scene scene = new Scene(root, 1200, 800);
            primaryStage.setTitle("Inventory Movement Report - Navigation Test");
            primaryStage.setScene(scene);
            primaryStage.show();
            
            System.out.println("✓ UI displayed successfully");
            System.out.println("\nTEST INSTRUCTIONS:");
            System.out.println("1. Verify the report loads with default date range (last 30 days)");
            System.out.println("2. Check that both tabs are visible: 'Items Sold/Processed' and 'Items Returned/Refunded'");
            System.out.println("3. Verify summary statistics are displayed in the top section");
            System.out.println("4. Test the 'Generate Report' button with different date ranges");
            System.out.println("5. Test the 'Export' functionality");
            System.out.println("6. Verify data is properly separated between sold and returned items");
            System.out.println("\nExpected Results:");
            System.out.println("- Items Sold/Processed: Should show completed transactions only");
            System.out.println("- Items Returned/Refunded: Should show refunded/cancelled transactions only");
            System.out.println("- Summary: Should show accurate counts, quantities, values, and return rate");
            System.out.println("\nClose the window when testing is complete.");
            
        } catch (Exception e) {
            System.err.println("❌ FXML Loading Failed: " + e.getMessage());
            e.printStackTrace();
            
            // Show error details
            System.err.println("\nTroubleshooting:");
            System.err.println("1. Verify InventoryMovementReport.fxml exists in src/main/resources/fxml/");
            System.err.println("2. Check that InventoryMovementReportController is compiled");
            System.err.println("3. Ensure all @FXML annotations match the FXML fx:id attributes");
            System.err.println("4. Verify JavaFX runtime is properly configured");
            
            primaryStage.close();
        }
    }
    
    @Override
    public void stop() {
        System.out.println("\n=== Navigation Test Completed ===");
        System.out.println("If the UI loaded and displayed correctly, the integration is successful!");
    }
}
