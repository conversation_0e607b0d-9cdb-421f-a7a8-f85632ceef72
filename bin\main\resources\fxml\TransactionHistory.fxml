<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import java.net.URL?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.TransactionHistoryController">
   <stylesheets>
      <URL value="@../css/transaction-history.css" />
   </stylesheets>
   <children>
      <!-- Dashboard Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label text="Transaction History Dashboard" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnRefresh" mnemonicParsing="false" onAction="#handleRefresh" text="Refresh" />
            <Button fx:id="btnExport" mnemonicParsing="false" onAction="#handleExport" text="Export" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Metrics Row -->
      <HBox spacing="20.0" alignment="CENTER_LEFT">
         <children>
            <VBox spacing="5.0">
               <children>
                  <Label fx:id="lblTotalTransactions" text="0" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
                  <Label text="Total Transactions" style="-fx-font-size: 12px;" />
               </children>
            </VBox>
            <VBox spacing="5.0">
               <children>
                  <Label fx:id="lblTotalAmount" text="0.00" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
                  <Label text="Total Revenue" style="-fx-font-size: 12px;" />
               </children>
            </VBox>
            <VBox spacing="5.0">
               <children>
                  <Label fx:id="lblAverageTransaction" text="0.00" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
                  <Label text="Average Sale" style="-fx-font-size: 12px;" />
               </children>
            </VBox>
            <VBox spacing="5.0">
               <children>
                  <Label fx:id="lblSelectedPeriod" text="All Time" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
                  <Label text="Period" style="-fx-font-size: 12px;" />
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Filter Section -->
      <VBox spacing="10.0">
         <children>
            <!-- Quick Filters -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="Quick Filters:" />
                  <ComboBox fx:id="cmbQuickFilter" onAction="#handleQuickFilter" prefWidth="150.0" promptText="Select Period" />
                  <Label text="Hours:" />
                  <ComboBox fx:id="cmbTimeRange" onAction="#handleTimeRangeFilter" prefWidth="120.0" promptText="Last Hours" />
                  <Label text="Search:" />
                  <TextField fx:id="txtSearchTransactionId" promptText="Transaction ID" prefWidth="150.0" />
               </children>
            </HBox>

            <!-- Enhanced Customer Search -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="Customer Search:" style="-fx-font-weight: bold;" />
                  <TextField fx:id="txtCustomerSearch" promptText="Search by name, phone, or email..." prefWidth="250.0" onKeyReleased="#handleCustomerSearch" styleClass="customer-search-field" />
                  <Button fx:id="btnClearCustomerFilter" text="Clear Customer Filter" onAction="#handleClearCustomerFilter" visible="false" styleClass="clear-customer-filter-btn" />
                  <Label fx:id="lblSelectedCustomer" text="" styleClass="selected-customer-label" />
               </children>
            </HBox>

            <!-- Date Range and Status -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="From:" />
                  <DatePicker fx:id="dateFrom" promptText="Start Date" prefWidth="120.0" />
                  <TextField fx:id="timeFrom" promptText="HH:MM" prefWidth="60.0" />
                  <Label text="To:" />
                  <DatePicker fx:id="dateTo" promptText="End Date" prefWidth="120.0" />
                  <TextField fx:id="timeTo" promptText="HH:MM" prefWidth="60.0" />
                  <Label text="Status:" />
                  <ComboBox fx:id="cmbStatus" promptText="All" prefWidth="100.0" />
                  <Label text="Payment:" />
                  <ComboBox fx:id="cmbPaymentMethod" promptText="All" prefWidth="100.0" />
               </children>
            </HBox>

            <!-- Filter Actions -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Button fx:id="btnApplyFilter" mnemonicParsing="false" onAction="#handleApplyFilter" text="Apply Filter" />
                  <Button fx:id="btnClearFilter" mnemonicParsing="false" onAction="#handleClearFilter" text="Clear All" />
                  <Label fx:id="lblActiveFilter" text="Filter: All Time" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Label fx:id="lblFilteredCount" text="Showing: 0 transactions" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </VBox>

      <!-- Customer Analytics Panel -->
      <VBox fx:id="customerAnalyticsPanel" spacing="10.0" visible="false" managed="false" styleClass="customer-analytics-panel">
         <children>
            <Label text="Customer Analytics Dashboard" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />

            <!-- Customer Info Row -->
            <HBox spacing="15.0" alignment="CENTER_LEFT">
               <children>
                  <VBox spacing="3.0" styleClass="customer-info-card">
                     <children>
                        <Label fx:id="lblCustomerName" text="Customer Name" styleClass="customer-metric-value" />
                        <Label fx:id="lblCustomerContact" text="Contact Info" styleClass="customer-metric-label" />
                     </children>
                  </VBox>
                  <VBox spacing="3.0" styleClass="customer-info-card">
                     <children>
                        <Label fx:id="lblCustomerTransactions" text="0" styleClass="customer-metric-value" />
                        <Label text="Total Transactions" styleClass="customer-metric-label" />
                     </children>
                  </VBox>
                  <VBox spacing="3.0" styleClass="customer-info-card">
                     <children>
                        <Label fx:id="lblCustomerLifetimeValue" text="0.00" styleClass="customer-metric-value" />
                        <Label text="Lifetime Value" styleClass="customer-metric-label" />
                     </children>
                  </VBox>
                  <VBox spacing="3.0" styleClass="customer-info-card">
                     <children>
                        <Label fx:id="lblCustomerAverage" text="0.00" styleClass="customer-metric-value" />
                        <Label text="Average Order" styleClass="customer-metric-label" />
                     </children>
                  </VBox>
                  <VBox spacing="3.0" styleClass="customer-info-card">
                     <children>
                        <Label fx:id="lblCustomerLastPurchase" text="N/A" styleClass="customer-metric-value" />
                        <Label text="Last Purchase" styleClass="customer-metric-label" />
                     </children>
                  </VBox>
                  <VBox spacing="3.0" styleClass="customer-info-card">
                     <children>
                        <Label fx:id="lblCustomerPreferredPayment" text="N/A" styleClass="customer-metric-value" />
                        <Label text="Preferred Payment" styleClass="customer-metric-label" />
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Additional Customer Details -->
            <HBox spacing="15.0" alignment="CENTER_LEFT">
               <children>
                  <VBox spacing="3.0" styleClass="customer-info-card">
                     <children>
                        <Label fx:id="lblCustomerCategories" text="N/A" styleClass="customer-metric-value" />
                        <Label text="Top Categories" styleClass="customer-metric-label" />
                     </children>
                  </VBox>
                  <VBox spacing="3.0" styleClass="customer-info-card">
                     <children>
                        <Label fx:id="lblCustomerMembership" text="Standard" styleClass="customer-metric-value" />
                        <Label text="Membership Level" styleClass="customer-metric-label" />
                     </children>
                  </VBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnExportCustomerData" text="Export Customer Data" onAction="#handleExportCustomerData" styleClass="export-customer-btn" />
               </children>
            </HBox>
         </children>
      </VBox>

      <!-- Transaction Table -->
      <TableView fx:id="tblTransactions" VBox.vgrow="ALWAYS">
        <columns>
          <TableColumn fx:id="colTransactionNumber" prefWidth="120.0" text="Transaction #" />
          <TableColumn fx:id="colDate" prefWidth="100.0" text="Date" />
          <TableColumn fx:id="colTime" prefWidth="80.0" text="Time" />
          <TableColumn fx:id="colCustomer" prefWidth="150.0" text="Customer" />
          <TableColumn fx:id="colItems" prefWidth="60.0" text="Items" />
          <TableColumn fx:id="colSubtotal" prefWidth="80.0" text="Subtotal" />
          <TableColumn fx:id="colDiscount" prefWidth="80.0" text="Discount" />
          <TableColumn fx:id="colTotal" prefWidth="80.0" text="Total" />
          <TableColumn fx:id="colPaymentMethod" prefWidth="100.0" text="Payment" />
          <TableColumn fx:id="colStatus" prefWidth="80.0" text="Status" />
          <TableColumn fx:id="colActions" prefWidth="300.0" text="Actions" />
        </columns>
         <contextMenu>
            <ContextMenu>
              <items>
                <MenuItem fx:id="menuViewDetails" mnemonicParsing="false" onAction="#handleViewDetails" text="View Details" />
                <MenuItem fx:id="menuPrintReceipt" mnemonicParsing="false" onAction="#handlePrintReceipt" text="Print Receipt" />
                <SeparatorMenuItem />
                <MenuItem fx:id="menuRefund" mnemonicParsing="false" onAction="#handleRefund" text="Process Refund" />
              </items>
            </ContextMenu>
         </contextMenu>
      </TableView>
   </children>
</VBox>
