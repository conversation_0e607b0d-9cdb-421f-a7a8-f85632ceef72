package com.clothingstore.model;

import java.time.LocalDateTime;

/**
 * Data model for customer report entries
 */
public class CustomerReportData {

    private String customerName;
    private String phone;
    private int totalPurchases;
    private double totalSpent;
    private LocalDateTime lastPurchase;
    private String status;
    private int loyaltyPoints;

    public CustomerReportData() {
    }

    public CustomerReportData(String customerName, int totalPurchases, double totalSpent) {
        this.customerName = customerName;
        this.totalPurchases = totalPurchases;
        this.totalSpent = totalSpent;
    }

    public CustomerReportData(String customerName, String phone, int totalPurchases,
            double totalSpent, LocalDateTime lastPurchase, String status, int loyaltyPoints) {
        this.customerName = customerName;
        this.phone = phone;
        this.totalPurchases = totalPurchases;
        this.totalSpent = totalSpent;
        this.lastPurchase = lastPurchase;
        this.status = status;
        this.loyaltyPoints = loyaltyPoints;
    }

    // Getters and Setters
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getTotalPurchases() {
        return totalPurchases;
    }

    public void setTotalPurchases(int totalPurchases) {
        this.totalPurchases = totalPurchases;
    }

    public double getTotalSpent() {
        return totalSpent;
    }

    public void setTotalSpent(double totalSpent) {
        this.totalSpent = totalSpent;
    }

    public LocalDateTime getLastPurchase() {
        return lastPurchase;
    }

    public void setLastPurchase(LocalDateTime lastPurchase) {
        this.lastPurchase = lastPurchase;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getLoyaltyPoints() {
        return loyaltyPoints;
    }

    public void setLoyaltyPoints(int loyaltyPoints) {
        this.loyaltyPoints = loyaltyPoints;
    }

    @Override
    public String toString() {
        return String.format("CustomerReportData{customerName='%s', phone='%s', totalPurchases=%d, totalSpent=%.2f}",
                customerName, phone, totalPurchases, totalSpent);
    }
}
