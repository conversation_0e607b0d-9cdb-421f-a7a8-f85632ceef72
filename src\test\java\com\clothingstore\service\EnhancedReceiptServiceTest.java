package com.clothingstore.service;

import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.model.Product;
import com.clothingstore.model.Customer;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Test class to demonstrate the enhanced receipt service with WhatsApp
 * integration
 */
public class EnhancedReceiptServiceTest {

    public static void main(String[] args) {
        System.out.println("=== Enhanced Receipt Service Test ===");

        try {
            // Create test data
            Transaction testTransaction = createTestTransaction();
            Customer testCustomer = createTestCustomer();

            // Initialize receipt service
            ReceiptPrintingService receiptService = ReceiptPrintingService.getInstance();
            receiptService.setStoreInfo(
                    "Clothing Store",
                    "123 Main Street",
                    "Anytown, ST 12345",
                    "(555) 123-4567",
                    "Thank you for shopping with us!"
            );

            System.out.println("Test transaction created:");
            System.out.println("- Transaction ID: " + testTransaction.getTransactionNumber());
            System.out.println("- Total Amount: $" + testTransaction.getTotalAmount());
            System.out.println("- Items: " + testTransaction.getItems().size());

            System.out.println("\nTest customer created:");
            System.out.println("- Customer: " + testCustomer.getFullName());
            System.out.println("- Phone: " + testCustomer.getPhone());

            // Generate receipt text to verify content
            String receiptText = receiptService.generateReceiptText(
                    testTransaction,
                    new BigDecimal("50.00"),
                    new BigDecimal("5.00")
            );

            System.out.println("\n=== Generated Receipt Content ===");
            System.out.println(receiptText);

            System.out.println("\n=== Enhancement Features ===");
            System.out.println("✅ Enhanced receipt preview dialog with WhatsApp option");
            System.out.println("✅ Customer context integration for phone number lookup");
            System.out.println("✅ Phone number collection dialog for walk-in customers");
            System.out.println("✅ WhatsApp validation and error handling");
            System.out.println("✅ Progress indicators during message sending");
            System.out.println("✅ Success/failure feedback to users");

            System.out.println("\n=== Integration Points ===");
            System.out.println("• SimplePOSController.handleProcessPayment() - Transaction completion");
            System.out.println("• SimplePOSController.handlePrintReceipt() - Manual receipt printing");
            System.out.println("• ReceiptPrintingService.showReceiptPreview() - Enhanced dialog");
            System.out.println("• WhatsAppService integration for message sending");
            System.out.println("• WhatsAppPhoneDialog for phone number collection");

            System.out.println("\n=== User Workflow ===");
            System.out.println("1. Customer completes purchase in POS");
            System.out.println("2. Receipt preview dialog appears with 3 options:");
            System.out.println("   - Print (traditional printing)");
            System.out.println("   - Send via WhatsApp (new feature)");
            System.out.println("   - Close (dismiss dialog)");
            System.out.println("3. If 'Send via WhatsApp' is selected:");
            System.out.println("   a. Check if customer has phone number on file");
            System.out.println("   b. If not, prompt for phone number entry");
            System.out.println("   c. Validate phone number format");
            System.out.println("   d. Send receipt via WhatsApp service");
            System.out.println("   e. Show progress and final status");

            System.out.println("\n=== Test completed successfully! ===");

        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Transaction createTestTransaction() {
        Transaction transaction = new Transaction();
        transaction.setId(1L);
        transaction.setTransactionNumber("TXN-001");
        transaction.setTransactionDate(LocalDateTime.now());
        transaction.setPaymentMethod("CASH");
        transaction.setStatus("COMPLETED");
        transaction.setCashierName("Test Cashier");

        // Set customer for the transaction
        Customer customer = createTestCustomer();
        transaction.setCustomer(customer);

        // Add test items
        Product product1 = new Product();
        product1.setId(1L);
        product1.setName("Blue Jeans");
        product1.setPrice(new BigDecimal("29.99"));

        Product product2 = new Product();
        product2.setId(2L);
        product2.setName("Cotton T-Shirt");
        product2.setPrice(new BigDecimal("15.00"));

        TransactionItem item1 = new TransactionItem(product1, 1);
        TransactionItem item2 = new TransactionItem(product2, 1);

        transaction.addItem(item1);
        transaction.addItem(item2);
        transaction.recalculateAmounts();

        return transaction;
    }

    private static Customer createTestCustomer() {
        Customer customer = new Customer();
        customer.setId(1L);
        customer.setFirstName("John");
        customer.setLastName("Doe");
        customer.setPhone("+1234567890");
        customer.setEmail("<EMAIL>");
        return customer;
    }
}
