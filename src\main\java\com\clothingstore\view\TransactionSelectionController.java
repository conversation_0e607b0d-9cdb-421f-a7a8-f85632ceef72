package com.clothingstore.view;

import java.net.URL;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;

import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.Tooltip;
import javafx.stage.Stage;
import javafx.util.Duration;

/**
 * Controller for Transaction Selection Window Shows a list of all transactions
 * for a customer to choose from
 */
public class TransactionSelectionController implements Initializable {

    // FXML Components
    @FXML
    private Label lblCustomerInfo;
    @FXML
    private Label lblTransactionCount;
    @FXML
    private TableView<Transaction> tblTransactions;
    @FXML
    private TableColumn<Transaction, String> colDate;
    @FXML
    private TableColumn<Transaction, String> colTransactionNumber;
    @FXML
    private TableColumn<Transaction, String> colAmount;
    @FXML
    private TableColumn<Transaction, String> colStatus;
    @FXML
    private TableColumn<Transaction, String> colPaymentMethod;
    @FXML
    private TableColumn<Transaction, String> colDescription;
    @FXML
    private Button btnViewSelected;
    @FXML
    private Button btnViewAll;
    @FXML
    private Button btnClose;

    // Data
    private Customer customer;
    private List<Transaction> transactions;
    private Transaction selectedTransaction;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateFormatter;
    private boolean transactionSelected = false;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");

        setupTableColumns();
        setupTableSelection();
        loadCustomStyling();
    }

    /**
     * Load custom CSS styling for enhanced appearance
     */
    private void loadCustomStyling() {
        try {
            // Load the transaction selection CSS file
            String cssFile = getClass().getResource("/css/transaction-selection.css").toExternalForm();
            if (tblTransactions != null && tblTransactions.getScene() != null) {
                tblTransactions.getScene().getStylesheets().add(cssFile);
            }
        } catch (Exception e) {
            System.err.println("Could not load transaction selection CSS: " + e.getMessage());
        }
    }

    /**
     * Initialize the dialog with customer and transaction data
     */
    public void initializeData(Customer customer, List<Transaction> transactions) {

        this.customer = customer;
        this.transactions = transactions;

        if (transactions != null && !transactions.isEmpty()) {
            // Sort transactions by date (newest first)
            this.transactions.sort((t1, t2) -> t2.getTransactionDate().compareTo(t1.getTransactionDate()));

            updateCustomerInfo();
            loadTransactions();
        } else {
            showNoTransactionsMessage();
        }
    }

    /**
     * Setup table columns
     */
    private void setupTableColumns() {
        System.out.println("Setting up table columns with proper data loading...");

        // Date & Time column
        colDate.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            if (transaction.getTransactionDate() != null) {
                // Format date as "Jun 24, 2025 13:35"
                String formattedDate = dateFormatter.format(transaction.getTransactionDate());
                return new SimpleStringProperty(formattedDate);
            }
            return new SimpleStringProperty("N/A");
        });

        // Transaction Number column
        colTransactionNumber.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String txnNumber = "TXN-" + String.format("%06d", transaction.getId());
            return new SimpleStringProperty(txnNumber);
        });

        // Amount column
        colAmount.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String formattedAmount = currencyFormat.format(transaction.getTotalAmount());
            return new SimpleStringProperty(formattedAmount);
        });

        // Status column
        colStatus.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            return new SimpleStringProperty(transaction.getStatus() != null ? transaction.getStatus() : "UNKNOWN");
        });

        // Payment Method column
        colPaymentMethod.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String paymentMethod = transaction.getPaymentMethod();
            if (paymentMethod != null) {
                // Format payment method to be user-friendly
                switch (paymentMethod.toUpperCase()) {
                    case "CREDIT_CARD":
                        return new SimpleStringProperty("Credit Card");
                    case "DEBIT_CARD":
                        return new SimpleStringProperty("Debit Card");
                    case "CASH":
                        return new SimpleStringProperty("Cash");
                    default:
                        return new SimpleStringProperty(paymentMethod);
                }
            }
            return new SimpleStringProperty("N/A");
        });

        // Description column
        colDescription.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            return new SimpleStringProperty(generateTransactionDescription(transaction));
        });

        System.out.println("Table columns setup completed with proper data loading");

        // Add hover tooltips for better user experience
        setupTableTooltips();
    }

    /**
     * Setup hover tooltips for table rows
     */
    private void setupTableTooltips() {
        tblTransactions.setRowFactory(tv -> {
            TableRow<Transaction> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldTransaction, newTransaction) -> {
                if (newTransaction != null) {
                    // Create detailed tooltip
                    String tooltipText = String.format(
                            "Transaction Details:\n"
                            + "ID: %d\n"
                            + "Number: TXN-%06d\n"
                            + "Date: %s\n"
                            + "Amount: %s\n"
                            + "Status: %s\n"
                            + "Payment: %s\n"
                            + "Description: %s",
                            newTransaction.getId(),
                            newTransaction.getId(),
                            dateFormatter.format(newTransaction.getTransactionDate()),
                            currencyFormat.format(newTransaction.getTotalAmount()),
                            newTransaction.getStatus(),
                            formatPaymentMethod(newTransaction.getPaymentMethod()),
                            generateTransactionDescription(newTransaction)
                    );

                    Tooltip tooltip = new Tooltip(tooltipText);
                    tooltip.setShowDelay(Duration.millis(500));
                    tooltip.setHideDelay(Duration.millis(100));
                    row.setTooltip(tooltip);
                } else {
                    row.setTooltip(null);
                }
            });
            return row;
        });
    }

    /**
     * Format payment method for display
     */
    private String formatPaymentMethod(String paymentMethod) {
        if (paymentMethod != null) {
            switch (paymentMethod.toUpperCase()) {
                case "CREDIT_CARD":
                    return "Credit Card";
                case "DEBIT_CARD":
                    return "Debit Card";
                case "CASH":
                    return "Cash";
                default:
                    return paymentMethod;
            }
        }
        return "N/A";
    }

    /**
     * Generate a descriptive text for the transaction
     */
    private String generateTransactionDescription(Transaction transaction) {
        try {
            if (transaction.getItems() != null && !transaction.getItems().isEmpty()) {
                int itemCount = transaction.getItems().size();
                if (itemCount == 1) {
                    // Single item - show product name
                    String productName = transaction.getItems().get(0).getProduct() != null
                            ? transaction.getItems().get(0).getProduct().getName() : "Unknown Product";
                    return productName;
                } else {
                    // Multiple items - show count
                    return itemCount + " items";
                }
            } else {
                return "No items";
            }
        } catch (Exception e) {
            System.err.println("Error in generateTransactionDescription: " + e.getMessage());
            return "Error loading items";
        }
    }

    /**
     * Setup table selection handling with enhanced visual feedback
     */
    private void setupTableSelection() {
        if (tblTransactions != null) {
            // Selection listener
            tblTransactions.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
                selectedTransaction = newSelection;
                updateButtonStates();
            });

            // Double-click to view transaction
            tblTransactions.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && selectedTransaction != null) {
                    handleViewSelected();
                }
            });

            // Enhanced table properties for better appearance
            tblTransactions.setRowFactory(tv -> {
                javafx.scene.control.TableRow<Transaction> row = new javafx.scene.control.TableRow<>();

                // Add hover effect and selection styling
                row.itemProperty().addListener((obs, oldItem, newItem) -> {
                    if (newItem != null) {
                        // Add custom styling classes
                        row.getStyleClass().add("enhanced-table-row");
                    }
                });

                return row;
            });

            // Disable column reordering for consistent layout
            tblTransactions.getColumns().forEach(column -> {
                column.setReorderable(false);
                column.setSortable(true);
            });
        }
    }

    /**
     * Update customer information display
     */
    private void updateCustomerInfo() {
        if (customer != null && lblCustomerInfo != null) {
            String customerInfo = String.format("Transaction History for: %s (Phone: %s)",
                    customer.getFullName(), customer.getPhone() != null ? customer.getPhone() : "No phone");
            lblCustomerInfo.setText(customerInfo);
        }
    }

    /**
     * Load transactions into the table with debugging
     */
    private void loadTransactions() {
        System.out.println("=== LOADING TRANSACTIONS ===");

        if (transactions != null && tblTransactions != null) {
            System.out.println("Found " + transactions.size() + " transactions to load");

            // Debug: Print transaction details
            for (int i = 0; i < Math.min(5, transactions.size()); i++) {
                Transaction t = transactions.get(i);
                System.out.println("Transaction " + i + ": "
                        + "ID=" + t.getId()
                        + ", Number=" + t.getTransactionNumber()
                        + ", Date=" + t.getTransactionDate()
                        + ", Amount=" + t.getTotalAmount()
                        + ", Status=" + t.getStatus()
                        + ", Payment=" + t.getPaymentMethod());
            }

            // Create observable list and set to table
            ObservableList<Transaction> transactionList = FXCollections.observableArrayList(transactions);

            // Add test data to verify table display works
            addTestDataIfEmpty(transactionList);

            tblTransactions.setItems(transactionList);

            // Force table refresh and layout
            Platform.runLater(() -> {
                tblTransactions.refresh();
                tblTransactions.requestLayout();
                System.out.println("Table refreshed and layout requested");
            });

            if (lblTransactionCount != null) {
                lblTransactionCount.setText(String.format("Total Transactions: %d", transactionList.size()));
            }

            updateButtonStates();
            System.out.println("Transactions loaded into table successfully");
            System.out.println("Table items count: " + tblTransactions.getItems().size());
        } else {
            System.err.println("Cannot load transactions: transactions=" + transactions + ", table=" + tblTransactions);
        }
    }

    /**
     * Add test data to verify table display works
     */
    private void addTestDataIfEmpty(ObservableList<Transaction> transactionList) {
        if (transactionList.isEmpty()) {
            System.out.println("Adding test data to verify table display...");

            // Create test transactions with simple data
            for (int i = 1; i <= 3; i++) {
                Transaction testTxn = new Transaction();
                testTxn.setId((long) i);
                testTxn.setTransactionNumber("TEST-" + String.format("%03d", i));
                testTxn.setTransactionDate(java.time.LocalDateTime.now().minusDays(i));
                testTxn.setTotalAmount(java.math.BigDecimal.valueOf(25.99 * i));
                testTxn.setStatus("COMPLETED");
                testTxn.setPaymentMethod("Cash");

                transactionList.add(testTxn);
            }

            System.out.println("Added " + transactionList.size() + " test transactions");
        }
    }

    /**
     * Update button states based on selection
     */
    private void updateButtonStates() {
        boolean hasSelection = selectedTransaction != null;
        if (btnViewSelected != null) {
            btnViewSelected.setDisable(!hasSelection);
        }
    }

    /**
     * Show message when no transactions are available
     */
    private void showNoTransactionsMessage() {
        if (lblCustomerInfo != null) {
            lblCustomerInfo.setText("No transactions found for this customer");
        }
        if (lblTransactionCount != null) {
            lblTransactionCount.setText("Total Transactions: 0");
        }
        if (btnViewSelected != null) {
            btnViewSelected.setDisable(true);
        }
        if (btnViewAll != null) {
            btnViewAll.setDisable(true);
        }
    }

    // ===== EVENT HANDLERS =====
    @FXML
    private void handleViewSelected() {
        if (selectedTransaction != null) {
            transactionSelected = true;
            closeWindow();
        }
    }

    @FXML
    private void handleViewAll() {
        selectedTransaction = null; // Indicates view all mode
        transactionSelected = true;
        closeWindow();
    }

    @FXML
    private void handleClose() {
        transactionSelected = false;
        closeWindow();
    }

    /**
     * Close the window
     */
    private void closeWindow() {
        Stage stage = (Stage) btnClose.getScene().getWindow();
        stage.close();
    }

    // ===== GETTERS =====
    /**
     * Check if a transaction was selected
     */
    public boolean isTransactionSelected() {
        return transactionSelected;
    }

    /**
     * Get the selected transaction (null means view all)
     */
    public Transaction getSelectedTransaction() {
        return selectedTransaction;
    }

    /**
     * Get all transactions
     */
    public List<Transaction> getAllTransactions() {
        return transactions;
    }

    /**
     * Get customer
     */
    public Customer getCustomer() {
        return customer;
    }
}
