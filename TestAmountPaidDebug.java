import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.dao.PaymentHistoryDAO;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.PaymentHistory;
import java.util.List;
import java.math.BigDecimal;

public class TestAmountPaidDebug {
    public static void main(String[] args) {
        try {
            System.out.println("=== Debugging Amount Paid vs Payment History ===");
            
            // Initialize DAOs
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            PaymentHistoryDAO paymentHistoryDAO = PaymentHistoryDAO.getInstance();
            
            // Check specific transactions
            Long[] transactionIds = {53L, 50L}; // The two partially refunded transactions
            
            for (Long transactionId : transactionIds) {
                Transaction transaction = transactionDAO.findById(transactionId).orElse(null);
                if (transaction == null) {
                    System.out.println("Transaction " + transactionId + " not found");
                    continue;
                }
                
                System.out.println("=== Transaction " + transaction.getTransactionNumber() + " (ID: " + transactionId + ") ===");
                System.out.println("Status: " + transaction.getStatus());
                System.out.println("Total Amount: $" + transaction.getTotalAmount());
                System.out.println("Amount Paid (field): $" + transaction.getAmountPaid());
                System.out.println("Refunded Amount (field): $" + transaction.getRefundedAmount());
                System.out.println("Remaining Balance (calculated): $" + transaction.getRemainingBalance());
                
                // Check payment history totals
                BigDecimal totalPaid = paymentHistoryDAO.getTotalAmountPaid(transactionId);
                BigDecimal totalRefunded = paymentHistoryDAO.getTotalAmountRefunded(transactionId);
                System.out.println("Payment History - Total Paid: $" + totalPaid);
                System.out.println("Payment History - Total Refunded: $" + totalRefunded);
                System.out.println("Payment History - Net: $" + totalPaid.subtract(totalRefunded));
                
                // List all payment history records
                List<PaymentHistory> paymentHistory = paymentHistoryDAO.findByTransactionId(transactionId);
                System.out.println("Payment History Records (" + paymentHistory.size() + "):");
                for (PaymentHistory payment : paymentHistory) {
                    System.out.println("  - " + payment.getPaymentType() + ": $" + payment.getPaymentAmount() + 
                                     " via " + payment.getPaymentMethod() + " on " + payment.getPaymentDate());
                    System.out.println("    Running Balance: $" + payment.getRunningBalance());
                    System.out.println("    Remaining Balance: $" + payment.getRemainingBalance());
                }
                
                // Check logic conditions
                boolean isCompleted = "COMPLETED".equals(transaction.getStatus());
                boolean amountPaidIsZero = transaction.getAmountPaid().compareTo(BigDecimal.ZERO) == 0;
                boolean hasPaymentHistory = !paymentHistory.isEmpty();
                boolean hasPaymentRecords = paymentHistory.stream().anyMatch(p -> "PAYMENT".equals(p.getPaymentType().toString()));
                
                System.out.println("Logic Check:");
                System.out.println("  Is COMPLETED: " + isCompleted);
                System.out.println("  Amount Paid is Zero: " + amountPaidIsZero);
                System.out.println("  Has Payment History: " + hasPaymentHistory);
                System.out.println("  Has Payment Records: " + hasPaymentRecords);
                System.out.println("  Should use special logic: " + (isCompleted && amountPaidIsZero));
                
                // Calculate expected remaining balance
                BigDecimal expectedRemaining;
                if (isCompleted && amountPaidIsZero && !hasPaymentRecords) {
                    // COMPLETED without payment records - use total - refunded
                    expectedRemaining = transaction.getTotalAmount().subtract(
                        transaction.getRefundedAmount() != null ? transaction.getRefundedAmount() : BigDecimal.ZERO);
                    System.out.println("  Expected Remaining (special): $" + expectedRemaining);
                } else {
                    // Standard calculation - use payment history net
                    expectedRemaining = transaction.getTotalAmount().subtract(totalPaid.subtract(totalRefunded));
                    System.out.println("  Expected Remaining (standard): $" + expectedRemaining);
                }
                
                System.out.println();
            }
            
        } catch (Exception e) {
            System.err.println("Error debugging amount paid: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
