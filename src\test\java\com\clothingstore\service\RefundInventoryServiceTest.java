package com.clothingstore.service;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for RefundInventoryService
 */
public class RefundInventoryServiceTest {
    
    private RefundInventoryService inventoryService;
    private ProductDAO productDAO;
    
    @BeforeEach
    void setUp() {
        inventoryService = RefundInventoryService.getInstance();
        productDAO = ProductDAO.getInstance();
    }
    
    @Test
    void testProcessRefundInventoryAdjustments_Success() {
        try {
            // Create test product
            Product testProduct = createTestProduct("TEST-REFUND-001", "Test Refund Product", 50);
            Product savedProduct = productDAO.save(testProduct);
            
            // Create transaction item for refund (returning 5 units)
            TransactionItem refundItem = new TransactionItem();
            refundItem.setProduct(savedProduct);
            refundItem.setQuantity(5); // Returning 5 units
            refundItem.setUnitPrice(new BigDecimal("25.00"));
            refundItem.setLineTotal(new BigDecimal("125.00"));
            
            List<TransactionItem> refundItems = new ArrayList<>();
            refundItems.add(refundItem);
            
            // Process inventory adjustments
            InventoryAdjustmentResult result = inventoryService.processRefundInventoryAdjustments(refundItems);
            
            // Verify result
            assertTrue(result.isSuccess(), "Inventory adjustment should succeed");
            assertNull(result.getErrorMessage(), "No error message should be present");
            assertEquals(1, result.getAdjustmentDetails().size(), "Should have one adjustment detail");
            
            // Verify adjustment details
            InventoryAdjustmentResult.InventoryAdjustmentDetail detail = result.getAdjustmentDetails().get(0);
            assertEquals(savedProduct.getId(), detail.getProductId());
            assertEquals(savedProduct.getName(), detail.getProductName());
            assertEquals(50, detail.getPreviousStock());
            assertEquals(5, detail.getAdjustmentQuantity());
            assertEquals(55, detail.getNewStock());
            assertTrue(detail.isSuccessful());
            
            // Verify actual stock was updated
            Optional<Product> updatedProduct = productDAO.findById(savedProduct.getId());
            assertTrue(updatedProduct.isPresent());
            assertEquals(55, updatedProduct.get().getStockQuantity());
            
            System.out.println("✓ Test passed: Inventory adjustment successful");
            
        } catch (Exception e) {
            fail("Test failed with exception: " + e.getMessage());
        }
    }
    
    @Test
    void testProcessRefundInventoryAdjustments_WithWarnings() {
        try {
            // Create test product with high stock that will trigger warning
            Product testProduct = createTestProduct("TEST-REFUND-002", "Test High Stock Product", 9950);
            Product savedProduct = productDAO.save(testProduct);
            
            // Create transaction item for large refund (returning 100 units - will exceed 10000 limit)
            TransactionItem refundItem = new TransactionItem();
            refundItem.setProduct(savedProduct);
            refundItem.setQuantity(100); // Returning 100 units
            refundItem.setUnitPrice(new BigDecimal("25.00"));
            refundItem.setLineTotal(new BigDecimal("2500.00"));
            
            List<TransactionItem> refundItems = new ArrayList<>();
            refundItems.add(refundItem);
            
            // Process inventory adjustments
            InventoryAdjustmentResult result = inventoryService.processRefundInventoryAdjustments(refundItems);
            
            // Verify result
            assertTrue(result.isSuccess(), "Inventory adjustment should succeed despite warning");
            assertTrue(result.hasWarnings(), "Should have warnings");
            assertFalse(result.getWarnings().isEmpty(), "Warnings list should not be empty");
            
            // Check that warning mentions exceeding maximum
            boolean hasMaxStockWarning = result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("exceeds recommended maximum"));
            assertTrue(hasMaxStockWarning, "Should have warning about exceeding maximum stock");
            
            System.out.println("✓ Test passed: Inventory adjustment with warnings handled correctly");
            
        } catch (Exception e) {
            fail("Test failed with exception: " + e.getMessage());
        }
    }
    
    @Test
    void testProcessRefundInventoryAdjustments_NullProduct() {
        try {
            // Create transaction item with null product
            TransactionItem refundItem = new TransactionItem();
            refundItem.setProduct(null);
            refundItem.setQuantity(5);
            refundItem.setUnitPrice(new BigDecimal("25.00"));
            refundItem.setLineTotal(new BigDecimal("125.00"));
            
            List<TransactionItem> refundItems = new ArrayList<>();
            refundItems.add(refundItem);
            
            // Process inventory adjustments
            InventoryAdjustmentResult result = inventoryService.processRefundInventoryAdjustments(refundItems);
            
            // Verify result
            assertTrue(result.isSuccess(), "Should succeed but skip null product items");
            assertTrue(result.hasWarnings(), "Should have warnings about null product");
            
            // Check that warning mentions null product
            boolean hasNullProductWarning = result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("null product"));
            assertTrue(hasNullProductWarning, "Should have warning about null product");
            
            System.out.println("✓ Test passed: Null product handled correctly with warning");
            
        } catch (Exception e) {
            fail("Test failed with exception: " + e.getMessage());
        }
    }
    
    private Product createTestProduct(String sku, String name, int stock) {
        Product product = new Product();
        product.setSku(sku);
        product.setName(name);
        product.setDescription("Test product for inventory service testing");
        product.setCategory("Test Category");
        product.setBrand("Test Brand");
        product.setPrice(new BigDecimal("29.99"));
        product.setCostPrice(new BigDecimal("15.00"));
        product.setStockQuantity(stock);
        product.setMinStockLevel(10);
        product.setActive(true);
        return product;
    }
}
