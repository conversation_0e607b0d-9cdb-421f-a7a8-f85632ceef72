
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.RefundItem;
import com.clothingstore.model.RefundResult;
import com.clothingstore.model.RefundValidationResult;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.model.TransactionItemRefundSummary;
import com.clothingstore.service.ReceiptPrintingService;
import com.clothingstore.service.RefundItemTrackingService;
import com.clothingstore.service.RefundService;

/**
 * Comprehensive test for Enhanced Partial Refund Functionality Tests the
 * complete workflow of partial refunds with item tracking
 */
public class TestEnhancedPartialRefund {

    public static void main(String[] args) {
        System.out.println("=== ENHANCED PARTIAL REFUND FUNCTIONALITY TEST ===\n");

        try {
            // Initialize services
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            RefundService refundService = RefundService.getInstance();
            RefundItemTrackingService trackingService = new RefundItemTrackingService();
            ReceiptPrintingService receiptService = ReceiptPrintingService.getInstance();

            // Step 1: Find a completed transaction with multiple items
            System.out.println("Step 1: Finding completed transactions with multiple items...");
            List<Transaction> completedTransactions = transactionDAO.findByStatus("COMPLETED");

            Transaction testTransaction = null;
            for (Transaction tx : completedTransactions) {
                if (tx.getItems().size() >= 2) { // Need multiple items for testing
                    testTransaction = tx;
                    break;
                }
            }

            if (testTransaction == null) {
                System.out.println("ERROR: No completed transactions with multiple items found. Creating test transaction...");
                testTransaction = createTestTransaction();
            }

            System.out.println("✅ Found test transaction: " + testTransaction.getTransactionNumber());
            System.out.println("   Total Amount: $" + testTransaction.getTotalAmount());
            System.out.println("   Items Count: " + testTransaction.getItems().size());

            // Display original transaction items
            System.out.println("\n📋 Original Transaction Items:");
            for (TransactionItem item : testTransaction.getItems()) {
                System.out.println("   - " + item.getProductName()
                        + " | Qty: " + item.getQuantity()
                        + " | Price: $" + item.getUnitPrice()
                        + " | Total: $" + item.getLineTotal());
            }

            // Step 2: Test partial refund with specific quantities
            System.out.println("\n" + "=".repeat(60));
            System.out.println("Step 2: Testing Partial Refund with Specific Quantities");
            System.out.println("=".repeat(60));

            // Select first item for partial refund
            TransactionItem firstItem = testTransaction.getItems().get(0);
            int originalQty = firstItem.getQuantity();
            int refundQty = Math.min(1, originalQty); // Refund 1 unit or all if only 1

            System.out.println("🔄 Processing partial refund:");
            System.out.println("   Product: " + firstItem.getProductName());
            System.out.println("   Original Quantity: " + originalQty);
            System.out.println("   Refunding Quantity: " + refundQty);

            // Create refund items list
            List<RefundItem> refundItems = new ArrayList<>();
            RefundItem refundItem = new RefundItem(firstItem);
            refundItem.setRefundQuantity(refundQty);
            refundItem.setRefundAmount(firstItem.getUnitPrice().multiply(BigDecimal.valueOf(refundQty)));
            refundItems.add(refundItem);

            // Process the partial refund
            RefundResult refundResult = refundService.processPartialRefund(
                    testTransaction,
                    refundItems,
                    "Test partial refund",
                    "Test Cashier"
            );

            if (!refundResult.isSuccess()) {
                System.out.println("ERROR: Refund failed: " + refundResult.getMessage());
                return;
            }

            Transaction refundTransaction = refundResult.getRefundTransaction();

            System.out.println("✅ Partial refund processed successfully!");
            System.out.println("   Refund Transaction: " + refundTransaction.getTransactionNumber());
            System.out.println("   Refund Amount: $" + refundTransaction.getTotalAmount());

            // Step 3: Verify refund tracking information
            System.out.println("\n" + "=".repeat(60));
            System.out.println("Step 3: Verifying Refund Tracking Information");
            System.out.println("=".repeat(60));

            // Reload the transaction to get updated item information
            Optional<Transaction> updatedTransactionOpt = transactionDAO.findById(testTransaction.getId());
            if (!updatedTransactionOpt.isPresent()) {
                System.out.println("ERROR: Could not reload transaction!");
                return;
            }
            Transaction updatedTransaction = updatedTransactionOpt.get();

            // Get refund tracking information for the first item using the updated transaction
            TransactionItem updatedFirstItem = updatedTransaction.getItems().get(0);
            TransactionItemRefundSummary refundSummary = trackingService.getTransactionItemRefundSummary(updatedFirstItem.getId());

            if (refundSummary != null) {
                System.out.println("✅ Refund tracking information found:");
                System.out.println("   " + refundSummary.getQuantityDisplay());
                System.out.println("   Original Quantity: " + refundSummary.getOriginalQuantity());
                System.out.println("   Total Refunded: " + refundSummary.getTotalRefundedQuantity());
                System.out.println("   Remaining: " + refundSummary.getRemainingQuantity());
                System.out.println("   Has Refunds: " + refundSummary.hasRefunds());
                System.out.println("   Fully Refunded: " + refundSummary.isFullyRefunded());
            } else {
                System.out.println("ERROR: No refund tracking information found!");
            }

            // Step 4: Test receipt generation with refund tracking
            System.out.println("\n" + "=".repeat(60));
            System.out.println("Step 4: Testing Receipt Generation with Refund Tracking");
            System.out.println("=".repeat(60));

            // Generate receipt for original transaction (should show refund info)
            String receiptText = receiptService.generateReceiptText(testTransaction);
            System.out.println("📄 Updated Receipt with Refund Tracking:");
            System.out.println(receiptText);

            // Check if receipt contains refund tracking information
            boolean hasRefundInfo = receiptText.contains("Returned:") || receiptText.contains("Remaining:");
            if (hasRefundInfo) {
                System.out.println("✅ Receipt contains refund tracking information!");
            } else {
                System.out.println("ERROR: Receipt does not contain refund tracking information!");
            }

            // Step 5: Test additional partial refund on same transaction
            if (refundSummary != null && refundSummary.getRemainingQuantity() > 0) {
                System.out.println("\n" + "=".repeat(60));
                System.out.println("Step 5: Testing Additional Partial Refund");
                System.out.println("=".repeat(60));

                int additionalRefundQty = Math.min(1, refundSummary.getRemainingQuantity());
                System.out.println("🔄 Processing additional partial refund:");
                System.out.println("   Remaining Quantity: " + refundSummary.getRemainingQuantity());
                System.out.println("   Additional Refund Quantity: " + additionalRefundQty);

                // Create second refund
                List<RefundItem> additionalRefundItems = new ArrayList<>();
                RefundItem additionalRefundItem = new RefundItem(updatedFirstItem);

                additionalRefundItem.setRefundQuantity(additionalRefundQty);
                additionalRefundItem.setRefundAmount(updatedFirstItem.getUnitPrice().multiply(BigDecimal.valueOf(additionalRefundQty)));
                additionalRefundItems.add(additionalRefundItem);

                RefundResult secondRefundResult = refundService.processPartialRefund(
                        testTransaction,
                        additionalRefundItems,
                        "Second partial refund test",
                        "Test Cashier"
                );

                if (!secondRefundResult.isSuccess()) {
                    System.out.println("ERROR: Second refund failed: " + secondRefundResult.getMessage());
                    return;
                }

                Transaction secondRefundTransaction = secondRefundResult.getRefundTransaction();

                System.out.println("✅ Additional partial refund processed!");
                System.out.println("   Second Refund Transaction: " + secondRefundTransaction.getTransactionNumber());

                // Verify updated tracking
                TransactionItemRefundSummary updatedSummary = trackingService.getTransactionItemRefundSummary(updatedFirstItem.getId());
                if (updatedSummary != null) {
                    System.out.println("✅ Updated refund tracking:");
                    System.out.println("   " + updatedSummary.getQuantityDisplay());
                }
            }

            // Step 6: Test refund validation (prevent over-refunding)
            System.out.println("\n" + "=".repeat(60));
            System.out.println("Step 6: Testing Refund Validation (Over-refund Prevention)");
            System.out.println("=".repeat(60));

            // Try to refund more than available
            List<RefundItem> invalidRefundItems = new ArrayList<>();
            RefundItem invalidRefundItem = new RefundItem(updatedFirstItem);
            invalidRefundItem.setRefundQuantity(999); // Excessive quantity
            invalidRefundItem.setRefundAmount(updatedFirstItem.getUnitPrice().multiply(BigDecimal.valueOf(999)));
            invalidRefundItems.add(invalidRefundItem);

            RefundValidationResult validationResult = trackingService.validateRefundRequest(
                    testTransaction.getId(),
                    invalidRefundItems
            );

            if (!validationResult.isValid()) {
                System.out.println("✅ Validation correctly prevented over-refunding!");
                System.out.println("   Error: " + validationResult.getErrorMessage());
            } else {
                System.out.println("ERROR: Validation failed to prevent over-refunding!");
            }

            System.out.println("\n" + "=".repeat(60));
            System.out.println("✅ ENHANCED PARTIAL REFUND TEST COMPLETED SUCCESSFULLY!");
            System.out.println("=".repeat(60));

        } catch (Exception e) {
            System.err.println("ERROR: Test failed with error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Transaction createTestTransaction() throws SQLException {
        System.out.println("Creating test transaction with multiple items...");

        // This is a simplified version - in real scenario, we'd use the POS system
        // For now, we'll just find an existing transaction or suggest manual creation
        TransactionDAO transactionDAO = TransactionDAO.getInstance();
        List<Transaction> allTransactions = transactionDAO.findAll();

        if (!allTransactions.isEmpty()) {
            return allTransactions.get(0); // Return first available transaction
        }

        throw new RuntimeException("No transactions available for testing. Please create a transaction through the POS system first.");
    }
}
