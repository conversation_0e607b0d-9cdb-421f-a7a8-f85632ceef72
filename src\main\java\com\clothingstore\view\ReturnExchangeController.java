package com.clothingstore.view;

// import com.clothingstore.dao.ReturnExchangeDAO; // Commented out - DAO not implemented
// import com.clothingstore.dao.ReturnExchangeItemDAO; // Commented out - <PERSON>AO not implemented
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.ReturnExchange;
import com.clothingstore.model.ReturnExchangeItem;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.util.AlertUtil;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;

import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Controller for Return/Exchange Management
 */
public class ReturnExchangeController implements Initializable {

    // FXML Components - Search and Filters
    @FXML private TextField transactionSearchField;
    @FXML private ComboBox<String> statusFilter;
    @FXML private ComboBox<String> typeFilter;
    @FXML private DatePicker startDatePicker;
    @FXML private DatePicker endDatePicker;
    @FXML private Button searchButton;
    @FXML private Button newReturnButton;
    @FXML private Button newExchangeButton;

    // FXML Components - Return/Exchange List
    @FXML private TableView<ReturnExchange> returnExchangeTable;
    @FXML private TableColumn<ReturnExchange, String> returnNumberColumn;
    @FXML private TableColumn<ReturnExchange, String> typeColumn;
    @FXML private TableColumn<ReturnExchange, String> customerColumn;
    @FXML private TableColumn<ReturnExchange, String> originalTransactionColumn;
    @FXML private TableColumn<ReturnExchange, String> requestDateColumn;
    @FXML private TableColumn<ReturnExchange, String> statusColumn;
    @FXML private TableColumn<ReturnExchange, String> amountColumn;

    // FXML Components - Details Panel
    @FXML private Label detailsReturnNumberLabel;
    @FXML private Label detailsTypeLabel;
    @FXML private Label detailsStatusLabel;
    @FXML private Label detailsCustomerLabel;
    @FXML private Label detailsOriginalTransactionLabel;
    @FXML private Label detailsRequestDateLabel;
    @FXML private Label detailsProcessedByLabel;
    @FXML private Label detailsTotalAmountLabel;
    @FXML private TextArea detailsNotesArea;

    // FXML Components - Items Table
    @FXML private TableView<ReturnExchangeItem> itemsTable;
    @FXML private TableColumn<ReturnExchangeItem, String> itemProductColumn;
    @FXML private TableColumn<ReturnExchangeItem, String> itemOriginalQtyColumn;
    @FXML private TableColumn<ReturnExchangeItem, String> itemReturnQtyColumn;
    @FXML private TableColumn<ReturnExchangeItem, String> itemConditionColumn;
    @FXML private TableColumn<ReturnExchangeItem, String> itemActionColumn;
    @FXML private TableColumn<ReturnExchangeItem, String> itemAmountColumn;

    // FXML Components - Action Buttons
    @FXML private Button approveButton;
    @FXML private Button rejectButton;
    @FXML private Button completeButton;
    @FXML private Button printButton;

    // FXML Components - Summary
    @FXML private Label totalReturnsLabel;
    @FXML private Label totalExchangesLabel;
    @FXML private Label pendingCountLabel;
    @FXML private Label totalRefundAmountLabel;

    // Data and Services
    private ObservableList<ReturnExchange> returnExchangeData;
    private ObservableList<ReturnExchangeItem> itemsData;
    private ReturnExchange selectedReturnExchange;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateTimeFormatter;
    // private ReturnExchangeDAO returnExchangeDAO; // Commented out - DAO not implemented
    // private ReturnExchangeItemDAO returnExchangeItemDAO; // Commented out - DAO not implemented
    private TransactionDAO transactionDAO;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeServices();
        initializeFormatters();
        initializeTableColumns();
        initializeComponents();
        loadReturnExchangeData();
        updateSummary();
    }

    private void initializeServices() {
        returnExchangeData = FXCollections.observableArrayList();
        itemsData = FXCollections.observableArrayList();

        returnExchangeTable.setItems(returnExchangeData);
        itemsTable.setItems(itemsData);

        try {
            // Initialize DAOs
            // returnExchangeDAO = ReturnExchangeDAO.getInstance(); // Commented out - DAO not implemented
            // returnExchangeItemDAO = ReturnExchangeItemDAO.getInstance(); // Commented out - DAO not implemented
            transactionDAO = TransactionDAO.getInstance();

            // Test database connectivity
            // returnExchangeDAO.findAll("All", "All", null, null, null); // Commented out - DAO not implemented
        } catch (Exception e) {
            // Database tables might not exist yet - show warning and initialize schema
            AlertUtil.showWarning("Database Setup Required",
                "Return/Exchange database tables need to be created. Please run the database initializer first.\n\n" +
                "Error: " + e.getMessage());

            // Try to create the schema
            try {
                // com.clothingstore.util.ReturnExchangeSchemaUpdater.runAllUpdates(); // Commented out - schema updater not implemented
                AlertUtil.showInfo("Database Initialized", "Return/Exchange database tables have been created successfully.");

                // Re-initialize DAOs
                // returnExchangeDAO = ReturnExchangeDAO.getInstance(); // Commented out - DAO not implemented
                // returnExchangeItemDAO = ReturnExchangeItemDAO.getInstance(); // Commented out - DAO not implemented
                transactionDAO = TransactionDAO.getInstance();
            } catch (Exception schemaError) {
                AlertUtil.showError("Database Error", "Failed to create database schema: " + schemaError.getMessage());
            }
        }
    }

    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");
    }

    private void initializeTableColumns() {
        // Return/Exchange Table
        returnNumberColumn.setCellValueFactory(new PropertyValueFactory<>("returnNumber"));
        typeColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getDisplayType()));
        customerColumn.setCellValueFactory(new PropertyValueFactory<>("customerName"));
        originalTransactionColumn.setCellValueFactory(new PropertyValueFactory<>("originalTransactionNumber"));
        requestDateColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getFormattedRequestDate()));
        statusColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getDisplayStatus()));
        amountColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(currencyFormat.format(cellData.getValue().getTotalAmount())));

        // Items Table
        itemProductColumn.setCellValueFactory(new PropertyValueFactory<>("productName"));
        itemOriginalQtyColumn.setCellValueFactory(new PropertyValueFactory<>("originalQuantity"));
        itemReturnQtyColumn.setCellValueFactory(new PropertyValueFactory<>("returnQuantity"));
        itemConditionColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getDisplayCondition()));
        itemActionColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getDisplayAction()));
        itemAmountColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(currencyFormat.format(cellData.getValue().getLineTotal())));

        // Row selection handler
        returnExchangeTable.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                selectedReturnExchange = newSelection;
                updateDetailsPanel();
                updateActionButtons();
            });

        // Row factory for status styling
        returnExchangeTable.setRowFactory(tv -> new TableRow<ReturnExchange>() {
            @Override
            protected void updateItem(ReturnExchange item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setStyle("");
                } else {
                    switch (item.getStatus()) {
                        case "PENDING":
                            setStyle("-fx-background-color: #fff3cd;");
                            break;
                        case "APPROVED":
                            setStyle("-fx-background-color: #d1ecf1;");
                            break;
                        case "REJECTED":
                            setStyle("-fx-background-color: #f8d7da;");
                            break;
                        case "COMPLETED":
                            setStyle("-fx-background-color: #d4edda;");
                            break;
                        default:
                            setStyle("");
                    }
                }
            }
        });
    }

    private void initializeComponents() {
        // Status filter
        statusFilter.setItems(FXCollections.observableArrayList(
            "All", "PENDING", "APPROVED", "REJECTED", "COMPLETED"));
        statusFilter.setValue("All");

        // Type filter
        typeFilter.setItems(FXCollections.observableArrayList(
            "All", "RETURN", "EXCHANGE"));
        typeFilter.setValue("All");

        // Date filters - default to last 30 days
        endDatePicker.setValue(LocalDate.now());
        startDatePicker.setValue(LocalDate.now().minusDays(30));

        // Event handlers
        statusFilter.setOnAction(e -> loadReturnExchangeData());
        typeFilter.setOnAction(e -> loadReturnExchangeData());
        startDatePicker.setOnAction(e -> loadReturnExchangeData());
        endDatePicker.setOnAction(e -> loadReturnExchangeData());
        transactionSearchField.textProperty().addListener((obs, oldText, newText) -> loadReturnExchangeData());
    }

    private void loadReturnExchangeData() {
        try {
            String status = statusFilter.getValue();
            String type = typeFilter.getValue();
            LocalDateTime startDate = startDatePicker.getValue() != null ?
                startDatePicker.getValue().atStartOfDay() : null;
            LocalDateTime endDate = endDatePicker.getValue() != null ?
                endDatePicker.getValue().atTime(23, 59, 59) : null;
            String searchTerm = transactionSearchField.getText();

            // List<ReturnExchange> data = returnExchangeDAO.findAll(status, type, startDate, endDate, searchTerm); // Commented out - DAO not implemented
            List<ReturnExchange> data = new ArrayList<>(); // Mock empty data
            returnExchangeData.setAll(data);
            updateSummary();
        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load return/exchange data: " + e.getMessage());
            e.printStackTrace();
        }
    }



    private void updateDetailsPanel() {
        if (selectedReturnExchange == null) {
            clearDetailsPanel();
            itemsData.clear();
            return;
        }

        ReturnExchange re = selectedReturnExchange;
        detailsReturnNumberLabel.setText(re.getReturnNumber());
        detailsTypeLabel.setText(re.getDisplayType());
        detailsStatusLabel.setText(re.getDisplayStatus());
        detailsCustomerLabel.setText(re.getCustomerName());
        detailsOriginalTransactionLabel.setText(re.getOriginalTransactionNumber());
        detailsRequestDateLabel.setText(re.getFormattedRequestDate());
        detailsProcessedByLabel.setText(re.getProcessedBy() != null ? re.getProcessedBy() : "--");
        detailsTotalAmountLabel.setText(currencyFormat.format(re.getTotalAmount()));
        detailsNotesArea.setText(re.getNotes() != null ? re.getNotes() : "");

        // Load items for selected return/exchange
        loadReturnExchangeItems(re);
    }

    private void clearDetailsPanel() {
        detailsReturnNumberLabel.setText("--");
        detailsTypeLabel.setText("--");
        detailsStatusLabel.setText("--");
        detailsCustomerLabel.setText("--");
        detailsOriginalTransactionLabel.setText("--");
        detailsRequestDateLabel.setText("--");
        detailsProcessedByLabel.setText("--");
        detailsTotalAmountLabel.setText("--");
        detailsNotesArea.setText("");
    }

    private void loadReturnExchangeItems(ReturnExchange returnExchange) {
        try {
            // List<ReturnExchangeItem> items = returnExchangeItemDAO.findByReturnExchangeId(returnExchange.getId()); // Commented out - DAO not implemented
            List<ReturnExchangeItem> items = new ArrayList<>(); // Mock empty data
            itemsData.setAll(items);
        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load return/exchange items: " + e.getMessage());
            e.printStackTrace();
        }
    }



    private void updateActionButtons() {
        boolean hasSelection = selectedReturnExchange != null;
        boolean isPending = hasSelection && selectedReturnExchange.isPending();
        boolean isApproved = hasSelection && selectedReturnExchange.isApproved();
        
        approveButton.setDisable(!isPending);
        rejectButton.setDisable(!isPending);
        completeButton.setDisable(!isApproved);
        printButton.setDisable(!hasSelection);
    }

    private void updateSummary() {
        try {
            // Use database statistics for more accurate counts
            LocalDateTime startDate = startDatePicker.getValue() != null ?
                startDatePicker.getValue().atStartOfDay() : LocalDateTime.now().minusMonths(1);
            LocalDateTime endDate = endDatePicker.getValue() != null ?
                endDatePicker.getValue().atTime(23, 59, 59) : LocalDateTime.now();

            // ReturnExchangeDAO.ReturnExchangeStats stats = returnExchangeDAO.getStatistics(startDate, endDate); // Commented out - DAO not implemented
            // Mock stats for now
            double totalRefunds = 0.0;
            int totalReturns = 0;
            int totalExchanges = 0;

            totalReturnsLabel.setText(String.valueOf(totalReturns));
            totalExchangesLabel.setText(String.valueOf(totalExchanges));
            pendingCountLabel.setText("0");
            totalRefundAmountLabel.setText(currencyFormat.format(totalRefunds));
        } catch (Exception e) {
            // Fallback to current data if database query fails
            long totalReturns = returnExchangeData.stream().filter(ReturnExchange::isReturn).count();
            long totalExchanges = returnExchangeData.stream().filter(ReturnExchange::isExchange).count();
            long pendingCount = returnExchangeData.stream().filter(ReturnExchange::isPending).count();

            BigDecimal totalRefundAmount = returnExchangeData.stream()
                .filter(re -> re.isReturn() && re.getRefundAmount() != null)
                .map(ReturnExchange::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            totalReturnsLabel.setText(String.valueOf(totalReturns));
            totalExchangesLabel.setText(String.valueOf(totalExchanges));
            pendingCountLabel.setText(String.valueOf(pendingCount));
            totalRefundAmountLabel.setText(currencyFormat.format(totalRefundAmount));
        }
    }

    @FXML
    private void handleSearch() {
        loadReturnExchangeData();
    }

    @FXML
    private void handleNewReturn() {
        AlertUtil.showInfo("Feature Not Available", "Return functionality is not yet implemented. This is a UI demonstration.");
    }

    @FXML
    private void handleNewExchange() {
        AlertUtil.showInfo("Feature Not Available", "Exchange functionality is not yet implemented. This is a UI demonstration.");
    }

    private void showNewReturnDialog() {
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle("New Return");
        dialog.setHeaderText("Enter transaction number to process return");

        ButtonType createButtonType = new ButtonType("Create Return", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(createButtonType, ButtonType.CANCEL);

        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextField transactionField = new TextField();
        transactionField.setPromptText("Transaction number");

        grid.add(new Label("Transaction:"), 0, 0);
        grid.add(transactionField, 1, 0);

        dialog.getDialogPane().setContent(grid);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == createButtonType) {
                return transactionField.getText();
            }
            return null;
        });

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(transactionNumber -> {
            try {
                // Find the original transaction
                Optional<Transaction> transactionOpt = transactionDAO.findByTransactionNumber(transactionNumber);
                if (!transactionOpt.isPresent()) {
                    AlertUtil.showError("Transaction Not Found", "No transaction found with number: " + transactionNumber);
                    return;
                }
                Transaction originalTransaction = transactionOpt.get();
                if (originalTransaction == null) {
                    AlertUtil.showWarning("Transaction Not Found", "Transaction " + transactionNumber + " not found.");
                    return;
                }

                // Generate return number - mock implementation
                String returnNumber = "RET" + System.currentTimeMillis();

                // Create return exchange record
                ReturnExchange returnExchange = new ReturnExchange(returnNumber, originalTransaction.getId(),
                    originalTransaction.getCustomer() != null ? originalTransaction.getCustomer().getId() : null, "RETURN");
                returnExchange.setOriginalTransactionNumber(transactionNumber);
                returnExchange.setCustomerName(originalTransaction.getCustomer() != null ?
                    originalTransaction.getCustomer().getFullName() : "Walk-in Customer");
                returnExchange.setTotalAmount(originalTransaction.getTotal());
                returnExchange.setRefundAmount(originalTransaction.getTotal());
                returnExchange.setPaymentMethod(originalTransaction.getPaymentMethod());

                // ReturnExchange created = returnExchangeDAO.create(returnExchange); // Commented out - DAO not implemented

                // Create return items for all transaction items - commented out for now
                // List<ReturnExchangeItem> returnItems = new ArrayList<>();
                // for (TransactionItem item : originalTransaction.getItems()) {
                //     ReturnExchangeItem returnItem = new ReturnExchangeItem(created.getId(),
                //         item.getProduct().getId(), item.getProduct().getName(),
                //         item.getQuantity(), item.getQuantity(), item.getUnitPrice());
                //     returnItem.setProductSku(item.getProduct().getSku());
                //     returnItem.setReason("Customer return");
                //     returnItems.add(returnItem);
                // }

                // if (!returnItems.isEmpty()) {
                //     returnExchangeItemDAO.createBatch(returnItems);
                // }

                AlertUtil.showInfo("Return Created", "Return " + returnNumber + " created successfully for transaction: " + transactionNumber);
                loadReturnExchangeData();
            } catch (Exception e) {
                AlertUtil.showError("Database Error", "Failed to create return: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private void showNewExchangeDialog() {
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle("New Exchange");
        dialog.setHeaderText("Enter transaction number to process exchange");

        ButtonType createButtonType = new ButtonType("Create Exchange", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(createButtonType, ButtonType.CANCEL);

        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextField transactionField = new TextField();
        transactionField.setPromptText("Transaction number");

        grid.add(new Label("Transaction:"), 0, 0);
        grid.add(transactionField, 1, 0);

        dialog.getDialogPane().setContent(grid);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == createButtonType) {
                return transactionField.getText();
            }
            return null;
        });

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(transactionNumber -> {
            try {
                // Find the original transaction
                Optional<Transaction> transactionOpt = transactionDAO.findByTransactionNumber(transactionNumber);
                if (!transactionOpt.isPresent()) {
                    AlertUtil.showError("Transaction Not Found", "No transaction found with number: " + transactionNumber);
                    return;
                }
                Transaction originalTransaction = transactionOpt.get();
                if (originalTransaction == null) {
                    AlertUtil.showWarning("Transaction Not Found", "Transaction " + transactionNumber + " not found.");
                    return;
                }

                // Generate exchange number - mock implementation
                String exchangeNumber = "EXC" + System.currentTimeMillis();

                // Create exchange record
                ReturnExchange exchange = new ReturnExchange(exchangeNumber, originalTransaction.getId(),
                    originalTransaction.getCustomer() != null ? originalTransaction.getCustomer().getId() : null, "EXCHANGE");
                exchange.setOriginalTransactionNumber(transactionNumber);
                exchange.setCustomerName(originalTransaction.getCustomer() != null ?
                    originalTransaction.getCustomer().getFullName() : "Walk-in Customer");
                exchange.setTotalAmount(originalTransaction.getTotal());
                exchange.setPaymentMethod(originalTransaction.getPaymentMethod());

                // ReturnExchange created = returnExchangeDAO.create(exchange); // Commented out - DAO not implemented

                AlertUtil.showInfo("Exchange Created", "Exchange " + exchangeNumber + " created successfully for transaction: " + transactionNumber +
                    "\nPlease add exchange items and specify the new products.");
                loadReturnExchangeData();
            } catch (Exception e) {
                AlertUtil.showError("Database Error", "Failed to create exchange: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    @FXML
    private void handleApprove() {
        AlertUtil.showInfo("Feature Not Available", "Approve functionality is not yet implemented. This is a UI demonstration.");
    }

    @FXML
    private void handleReject() {
        AlertUtil.showInfo("Feature Not Available", "Reject functionality is not yet implemented. This is a UI demonstration.");
    }

    @FXML
    private void handleComplete() {
        AlertUtil.showInfo("Feature Not Available", "Complete functionality is not yet implemented. This is a UI demonstration.");
    }

    @FXML
    private void handlePrint() {
        if (selectedReturnExchange == null) {
            AlertUtil.showWarning("No Selection", "Please select a return/exchange to print.");
            return;
        }

        // TODO: Implement print functionality
        String receipt = generateReturnExchangeReceipt(selectedReturnExchange);
        AlertUtil.showInfo("Print Receipt", receipt);
    }

    private String generateReturnExchangeReceipt(ReturnExchange returnExchange) {
        StringBuilder receipt = new StringBuilder();
        receipt.append("RETURN/EXCHANGE RECEIPT\n");
        receipt.append("========================\n\n");
        receipt.append("Number: ").append(returnExchange.getReturnNumber()).append("\n");
        receipt.append("Type: ").append(returnExchange.getType()).append("\n");
        receipt.append("Status: ").append(returnExchange.getStatus()).append("\n");
        receipt.append("Customer: ").append(returnExchange.getCustomerName()).append("\n");
        receipt.append("Original Transaction: ").append(returnExchange.getOriginalTransactionNumber()).append("\n");
        receipt.append("Date: ").append(returnExchange.getFormattedRequestDate()).append("\n");
        
        if (returnExchange.getReason() != null) {
            receipt.append("Reason: ").append(returnExchange.getReason()).append("\n");
        }
        
        receipt.append("\nTotal Amount: ").append(currencyFormat.format(returnExchange.getTotalAmount())).append("\n");
        
        if (returnExchange.getRefundAmount() != null) {
            receipt.append("Refund Amount: ").append(currencyFormat.format(returnExchange.getRefundAmount())).append("\n");
        }
        
        if (returnExchange.getProcessedBy() != null) {
            receipt.append("Processed By: ").append(returnExchange.getProcessedBy()).append("\n");
        }
        
        return receipt.toString();
    }

    @FXML
    private void handleRefreshData() {
        loadReturnExchangeData();
    }
}
