package com.clothingstore.test;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.TransactionDAO;
import java.sql.SQLException;

/**
 * Test to verify JavaFX application components can be loaded without JavaFX runtime
 */
public class JavaFXApplicationTest {
    
    public static void main(String[] args) {
        System.out.println("JAVAFX APPLICATION COMPONENT TEST");
        System.out.println("=================================");
        
        try {
            // Test database initialization
            testDatabaseInitialization();
            
            // Test DAO components
            testDAOComponents();
            
            // Test application readiness
            testApplicationReadiness();
            
            System.out.println("\n=== JAVAFX APPLICATION COMPONENT TEST COMPLETED ===");
            
        } catch (Exception e) {
            System.err.println("JAVAFX APPLICATION TEST FAILED: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseManager.getInstance().closeConnection();
        }
    }
    
    private static void testDatabaseInitialization() throws SQLException {
        System.out.println("\n--- Testing Database Initialization ---");
        
        DatabaseManager dbManager = DatabaseManager.getInstance();
        dbManager.initializeDatabase();
        
        // Verify database connection
        var connection = dbManager.getConnection();
        if (connection != null && !connection.isClosed()) {
            System.out.println("SUCCESS: Database connection established");
        } else {
            System.out.println("ERROR: Database connection failed");
        }
        
        // Test database file exists
        java.io.File dbFile = new java.io.File("clothing_store.db");
        if (dbFile.exists()) {
            System.out.println("SUCCESS: Database file exists (" + dbFile.length() + " bytes)");
        } else {
            System.out.println("ERROR: Database file not found");
        }
    }
    
    private static void testDAOComponents() throws SQLException {
        System.out.println("\n--- Testing DAO Components ---");
        
        // Test ProductDAO
        ProductDAO productDAO = ProductDAO.getInstance();
        int productCount = productDAO.findAll().size();
        System.out.println("SUCCESS: ProductDAO loaded " + productCount + " products");
        
        // Test CustomerDAO
        CustomerDAO customerDAO = CustomerDAO.getInstance();
        int customerCount = customerDAO.findAll().size();
        System.out.println("SUCCESS: CustomerDAO loaded " + customerCount + " customers");
        
        // Test TransactionDAO
        TransactionDAO transactionDAO = TransactionDAO.getInstance();
        int transactionCount = transactionDAO.findAll().size();
        System.out.println("SUCCESS: TransactionDAO loaded " + transactionCount + " transactions");
        
        // Test transaction number generation
        String txnNumber = transactionDAO.generateTransactionNumber();
        if (txnNumber.startsWith("TXN")) {
            System.out.println("SUCCESS: Transaction number generation working: " + txnNumber);
        } else {
            System.out.println("ERROR: Transaction number generation failed");
        }
    }
    
    private static void testApplicationReadiness() {
        System.out.println("\n--- Testing Application Readiness ---");
        
        // Check if required directories exist
        checkDirectory("target/classes", "Compiled classes");
        checkDirectory("src/main/resources", "Resources");
        checkDirectory("lib", "Libraries");
        
        // Check if required files exist
        checkFile("lib/sqlite-jdbc-3.50.1.0.jar", "SQLite JDBC driver");
        checkFile("target/classes/com/clothingstore/ClothingStoreApp.class", "Main application class");
        checkFile("target/classes/fxml/MainWindow.fxml", "Main FXML file");
        checkFile("target/classes/css/main-style.css", "Main stylesheet");
        
        // Check JavaFX SDK
        if (new java.io.File("javafx-sdk-17.0.2/lib").exists()) {
            System.out.println("SUCCESS: JavaFX SDK found");
        } else {
            System.out.println("WARNING: JavaFX SDK not found - may need to be downloaded");
        }
        
        System.out.println("SUCCESS: Application appears ready for deployment");
    }
    
    private static void checkDirectory(String path, String description) {
        java.io.File dir = new java.io.File(path);
        if (dir.exists() && dir.isDirectory()) {
            System.out.println("SUCCESS: " + description + " directory exists");
        } else {
            System.out.println("WARNING: " + description + " directory not found: " + path);
        }
    }
    
    private static void checkFile(String path, String description) {
        java.io.File file = new java.io.File(path);
        if (file.exists() && file.isFile()) {
            System.out.println("SUCCESS: " + description + " file exists (" + file.length() + " bytes)");
        } else {
            System.out.println("WARNING: " + description + " file not found: " + path);
        }
    }
}
