import java.sql.*;

public class DatabaseIndexOptimization {
    
    public static void main(String[] args) {
        System.out.println("=== DATABASE INDEX OPTIMIZATION ===");
        System.out.println("Adding indexes for frequently queried columns...\n");
        
        try (Connection conn = DriverManager.getConnection("*****************************")) {
            
            // Check existing indexes first
            System.out.println("1. CHECKING EXISTING INDEXES");
            System.out.println("----------------------------------------");
            checkExistingIndexes(conn);
            
            // Add performance indexes
            System.out.println("\n2. ADDING PERFORMANCE INDEXES");
            System.out.println("----------------------------------------");
            addPerformanceIndexes(conn);
            
            // Verify new indexes
            System.out.println("\n3. VERIFYING NEW INDEXES");
            System.out.println("----------------------------------------");
            checkExistingIndexes(conn);
            
            // Test query performance with indexes
            System.out.println("\n4. TESTING QUERY PERFORMANCE");
            System.out.println("----------------------------------------");
            testQueryPerformance(conn);
            
            System.out.println("\n=== INDEX OPTIMIZATION COMPLETE ===");
            
        } catch (SQLException e) {
            System.err.println("Index optimization failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkExistingIndexes(Connection conn) throws SQLException {
        String[] tables = {"transactions", "transaction_items", "customers", "products", "payment_history"};
        
        for (String table : tables) {
            System.out.println("  " + table.toUpperCase() + " indexes:");
            
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("PRAGMA index_list(" + table + ")")) {
                
                boolean hasIndexes = false;
                while (rs.next()) {
                    hasIndexes = true;
                    String indexName = rs.getString("name");
                    boolean isUnique = rs.getBoolean("unique");
                    System.out.println("    - " + indexName + (isUnique ? " (UNIQUE)" : ""));
                }
                
                if (!hasIndexes) {
                    System.out.println("    - No indexes found");
                }
            }
        }
    }
    
    private static void addPerformanceIndexes(Connection conn) throws SQLException {
        Statement stmt = conn.createStatement();
        
        // Indexes for transactions table
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)");
            System.out.println("  ✓ Added index on transactions.transaction_date");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on transactions.transaction_date: " + e.getMessage());
        }
        
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_id)");
            System.out.println("  ✓ Added index on transactions.customer_id");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on transactions.customer_id: " + e.getMessage());
        }
        
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)");
            System.out.println("  ✓ Added index on transactions.status");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on transactions.status: " + e.getMessage());
        }
        
        // Indexes for transaction_items table
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction ON transaction_items(transaction_id)");
            System.out.println("  ✓ Added index on transaction_items.transaction_id");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on transaction_items.transaction_id: " + e.getMessage());
        }
        
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_transaction_items_product ON transaction_items(product_id)");
            System.out.println("  ✓ Added index on transaction_items.product_id");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on transaction_items.product_id: " + e.getMessage());
        }
        
        // Indexes for payment_history table
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_payment_history_transaction ON payment_history(transaction_id)");
            System.out.println("  ✓ Added index on payment_history.transaction_id");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on payment_history.transaction_id: " + e.getMessage());
        }
        
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_payment_history_date ON payment_history(payment_date)");
            System.out.println("  ✓ Added index on payment_history.payment_date");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on payment_history.payment_date: " + e.getMessage());
        }
        
        // Indexes for customers table
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(first_name, last_name)");
            System.out.println("  ✓ Added composite index on customers.first_name, last_name");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on customers names: " + e.getMessage());
        }
        
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)");
            System.out.println("  ✓ Added index on customers.phone");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on customers.phone: " + e.getMessage());
        }
        
        // Indexes for products table
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)");
            System.out.println("  ✓ Added index on products.sku");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on products.sku: " + e.getMessage());
        }
        
        try {
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)");
            System.out.println("  ✓ Added index on products.category");
        } catch (SQLException e) {
            System.out.println("  ⚠ Index on products.category: " + e.getMessage());
        }
    }
    
    private static void testQueryPerformance(Connection conn) throws SQLException {
        // Test transaction queries with indexes
        long startTime = System.currentTimeMillis();
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM transactions WHERE status = 'COMPLETED'")) {
            rs.next();
            int count = rs.getInt(1);
            long queryTime = System.currentTimeMillis() - startTime;
            System.out.println("  Status filter query: " + queryTime + "ms (" + count + " records)");
        }
        
        // Test date range query
        startTime = System.currentTimeMillis();
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM transactions WHERE transaction_date >= datetime('now', '-30 days')")) {
            rs.next();
            int count = rs.getInt(1);
            long queryTime = System.currentTimeMillis() - startTime;
            System.out.println("  Date range query: " + queryTime + "ms (" + count + " records)");
        }
        
        // Test JOIN query performance
        startTime = System.currentTimeMillis();
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM transactions t JOIN transaction_items ti ON t.id = ti.transaction_id")) {
            rs.next();
            int count = rs.getInt(1);
            long queryTime = System.currentTimeMillis() - startTime;
            System.out.println("  JOIN query: " + queryTime + "ms (" + count + " records)");
        }
        
        // Test customer lookup
        startTime = System.currentTimeMillis();
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM customers WHERE first_name LIKE 'J%'")) {
            rs.next();
            int count = rs.getInt(1);
            long queryTime = System.currentTimeMillis() - startTime;
            System.out.println("  Customer name search: " + queryTime + "ms (" + count + " records)");
        }
    }
}
