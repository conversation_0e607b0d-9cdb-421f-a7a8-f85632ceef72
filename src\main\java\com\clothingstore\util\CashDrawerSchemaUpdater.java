package com.clothingstore.util;

import com.clothingstore.database.DatabaseManager;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Logger;

/**
 * Database schema updater for Cash Drawer functionality
 */
public class CashDrawerSchemaUpdater {

    private static final Logger LOGGER = Logger.getLogger(CashDrawerSchemaUpdater.class.getName());

    /**
     * Update database schema to support cash drawer functionality
     */
    public static void updateSchemaForCashDrawer() throws SQLException {
        LOGGER.info("Starting cash drawer database schema updates...");

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            createCashDrawersTable(conn);
            createCashDropsTable(conn);
            createCashPayoutsTable(conn);
            createIndexes(conn);

            LOGGER.info("Cash drawer database schema updates completed successfully");
        }
    }

    private static void createCashDrawersTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS cash_drawers (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "drawer_number TEXT UNIQUE NOT NULL, " +
                "cashier_name TEXT NOT NULL, " +
                "cashier_id INTEGER, " +
                "opened_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "closed_at DATETIME, " +
                "opening_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00, " +
                "closing_amount DECIMAL(10,2) DEFAULT 0.00, " +
                "expected_amount DECIMAL(10,2) DEFAULT 0.00, " +
                "actual_amount DECIMAL(10,2) DEFAULT 0.00, " +
                "variance DECIMAL(10,2) DEFAULT 0.00, " +
                "total_sales DECIMAL(10,2) DEFAULT 0.00, " +
                "total_cash_sales DECIMAL(10,2) DEFAULT 0.00, " +
                "total_refunds DECIMAL(10,2) DEFAULT 0.00, " +
                "total_cash_drops DECIMAL(10,2) DEFAULT 0.00, " +
                "total_payouts DECIMAL(10,2) DEFAULT 0.00, " +
                "transaction_count INTEGER DEFAULT 0, " +
                "status TEXT NOT NULL DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSED', 'RECONCILED')), " +
                "notes TEXT, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            LOGGER.info("Created cash_drawers table");
        }
    }

    private static void createCashDropsTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS cash_drops (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "cash_drawer_id INTEGER NOT NULL, " +
                "drawer_number TEXT NOT NULL, " +
                "cashier_name TEXT NOT NULL, " +
                "amount DECIMAL(10,2) NOT NULL, " +
                "reason TEXT NOT NULL, " +
                "notes TEXT, " +
                "drop_time DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "status TEXT NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'CONFIRMED', 'CANCELLED')), " +
                "authorized_by TEXT, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (cash_drawer_id) REFERENCES cash_drawers (id) ON DELETE CASCADE" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            LOGGER.info("Created cash_drops table");
        }
    }

    private static void createCashPayoutsTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS cash_payouts (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "cash_drawer_id INTEGER NOT NULL, " +
                "drawer_number TEXT NOT NULL, " +
                "cashier_name TEXT NOT NULL, " +
                "amount DECIMAL(10,2) NOT NULL, " +
                "reason TEXT NOT NULL, " +
                "recipient TEXT, " +
                "notes TEXT, " +
                "payout_time DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "status TEXT NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'CONFIRMED', 'CANCELLED')), " +
                "authorized_by TEXT, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (cash_drawer_id) REFERENCES cash_drawers (id) ON DELETE CASCADE" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            LOGGER.info("Created cash_payouts table");
        }
    }

    private static void createIndexes(Connection conn) throws SQLException {
        String[] indexes = {
                "CREATE INDEX IF NOT EXISTS idx_cash_drawers_status ON cash_drawers(status)",
                "CREATE INDEX IF NOT EXISTS idx_cash_drawers_cashier ON cash_drawers(cashier_name)",
                "CREATE INDEX IF NOT EXISTS idx_cash_drawers_opened ON cash_drawers(opened_at)",
                "CREATE INDEX IF NOT EXISTS idx_cash_drops_drawer ON cash_drops(cash_drawer_id)",
                "CREATE INDEX IF NOT EXISTS idx_cash_drops_time ON cash_drops(drop_time)",
                "CREATE INDEX IF NOT EXISTS idx_cash_drops_status ON cash_drops(status)",
                "CREATE INDEX IF NOT EXISTS idx_cash_payouts_drawer ON cash_payouts(cash_drawer_id)",
                "CREATE INDEX IF NOT EXISTS idx_cash_payouts_time ON cash_payouts(payout_time)",
                "CREATE INDEX IF NOT EXISTS idx_cash_payouts_status ON cash_payouts(status)"
        };

        try (Statement stmt = conn.createStatement()) {
            for (String index : indexes) {
                stmt.execute(index);
            }
            LOGGER.info("Created cash drawer indexes");
        }
    }

    /**
     * Run all cash drawer schema updates
     */
    public static void runAllUpdates() {
        try {
            updateSchemaForCashDrawer();
        } catch (SQLException e) {
            LOGGER.severe("Failed to update cash drawer schema: " + e.getMessage());
            throw new RuntimeException("Cash drawer database schema update failed", e);
        }
    }

    /**
     * Main method for running schema updates
     */
    public static void main(String[] args) {
        System.out.println("Running cash drawer schema updates...");
        runAllUpdates();
        System.out.println("Cash drawer schema updates completed successfully!");
    }
}
