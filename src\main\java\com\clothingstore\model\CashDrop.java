package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class for Cash Drop transactions
 */
public class CashDrop {
    private Long id;
    private Long cashDrawerId;
    private String drawerNumber;
    private String cashierName;
    private BigDecimal amount;
    private String reason;
    private String notes;
    private LocalDateTime dropTime;
    private String status; // PENDING, CONFIRMED, CANCELLED
    private String authorizedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public CashDrop() {
        this.status = "PENDING";
        this.dropTime = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public CashDrop(Long cashDrawerId, String drawerNumber, String cashierName, 
                   BigDecimal amount, String reason) {
        this();
        this.cashDrawerId = cashDrawerId;
        this.drawerNumber = drawerNumber;
        this.cashierName = cashierName;
        this.amount = amount;
        this.reason = reason;
    }

    // Get<PERSON> and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCashDrawerId() {
        return cashDrawerId;
    }

    public void setCashDrawerId(Long cashDrawerId) {
        this.cashDrawerId = cashDrawerId;
    }

    public String getDrawerNumber() {
        return drawerNumber;
    }

    public void setDrawerNumber(String drawerNumber) {
        this.drawerNumber = drawerNumber;
    }

    public String getCashierName() {
        return cashierName;
    }

    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDateTime getDropTime() {
        return dropTime;
    }

    public void setDropTime(LocalDateTime dropTime) {
        this.dropTime = dropTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAuthorizedBy() {
        return authorizedBy;
    }

    public void setAuthorizedBy(String authorizedBy) {
        this.authorizedBy = authorizedBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Utility methods
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    public boolean isConfirmed() {
        return "CONFIRMED".equals(status);
    }

    public boolean isCancelled() {
        return "CANCELLED".equals(status);
    }

    public void confirm(String authorizer) {
        this.status = "CONFIRMED";
        this.authorizedBy = authorizer;
        this.updatedAt = LocalDateTime.now();
    }

    public void cancel() {
        this.status = "CANCELLED";
        this.updatedAt = LocalDateTime.now();
    }

    public String getFormattedDropTime() {
        return dropTime != null ? dropTime.format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm")) : "";
    }

    @Override
    public String toString() {
        return String.format("CashDrop{drawer=%s, amount=%s, reason=%s, status=%s}", 
                drawerNumber, amount, reason, status);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        CashDrop that = (CashDrop) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
