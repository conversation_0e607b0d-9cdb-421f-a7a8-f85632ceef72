package com.clothingstore.view;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.net.URL;
import java.time.LocalDate;
import java.time.Month;
import java.util.ResourceBundle;

import com.clothingstore.util.AlertUtil;

/**
 * Controller for Monthly Sales Report interface
 */
public class MonthlySalesReportController implements Initializable {

    @FXML private ComboBox<String> cmbMonth;
    @FXML private ComboBox<String> cmbYear;
    @FXML private Button btnCurrentMonth;
    @FXML private Button btnRefresh;
    @FXML private Button btnExport;

    @FXML private Label lblTotalRevenue;
    @FXML private Label lblTotalTransactions;
    @FXML private Label lblAvgDailySales;
    @FXML private Label lblUniqueCustomers;
    @FXML private Label lblGrowthRate;
    @FXML private Label lblBestDay;
    @FXML private Label lblBestDaySales;
    @FXML private Label lblTotalItemsSold;

    @FXML private TableView<DailySalesItem> tblDailySales;
    @FXML private TableColumn<DailySalesItem, String> colDate;
    @FXML private TableColumn<DailySalesItem, String> colDayOfWeek;
    @FXML private TableColumn<DailySalesItem, String> colDailyTransactions;
    @FXML private TableColumn<DailySalesItem, String> colDailySales;
    @FXML private TableColumn<DailySalesItem, String> colDailyAvg;
    @FXML private TableColumn<DailySalesItem, String> colDailyCustomers;
    @FXML private TableColumn<DailySalesItem, String> colDailyItems;

    @FXML private TableView<TopProductItem> tblTopProducts;
    @FXML private TableColumn<TopProductItem, String> colProductRank;
    @FXML private TableColumn<TopProductItem, String> colProductName;
    @FXML private TableColumn<TopProductItem, String> colQuantitySold;
    @FXML private TableColumn<TopProductItem, String> colProductRevenue;

    @FXML private TableView<TopCategoryItem> tblTopCategories;
    @FXML private TableColumn<TopCategoryItem, String> colCategoryRank;
    @FXML private TableColumn<TopCategoryItem, String> colCategoryName;
    @FXML private TableColumn<TopCategoryItem, String> colCategoryItems;
    @FXML private TableColumn<TopCategoryItem, String> colCategoryRevenue;

    private ObservableList<DailySalesItem> dailySalesData;
    private ObservableList<TopProductItem> topProductData;
    private ObservableList<TopCategoryItem> topCategoryData;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        dailySalesData = FXCollections.observableArrayList();
        topProductData = FXCollections.observableArrayList();
        topCategoryData = FXCollections.observableArrayList();
        
        setupTables();
        setupDateSelectors();
        loadMonthlySalesData();
    }

    private void setupTables() {
        // Daily sales table
        colDate.setCellValueFactory(new PropertyValueFactory<>("date"));
        colDayOfWeek.setCellValueFactory(new PropertyValueFactory<>("dayOfWeek"));
        colDailyTransactions.setCellValueFactory(new PropertyValueFactory<>("transactions"));
        colDailySales.setCellValueFactory(new PropertyValueFactory<>("sales"));
        colDailyAvg.setCellValueFactory(new PropertyValueFactory<>("avgTransaction"));
        colDailyCustomers.setCellValueFactory(new PropertyValueFactory<>("customers"));
        colDailyItems.setCellValueFactory(new PropertyValueFactory<>("itemsSold"));
        tblDailySales.setItems(dailySalesData);

        // Top products table
        colProductRank.setCellValueFactory(new PropertyValueFactory<>("rank"));
        colProductName.setCellValueFactory(new PropertyValueFactory<>("productName"));
        colQuantitySold.setCellValueFactory(new PropertyValueFactory<>("quantitySold"));
        colProductRevenue.setCellValueFactory(new PropertyValueFactory<>("revenue"));
        tblTopProducts.setItems(topProductData);

        // Top categories table
        colCategoryRank.setCellValueFactory(new PropertyValueFactory<>("rank"));
        colCategoryName.setCellValueFactory(new PropertyValueFactory<>("categoryName"));
        colCategoryItems.setCellValueFactory(new PropertyValueFactory<>("itemsSold"));
        colCategoryRevenue.setCellValueFactory(new PropertyValueFactory<>("revenue"));
        tblTopCategories.setItems(topCategoryData);
    }

    private void setupDateSelectors() {
        // Setup month combo box
        ObservableList<String> months = FXCollections.observableArrayList();
        for (Month month : Month.values()) {
            months.add(month.name());
        }
        cmbMonth.setItems(months);
        cmbMonth.setValue(LocalDate.now().getMonth().name());

        // Setup year combo box
        ObservableList<String> years = FXCollections.observableArrayList();
        int currentYear = LocalDate.now().getYear();
        for (int year = currentYear - 5; year <= currentYear + 1; year++) {
            years.add(String.valueOf(year));
        }
        cmbYear.setItems(years);
        cmbYear.setValue(String.valueOf(currentYear));
    }

    private void loadMonthlySalesData() {
        // Load placeholder data
        loadSummaryMetrics();
        loadDailySalesBreakdown();
        loadTopProducts();
        loadTopCategories();
    }

    private void loadSummaryMetrics() {
        // Placeholder summary metrics
        lblTotalRevenue.setText("$0.00");
        lblTotalTransactions.setText("0");
        lblAvgDailySales.setText("$0.00");
        lblUniqueCustomers.setText("0");
        lblGrowthRate.setText("0%");
        lblBestDay.setText("-");
        lblBestDaySales.setText("$0.00");
        lblTotalItemsSold.setText("0");
    }

    private void loadDailySalesBreakdown() {
        dailySalesData.clear();
        
        // Create placeholder daily sales data for the month
        LocalDate startOfMonth = LocalDate.of(
            Integer.parseInt(cmbYear.getValue()),
            Month.valueOf(cmbMonth.getValue()),
            1
        );
        
        LocalDate endOfMonth = startOfMonth.withDayOfMonth(startOfMonth.lengthOfMonth());
        
        for (LocalDate date = startOfMonth; !date.isAfter(endOfMonth); date = date.plusDays(1)) {
            DailySalesItem item = new DailySalesItem();
            item.setDate(date.toString());
            item.setDayOfWeek(date.getDayOfWeek().name());
            item.setTransactions("0");
            item.setSales("$0.00");
            item.setAvgTransaction("$0.00");
            item.setCustomers("0");
            item.setItemsSold("0");
            
            dailySalesData.add(item);
        }
    }

    private void loadTopProducts() {
        topProductData.clear();
        
        // Create placeholder top products data
        for (int i = 1; i <= 10; i++) {
            TopProductItem item = new TopProductItem();
            item.setRank(String.valueOf(i));
            item.setProductName("Product " + i);
            item.setQuantitySold("0");
            item.setRevenue("$0.00");
            
            topProductData.add(item);
        }
    }

    private void loadTopCategories() {
        topCategoryData.clear();
        
        // Create placeholder top categories data
        String[] categories = {"Shirts", "Pants", "Dresses", "Accessories", "Shoes"};
        for (int i = 0; i < categories.length; i++) {
            TopCategoryItem item = new TopCategoryItem();
            item.setRank(String.valueOf(i + 1));
            item.setCategoryName(categories[i]);
            item.setItemsSold("0");
            item.setRevenue("$0.00");
            
            topCategoryData.add(item);
        }
    }

    @FXML
    private void handleMonthChange() {
        loadMonthlySalesData();
    }

    @FXML
    private void handleYearChange() {
        loadMonthlySalesData();
    }

    @FXML
    private void handleCurrentMonth() {
        LocalDate now = LocalDate.now();
        cmbMonth.setValue(now.getMonth().name());
        cmbYear.setValue(String.valueOf(now.getYear()));
        loadMonthlySalesData();
    }

    @FXML
    private void handleRefresh() {
        loadMonthlySalesData();
        AlertUtil.showInfo("Refreshed", "Monthly sales report has been refreshed.");
    }

    @FXML
    private void handleExport() {
        AlertUtil.showInfo("Export", "Monthly sales report export functionality will be implemented in future version.");
    }

    // Inner classes for table data
    public static class DailySalesItem {
        private String date;
        private String dayOfWeek;
        private String transactions;
        private String sales;
        private String avgTransaction;
        private String customers;
        private String itemsSold;

        // Getters and setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        
        public String getDayOfWeek() { return dayOfWeek; }
        public void setDayOfWeek(String dayOfWeek) { this.dayOfWeek = dayOfWeek; }
        
        public String getTransactions() { return transactions; }
        public void setTransactions(String transactions) { this.transactions = transactions; }
        
        public String getSales() { return sales; }
        public void setSales(String sales) { this.sales = sales; }
        
        public String getAvgTransaction() { return avgTransaction; }
        public void setAvgTransaction(String avgTransaction) { this.avgTransaction = avgTransaction; }
        
        public String getCustomers() { return customers; }
        public void setCustomers(String customers) { this.customers = customers; }
        
        public String getItemsSold() { return itemsSold; }
        public void setItemsSold(String itemsSold) { this.itemsSold = itemsSold; }
    }

    public static class TopProductItem {
        private String rank;
        private String productName;
        private String quantitySold;
        private String revenue;

        // Getters and setters
        public String getRank() { return rank; }
        public void setRank(String rank) { this.rank = rank; }
        
        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }
        
        public String getQuantitySold() { return quantitySold; }
        public void setQuantitySold(String quantitySold) { this.quantitySold = quantitySold; }
        
        public String getRevenue() { return revenue; }
        public void setRevenue(String revenue) { this.revenue = revenue; }
    }

    public static class TopCategoryItem {
        private String rank;
        private String categoryName;
        private String itemsSold;
        private String revenue;

        // Getters and setters
        public String getRank() { return rank; }
        public void setRank(String rank) { this.rank = rank; }
        
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }
        
        public String getItemsSold() { return itemsSold; }
        public void setItemsSold(String itemsSold) { this.itemsSold = itemsSold; }
        
        public String getRevenue() { return revenue; }
        public void setRevenue(String revenue) { this.revenue = revenue; }
    }
}
