<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox fx:id="mainContainer" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.controller.CustomerGroupsController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="header-section">
         <children>
            <Label styleClass="page-title" text="Customer Groups Management">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="refreshButton" mnemonicParsing="false" onAction="#handleRefresh" styleClass="action-button" text="Refresh" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Search and Filter Section -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="filter-section">
         <children>
            <Label text="Search:" />
            <TextField fx:id="searchField" prefWidth="200.0" promptText="Search groups..." />
            <Label text="Type:" />
            <ComboBox fx:id="filterTypeComboBox" prefWidth="120.0" promptText="All Types" />
            <CheckBox fx:id="activeOnlyCheckBox" text="Active Only" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="5.0" />
         </padding>
      </HBox>

      <!-- Statistics Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="stats-section">
         <children>
            <Label fx:id="totalGroupsLabel" text="Total Groups: 0" />
            <Label fx:id="activeGroupsLabel" text="Active: 0" />
            <Label fx:id="totalCustomersLabel" text="Total Customers: 0" />
            <Label fx:id="averageDiscountLabel" text="Avg Discount: 0.0%" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="5.0" />
         </padding>
      </HBox>

      <!-- Main Content Area - Better split ratio -->
      <SplitPane dividerPositions="0.55" VBox.vgrow="ALWAYS">
         <items>
            <!-- Left Side - Groups and Customers -->
            <VBox spacing="12.0" minWidth="450.0">
               <children>
                  <!-- Customer Groups Section -->
                  <VBox spacing="8.0" VBox.vgrow="ALWAYS">
                     <children>
                        <!-- Group List Header -->
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="section-title" text="Customer Groups">
                                 <font>
                                    <Font name="System Bold" size="16.0" />
                                 </font>
                              </Label>
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="addGroupButton" mnemonicParsing="false" onAction="#handleAddGroup" styleClass="primary-button" text="Add Group" />
                           </children>
                        </HBox>

                        <!-- Group Table - Improved column widths -->
                        <TableView fx:id="groupTable" prefHeight="220.0" minHeight="180.0">
                           <columns>
                              <TableColumn fx:id="groupCodeColumn" prefWidth="90.0" text="Code" />
                              <TableColumn fx:id="groupNameColumn" prefWidth="140.0" text="Group Name" />
                              <TableColumn fx:id="groupTypeColumn" prefWidth="110.0" text="Type" />
                              <TableColumn fx:id="discountColumn" prefWidth="90.0" text="Discount" />
                              <TableColumn fx:id="creditLimitColumn" prefWidth="110.0" text="Credit Limit" />
                              <TableColumn fx:id="statusColumn" prefWidth="80.0" text="Status" />
                           </columns>
                        </TableView>

                        <!-- Group Action Buttons -->
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Button fx:id="editGroupButton" disable="true" mnemonicParsing="false" onAction="#handleEditGroup" styleClass="secondary-button" text="Edit" />
                              <Button fx:id="deleteGroupButton" disable="true" mnemonicParsing="false" onAction="#handleDeleteGroup" styleClass="danger-button" text="Delete" />
                           </children>
                           <padding>
                              <Insets top="5.0" />
                           </padding>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Customer Assignment Section -->
                  <VBox spacing="8.0" VBox.vgrow="ALWAYS">
                     <children>
                        <!-- Customer Assignment Header -->
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="section-title" text="Customer Assignment">
                                 <font>
                                    <Font name="System Bold" size="16.0" />
                                 </font>
                              </Label>
                           </children>
                        </HBox>

                        <!-- Customer Table - Better sizing -->
                        <TableView fx:id="customerTable" prefHeight="220.0" minHeight="180.0" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="customerNameColumn" prefWidth="170.0" text="Customer Name" />
                              <TableColumn fx:id="customerEmailColumn" prefWidth="170.0" text="Email" />
                              <TableColumn fx:id="customerGroupColumn" prefWidth="140.0" text="Current Group" />
                           </columns>
                        </TableView>

                        <!-- Assignment Controls -->
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                           <children>
                              <Label text="Assign to Group:" />
                              <ComboBox fx:id="assignGroupComboBox" prefWidth="170.0" />
                              <Button fx:id="assignGroupButton" disable="true" mnemonicParsing="false" onAction="#handleAssignGroup" styleClass="primary-button" text="Assign" />
                              <Button fx:id="removeGroupButton" disable="true" mnemonicParsing="false" onAction="#handleRemoveGroup" styleClass="secondary-button" text="Remove" />
                           </children>
                           <padding>
                              <Insets top="8.0" />
                           </padding>
                        </HBox>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="5.0" top="10.0" />
               </padding>
            </VBox>

            <!-- Right Side - Group Details Form -->
            <VBox spacing="5.0">
               <children>
                  <!-- Group Details Form -->
                  <VBox spacing="5.0" styleClass="form-section">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="section-title" text="Group Details">
                                 <font>
                                    <Font name="System Bold" size="16.0" />
                                 </font>
                              </Label>
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="loadDefaultsButton" mnemonicParsing="false" onAction="#handleLoadDefaults" styleClass="action-button" text="Load Defaults" visible="false" />
                              <Button fx:id="saveGroupButton" mnemonicParsing="false" onAction="#handleSaveGroup" styleClass="primary-button" text="Save" visible="false" />
                              <Button fx:id="cancelGroupButton" mnemonicParsing="false" onAction="#handleCancelGroup" styleClass="secondary-button" text="Cancel" visible="false" />
                           </children>
                        </HBox>

                        <!-- Group Form -->
                        <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
                           <content>
                              <GridPane hgap="10.0" vgap="8.0">
                                 <columnConstraints>
                                    <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                                    <ColumnConstraints hgrow="ALWAYS" />
                                 </columnConstraints>
                                 <children>
                                    <Label text="Group Code:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                    <TextField fx:id="groupCodeField" disable="true" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                                    <Label text="Group Name:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                    <TextField fx:id="groupNameField" disable="true" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                                    <Label text="Group Type:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                    <ComboBox fx:id="groupTypeComboBox" disable="true" prefWidth="200.0" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                                    <Label text="Description:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                                    <TextArea fx:id="descriptionArea" disable="true" prefRowCount="2" GridPane.columnIndex="1" GridPane.rowIndex="3" />

                                    <Label text="Discount %:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                                    <HBox spacing="10.0" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                       <children>
                                          <TextField fx:id="discountPercentageField" disable="true" prefWidth="80.0" />
                                          <Label text="Priority:" />
                                          <TextField fx:id="priorityField" disable="true" prefWidth="60.0" />
                                          <CheckBox fx:id="activeCheckBox" disable="true" text="Active" />
                                       </children>
                                    </HBox>

                                    <Label text="Min Order Amount:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                                    <HBox spacing="10.0" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                       <children>
                                          <TextField fx:id="minimumOrderAmountField" disable="true" prefWidth="100.0" />
                                          <Label text="Credit Limit:" />
                                          <TextField fx:id="creditLimitField" disable="true" prefWidth="100.0" />
                                       </children>
                                    </HBox>

                                    <Label text="Payment Terms:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                                    <HBox spacing="10.0" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                       <children>
                                          <TextField fx:id="paymentTermsDaysField" disable="true" prefWidth="60.0" />
                                          <Label text="days" />
                                          <CheckBox fx:id="taxExemptCheckBox" disable="true" text="Tax Exempt" />
                                       </children>
                                    </HBox>

                                    <Label text="Options:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                                    <HBox spacing="15.0" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                       <children>
                                          <CheckBox fx:id="allowBackordersCheckBox" disable="true" text="Allow Backorders" />
                                          <CheckBox fx:id="requireApprovalCheckBox" disable="true" text="Require Approval" />
                                       </children>
                                    </HBox>

                                    <Label text="Benefits:" GridPane.columnIndex="0" GridPane.rowIndex="8" />
                                    <TextArea fx:id="benefitsArea" disable="true" prefRowCount="4" GridPane.columnIndex="1" GridPane.rowIndex="8" />
                                 </children>
                              </GridPane>
                           </content>
                        </ScrollPane>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="5.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </children>
</VBox>
