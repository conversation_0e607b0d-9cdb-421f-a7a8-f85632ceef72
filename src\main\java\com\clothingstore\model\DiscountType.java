package com.clothingstore.model;

/**
 * Enumeration for different types of discounts
 */
public enum DiscountType {
    PERCENTAGE("Percentage", "Discount as a percentage of the total"),
    FIXED_AMOUNT("Fixed Amount", "Fixed dollar amount discount"),
    BUY_ONE_GET_ONE("Buy One Get One", "Buy one item, get another free or discounted"),
    BUY_X_GET_Y("Buy X Get Y", "Buy X items, get Y items free or discounted"),
    BULK_DISCOUNT("Bulk Discount", "Discount for purchasing multiple quantities"),
    CUSTOMER_GROUP("Customer Group", "Discount for specific customer groups"),
    SEASONAL("Seasonal", "Time-based seasonal discount"),
    CLEARANCE("Clearance", "Clearance sale discount"),
    EMPLOYEE("Employee", "Employee discount"),
    LOYALTY("Loyalty", "Loyalty program discount");

    private final String displayName;
    private final String description;

    DiscountType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Check if this discount type applies to individual items
     */
    public boolean isItemLevel() {
        return this == BUY_ONE_GET_ONE || this == BUY_X_GET_Y || this == BULK_DISCOUNT;
    }

    /**
     * Check if this discount type applies to the entire transaction
     */
    public boolean isTransactionLevel() {
        return this == PERCENTAGE || this == FIXED_AMOUNT || this == CUSTOMER_GROUP || 
               this == EMPLOYEE || this == LOYALTY;
    }

    /**
     * Check if this discount type requires customer validation
     */
    public boolean requiresCustomerValidation() {
        return this == CUSTOMER_GROUP || this == EMPLOYEE || this == LOYALTY;
    }
}
