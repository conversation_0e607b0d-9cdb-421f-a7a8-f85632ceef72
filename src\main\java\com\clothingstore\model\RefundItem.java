package com.clothingstore.model;

import java.math.BigDecimal;

/**
 * Model class representing an item being refunded
 */
public class RefundItem {
    private TransactionItem originalItem;
    private int refundQuantity;
    private BigDecimal refundAmount;
    private String reason;
    private boolean selected;
    
    public RefundItem(TransactionItem originalItem) {
        this.originalItem = originalItem;
        this.refundQuantity = originalItem.getQuantity();
        this.refundAmount = originalItem.getLineTotal();
        this.selected = true;
        this.reason = "";
    }
    
    // Getters and Setters
    public TransactionItem getOriginalItem() { return originalItem; }
    public void setOriginalItem(TransactionItem originalItem) { this.originalItem = originalItem; }
    
    public int getRefundQuantity() { return refundQuantity; }
    public void setRefundQuantity(int refundQuantity) { 
        this.refundQuantity = refundQuantity;
        calculateRefundAmount();
    }
    
    public BigDecimal getRefundAmount() { return refundAmount; }
    public void setRefundAmount(BigDecimal refundAmount) { this.refundAmount = refundAmount; }
    
    public String getReason() { return reason; }
    public void setReason(String reason) { this.reason = reason; }
    
    public boolean isSelected() { return selected; }
    public void setSelected(boolean selected) { this.selected = selected; }
    
    // Convenience methods
    public String getProductName() {
        return originalItem.getProduct() != null ? originalItem.getProduct().getName() : "Unknown Product";
    }
    
    public String getProductSku() {
        return originalItem.getProduct() != null ? originalItem.getProduct().getSku() : "";
    }
    
    public BigDecimal getUnitPrice() {
        return originalItem.getUnitPrice();
    }
    
    public int getOriginalQuantity() {
        return originalItem.getQuantity();
    }
    
    public BigDecimal getOriginalLineTotal() {
        return originalItem.getLineTotal();
    }
    
    public int getMaxRefundQuantity() {
        return originalItem.getQuantity();
    }
    
    public boolean canRefund() {
        return refundQuantity > 0 && refundQuantity <= originalItem.getQuantity();
    }
    
    private void calculateRefundAmount() {
        if (originalItem != null && originalItem.getQuantity() > 0) {
            BigDecimal unitAmount = originalItem.getLineTotal().divide(
                BigDecimal.valueOf(originalItem.getQuantity()), 2, BigDecimal.ROUND_HALF_UP);
            refundAmount = unitAmount.multiply(BigDecimal.valueOf(refundQuantity));
        } else {
            refundAmount = BigDecimal.ZERO;
        }
    }
    
    @Override
    public String toString() {
        return String.format("RefundItem{product='%s', refundQty=%d, amount=%s}", 
                           getProductName(), refundQuantity, refundAmount);
    }
}
