<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.WhatsAppAdminController">
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
   
   <!-- Header Section -->
   <HBox alignment="CENTER_LEFT" spacing="10">
      <Label text="WhatsApp Administration" styleClass="page-title"/>
      <Region HBox.hgrow="ALWAYS"/>
      <Button fx:id="btnRefreshStatus" text="Refresh Status" onAction="#handleRefreshStatus"/>
   </HBox>
   
   <Separator />
   
   <!-- System Health Section -->
   <VBox spacing="15" style="-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-background-radius: 5;">
      <Label text="System Health" style="-fx-font-weight: bold; -fx-font-size: 16px;"/>
      
      <HBox spacing="20">
         <VBox spacing="5">
            <Label text="Overall Status:" style="-fx-font-weight: bold;"/>
            <Label fx:id="lblHealthStatus" text="Unknown" style="-fx-font-size: 14px;"/>
         </VBox>
         <VBox spacing="5">
            <Label text="Last Health Check:" style="-fx-font-weight: bold;"/>
            <Label fx:id="lblLastHealthCheck" text="Never" style="-fx-font-size: 14px;"/>
         </VBox>
         <VBox spacing="5">
            <Label text="Queue Status:" style="-fx-font-weight: bold;"/>
            <Label fx:id="lblQueueStatus" text="Unknown" style="-fx-font-size: 14px;"/>
         </VBox>
      </HBox>
      
      <HBox spacing="10">
         <Button fx:id="btnRunHealthCheck" text="Run Health Check" onAction="#handleRunHealthCheck"/>
         <Button fx:id="btnViewDiagnostics" text="View Diagnostics" onAction="#handleViewDiagnostics"/>
         <Button fx:id="btnTestConnection" text="Test Connection" onAction="#handleTestConnection"/>
      </HBox>
   </VBox>
   
   <!-- Performance Metrics Section -->
   <VBox spacing="15" style="-fx-background-color: #e9ecef; -fx-padding: 15; -fx-background-radius: 5;">
      <Label text="Performance Metrics" style="-fx-font-weight: bold; -fx-font-size: 16px;"/>
      
      <HBox spacing="30">
         <VBox alignment="CENTER" spacing="5">
            <Label fx:id="lblTotalSent" text="0" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #28a745;"/>
            <Label text="Messages Sent" style="-fx-font-size: 12px;"/>
         </VBox>
         <VBox alignment="CENTER" spacing="5">
            <Label fx:id="lblTotalFailed" text="0" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #dc3545;"/>
            <Label text="Messages Failed" style="-fx-font-size: 12px;"/>
         </VBox>
         <VBox alignment="CENTER" spacing="5">
            <Label fx:id="lblTotalQueued" text="0" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #ffc107;"/>
            <Label text="Messages Queued" style="-fx-font-size: 12px;"/>
         </VBox>
         <VBox alignment="CENTER" spacing="5">
            <Label fx:id="lblSuccessRate" text="0%" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #17a2b8;"/>
            <Label text="Success Rate" style="-fx-font-size: 12px;"/>
         </VBox>
      </HBox>
   </VBox>
   
   <!-- Admin Operations Section -->
   <VBox spacing="15">
      <Label text="Administrative Operations" style="-fx-font-weight: bold; -fx-font-size: 16px;"/>
      
      <!-- Backup and Restore -->
      <HBox spacing="15">
         <VBox spacing="10" style="-fx-background-color: #fff3cd; -fx-padding: 15; -fx-background-radius: 5;">
            <Label text="Backup & Restore" style="-fx-font-weight: bold;"/>
            <HBox spacing="10">
               <Button fx:id="btnCreateBackup" text="Create Full Backup" onAction="#handleCreateBackup"/>
               <Button fx:id="btnCreateIncrementalBackup" text="Incremental Backup" onAction="#handleCreateIncrementalBackup"/>
            </HBox>
            <HBox spacing="10">
               <Button fx:id="btnRestoreBackup" text="Restore from Backup" onAction="#handleRestoreBackup"/>
               <Button fx:id="btnListBackups" text="List Backups" onAction="#handleListBackups"/>
            </HBox>
            <HBox spacing="10">
               <Button fx:id="btnExportCSV" text="Export to CSV" onAction="#handleExportCSV"/>
               <Button fx:id="btnCleanupBackups" text="Cleanup Old Backups" onAction="#handleCleanupBackups"/>
            </HBox>
         </VBox>
         
         <!-- Queue Management -->
         <VBox spacing="10" style="-fx-background-color: #d1ecf1; -fx-padding: 15; -fx-background-radius: 5;">
            <Label text="Queue Management" style="-fx-font-weight: bold;"/>
            <HBox spacing="10">
               <Button fx:id="btnViewQueue" text="View Queue Status" onAction="#handleViewQueue"/>
               <Button fx:id="btnClearQueue" text="Clear Queue" onAction="#handleClearQueue"/>
            </HBox>
            <HBox spacing="10">
               <Button fx:id="btnRetryFailed" text="Retry All Failed" onAction="#handleRetryFailed"/>
               <Button fx:id="btnPurgeOldMessages" text="Purge Old Messages" onAction="#handlePurgeOldMessages"/>
            </HBox>
         </VBox>
      </HBox>
      
      <!-- System Maintenance -->
      <VBox spacing="10" style="-fx-background-color: #f8d7da; -fx-padding: 15; -fx-background-radius: 5;">
         <Label text="System Maintenance" style="-fx-font-weight: bold;"/>
         <HBox spacing="10">
            <Button fx:id="btnRestartService" text="Restart WhatsApp Service" onAction="#handleRestartService"/>
            <Button fx:id="btnResetStatistics" text="Reset Statistics" onAction="#handleResetStatistics"/>
            <Button fx:id="btnOptimizeDatabase" text="Optimize Database" onAction="#handleOptimizeDatabase"/>
         </HBox>
         <HBox spacing="10">
            <Button fx:id="btnViewLogs" text="View System Logs" onAction="#handleViewLogs"/>
            <Button fx:id="btnExportLogs" text="Export Logs" onAction="#handleExportLogs"/>
         </HBox>
      </VBox>
   </VBox>
   
   <!-- Progress Section -->
   <VBox spacing="10" fx:id="progressSection" visible="false">
      <Separator />
      <Label fx:id="lblProgressStatus" text="Operation in progress..." style="-fx-font-weight: bold;"/>
      <ProgressBar fx:id="progressBar" prefWidth="400" progress="0"/>
      <HBox spacing="10">
         <Button fx:id="btnCancelOperation" text="Cancel" onAction="#handleCancelOperation"/>
         <Region HBox.hgrow="ALWAYS"/>
         <Label fx:id="lblProgressDetails" text="" style="-fx-font-style: italic;"/>
      </HBox>
   </VBox>
   
   <!-- Recent Operations Log -->
   <VBox VBox.vgrow="ALWAYS" spacing="10">
      <Label text="Recent Operations" style="-fx-font-weight: bold; -fx-font-size: 14px;"/>
      <TextArea fx:id="txtOperationLog" VBox.vgrow="ALWAYS" editable="false" 
                style="-fx-font-family: monospace; -fx-font-size: 12px;"
                promptText="Administrative operations will be logged here..."/>
      <HBox spacing="10">
         <Button fx:id="btnClearLog" text="Clear Log" onAction="#handleClearLog"/>
         <Button fx:id="btnSaveLog" text="Save Log" onAction="#handleSaveLog"/>
         <Region HBox.hgrow="ALWAYS"/>
         <Label fx:id="lblLogStatus" text="Ready" style="-fx-font-style: italic;"/>
      </HBox>
   </VBox>
   
</VBox>
