#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1536496 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=28612, tid=38320
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -Xlog:disable --add-modules=javafx.controls,javafx.fxml --module-path=./javafx-sdk-11.0.2/lib -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\75ec01de8fef35e90c2497b506034143\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\75ec01de8fef35e90c2497b506034143\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-500fb749714c7cd36049c5eebcc43b26-sock

Host: Intel(R) Core(TM) i7-14700HX, 28 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Fri Jun 27 01:08:09 2025 Egypt Daylight Time elapsed time: 28.601383 seconds (0d 0h 0m 28s)

---------------  T H R E A D  ---------------

Current thread (0x0000026374e64890):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=38320, stack(0x00000092aab00000,0x00000092aac00000) (1024K)]


Current CompileTask:
C2:28601 20207   !   4       org.eclipse.jdt.internal.core.index.DiskIndex::mergeWith (623 bytes)

Stack: [0x00000092aab00000,0x00000092aac00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x1e0029]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002637a182af0, length=159, elements={
0x0000026357e3e2f0, 0x0000026374e28870, 0x0000026374e2b2d0, 0x0000026374e2e270,
0x0000026374e2edf0, 0x0000026374e3bac0, 0x0000026374e60000, 0x0000026374e64890,
0x0000026374e65e80, 0x0000026374e5d210, 0x0000026374e5f2e0, 0x0000026374e5d8a0,
0x0000026374e5cb80, 0x0000026374e5ec50, 0x0000026374e5e5c0, 0x0000026374e5f970,
0x0000026374e5df30, 0x000002637aba3470, 0x000002637aba3b00, 0x000002637aba4190,
0x000002637aba4eb0, 0x000002637aba4820, 0x000002637aba2750, 0x000002637aba6260,
0x000002637aba2de0, 0x000002637aba8330, 0x000002637aba9d70, 0x000002637aba68f0,
0x000002637aba7610, 0x000002637aba6f80, 0x000002637aba9050, 0x000002637aba7ca0,
0x000002637aba96e0, 0x000002637aba89c0, 0x000002637ecb60d0, 0x000002637ecb4d20,
0x000002637ecb53b0, 0x000002637ecb8ec0, 0x000002637ecb7480, 0x000002637ecb5a40,
0x000002637ecb7b10, 0x000002637ecb9be0, 0x000002637ecb81a0, 0x000002637ecb6df0,
0x000002637ecb6760, 0x000002637ecbb620, 0x000002637ecbaf90, 0x000002637ecb9550,
0x000002637ecbbcb0, 0x000002637ecba900, 0x000002637ecb8830, 0x000002637ecbc340,
0x000002637ecba270, 0x000002637db55970, 0x000002637db552e0, 0x000002637db54c50,
0x000002637db538a0, 0x000002637db56690, 0x000002637db53f30, 0x000002637db56d20,
0x000002637db58df0, 0x000002637db59b10, 0x000002637db5a1a0, 0x000002637db58760,
0x000002637db57a40, 0x000002637db5aec0, 0x000002637db580d0, 0x000002637db59480,
0x000002637dcbc150, 0x000002637dcba710, 0x000002637dcbb430, 0x000002637dcb8cd0,
0x000002637dcbc7e0, 0x000002637dcb9360, 0x000002637dcba080, 0x000002637dcbce70,
0x000002637dcb99f0, 0x000002637dcbd500, 0x000002637dcbdb90, 0x000002637dcbe220,
0x000002637dcbbac0, 0x000002637dcbada0, 0x000002637dcbfc60, 0x000002637dcbe8b0,
0x000002637dcbf5d0, 0x000002637dcbef40, 0x000002637dcc02f0, 0x000002637c9b8730,
0x000002637c9b80a0, 0x000002637c9b7380, 0x000002637c9b8dc0, 0x000002637c9b6cf0,
0x000002637c9bc8d0, 0x000002637c9bb520, 0x000002637c9bbbb0, 0x000002637c9bae90,
0x000002637c9ba170, 0x000002637c9ba800, 0x000002637c9bc240, 0x000002637c9b9450,
0x000002637c9bcf60, 0x000002637c9b9ae0, 0x000002637c9bdc80, 0x00000263416c2040,
0x000002634d3badc0, 0x000002634d3bce90, 0x000002634d3bb450, 0x000002634d3bc170,
0x000002634d3bbae0, 0x00000263416c4e30, 0x00000263416c54c0, 0x00000263416c26d0,
0x00000263416c5b50, 0x00000263416c61e0, 0x00000263416c6870, 0x00000263416c6f00,
0x00000263416c3a80, 0x00000263416c7590, 0x00000263416c47a0, 0x000002634c7fc7d0,
0x000002634c7fad90, 0x000002634c7fc140, 0x000002634c7fb420, 0x000002634c7fbab0,
0x000002634c7f9350, 0x000002634c7fce60, 0x000002634c7f99e0, 0x000002634c7fa070,
0x000002634c7fe8a0, 0x000002634c7fd4f0, 0x000002634c7ff5c0, 0x000002637d1d9dc0,
0x000002637d1dd8d0, 0x000002637d1db170, 0x000002637d1dcbb0, 0x000002637d1e0d50,
0x000002637d1e2e20, 0x000002637d1e41d0, 0x000002637d1e34b0, 0x000002637d1e3b40,
0x000002637d1e4860, 0x000002637d1e4ef0, 0x000002637d1e1a70, 0x000002637d1e6fc0,
0x000002637d1e7ce0, 0x000002637d1e8a00, 0x000002637d1e9090, 0x000002637d1e5c10,
0x000002637d1e6930, 0x000002637d1e62a0, 0x0000026354163440, 0x0000026354164f80,
0x00000263541641e0, 0x00000263541663f0, 0x0000026354167190, 0x0000026354163b10,
0x0000026354167860, 0x0000026354165d20, 0x0000026354167f30
}

Java Threads: ( => current thread )
  0x0000026357e3e2f0 JavaThread "main"                              [_thread_blocked, id=38980, stack(0x00000092a9d00000,0x00000092a9e00000) (1024K)]
  0x0000026374e28870 JavaThread "Reference Handler"          daemon [_thread_blocked, id=41532, stack(0x00000092aa500000,0x00000092aa600000) (1024K)]
  0x0000026374e2b2d0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=36628, stack(0x00000092aa600000,0x00000092aa700000) (1024K)]
  0x0000026374e2e270 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=24456, stack(0x00000092aa700000,0x00000092aa800000) (1024K)]
  0x0000026374e2edf0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=2400, stack(0x00000092aa800000,0x00000092aa900000) (1024K)]
  0x0000026374e3bac0 JavaThread "Service Thread"             daemon [_thread_blocked, id=18392, stack(0x00000092aa900000,0x00000092aaa00000) (1024K)]
  0x0000026374e60000 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=32072, stack(0x00000092aaa00000,0x00000092aab00000) (1024K)]
=>0x0000026374e64890 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=38320, stack(0x00000092aab00000,0x00000092aac00000) (1024K)]
  0x0000026374e65e80 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=5452, stack(0x00000092aac00000,0x00000092aad00000) (1024K)]
  0x0000026374e5d210 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=39888, stack(0x00000092aad00000,0x00000092aae00000) (1024K)]
  0x0000026374e5f2e0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=23908, stack(0x00000092ab800000,0x00000092ab900000) (1024K)]
  0x0000026374e5d8a0 JavaThread "Active Thread: Equinox Container: d8db1d26-e6fa-4d46-8344-8b5e0c9c8240"        [_thread_in_Java, id=33260, stack(0x00000092abe00000,0x00000092abf00000) (1024K)]
  0x0000026374e5cb80 JavaThread "Refresh Thread: Equinox Container: d8db1d26-e6fa-4d46-8344-8b5e0c9c8240" daemon [_thread_blocked, id=33456, stack(0x00000092ab700000,0x00000092ab800000) (1024K)]
  0x0000026374e5ec50 JavaThread "Framework Event Dispatcher: Equinox Container: d8db1d26-e6fa-4d46-8344-8b5e0c9c8240" daemon [_thread_blocked, id=13812, stack(0x00000092abf00000,0x00000092ac000000) (1024K)]
  0x0000026374e5e5c0 JavaThread "Start Level: Equinox Container: d8db1d26-e6fa-4d46-8344-8b5e0c9c8240" daemon [_thread_blocked, id=15488, stack(0x00000092ac000000,0x00000092ac100000) (1024K)]
  0x0000026374e5f970 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=3308, stack(0x00000092ac400000,0x00000092ac500000) (1024K)]
  0x0000026374e5df30 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=40780, stack(0x00000092ab300000,0x00000092ab400000) (1024K)]
  0x000002637aba3470 JavaThread "Worker-JM"                         [_thread_blocked, id=9728, stack(0x00000092ab500000,0x00000092ab600000) (1024K)]
  0x000002637aba3b00 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=2884, stack(0x00000092acb00000,0x00000092acc00000) (1024K)]
  0x000002637aba4190 JavaThread "Worker-0"                          [_thread_blocked, id=41656, stack(0x00000092acc00000,0x00000092acd00000) (1024K)]
  0x000002637aba4eb0 JavaThread "Worker-1: Java indexing... "        [_thread_blocked, id=31676, stack(0x00000092acd00000,0x00000092ace00000) (1024K)]
  0x000002637aba4820 JavaThread "Worker-2"                          [_thread_blocked, id=34452, stack(0x00000092ace00000,0x00000092acf00000) (1024K)]
  0x000002637aba2750 JavaThread "Java indexing"              daemon [_thread_in_Java, id=35960, stack(0x00000092ad700000,0x00000092ad800000) (1024K)]
  0x000002637aba6260 JavaThread "Worker-3: Initialize workspace"        [_thread_blocked, id=34572, stack(0x00000092add00000,0x00000092ade00000) (1024K)]
  0x000002637aba2de0 JavaThread "Worker-4"                          [_thread_blocked, id=14180, stack(0x00000092ae100000,0x00000092ae200000) (1024K)]
  0x000002637aba8330 JavaThread "Worker-5"                          [_thread_blocked, id=2792, stack(0x00000092ae200000,0x00000092ae300000) (1024K)]
  0x000002637aba9d70 JavaThread "Worker-6"                          [_thread_blocked, id=34504, stack(0x00000092ae300000,0x00000092ae400000) (1024K)]
  0x000002637aba68f0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=33752, stack(0x00000092ae400000,0x00000092ae500000) (1024K)]
  0x000002637aba7610 JavaThread "Thread-3"                   daemon [_thread_in_native, id=29332, stack(0x00000092ae500000,0x00000092ae600000) (1024K)]
  0x000002637aba6f80 JavaThread "Thread-4"                   daemon [_thread_in_native, id=29800, stack(0x00000092ae600000,0x00000092ae700000) (1024K)]
  0x000002637aba9050 JavaThread "Thread-5"                   daemon [_thread_in_native, id=37664, stack(0x00000092ae700000,0x00000092ae800000) (1024K)]
  0x000002637aba7ca0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=23088, stack(0x00000092ae800000,0x00000092ae900000) (1024K)]
  0x000002637aba96e0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=34900, stack(0x00000092ae900000,0x00000092aea00000) (1024K)]
  0x000002637aba89c0 JavaThread "Thread-8"                   daemon [_thread_in_native, id=20028, stack(0x00000092aea00000,0x00000092aeb00000) (1024K)]
  0x000002637ecb60d0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=23872, stack(0x00000092aeb00000,0x00000092aec00000) (1024K)]
  0x000002637ecb4d20 JavaThread "Thread-10"                  daemon [_thread_in_native, id=39644, stack(0x00000092aec00000,0x00000092aed00000) (1024K)]
  0x000002637ecb53b0 JavaThread "Thread-11"                  daemon [_thread_in_native, id=4572, stack(0x00000092aed00000,0x00000092aee00000) (1024K)]
  0x000002637ecb8ec0 JavaThread "Thread-12"                  daemon [_thread_in_native, id=31620, stack(0x00000092aee00000,0x00000092aef00000) (1024K)]
  0x000002637ecb7480 JavaThread "Thread-13"                  daemon [_thread_in_native, id=9804, stack(0x00000092aef00000,0x00000092af000000) (1024K)]
  0x000002637ecb5a40 JavaThread "Thread-14"                  daemon [_thread_in_native, id=9048, stack(0x00000092af000000,0x00000092af100000) (1024K)]
  0x000002637ecb7b10 JavaThread "Thread-15"                  daemon [_thread_in_native, id=38664, stack(0x00000092af100000,0x00000092af200000) (1024K)]
  0x000002637ecb9be0 JavaThread "Thread-16"                  daemon [_thread_in_native, id=7468, stack(0x00000092af200000,0x00000092af300000) (1024K)]
  0x000002637ecb81a0 JavaThread "Thread-17"                  daemon [_thread_in_native, id=39616, stack(0x00000092af300000,0x00000092af400000) (1024K)]
  0x000002637ecb6df0 JavaThread "Thread-18"                  daemon [_thread_in_native, id=40160, stack(0x00000092af400000,0x00000092af500000) (1024K)]
  0x000002637ecb6760 JavaThread "Thread-19"                  daemon [_thread_in_native, id=26348, stack(0x00000092af500000,0x00000092af600000) (1024K)]
  0x000002637ecbb620 JavaThread "Thread-20"                  daemon [_thread_in_native, id=3012, stack(0x00000092af600000,0x00000092af700000) (1024K)]
  0x000002637ecbaf90 JavaThread "Thread-21"                  daemon [_thread_in_native, id=28828, stack(0x00000092af700000,0x00000092af800000) (1024K)]
  0x000002637ecb9550 JavaThread "Thread-22"                  daemon [_thread_in_native, id=42400, stack(0x00000092af800000,0x00000092af900000) (1024K)]
  0x000002637ecbbcb0 JavaThread "Thread-23"                  daemon [_thread_in_native, id=24164, stack(0x00000092af900000,0x00000092afa00000) (1024K)]
  0x000002637ecba900 JavaThread "Thread-24"                  daemon [_thread_in_native, id=12448, stack(0x00000092afa00000,0x00000092afb00000) (1024K)]
  0x000002637ecb8830 JavaThread "Thread-25"                  daemon [_thread_in_native, id=15960, stack(0x00000092afb00000,0x00000092afc00000) (1024K)]
  0x000002637ecbc340 JavaThread "Thread-26"                  daemon [_thread_in_native, id=23284, stack(0x00000092afc00000,0x00000092afd00000) (1024K)]
  0x000002637ecba270 JavaThread "Thread-27"                  daemon [_thread_in_native, id=34132, stack(0x00000092afd00000,0x00000092afe00000) (1024K)]
  0x000002637db55970 JavaThread "Thread-28"                  daemon [_thread_in_native, id=4436, stack(0x00000092afe00000,0x00000092aff00000) (1024K)]
  0x000002637db552e0 JavaThread "Thread-29"                  daemon [_thread_in_native, id=34644, stack(0x00000092aff00000,0x00000092b0000000) (1024K)]
  0x000002637db54c50 JavaThread "Thread-30"                  daemon [_thread_in_native, id=3868, stack(0x00000092b0000000,0x00000092b0100000) (1024K)]
  0x000002637db538a0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=35868, stack(0x00000092b0100000,0x00000092b0200000) (1024K)]
  0x000002637db56690 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=24632, stack(0x00000092b0200000,0x00000092b0300000) (1024K)]
  0x000002637db53f30 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=7160, stack(0x00000092b0300000,0x00000092b0400000) (1024K)]
  0x000002637db56d20 JavaThread "Worker-7"                          [_thread_blocked, id=29736, stack(0x00000092b0400000,0x00000092b0500000) (1024K)]
  0x000002637db58df0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=42956, stack(0x00000092b0500000,0x00000092b0600000) (1024K)]
  0x000002637db59b10 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=39912, stack(0x00000092b0600000,0x00000092b0700000) (1024K)]
  0x000002637db5a1a0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=7752, stack(0x00000092b0700000,0x00000092b0800000) (1024K)]
  0x000002637db58760 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=39132, stack(0x00000092b0800000,0x00000092b0900000) (1024K)]
  0x000002637db57a40 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=5664, stack(0x00000092b0900000,0x00000092b0a00000) (1024K)]
  0x000002637db5aec0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=34600, stack(0x00000092b0a00000,0x00000092b0b00000) (1024K)]
  0x000002637db580d0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=38624, stack(0x00000092b0b00000,0x00000092b0c00000) (1024K)]
  0x000002637db59480 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=21952, stack(0x00000092b0c00000,0x00000092b0d00000) (1024K)]
  0x000002637dcbc150 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=27296, stack(0x00000092b0d00000,0x00000092b0e00000) (1024K)]
  0x000002637dcba710 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=35476, stack(0x00000092b0e00000,0x00000092b0f00000) (1024K)]
  0x000002637dcbb430 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=34880, stack(0x00000092b0f00000,0x00000092b1000000) (1024K)]
  0x000002637dcb8cd0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=20016, stack(0x00000092b1000000,0x00000092b1100000) (1024K)]
  0x000002637dcbc7e0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=42776, stack(0x00000092b1100000,0x00000092b1200000) (1024K)]
  0x000002637dcb9360 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=22988, stack(0x00000092b1200000,0x00000092b1300000) (1024K)]
  0x000002637dcba080 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=35956, stack(0x00000092b1300000,0x00000092b1400000) (1024K)]
  0x000002637dcbce70 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=43648, stack(0x00000092b1400000,0x00000092b1500000) (1024K)]
  0x000002637dcb99f0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=22940, stack(0x00000092b1500000,0x00000092b1600000) (1024K)]
  0x000002637dcbd500 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=14896, stack(0x00000092b1600000,0x00000092b1700000) (1024K)]
  0x000002637dcbdb90 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=14376, stack(0x00000092b1700000,0x00000092b1800000) (1024K)]
  0x000002637dcbe220 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=10828, stack(0x00000092b1800000,0x00000092b1900000) (1024K)]
  0x000002637dcbbac0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=23644, stack(0x00000092b1900000,0x00000092b1a00000) (1024K)]
  0x000002637dcbada0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=18740, stack(0x00000092b1a00000,0x00000092b1b00000) (1024K)]
  0x000002637dcbfc60 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=3288, stack(0x00000092b1b00000,0x00000092b1c00000) (1024K)]
  0x000002637dcbe8b0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=26980, stack(0x00000092b1c00000,0x00000092b1d00000) (1024K)]
  0x000002637dcbf5d0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=40208, stack(0x00000092b1d00000,0x00000092b1e00000) (1024K)]
  0x000002637dcbef40 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=30076, stack(0x00000092b1e00000,0x00000092b1f00000) (1024K)]
  0x000002637dcc02f0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=24532, stack(0x00000092b1f00000,0x00000092b2000000) (1024K)]
  0x000002637c9b8730 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=42312, stack(0x00000092b2000000,0x00000092b2100000) (1024K)]
  0x000002637c9b80a0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=38552, stack(0x00000092b2100000,0x00000092b2200000) (1024K)]
  0x000002637c9b7380 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=19756, stack(0x00000092b2200000,0x00000092b2300000) (1024K)]
  0x000002637c9b8dc0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=18420, stack(0x00000092b2300000,0x00000092b2400000) (1024K)]
  0x000002637c9b6cf0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=28588, stack(0x00000092b2400000,0x00000092b2500000) (1024K)]
  0x000002637c9bc8d0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=34580, stack(0x00000092b2500000,0x00000092b2600000) (1024K)]
  0x000002637c9bb520 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=18872, stack(0x00000092b2600000,0x00000092b2700000) (1024K)]
  0x000002637c9bbbb0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=30504, stack(0x00000092b2700000,0x00000092b2800000) (1024K)]
  0x000002637c9bae90 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=43056, stack(0x00000092b2800000,0x00000092b2900000) (1024K)]
  0x000002637c9ba170 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=34996, stack(0x00000092b2900000,0x00000092b2a00000) (1024K)]
  0x000002637c9ba800 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=34000, stack(0x00000092b2a00000,0x00000092b2b00000) (1024K)]
  0x000002637c9bc240 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=34664, stack(0x00000092b2b00000,0x00000092b2c00000) (1024K)]
  0x000002637c9b9450 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=42608, stack(0x00000092b2c00000,0x00000092b2d00000) (1024K)]
  0x000002637c9bcf60 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=28560, stack(0x00000092b2d00000,0x00000092b2e00000) (1024K)]
  0x000002637c9b9ae0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=17920, stack(0x00000092b2e00000,0x00000092b2f00000) (1024K)]
  0x000002637c9bdc80 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=13232, stack(0x00000092b2f00000,0x00000092b3000000) (1024K)]
  0x00000263416c2040 JavaThread "Compiler Processing Task"   daemon [_thread_blocked, id=16748, stack(0x00000092ab400000,0x00000092ab500000) (1024K)]
  0x000002634d3badc0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=11008, stack(0x00000092b3000000,0x00000092b3100000) (1024K)]
  0x000002634d3bce90 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=42408, stack(0x00000092b3100000,0x00000092b3200000) (1024K)]
  0x000002634d3bb450 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=37228, stack(0x00000092b3200000,0x00000092b3300000) (1024K)]
  0x000002634d3bc170 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=19588, stack(0x00000092b3300000,0x00000092b3400000) (1024K)]
  0x000002634d3bbae0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=26640, stack(0x00000092b3400000,0x00000092b3500000) (1024K)]
  0x00000263416c4e30 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=24360, stack(0x00000092b3500000,0x00000092b3600000) (1024K)]
  0x00000263416c54c0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=24448, stack(0x00000092b3600000,0x00000092b3700000) (1024K)]
  0x00000263416c26d0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=14200, stack(0x00000092b3700000,0x00000092b3800000) (1024K)]
  0x00000263416c5b50 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=13384, stack(0x00000092b3800000,0x00000092b3900000) (1024K)]
  0x00000263416c61e0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=35100, stack(0x00000092b3900000,0x00000092b3a00000) (1024K)]
  0x00000263416c6870 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=39696, stack(0x00000092b3a00000,0x00000092b3b00000) (1024K)]
  0x00000263416c6f00 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=3384, stack(0x00000092b3b00000,0x00000092b3c00000) (1024K)]
  0x00000263416c3a80 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=21444, stack(0x00000092b3c00000,0x00000092b3d00000) (1024K)]
  0x00000263416c7590 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=41008, stack(0x00000092b3d00000,0x00000092b3e00000) (1024K)]
  0x00000263416c47a0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=1848, stack(0x00000092b3e00000,0x00000092b3f00000) (1024K)]
  0x000002634c7fc7d0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=31920, stack(0x00000092b3f00000,0x00000092b4000000) (1024K)]
  0x000002634c7fad90 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=28204, stack(0x00000092b4000000,0x00000092b4100000) (1024K)]
  0x000002634c7fc140 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=11712, stack(0x00000092b4100000,0x00000092b4200000) (1024K)]
  0x000002634c7fb420 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=32264, stack(0x00000092b4200000,0x00000092b4300000) (1024K)]
  0x000002634c7fbab0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=25336, stack(0x00000092b4300000,0x00000092b4400000) (1024K)]
  0x000002634c7f9350 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=8808, stack(0x00000092b4400000,0x00000092b4500000) (1024K)]
  0x000002634c7fce60 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=40316, stack(0x00000092b4500000,0x00000092b4600000) (1024K)]
  0x000002634c7f99e0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=17448, stack(0x00000092b4600000,0x00000092b4700000) (1024K)]
  0x000002634c7fa070 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=38816, stack(0x00000092b4700000,0x00000092b4800000) (1024K)]
  0x000002634c7fe8a0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=8764, stack(0x00000092b4800000,0x00000092b4900000) (1024K)]
  0x000002634c7fd4f0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=30464, stack(0x00000092b4900000,0x00000092b4a00000) (1024K)]
  0x000002634c7ff5c0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=2644, stack(0x00000092b4a00000,0x00000092b4b00000) (1024K)]
  0x000002637d1d9dc0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=8988, stack(0x00000092b4b00000,0x00000092b4c00000) (1024K)]
  0x000002637d1dd8d0 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=37040, stack(0x00000092b4c00000,0x00000092b4d00000) (1024K)]
  0x000002637d1db170 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=30860, stack(0x00000092b4d00000,0x00000092b4e00000) (1024K)]
  0x000002637d1dcbb0 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=22384, stack(0x00000092b4e00000,0x00000092b4f00000) (1024K)]
  0x000002637d1e0d50 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=42296, stack(0x00000092b4f00000,0x00000092b5000000) (1024K)]
  0x000002637d1e2e20 JavaThread "Cleaner-0"                  daemon [_thread_blocked, id=2456, stack(0x00000092b5200000,0x00000092b5300000) (1024K)]
  0x000002637d1e41d0 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=20476, stack(0x00000092b5100000,0x00000092b5200000) (1024K)]
  0x000002637d1e34b0 JavaThread "ForkJoinPool.commonPool-worker-8" daemon [_thread_blocked, id=17208, stack(0x00000092b5300000,0x00000092b5400000) (1024K)]
  0x000002637d1e3b40 JavaThread "ForkJoinPool.commonPool-worker-9" daemon [_thread_blocked, id=41432, stack(0x00000092b5400000,0x00000092b5500000) (1024K)]
  0x000002637d1e4860 JavaThread "ForkJoinPool.commonPool-worker-10" daemon [_thread_blocked, id=9044, stack(0x00000092b5500000,0x00000092b5600000) (1024K)]
  0x000002637d1e4ef0 JavaThread "ForkJoinPool.commonPool-worker-11" daemon [_thread_blocked, id=24120, stack(0x00000092b5600000,0x00000092b5700000) (1024K)]
  0x000002637d1e1a70 JavaThread "ForkJoinPool.commonPool-worker-12" daemon [_thread_blocked, id=31804, stack(0x00000092b5700000,0x00000092b5800000) (1024K)]
  0x000002637d1e6fc0 JavaThread "ForkJoinPool.commonPool-worker-13" daemon [_thread_blocked, id=35080, stack(0x00000092b5800000,0x00000092b5900000) (1024K)]
  0x000002637d1e7ce0 JavaThread "ForkJoinPool.commonPool-worker-14" daemon [_thread_blocked, id=43712, stack(0x00000092b5900000,0x00000092b5a00000) (1024K)]
  0x000002637d1e8a00 JavaThread "ForkJoinPool.commonPool-worker-15" daemon [_thread_blocked, id=6496, stack(0x00000092b5a00000,0x00000092b5b00000) (1024K)]
  0x000002637d1e9090 JavaThread "ForkJoinPool.commonPool-worker-16" daemon [_thread_blocked, id=39932, stack(0x00000092b5b00000,0x00000092b5c00000) (1024K)]
  0x000002637d1e5c10 JavaThread "ForkJoinPool.commonPool-worker-17" daemon [_thread_blocked, id=1804, stack(0x00000092b5c00000,0x00000092b5d00000) (1024K)]
  0x000002637d1e6930 JavaThread "ForkJoinPool.commonPool-worker-18" daemon [_thread_blocked, id=19908, stack(0x00000092b5d00000,0x00000092b5e00000) (1024K)]
  0x000002637d1e62a0 JavaThread "ForkJoinPool.commonPool-worker-19" daemon [_thread_blocked, id=24908, stack(0x00000092b5e00000,0x00000092b5f00000) (1024K)]
  0x0000026354163440 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=32040, stack(0x00000092aae00000,0x00000092aaf00000) (1024K)]
  0x0000026354164f80 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=27244, stack(0x00000092aaf00000,0x00000092ab000000) (1024K)]
  0x00000263541641e0 JavaThread "C2 CompilerThread3"         daemon [_thread_in_native, id=25408, stack(0x00000092ab000000,0x00000092ab100000) (1024K)]
  0x00000263541663f0 JavaThread "C1 CompilerThread1"         daemon [_thread_in_native, id=11084, stack(0x00000092a9b00000,0x00000092a9c00000) (1024K)]
  0x0000026354167190 JavaThread "C1 CompilerThread2"         daemon [_thread_in_native, id=29384, stack(0x00000092a9c00000,0x00000092a9d00000) (1024K)]
  0x0000026354163b10 JavaThread "C2 CompilerThread4"         daemon [_thread_in_native, id=1856, stack(0x00000092ab100000,0x00000092ab200000) (1024K)]
  0x0000026354167860 JavaThread "C2 CompilerThread5"         daemon [_thread_in_native, id=42236, stack(0x00000092ab200000,0x00000092ab300000) (1024K)]
  0x0000026354165d20 JavaThread "C2 CompilerThread6"         daemon [_thread_in_native, id=37568, stack(0x00000092ab600000,0x00000092ab700000) (1024K)]
  0x0000026354167f30 JavaThread "C2 CompilerThread7"         daemon [_thread_in_native, id=2468, stack(0x00000092ad900000,0x00000092ada00000) (1024K)]
Total: 159

Other Threads:
  0x0000026357e10e70 VMThread "VM Thread"                           [id=36004, stack(0x00000092aa400000,0x00000092aa500000) (1024K)]
  0x0000026375bb19e0 WatcherThread "VM Periodic Task Thread"        [id=11352, stack(0x00000092aa300000,0x00000092aa400000) (1024K)]
  0x0000026357e12090 WorkerThread "GC Thread#0"                     [id=24472, stack(0x00000092a9e00000,0x00000092a9f00000) (1024K)]
  0x0000026357e11950 WorkerThread "GC Thread#1"                     [id=27560, stack(0x00000092ab900000,0x00000092aba00000) (1024K)]
  0x0000026357e11210 WorkerThread "GC Thread#2"                     [id=24848, stack(0x00000092aba00000,0x00000092abb00000) (1024K)]
  0x000002637ab7c6d0 WorkerThread "GC Thread#3"                     [id=9476, stack(0x00000092abb00000,0x00000092abc00000) (1024K)]
  0x000002637ab7b850 WorkerThread "GC Thread#4"                     [id=7004, stack(0x00000092abc00000,0x00000092abd00000) (1024K)]
  0x000002637ab7ca70 WorkerThread "GC Thread#5"                     [id=29908, stack(0x00000092abd00000,0x00000092abe00000) (1024K)]
  0x000002637ab7a630 WorkerThread "GC Thread#6"                     [id=2904, stack(0x00000092ac100000,0x00000092ac200000) (1024K)]
  0x000002637ab7b110 WorkerThread "GC Thread#7"                     [id=29376, stack(0x00000092ac200000,0x00000092ac300000) (1024K)]
  0x000002637ab7ce10 WorkerThread "GC Thread#8"                     [id=40128, stack(0x00000092ac300000,0x00000092ac400000) (1024K)]
  0x000002637ab7c330 WorkerThread "GC Thread#9"                     [id=5964, stack(0x00000092ac500000,0x00000092ac600000) (1024K)]
  0x000002637ab7a290 WorkerThread "GC Thread#10"                    [id=9928, stack(0x00000092ac600000,0x00000092ac700000) (1024K)]
  0x000002637ab7b4b0 WorkerThread "GC Thread#11"                    [id=38080, stack(0x00000092ac700000,0x00000092ac800000) (1024K)]
  0x000002637ab79b50 WorkerThread "GC Thread#12"                    [id=13040, stack(0x00000092ac800000,0x00000092ac900000) (1024K)]
  0x000002637ab7bbf0 WorkerThread "GC Thread#13"                    [id=5388, stack(0x00000092ac900000,0x00000092aca00000) (1024K)]
  0x000002637ab7bf90 WorkerThread "GC Thread#14"                    [id=18172, stack(0x00000092aca00000,0x00000092acb00000) (1024K)]
  0x000002637bf674e0 WorkerThread "GC Thread#15"                    [id=29012, stack(0x00000092acf00000,0x00000092ad000000) (1024K)]
  0x000002637bf66a00 WorkerThread "GC Thread#16"                    [id=10976, stack(0x00000092ad000000,0x00000092ad100000) (1024K)]
  0x000002637bf650a0 WorkerThread "GC Thread#17"                    [id=9540, stack(0x00000092ad500000,0x00000092ad600000) (1024K)]
  0x000002637bf67fc0 WorkerThread "GC Thread#18"                    [id=27944, stack(0x00000092ad600000,0x00000092ad700000) (1024K)]
  0x000002637bf65440 WorkerThread "GC Thread#19"                    [id=28304, stack(0x00000092ad800000,0x00000092ad900000) (1024K)]
  0x0000026359a36e30 ConcurrentGCThread "G1 Main Marker"            [id=23352, stack(0x00000092a9f00000,0x00000092aa000000) (1024K)]
  0x0000026357e12430 WorkerThread "G1 Conc#0"                       [id=36212, stack(0x00000092aa000000,0x00000092aa100000) (1024K)]
  0x000002637bf66660 WorkerThread "G1 Conc#1"                       [id=7972, stack(0x00000092ad100000,0x00000092ad200000) (1024K)]
  0x000002637bf66da0 WorkerThread "G1 Conc#2"                       [id=30352, stack(0x00000092ad200000,0x00000092ad300000) (1024K)]
  0x000002637bf67880 WorkerThread "G1 Conc#3"                       [id=38532, stack(0x00000092ad300000,0x00000092ad400000) (1024K)]
  0x000002637bf67c20 WorkerThread "G1 Conc#4"                       [id=18160, stack(0x00000092ad400000,0x00000092ad500000) (1024K)]
  0x0000026374d4f610 ConcurrentGCThread "G1 Refine#0"               [id=7228, stack(0x00000092aa100000,0x00000092aa200000) (1024K)]
  0x0000026374d51340 ConcurrentGCThread "G1 Service"                [id=14680, stack(0x00000092aa200000,0x00000092aa300000) (1024K)]
Total: 30

Threads with active compile tasks:
C2 CompilerThread0  31657 20207   !   4       org.eclipse.jdt.internal.core.index.DiskIndex::mergeWith (623 bytes)
C1 CompilerThread0  31657 20303       3       java.lang.Long::bitCount (61 bytes)
C2 CompilerThread1  31657 20208       4       org.eclipse.jdt.internal.core.index.DiskIndex::writeCategoryTable (252 bytes)
C2 CompilerThread2  31657 20222       4       org.eclipse.jdt.internal.core.search.JavaSearchParticipant::indexDocument (89 bytes)
C2 CompilerThread3  31657 20275 %     4       java.io.DataOutputStream::writeUTF @ 10 (389 bytes)
C1 CompilerThread1  31657 20301       3       java.util.RegularEnumSet::size (8 bytes)
C1 CompilerThread2  31657 20304       3       org.eclipse.osgi.container.ModuleRevision::getModuleRequirements (9 bytes)
C2 CompilerThread4  31657 20224       4       java.lang.invoke.MethodType::checkPtypes (76 bytes)
C2 CompilerThread5  31657 20289       4       java.util.HashMap$KeySet::iterator (12 bytes)
C2 CompilerThread6  31657 20272   !   4       java.io.BufferedOutputStream::write (73 bytes)
C2 CompilerThread7  31657 20277       4       java.io.BufferedOutputStream::implWrite (71 bytes)
Total: 11

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000704800000, size: 4024 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000026300000000-0x0000026300ba0000-0x0000026300ba0000), size 12189696, SharedBaseAddress: 0x0000026300000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000026301000000-0x0000026341000000, reserved size: 1073741824
Narrow klass base: 0x0000026300000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 28 total, 28 available
 Memory: 16091M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 4024M
 Pre-touch: Disabled
 Parallel Workers: 20
 Concurrent Workers: 5
 Concurrent Refinement Workers: 20
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 747520K, used 532480K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 58 young (118784K), 15 survivors (30720K)
 Metaspace       used 74166K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000704800000, 0x0000000704a00000, 0x0000000704a00000|100%| O|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|   1|0x0000000704a00000, 0x0000000704c00000, 0x0000000704c00000|100%| O|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|   2|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|   3|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|   4|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|   5|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|   6|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|   7|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|   8|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|   9|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  10|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  11|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  12|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  13|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  14|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  15|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  16|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  17|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  18|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|Cm|TAMS 0x0000000706c00000| PB 0x0000000706c00000| Complete 
|  19|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%|HS|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Complete 
|  20|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  21|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  22|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  23|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  24|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| O|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  25|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| O|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  26|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| O|Cm|TAMS 0x0000000707c00000| PB 0x0000000707c00000| Complete 
|  27|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| O|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  28|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| O|Cm|TAMS 0x0000000708000000| PB 0x0000000708000000| Complete 
|  29|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| O|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  30|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| O|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  31|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| O|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  32|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| O|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  33|0x0000000708a00000, 0x0000000708c00000, 0x0000000708c00000|100%| O|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  34|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%| O|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  35|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%| O|Cm|TAMS 0x0000000708e00000| PB 0x0000000708e00000| Complete 
|  36|0x0000000709000000, 0x0000000709200000, 0x0000000709200000|100%| O|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  37|0x0000000709200000, 0x0000000709400000, 0x0000000709400000|100%| O|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  38|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| O|Cm|TAMS 0x0000000709400000| PB 0x0000000709400000| Complete 
|  39|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| O|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  40|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| O|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  41|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| O|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  42|0x0000000709c00000, 0x0000000709e00000, 0x0000000709e00000|100%| O|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  43|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%| O|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  44|0x000000070a000000, 0x000000070a200000, 0x000000070a200000|100%| O|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  45|0x000000070a200000, 0x000000070a400000, 0x000000070a400000|100%| O|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  46|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%| O|Cm|TAMS 0x000000070a400000| PB 0x000000070a400000| Complete 
|  47|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| O|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  48|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| O|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  49|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| O|Cm|TAMS 0x000000070aa00000| PB 0x000000070aa00000| Complete 
|  50|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| O|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  51|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| O|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  52|0x000000070b000000, 0x000000070b200000, 0x000000070b200000|100%| O|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  53|0x000000070b200000, 0x000000070b400000, 0x000000070b400000|100%| O|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  54|0x000000070b400000, 0x000000070b600000, 0x000000070b600000|100%| O|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  55|0x000000070b600000, 0x000000070b800000, 0x000000070b800000|100%| O|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  56|0x000000070b800000, 0x000000070ba00000, 0x000000070ba00000|100%| O|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  57|0x000000070ba00000, 0x000000070bc00000, 0x000000070bc00000|100%| O|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  58|0x000000070bc00000, 0x000000070be00000, 0x000000070be00000|100%| O|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  59|0x000000070be00000, 0x000000070c000000, 0x000000070c000000|100%| O|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  60|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  61|0x000000070c200000, 0x000000070c400000, 0x000000070c400000|100%| O|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  62|0x000000070c400000, 0x000000070c600000, 0x000000070c600000|100%| O|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  63|0x000000070c600000, 0x000000070c800000, 0x000000070c800000|100%| O|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
|  64|0x000000070c800000, 0x000000070ca00000, 0x000000070ca00000|100%| O|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
|  65|0x000000070ca00000, 0x000000070cc00000, 0x000000070cc00000|100%| O|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
|  66|0x000000070cc00000, 0x000000070ce00000, 0x000000070ce00000|100%| O|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
|  67|0x000000070ce00000, 0x000000070d000000, 0x000000070d000000|100%| O|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
|  68|0x000000070d000000, 0x000000070d200000, 0x000000070d200000|100%| O|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
|  69|0x000000070d200000, 0x000000070d400000, 0x000000070d400000|100%| O|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
|  70|0x000000070d400000, 0x000000070d600000, 0x000000070d600000|100%| O|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
|  71|0x000000070d600000, 0x000000070d800000, 0x000000070d800000|100%| O|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
|  72|0x000000070d800000, 0x000000070da00000, 0x000000070da00000|100%| O|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
|  73|0x000000070da00000, 0x000000070dc00000, 0x000000070dc00000|100%| O|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
|  74|0x000000070dc00000, 0x000000070de00000, 0x000000070de00000|100%| O|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
|  75|0x000000070de00000, 0x000000070e000000, 0x000000070e000000|100%| O|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
|  76|0x000000070e000000, 0x000000070e200000, 0x000000070e200000|100%| O|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
|  77|0x000000070e200000, 0x000000070e400000, 0x000000070e400000|100%| O|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
|  78|0x000000070e400000, 0x000000070e600000, 0x000000070e600000|100%| O|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
|  79|0x000000070e600000, 0x000000070e800000, 0x000000070e800000|100%| O|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
|  80|0x000000070e800000, 0x000000070ea00000, 0x000000070ea00000|100%| O|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
|  81|0x000000070ea00000, 0x000000070ec00000, 0x000000070ec00000|100%| O|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
|  82|0x000000070ec00000, 0x000000070ee00000, 0x000000070ee00000|100%| O|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
|  83|0x000000070ee00000, 0x000000070f000000, 0x000000070f000000|100%| O|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
|  84|0x000000070f000000, 0x000000070f200000, 0x000000070f200000|100%| O|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
|  85|0x000000070f200000, 0x000000070f400000, 0x000000070f400000|100%| O|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
|  86|0x000000070f400000, 0x000000070f600000, 0x000000070f600000|100%| O|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
|  87|0x000000070f600000, 0x000000070f800000, 0x000000070f800000|100%| O|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
|  88|0x000000070f800000, 0x000000070fa00000, 0x000000070fa00000|100%| O|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
|  89|0x000000070fa00000, 0x000000070fc00000, 0x000000070fc00000|100%| O|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
|  90|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%| O|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked 
|  91|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| O|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked 
|  92|0x0000000710000000, 0x0000000710200000, 0x0000000710200000|100%|HS|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Complete 
|  93|0x0000000710200000, 0x0000000710400000, 0x0000000710400000|100%|HC|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Complete 
|  94|0x0000000710400000, 0x0000000710600000, 0x0000000710600000|100%| O|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked 
|  95|0x0000000710600000, 0x0000000710800000, 0x0000000710800000|100%| O|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked 
|  96|0x0000000710800000, 0x0000000710a00000, 0x0000000710a00000|100%| O|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked 
|  97|0x0000000710a00000, 0x0000000710c00000, 0x0000000710c00000|100%| O|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked 
|  98|0x0000000710c00000, 0x0000000710e00000, 0x0000000710e00000|100%| O|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked 
|  99|0x0000000710e00000, 0x0000000711000000, 0x0000000711000000|100%| O|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked 
| 100|0x0000000711000000, 0x0000000711200000, 0x0000000711200000|100%| O|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Untracked 
| 101|0x0000000711200000, 0x0000000711400000, 0x0000000711400000|100%| O|  |TAMS 0x0000000711200000| PB 0x0000000711200000| Untracked 
| 102|0x0000000711400000, 0x0000000711600000, 0x0000000711600000|100%| O|  |TAMS 0x0000000711400000| PB 0x0000000711400000| Untracked 
| 103|0x0000000711600000, 0x0000000711800000, 0x0000000711800000|100%| O|  |TAMS 0x0000000711600000| PB 0x0000000711600000| Untracked 
| 104|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| O|  |TAMS 0x0000000711800000| PB 0x0000000711800000| Untracked 
| 105|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| O|  |TAMS 0x0000000711a00000| PB 0x0000000711a00000| Untracked 
| 106|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| O|  |TAMS 0x0000000711c00000| PB 0x0000000711c00000| Untracked 
| 107|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| O|  |TAMS 0x0000000711e00000| PB 0x0000000711e00000| Untracked 
| 108|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| O|  |TAMS 0x0000000712000000| PB 0x0000000712000000| Untracked 
| 109|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| O|  |TAMS 0x0000000712200000| PB 0x0000000712200000| Untracked 
| 110|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| O|  |TAMS 0x0000000712400000| PB 0x0000000712400000| Untracked 
| 111|0x0000000712600000, 0x0000000712800000, 0x0000000712800000|100%| O|  |TAMS 0x0000000712600000| PB 0x0000000712600000| Untracked 
| 112|0x0000000712800000, 0x0000000712a00000, 0x0000000712a00000|100%| O|  |TAMS 0x0000000712800000| PB 0x0000000712800000| Untracked 
| 113|0x0000000712a00000, 0x0000000712c00000, 0x0000000712c00000|100%| O|  |TAMS 0x0000000712a00000| PB 0x0000000712a00000| Untracked 
| 114|0x0000000712c00000, 0x0000000712e00000, 0x0000000712e00000|100%| O|  |TAMS 0x0000000712c00000| PB 0x0000000712c00000| Untracked 
| 115|0x0000000712e00000, 0x0000000713000000, 0x0000000713000000|100%| O|  |TAMS 0x0000000712e00000| PB 0x0000000712e00000| Untracked 
| 116|0x0000000713000000, 0x0000000713200000, 0x0000000713200000|100%| O|  |TAMS 0x0000000713000000| PB 0x0000000713000000| Untracked 
| 117|0x0000000713200000, 0x0000000713400000, 0x0000000713400000|100%| O|  |TAMS 0x0000000713200000| PB 0x0000000713200000| Untracked 
| 118|0x0000000713400000, 0x0000000713600000, 0x0000000713600000|100%| O|  |TAMS 0x0000000713400000| PB 0x0000000713400000| Untracked 
| 119|0x0000000713600000, 0x0000000713800000, 0x0000000713800000|100%| O|  |TAMS 0x0000000713600000| PB 0x0000000713600000| Untracked 
| 120|0x0000000713800000, 0x0000000713a00000, 0x0000000713a00000|100%| O|  |TAMS 0x0000000713800000| PB 0x0000000713800000| Untracked 
| 121|0x0000000713a00000, 0x0000000713c00000, 0x0000000713c00000|100%| O|  |TAMS 0x0000000713a00000| PB 0x0000000713a00000| Untracked 
| 122|0x0000000713c00000, 0x0000000713e00000, 0x0000000713e00000|100%| O|  |TAMS 0x0000000713c00000| PB 0x0000000713c00000| Untracked 
| 123|0x0000000713e00000, 0x0000000714000000, 0x0000000714000000|100%| O|  |TAMS 0x0000000713e00000| PB 0x0000000713e00000| Untracked 
| 124|0x0000000714000000, 0x0000000714200000, 0x0000000714200000|100%| O|  |TAMS 0x0000000714000000| PB 0x0000000714000000| Untracked 
| 125|0x0000000714200000, 0x0000000714400000, 0x0000000714400000|100%| O|  |TAMS 0x0000000714200000| PB 0x0000000714200000| Untracked 
| 126|0x0000000714400000, 0x0000000714600000, 0x0000000714600000|100%| O|  |TAMS 0x0000000714400000| PB 0x0000000714400000| Untracked 
| 127|0x0000000714600000, 0x0000000714800000, 0x0000000714800000|100%| O|  |TAMS 0x0000000714600000| PB 0x0000000714600000| Untracked 
| 128|0x0000000714800000, 0x0000000714a00000, 0x0000000714a00000|100%| O|  |TAMS 0x0000000714800000| PB 0x0000000714800000| Untracked 
| 129|0x0000000714a00000, 0x0000000714c00000, 0x0000000714c00000|100%| O|  |TAMS 0x0000000714a00000| PB 0x0000000714a00000| Untracked 
| 130|0x0000000714c00000, 0x0000000714e00000, 0x0000000714e00000|100%| O|  |TAMS 0x0000000714c00000| PB 0x0000000714c00000| Untracked 
| 131|0x0000000714e00000, 0x0000000715000000, 0x0000000715000000|100%| O|Cm|TAMS 0x0000000714e00000| PB 0x0000000714e00000| Complete 
| 132|0x0000000715000000, 0x0000000715200000, 0x0000000715200000|100%| O|  |TAMS 0x0000000715000000| PB 0x0000000715000000| Untracked 
| 133|0x0000000715200000, 0x0000000715400000, 0x0000000715400000|100%| O|  |TAMS 0x0000000715200000| PB 0x0000000715200000| Untracked 
| 134|0x0000000715400000, 0x0000000715600000, 0x0000000715600000|100%| O|  |TAMS 0x0000000715400000| PB 0x0000000715400000| Untracked 
| 135|0x0000000715600000, 0x0000000715800000, 0x0000000715800000|100%| O|  |TAMS 0x0000000715600000| PB 0x0000000715600000| Untracked 
| 136|0x0000000715800000, 0x0000000715a00000, 0x0000000715a00000|100%| O|  |TAMS 0x0000000715800000| PB 0x0000000715800000| Untracked 
| 137|0x0000000715a00000, 0x0000000715c00000, 0x0000000715c00000|100%| O|  |TAMS 0x0000000715a00000| PB 0x0000000715a00000| Untracked 
| 138|0x0000000715c00000, 0x0000000715e00000, 0x0000000715e00000|100%| O|  |TAMS 0x0000000715c00000| PB 0x0000000715c00000| Untracked 
| 139|0x0000000715e00000, 0x0000000716000000, 0x0000000716000000|100%| O|  |TAMS 0x0000000715e00000| PB 0x0000000715e00000| Untracked 
| 140|0x0000000716000000, 0x0000000716200000, 0x0000000716200000|100%| O|  |TAMS 0x0000000716000000| PB 0x0000000716000000| Untracked 
| 141|0x0000000716200000, 0x0000000716400000, 0x0000000716400000|100%| O|  |TAMS 0x0000000716200000| PB 0x0000000716200000| Untracked 
| 142|0x0000000716400000, 0x0000000716600000, 0x0000000716600000|100%| O|  |TAMS 0x0000000716400000| PB 0x0000000716400000| Untracked 
| 143|0x0000000716600000, 0x0000000716800000, 0x0000000716800000|100%| O|  |TAMS 0x0000000716600000| PB 0x0000000716600000| Untracked 
| 144|0x0000000716800000, 0x0000000716a00000, 0x0000000716a00000|100%| O|  |TAMS 0x0000000716800000| PB 0x0000000716800000| Untracked 
| 145|0x0000000716a00000, 0x0000000716c00000, 0x0000000716c00000|100%| O|  |TAMS 0x0000000716a00000| PB 0x0000000716a00000| Untracked 
| 146|0x0000000716c00000, 0x0000000716e00000, 0x0000000716e00000|100%| O|  |TAMS 0x0000000716c00000| PB 0x0000000716c00000| Untracked 
| 147|0x0000000716e00000, 0x0000000717000000, 0x0000000717000000|100%| O|Cm|TAMS 0x0000000716e00000| PB 0x0000000716e00000| Complete 
| 148|0x0000000717000000, 0x0000000717200000, 0x0000000717200000|100%| O|Cm|TAMS 0x0000000717000000| PB 0x0000000717000000| Complete 
| 149|0x0000000717200000, 0x0000000717400000, 0x0000000717400000|100%| O|Cm|TAMS 0x0000000717200000| PB 0x0000000717200000| Complete 
| 150|0x0000000717400000, 0x0000000717600000, 0x0000000717600000|100%| O|  |TAMS 0x0000000717400000| PB 0x0000000717400000| Untracked 
| 151|0x0000000717600000, 0x0000000717800000, 0x0000000717800000|100%| O|  |TAMS 0x0000000717600000| PB 0x0000000717600000| Untracked 
| 152|0x0000000717800000, 0x0000000717a00000, 0x0000000717a00000|100%| O|  |TAMS 0x0000000717800000| PB 0x0000000717800000| Untracked 
| 153|0x0000000717a00000, 0x0000000717c00000, 0x0000000717c00000|100%| O|  |TAMS 0x0000000717a00000| PB 0x0000000717a00000| Untracked 
| 154|0x0000000717c00000, 0x0000000717e00000, 0x0000000717e00000|100%| O|  |TAMS 0x0000000717c00000| PB 0x0000000717c00000| Untracked 
| 155|0x0000000717e00000, 0x0000000718000000, 0x0000000718000000|100%| O|  |TAMS 0x0000000717e00000| PB 0x0000000717e00000| Untracked 
| 156|0x0000000718000000, 0x0000000718200000, 0x0000000718200000|100%| O|  |TAMS 0x0000000718000000| PB 0x0000000718000000| Untracked 
| 157|0x0000000718200000, 0x0000000718400000, 0x0000000718400000|100%| O|Cm|TAMS 0x0000000718200000| PB 0x0000000718200000| Complete 
| 158|0x0000000718400000, 0x0000000718600000, 0x0000000718600000|100%| O|Cm|TAMS 0x0000000718400000| PB 0x0000000718400000| Complete 
| 159|0x0000000718600000, 0x0000000718800000, 0x0000000718800000|100%| O|  |TAMS 0x0000000718600000| PB 0x0000000718600000| Untracked 
| 160|0x0000000718800000, 0x0000000718a00000, 0x0000000718a00000|100%| O|  |TAMS 0x0000000718800000| PB 0x0000000718800000| Untracked 
| 161|0x0000000718a00000, 0x0000000718c00000, 0x0000000718c00000|100%| O|  |TAMS 0x0000000718a00000| PB 0x0000000718a00000| Untracked 
| 162|0x0000000718c00000, 0x0000000718e00000, 0x0000000718e00000|100%| O|  |TAMS 0x0000000718c00000| PB 0x0000000718c00000| Untracked 
| 163|0x0000000718e00000, 0x0000000719000000, 0x0000000719000000|100%| O|Cm|TAMS 0x0000000718e00000| PB 0x0000000718e00000| Complete 
| 164|0x0000000719000000, 0x0000000719200000, 0x0000000719200000|100%| O|  |TAMS 0x0000000719000000| PB 0x0000000719000000| Untracked 
| 165|0x0000000719200000, 0x0000000719400000, 0x0000000719400000|100%| O|  |TAMS 0x0000000719200000| PB 0x0000000719200000| Untracked 
| 166|0x0000000719400000, 0x0000000719600000, 0x0000000719600000|100%| O|  |TAMS 0x0000000719400000| PB 0x0000000719400000| Untracked 
| 167|0x0000000719600000, 0x0000000719800000, 0x0000000719800000|100%| O|  |TAMS 0x0000000719600000| PB 0x0000000719600000| Untracked 
| 168|0x0000000719800000, 0x0000000719a00000, 0x0000000719a00000|100%| O|Cm|TAMS 0x0000000719800000| PB 0x0000000719800000| Complete 
| 169|0x0000000719a00000, 0x0000000719c00000, 0x0000000719c00000|100%| O|  |TAMS 0x0000000719a00000| PB 0x0000000719a00000| Untracked 
| 170|0x0000000719c00000, 0x0000000719e00000, 0x0000000719e00000|100%| O|  |TAMS 0x0000000719c00000| PB 0x0000000719c00000| Untracked 
| 171|0x0000000719e00000, 0x000000071a000000, 0x000000071a000000|100%| O|  |TAMS 0x0000000719e00000| PB 0x0000000719e00000| Untracked 
| 172|0x000000071a000000, 0x000000071a200000, 0x000000071a200000|100%| O|  |TAMS 0x000000071a000000| PB 0x000000071a000000| Untracked 
| 173|0x000000071a200000, 0x000000071a400000, 0x000000071a400000|100%| O|  |TAMS 0x000000071a200000| PB 0x000000071a200000| Untracked 
| 174|0x000000071a400000, 0x000000071a600000, 0x000000071a600000|100%| O|  |TAMS 0x000000071a400000| PB 0x000000071a400000| Untracked 
| 175|0x000000071a600000, 0x000000071a800000, 0x000000071a800000|100%| O|  |TAMS 0x000000071a600000| PB 0x000000071a600000| Untracked 
| 176|0x000000071a800000, 0x000000071aa00000, 0x000000071aa00000|100%| O|  |TAMS 0x000000071a800000| PB 0x000000071a800000| Untracked 
| 177|0x000000071aa00000, 0x000000071ac00000, 0x000000071ac00000|100%| O|  |TAMS 0x000000071aa00000| PB 0x000000071aa00000| Untracked 
| 178|0x000000071ac00000, 0x000000071ae00000, 0x000000071ae00000|100%|HS|  |TAMS 0x000000071ac00000| PB 0x000000071ac00000| Complete 
| 179|0x000000071ae00000, 0x000000071b000000, 0x000000071b000000|100%| O|  |TAMS 0x000000071ae00000| PB 0x000000071ae00000| Untracked 
| 180|0x000000071b000000, 0x000000071b200000, 0x000000071b200000|100%| O|  |TAMS 0x000000071b000000| PB 0x000000071b000000| Untracked 
| 181|0x000000071b200000, 0x000000071b400000, 0x000000071b400000|100%| O|  |TAMS 0x000000071b200000| PB 0x000000071b200000| Untracked 
| 182|0x000000071b400000, 0x000000071b600000, 0x000000071b600000|100%| O|  |TAMS 0x000000071b400000| PB 0x000000071b400000| Untracked 
| 183|0x000000071b600000, 0x000000071b800000, 0x000000071b800000|100%| O|  |TAMS 0x000000071b600000| PB 0x000000071b600000| Untracked 
| 184|0x000000071b800000, 0x000000071ba00000, 0x000000071ba00000|100%|HS|  |TAMS 0x000000071b800000| PB 0x000000071b800000| Complete 
| 185|0x000000071ba00000, 0x000000071ba00000, 0x000000071bc00000|  0%| F|  |TAMS 0x000000071ba00000| PB 0x000000071ba00000| Untracked 
| 186|0x000000071bc00000, 0x000000071bc00000, 0x000000071be00000|  0%| F|  |TAMS 0x000000071bc00000| PB 0x000000071bc00000| Untracked 
| 187|0x000000071be00000, 0x000000071be00000, 0x000000071c000000|  0%| F|  |TAMS 0x000000071be00000| PB 0x000000071be00000| Untracked 
| 188|0x000000071c000000, 0x000000071c000000, 0x000000071c200000|  0%| F|  |TAMS 0x000000071c000000| PB 0x000000071c000000| Untracked 
| 189|0x000000071c200000, 0x000000071c200000, 0x000000071c400000|  0%| F|  |TAMS 0x000000071c200000| PB 0x000000071c200000| Untracked 
| 190|0x000000071c400000, 0x000000071c400000, 0x000000071c600000|  0%| F|  |TAMS 0x000000071c400000| PB 0x000000071c400000| Untracked 
| 191|0x000000071c600000, 0x000000071c600000, 0x000000071c800000|  0%| F|  |TAMS 0x000000071c600000| PB 0x000000071c600000| Untracked 
| 192|0x000000071c800000, 0x000000071c800000, 0x000000071ca00000|  0%| F|  |TAMS 0x000000071c800000| PB 0x000000071c800000| Untracked 
| 193|0x000000071ca00000, 0x000000071ca00000, 0x000000071cc00000|  0%| F|  |TAMS 0x000000071ca00000| PB 0x000000071ca00000| Untracked 
| 194|0x000000071cc00000, 0x000000071cc00000, 0x000000071ce00000|  0%| F|  |TAMS 0x000000071cc00000| PB 0x000000071cc00000| Untracked 
| 195|0x000000071ce00000, 0x000000071ce00000, 0x000000071d000000|  0%| F|  |TAMS 0x000000071ce00000| PB 0x000000071ce00000| Untracked 
| 196|0x000000071d000000, 0x000000071d000000, 0x000000071d200000|  0%| F|  |TAMS 0x000000071d000000| PB 0x000000071d000000| Untracked 
| 197|0x000000071d200000, 0x000000071d200000, 0x000000071d400000|  0%| F|  |TAMS 0x000000071d200000| PB 0x000000071d200000| Untracked 
| 198|0x000000071d400000, 0x000000071d400000, 0x000000071d600000|  0%| F|  |TAMS 0x000000071d400000| PB 0x000000071d400000| Untracked 
| 199|0x000000071d600000, 0x000000071d600000, 0x000000071d800000|  0%| F|  |TAMS 0x000000071d600000| PB 0x000000071d600000| Untracked 
| 200|0x000000071d800000, 0x000000071d800000, 0x000000071da00000|  0%| F|  |TAMS 0x000000071d800000| PB 0x000000071d800000| Untracked 
| 201|0x000000071da00000, 0x000000071da00000, 0x000000071dc00000|  0%| F|  |TAMS 0x000000071da00000| PB 0x000000071da00000| Untracked 
| 202|0x000000071dc00000, 0x000000071dc00000, 0x000000071de00000|  0%| F|  |TAMS 0x000000071dc00000| PB 0x000000071dc00000| Untracked 
| 203|0x000000071de00000, 0x000000071de00000, 0x000000071e000000|  0%| F|  |TAMS 0x000000071de00000| PB 0x000000071de00000| Untracked 
| 204|0x000000071e000000, 0x000000071e000000, 0x000000071e200000|  0%| F|  |TAMS 0x000000071e000000| PB 0x000000071e000000| Untracked 
| 205|0x000000071e200000, 0x000000071e200000, 0x000000071e400000|  0%| F|  |TAMS 0x000000071e200000| PB 0x000000071e200000| Untracked 
| 206|0x000000071e400000, 0x000000071e400000, 0x000000071e600000|  0%| F|  |TAMS 0x000000071e400000| PB 0x000000071e400000| Untracked 
| 207|0x000000071e600000, 0x000000071e600000, 0x000000071e800000|  0%| F|  |TAMS 0x000000071e600000| PB 0x000000071e600000| Untracked 
| 208|0x000000071e800000, 0x000000071e800000, 0x000000071ea00000|  0%| F|  |TAMS 0x000000071e800000| PB 0x000000071e800000| Untracked 
| 209|0x000000071ea00000, 0x000000071ea00000, 0x000000071ec00000|  0%| F|  |TAMS 0x000000071ea00000| PB 0x000000071ea00000| Untracked 
| 210|0x000000071ec00000, 0x000000071ec00000, 0x000000071ee00000|  0%| F|  |TAMS 0x000000071ec00000| PB 0x000000071ec00000| Untracked 
| 211|0x000000071ee00000, 0x000000071ee00000, 0x000000071f000000|  0%| F|  |TAMS 0x000000071ee00000| PB 0x000000071ee00000| Untracked 
| 212|0x000000071f000000, 0x000000071f000000, 0x000000071f200000|  0%| F|  |TAMS 0x000000071f000000| PB 0x000000071f000000| Untracked 
| 213|0x000000071f200000, 0x000000071f200000, 0x000000071f400000|  0%| F|  |TAMS 0x000000071f200000| PB 0x000000071f200000| Untracked 
| 214|0x000000071f400000, 0x000000071f400000, 0x000000071f600000|  0%| F|  |TAMS 0x000000071f400000| PB 0x000000071f400000| Untracked 
| 215|0x000000071f600000, 0x000000071f600000, 0x000000071f800000|  0%| F|  |TAMS 0x000000071f600000| PB 0x000000071f600000| Untracked 
| 216|0x000000071f800000, 0x000000071f800000, 0x000000071fa00000|  0%| F|  |TAMS 0x000000071f800000| PB 0x000000071f800000| Untracked 
| 217|0x000000071fa00000, 0x000000071fa00000, 0x000000071fc00000|  0%| F|  |TAMS 0x000000071fa00000| PB 0x000000071fa00000| Untracked 
| 218|0x000000071fc00000, 0x000000071fc00000, 0x000000071fe00000|  0%| F|  |TAMS 0x000000071fc00000| PB 0x000000071fc00000| Untracked 
| 219|0x000000071fe00000, 0x000000071fe00000, 0x0000000720000000|  0%| F|  |TAMS 0x000000071fe00000| PB 0x000000071fe00000| Untracked 
| 220|0x0000000720000000, 0x0000000720000000, 0x0000000720200000|  0%| F|  |TAMS 0x0000000720000000| PB 0x0000000720000000| Untracked 
| 221|0x0000000720200000, 0x0000000720200000, 0x0000000720400000|  0%| F|  |TAMS 0x0000000720200000| PB 0x0000000720200000| Untracked 
| 222|0x0000000720400000, 0x0000000720400000, 0x0000000720600000|  0%| F|  |TAMS 0x0000000720400000| PB 0x0000000720400000| Untracked 
| 223|0x0000000720600000, 0x0000000720600000, 0x0000000720800000|  0%| F|  |TAMS 0x0000000720600000| PB 0x0000000720600000| Untracked 
| 224|0x0000000720800000, 0x0000000720800000, 0x0000000720a00000|  0%| F|  |TAMS 0x0000000720800000| PB 0x0000000720800000| Untracked 
| 225|0x0000000720a00000, 0x0000000720a00000, 0x0000000720c00000|  0%| F|  |TAMS 0x0000000720a00000| PB 0x0000000720a00000| Untracked 
| 226|0x0000000720c00000, 0x0000000720c00000, 0x0000000720e00000|  0%| F|  |TAMS 0x0000000720c00000| PB 0x0000000720c00000| Untracked 
| 227|0x0000000720e00000, 0x0000000720e00000, 0x0000000721000000|  0%| F|  |TAMS 0x0000000720e00000| PB 0x0000000720e00000| Untracked 
| 228|0x0000000721000000, 0x0000000721000000, 0x0000000721200000|  0%| F|  |TAMS 0x0000000721000000| PB 0x0000000721000000| Untracked 
| 229|0x0000000721200000, 0x0000000721200000, 0x0000000721400000|  0%| F|  |TAMS 0x0000000721200000| PB 0x0000000721200000| Untracked 
| 230|0x0000000721400000, 0x0000000721400000, 0x0000000721600000|  0%| F|  |TAMS 0x0000000721400000| PB 0x0000000721400000| Untracked 
| 231|0x0000000721600000, 0x0000000721600000, 0x0000000721800000|  0%| F|  |TAMS 0x0000000721600000| PB 0x0000000721600000| Untracked 
| 232|0x0000000721800000, 0x0000000721800000, 0x0000000721a00000|  0%| F|  |TAMS 0x0000000721800000| PB 0x0000000721800000| Untracked 
| 233|0x0000000721a00000, 0x0000000721a00000, 0x0000000721c00000|  0%| F|  |TAMS 0x0000000721a00000| PB 0x0000000721a00000| Untracked 
| 234|0x0000000721c00000, 0x0000000721c00000, 0x0000000721e00000|  0%| F|  |TAMS 0x0000000721c00000| PB 0x0000000721c00000| Untracked 
| 235|0x0000000721e00000, 0x0000000721e00000, 0x0000000722000000|  0%| F|  |TAMS 0x0000000721e00000| PB 0x0000000721e00000| Untracked 
| 236|0x0000000722000000, 0x0000000722000000, 0x0000000722200000|  0%| F|  |TAMS 0x0000000722000000| PB 0x0000000722000000| Untracked 
| 237|0x0000000722200000, 0x0000000722200000, 0x0000000722400000|  0%| F|  |TAMS 0x0000000722200000| PB 0x0000000722200000| Untracked 
| 238|0x0000000722400000, 0x0000000722400000, 0x0000000722600000|  0%| F|  |TAMS 0x0000000722400000| PB 0x0000000722400000| Untracked 
| 239|0x0000000722600000, 0x0000000722600000, 0x0000000722800000|  0%| F|  |TAMS 0x0000000722600000| PB 0x0000000722600000| Untracked 
| 240|0x0000000722800000, 0x0000000722800000, 0x0000000722a00000|  0%| F|  |TAMS 0x0000000722800000| PB 0x0000000722800000| Untracked 
| 241|0x0000000722a00000, 0x0000000722a00000, 0x0000000722c00000|  0%| F|  |TAMS 0x0000000722a00000| PB 0x0000000722a00000| Untracked 
| 242|0x0000000722c00000, 0x0000000722c00000, 0x0000000722e00000|  0%| F|  |TAMS 0x0000000722c00000| PB 0x0000000722c00000| Untracked 
| 243|0x0000000722e00000, 0x0000000722e00000, 0x0000000723000000|  0%| F|  |TAMS 0x0000000722e00000| PB 0x0000000722e00000| Untracked 
| 244|0x0000000723000000, 0x0000000723000000, 0x0000000723200000|  0%| F|  |TAMS 0x0000000723000000| PB 0x0000000723000000| Untracked 
| 245|0x0000000723200000, 0x0000000723200000, 0x0000000723400000|  0%| F|  |TAMS 0x0000000723200000| PB 0x0000000723200000| Untracked 
| 246|0x0000000723400000, 0x0000000723400000, 0x0000000723600000|  0%| F|  |TAMS 0x0000000723400000| PB 0x0000000723400000| Untracked 
| 247|0x0000000723600000, 0x0000000723600000, 0x0000000723800000|  0%| F|  |TAMS 0x0000000723600000| PB 0x0000000723600000| Untracked 
| 248|0x0000000723800000, 0x0000000723800000, 0x0000000723a00000|  0%| F|  |TAMS 0x0000000723800000| PB 0x0000000723800000| Untracked 
| 249|0x0000000723a00000, 0x0000000723a00000, 0x0000000723c00000|  0%| F|  |TAMS 0x0000000723a00000| PB 0x0000000723a00000| Untracked 
| 250|0x0000000723c00000, 0x0000000723c00000, 0x0000000723e00000|  0%| F|  |TAMS 0x0000000723c00000| PB 0x0000000723c00000| Untracked 
| 251|0x0000000723e00000, 0x0000000723e00000, 0x0000000724000000|  0%| F|  |TAMS 0x0000000723e00000| PB 0x0000000723e00000| Untracked 
| 252|0x0000000724000000, 0x0000000724000000, 0x0000000724200000|  0%| F|  |TAMS 0x0000000724000000| PB 0x0000000724000000| Untracked 
| 253|0x0000000724200000, 0x0000000724200000, 0x0000000724400000|  0%| F|  |TAMS 0x0000000724200000| PB 0x0000000724200000| Untracked 
| 254|0x0000000724400000, 0x0000000724400000, 0x0000000724600000|  0%| F|  |TAMS 0x0000000724400000| PB 0x0000000724400000| Untracked 
| 255|0x0000000724600000, 0x0000000724800000, 0x0000000724800000|100%| S|CS|TAMS 0x0000000724600000| PB 0x0000000724600000| Complete 
| 256|0x0000000724800000, 0x0000000724a00000, 0x0000000724a00000|100%| S|CS|TAMS 0x0000000724800000| PB 0x0000000724800000| Complete 
| 257|0x0000000724a00000, 0x0000000724c00000, 0x0000000724c00000|100%| S|CS|TAMS 0x0000000724a00000| PB 0x0000000724a00000| Complete 
| 258|0x0000000724c00000, 0x0000000724e00000, 0x0000000724e00000|100%| S|CS|TAMS 0x0000000724c00000| PB 0x0000000724c00000| Complete 
| 259|0x0000000724e00000, 0x0000000725000000, 0x0000000725000000|100%| S|CS|TAMS 0x0000000724e00000| PB 0x0000000724e00000| Complete 
| 260|0x0000000725000000, 0x0000000725200000, 0x0000000725200000|100%| S|CS|TAMS 0x0000000725000000| PB 0x0000000725000000| Complete 
| 261|0x0000000725200000, 0x0000000725400000, 0x0000000725400000|100%| S|CS|TAMS 0x0000000725200000| PB 0x0000000725200000| Complete 
| 262|0x0000000725400000, 0x0000000725600000, 0x0000000725600000|100%| S|CS|TAMS 0x0000000725400000| PB 0x0000000725400000| Complete 
| 263|0x0000000725600000, 0x0000000725800000, 0x0000000725800000|100%| S|CS|TAMS 0x0000000725600000| PB 0x0000000725600000| Complete 
| 264|0x0000000725800000, 0x0000000725a00000, 0x0000000725a00000|100%| S|CS|TAMS 0x0000000725800000| PB 0x0000000725800000| Complete 
| 265|0x0000000725a00000, 0x0000000725c00000, 0x0000000725c00000|100%| S|CS|TAMS 0x0000000725a00000| PB 0x0000000725a00000| Complete 
| 266|0x0000000725c00000, 0x0000000725e00000, 0x0000000725e00000|100%| S|CS|TAMS 0x0000000725c00000| PB 0x0000000725c00000| Complete 
| 267|0x0000000725e00000, 0x0000000726000000, 0x0000000726000000|100%| S|CS|TAMS 0x0000000725e00000| PB 0x0000000725e00000| Complete 
| 268|0x0000000726000000, 0x0000000726200000, 0x0000000726200000|100%| S|CS|TAMS 0x0000000726000000| PB 0x0000000726000000| Complete 
| 269|0x0000000726200000, 0x0000000726400000, 0x0000000726400000|100%| S|CS|TAMS 0x0000000726200000| PB 0x0000000726200000| Complete 
| 270|0x0000000726400000, 0x0000000726400000, 0x0000000726600000|  0%| F|  |TAMS 0x0000000726400000| PB 0x0000000726400000| Untracked 
| 271|0x0000000726600000, 0x0000000726600000, 0x0000000726800000|  0%| F|  |TAMS 0x0000000726600000| PB 0x0000000726600000| Untracked 
| 272|0x0000000726800000, 0x0000000726800000, 0x0000000726a00000|  0%| F|  |TAMS 0x0000000726800000| PB 0x0000000726800000| Untracked 
| 273|0x0000000726a00000, 0x0000000726a00000, 0x0000000726c00000|  0%| F|  |TAMS 0x0000000726a00000| PB 0x0000000726a00000| Untracked 
| 274|0x0000000726c00000, 0x0000000726c00000, 0x0000000726e00000|  0%| F|  |TAMS 0x0000000726c00000| PB 0x0000000726c00000| Untracked 
| 275|0x0000000726e00000, 0x0000000726e00000, 0x0000000727000000|  0%| F|  |TAMS 0x0000000726e00000| PB 0x0000000726e00000| Untracked 
| 276|0x0000000727000000, 0x0000000727000000, 0x0000000727200000|  0%| F|  |TAMS 0x0000000727000000| PB 0x0000000727000000| Untracked 
| 277|0x0000000727200000, 0x0000000727200000, 0x0000000727400000|  0%| F|  |TAMS 0x0000000727200000| PB 0x0000000727200000| Untracked 
| 278|0x0000000727400000, 0x0000000727400000, 0x0000000727600000|  0%| F|  |TAMS 0x0000000727400000| PB 0x0000000727400000| Untracked 
| 279|0x0000000727600000, 0x0000000727600000, 0x0000000727800000|  0%| F|  |TAMS 0x0000000727600000| PB 0x0000000727600000| Untracked 
| 280|0x0000000727800000, 0x0000000727800000, 0x0000000727a00000|  0%| F|  |TAMS 0x0000000727800000| PB 0x0000000727800000| Untracked 
| 281|0x0000000727a00000, 0x0000000727a00000, 0x0000000727c00000|  0%| F|  |TAMS 0x0000000727a00000| PB 0x0000000727a00000| Untracked 
| 282|0x0000000727c00000, 0x0000000727c00000, 0x0000000727e00000|  0%| F|  |TAMS 0x0000000727c00000| PB 0x0000000727c00000| Untracked 
| 283|0x0000000727e00000, 0x0000000727e00000, 0x0000000728000000|  0%| F|  |TAMS 0x0000000727e00000| PB 0x0000000727e00000| Untracked 
| 284|0x0000000728000000, 0x0000000728000000, 0x0000000728200000|  0%| F|  |TAMS 0x0000000728000000| PB 0x0000000728000000| Untracked 
| 285|0x0000000728200000, 0x0000000728200000, 0x0000000728400000|  0%| F|  |TAMS 0x0000000728200000| PB 0x0000000728200000| Untracked 
| 286|0x0000000728400000, 0x0000000728400000, 0x0000000728600000|  0%| F|  |TAMS 0x0000000728400000| PB 0x0000000728400000| Untracked 
| 287|0x0000000728600000, 0x0000000728600000, 0x0000000728800000|  0%| F|  |TAMS 0x0000000728600000| PB 0x0000000728600000| Untracked 
| 288|0x0000000728800000, 0x0000000728800000, 0x0000000728a00000|  0%| F|  |TAMS 0x0000000728800000| PB 0x0000000728800000| Untracked 
| 289|0x0000000728a00000, 0x0000000728a00000, 0x0000000728c00000|  0%| F|  |TAMS 0x0000000728a00000| PB 0x0000000728a00000| Untracked 
| 290|0x0000000728c00000, 0x0000000728c00000, 0x0000000728e00000|  0%| F|  |TAMS 0x0000000728c00000| PB 0x0000000728c00000| Untracked 
| 291|0x0000000728e00000, 0x0000000728e00000, 0x0000000729000000|  0%| F|  |TAMS 0x0000000728e00000| PB 0x0000000728e00000| Untracked 
| 292|0x0000000729000000, 0x0000000729000000, 0x0000000729200000|  0%| F|  |TAMS 0x0000000729000000| PB 0x0000000729000000| Untracked 
| 293|0x0000000729200000, 0x0000000729200000, 0x0000000729400000|  0%| F|  |TAMS 0x0000000729200000| PB 0x0000000729200000| Untracked 
| 294|0x0000000729400000, 0x0000000729400000, 0x0000000729600000|  0%| F|  |TAMS 0x0000000729400000| PB 0x0000000729400000| Untracked 
| 295|0x0000000729600000, 0x0000000729600000, 0x0000000729800000|  0%| F|  |TAMS 0x0000000729600000| PB 0x0000000729600000| Untracked 
| 296|0x0000000729800000, 0x0000000729800000, 0x0000000729a00000|  0%| F|  |TAMS 0x0000000729800000| PB 0x0000000729800000| Untracked 
| 297|0x0000000729a00000, 0x0000000729a00000, 0x0000000729c00000|  0%| F|  |TAMS 0x0000000729a00000| PB 0x0000000729a00000| Untracked 
| 298|0x0000000729c00000, 0x0000000729c00000, 0x0000000729e00000|  0%| F|  |TAMS 0x0000000729c00000| PB 0x0000000729c00000| Untracked 
| 299|0x0000000729e00000, 0x0000000729e00000, 0x000000072a000000|  0%| F|  |TAMS 0x0000000729e00000| PB 0x0000000729e00000| Untracked 
| 300|0x000000072a000000, 0x000000072a000000, 0x000000072a200000|  0%| F|  |TAMS 0x000000072a000000| PB 0x000000072a000000| Untracked 
| 301|0x000000072a200000, 0x000000072a300000, 0x000000072a400000| 50%| E|  |TAMS 0x000000072a200000| PB 0x000000072a200000| Complete 
| 302|0x000000072a400000, 0x000000072a600000, 0x000000072a600000|100%| E|CS|TAMS 0x000000072a400000| PB 0x000000072a400000| Complete 
| 303|0x000000072a600000, 0x000000072a800000, 0x000000072a800000|100%| E|CS|TAMS 0x000000072a600000| PB 0x000000072a600000| Complete 
| 304|0x000000072a800000, 0x000000072aa00000, 0x000000072aa00000|100%| E|  |TAMS 0x000000072a800000| PB 0x000000072a800000| Complete 
| 305|0x000000072aa00000, 0x000000072ac00000, 0x000000072ac00000|100%| E|CS|TAMS 0x000000072aa00000| PB 0x000000072aa00000| Complete 
| 306|0x000000072ac00000, 0x000000072ae00000, 0x000000072ae00000|100%| E|CS|TAMS 0x000000072ac00000| PB 0x000000072ac00000| Complete 
| 307|0x000000072ae00000, 0x000000072b000000, 0x000000072b000000|100%| E|CS|TAMS 0x000000072ae00000| PB 0x000000072ae00000| Complete 
| 308|0x000000072b000000, 0x000000072b200000, 0x000000072b200000|100%| E|CS|TAMS 0x000000072b000000| PB 0x000000072b000000| Complete 
| 309|0x000000072b200000, 0x000000072b400000, 0x000000072b400000|100%| E|CS|TAMS 0x000000072b200000| PB 0x000000072b200000| Complete 
| 310|0x000000072b400000, 0x000000072b600000, 0x000000072b600000|100%| E|CS|TAMS 0x000000072b400000| PB 0x000000072b400000| Complete 
| 311|0x000000072b600000, 0x000000072b800000, 0x000000072b800000|100%| E|CS|TAMS 0x000000072b600000| PB 0x000000072b600000| Complete 
| 312|0x000000072b800000, 0x000000072ba00000, 0x000000072ba00000|100%| E|CS|TAMS 0x000000072b800000| PB 0x000000072b800000| Complete 
| 313|0x000000072ba00000, 0x000000072bc00000, 0x000000072bc00000|100%| E|CS|TAMS 0x000000072ba00000| PB 0x000000072ba00000| Complete 
| 314|0x000000072bc00000, 0x000000072be00000, 0x000000072be00000|100%| E|CS|TAMS 0x000000072bc00000| PB 0x000000072bc00000| Complete 
| 315|0x000000072be00000, 0x000000072c000000, 0x000000072c000000|100%| E|CS|TAMS 0x000000072be00000| PB 0x000000072be00000| Complete 
| 316|0x000000072c000000, 0x000000072c200000, 0x000000072c200000|100%| E|CS|TAMS 0x000000072c000000| PB 0x000000072c000000| Complete 
| 317|0x000000072c200000, 0x000000072c400000, 0x000000072c400000|100%| E|CS|TAMS 0x000000072c200000| PB 0x000000072c200000| Complete 
| 318|0x000000072c400000, 0x000000072c600000, 0x000000072c600000|100%| E|CS|TAMS 0x000000072c400000| PB 0x000000072c400000| Complete 
| 319|0x000000072c600000, 0x000000072c800000, 0x000000072c800000|100%| E|CS|TAMS 0x000000072c600000| PB 0x000000072c600000| Complete 
| 320|0x000000072c800000, 0x000000072ca00000, 0x000000072ca00000|100%| E|CS|TAMS 0x000000072c800000| PB 0x000000072c800000| Complete 
| 321|0x000000072ca00000, 0x000000072cc00000, 0x000000072cc00000|100%| E|CS|TAMS 0x000000072ca00000| PB 0x000000072ca00000| Complete 
| 322|0x000000072cc00000, 0x000000072ce00000, 0x000000072ce00000|100%| E|CS|TAMS 0x000000072cc00000| PB 0x000000072cc00000| Complete 
| 323|0x000000072ce00000, 0x000000072d000000, 0x000000072d000000|100%| E|CS|TAMS 0x000000072ce00000| PB 0x000000072ce00000| Complete 
| 324|0x000000072d000000, 0x000000072d200000, 0x000000072d200000|100%| E|CS|TAMS 0x000000072d000000| PB 0x000000072d000000| Complete 
| 325|0x000000072d200000, 0x000000072d400000, 0x000000072d400000|100%| E|CS|TAMS 0x000000072d200000| PB 0x000000072d200000| Complete 
| 326|0x000000072d400000, 0x000000072d600000, 0x000000072d600000|100%| E|CS|TAMS 0x000000072d400000| PB 0x000000072d400000| Complete 
| 327|0x000000072d600000, 0x000000072d800000, 0x000000072d800000|100%| E|CS|TAMS 0x000000072d600000| PB 0x000000072d600000| Complete 
| 328|0x000000072d800000, 0x000000072da00000, 0x000000072da00000|100%| E|CS|TAMS 0x000000072d800000| PB 0x000000072d800000| Complete 
| 329|0x000000072da00000, 0x000000072dc00000, 0x000000072dc00000|100%| E|CS|TAMS 0x000000072da00000| PB 0x000000072da00000| Complete 
| 330|0x000000072dc00000, 0x000000072de00000, 0x000000072de00000|100%| E|CS|TAMS 0x000000072dc00000| PB 0x000000072dc00000| Complete 
| 331|0x000000072de00000, 0x000000072e000000, 0x000000072e000000|100%| E|CS|TAMS 0x000000072de00000| PB 0x000000072de00000| Complete 
| 332|0x000000072e000000, 0x000000072e200000, 0x000000072e200000|100%| E|CS|TAMS 0x000000072e000000| PB 0x000000072e000000| Complete 
| 333|0x000000072e200000, 0x000000072e400000, 0x000000072e400000|100%| E|CS|TAMS 0x000000072e200000| PB 0x000000072e200000| Complete 
| 334|0x000000072e400000, 0x000000072e600000, 0x000000072e600000|100%| E|CS|TAMS 0x000000072e400000| PB 0x000000072e400000| Complete 
| 335|0x000000072e600000, 0x000000072e800000, 0x000000072e800000|100%| E|CS|TAMS 0x000000072e600000| PB 0x000000072e600000| Complete 
| 336|0x000000072e800000, 0x000000072ea00000, 0x000000072ea00000|100%| E|CS|TAMS 0x000000072e800000| PB 0x000000072e800000| Complete 
| 337|0x000000072ea00000, 0x000000072ec00000, 0x000000072ec00000|100%| E|CS|TAMS 0x000000072ea00000| PB 0x000000072ea00000| Complete 
| 338|0x000000072ec00000, 0x000000072ee00000, 0x000000072ee00000|100%| E|CS|TAMS 0x000000072ec00000| PB 0x000000072ec00000| Complete 
| 339|0x000000072ee00000, 0x000000072f000000, 0x000000072f000000|100%| E|CS|TAMS 0x000000072ee00000| PB 0x000000072ee00000| Complete 
| 340|0x000000072f000000, 0x000000072f200000, 0x000000072f200000|100%| E|CS|TAMS 0x000000072f000000| PB 0x000000072f000000| Complete 
| 341|0x000000072f200000, 0x000000072f400000, 0x000000072f400000|100%| E|CS|TAMS 0x000000072f200000| PB 0x000000072f200000| Complete 
|1989|0x00000007fd200000, 0x00000007fd400000, 0x00000007fd400000|100%| O|  |TAMS 0x00000007fd200000| PB 0x00000007fd200000| Untracked 
|1990|0x00000007fd400000, 0x00000007fd600000, 0x00000007fd600000|100%| O|Cm|TAMS 0x00000007fd400000| PB 0x00000007fd400000| Complete 
|1991|0x00000007fd600000, 0x00000007fd800000, 0x00000007fd800000|100%| O|Cm|TAMS 0x00000007fd600000| PB 0x00000007fd600000| Complete 
|1992|0x00000007fd800000, 0x00000007fda00000, 0x00000007fda00000|100%| O|  |TAMS 0x00000007fd800000| PB 0x00000007fd800000| Untracked 
|1993|0x00000007fda00000, 0x00000007fdc00000, 0x00000007fdc00000|100%| O|  |TAMS 0x00000007fda00000| PB 0x00000007fda00000| Untracked 
|1994|0x00000007fdc00000, 0x00000007fde00000, 0x00000007fde00000|100%| O|  |TAMS 0x00000007fdc00000| PB 0x00000007fdc00000| Untracked 
|1995|0x00000007fde00000, 0x00000007fe000000, 0x00000007fe000000|100%| O|Cm|TAMS 0x00000007fde00000| PB 0x00000007fde00000| Complete 
|1996|0x00000007fe000000, 0x00000007fe200000, 0x00000007fe200000|100%| O|  |TAMS 0x00000007fe000000| PB 0x00000007fe000000| Untracked 
|1997|0x00000007fe200000, 0x00000007fe400000, 0x00000007fe400000|100%| O|  |TAMS 0x00000007fe200000| PB 0x00000007fe200000| Untracked 
|1998|0x00000007fe400000, 0x00000007fe600000, 0x00000007fe600000|100%| O|  |TAMS 0x00000007fe400000| PB 0x00000007fe400000| Untracked 
|1999|0x00000007fe600000, 0x00000007fe800000, 0x00000007fe800000|100%| O|  |TAMS 0x00000007fe600000| PB 0x00000007fe600000| Untracked 
|2000|0x00000007fe800000, 0x00000007fea00000, 0x00000007fea00000|100%| O|  |TAMS 0x00000007fe800000| PB 0x00000007fe800000| Untracked 
|2001|0x00000007fea00000, 0x00000007fec00000, 0x00000007fec00000|100%| O|  |TAMS 0x00000007fea00000| PB 0x00000007fea00000| Untracked 
|2002|0x00000007fec00000, 0x00000007fee00000, 0x00000007fee00000|100%| O|  |TAMS 0x00000007fec00000| PB 0x00000007fec00000| Untracked 
|2003|0x00000007fee00000, 0x00000007ff000000, 0x00000007ff000000|100%| O|  |TAMS 0x00000007fee00000| PB 0x00000007fee00000| Untracked 
|2004|0x00000007ff000000, 0x00000007ff200000, 0x00000007ff200000|100%| O|  |TAMS 0x00000007ff000000| PB 0x00000007ff000000| Untracked 
|2005|0x00000007ff200000, 0x00000007ff400000, 0x00000007ff400000|100%| O|  |TAMS 0x00000007ff200000| PB 0x00000007ff200000| Untracked 
|2006|0x00000007ff400000, 0x00000007ff600000, 0x00000007ff600000|100%| O|  |TAMS 0x00000007ff400000| PB 0x00000007ff400000| Untracked 
|2007|0x00000007ff600000, 0x00000007ff800000, 0x00000007ff800000|100%| O|  |TAMS 0x00000007ff600000| PB 0x00000007ff600000| Untracked 
|2008|0x00000007ff800000, 0x00000007ffa00000, 0x00000007ffa00000|100%| O|  |TAMS 0x00000007ff800000| PB 0x00000007ff800000| Untracked 
|2009|0x00000007ffa00000, 0x00000007ffc00000, 0x00000007ffc00000|100%| E|CS|TAMS 0x00000007ffa00000| PB 0x00000007ffa00000| Complete 
|2010|0x00000007ffc00000, 0x00000007ffe00000, 0x00000007ffe00000|100%| E|CS|TAMS 0x00000007ffc00000| PB 0x00000007ffc00000| Complete 
|2011|0x00000007ffe00000, 0x0000000800000000, 0x0000000800000000|100%| E|CS|TAMS 0x00000007ffe00000| PB 0x00000007ffe00000| Complete 

Card table byte_map: [0x000002636dbe0000,0x000002636e3c0000] _byte_map_base: 0x000002636a3bc000

Marking Bits: (CMBitMap*) 0x0000026359a25300
 Bits: [0x000002636e3c0000, 0x00000263722a0000)

Polling page: 0x0000026357dc0000

Metaspace:

Usage:
  Non-class:     65.34 MB used.
      Class:      7.09 MB used.
       Both:     72.43 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      66.31 MB ( 52%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       7.81 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      74.12 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  13.42 MB
       Class:  8.12 MB
        Both:  21.55 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 123.44 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 1564.
num_arena_deaths: 20.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1186.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 29.
num_chunks_taken_from_freelist: 5419.
num_chunk_merges: 12.
num_chunk_splits: 3159.
num_chunks_enlarged: 1554.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=11052Kb max_used=13319Kb free=108115Kb
 bounds [0x0000026364860000, 0x0000026365570000, 0x000002636bcc0000]
CodeHeap 'profiled nmethods': size=119104Kb used=30236Kb max_used=39433Kb free=88867Kb
 bounds [0x000002635ccc0000, 0x000002635f350000, 0x0000026364110000]
CodeHeap 'non-nmethods': size=7488Kb used=2915Kb max_used=3565Kb free=4572Kb
 bounds [0x0000026364110000, 0x0000026364490000, 0x0000026364860000]
 total_blobs=14524 nmethods=13725 adapters=699
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 25.939 Thread 0x0000026374e64890 20203       4       jdk.internal.jimage.ImageReader$Directory::getChildren (8 bytes)
Event: 25.941 Thread 0x0000026374e64890 nmethod 20203 0x0000026365558b10 code [0x0000026365558cc0, 0x0000026365558fe0]
Event: 25.949 Thread 0x0000026374e64890 20204 %     4       org.eclipse.jdt.internal.core.index.DiskIndex::computeDocumentNames @ 40 (607 bytes)
Event: 25.958 Thread 0x0000026374e64890 nmethod 20204% 0x0000026365559190 code [0x0000026365559380, 0x0000026365559b20]
Event: 25.966 Thread 0x0000026374e64890 20205 %     4       org.eclipse.jdt.internal.core.index.DiskIndex::computeDocumentNames @ 152 (607 bytes)
Event: 25.969 Thread 0x0000026374e64890 nmethod 20205% 0x000002636555a010 code [0x000002636555a1c0, 0x000002636555a480]
Event: 26.265 Thread 0x0000026374e64890 20206 % !   4       org.eclipse.jdt.internal.core.index.DiskIndex::mergeWith @ 262 (623 bytes)
Event: 26.332 Thread 0x0000026374e64890 nmethod 20206% 0x000002636555a690 code [0x000002636555ad40, 0x000002636555f0c0]
Event: 26.369 Thread 0x0000026374e64890 20207   !   4       org.eclipse.jdt.internal.core.index.DiskIndex::mergeWith (623 bytes)
Event: 27.532 Thread 0x0000026374e65e80 20209       3       org.eclipse.jdt.internal.core.search.indexing.IndexManager$MetaIndexUpdateRequest::equals (36 bytes)
Event: 27.533 Thread 0x0000026374e65e80 nmethod 20209 0x000002635cd6f610 code [0x000002635cd6f7e0, 0x000002635cd6fc00]
Event: 27.535 Thread 0x0000026374e65e80 20212   !   3       org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent (577 bytes)
Event: 27.540 Thread 0x0000026374e65e80 nmethod 20212 0x000002635e0fee10 code [0x000002635e0ff880, 0x000002635e105370]
Event: 27.540 Thread 0x0000026374e65e80 20211   !   3       org.eclipse.osgi.internal.framework.BundleContextImpl::setContextFinder (84 bytes)
Event: 27.540 Thread 0x0000026374e65e80 nmethod 20211 0x000002635cd42d90 code [0x000002635cd42fc0, 0x000002635cd43748]
Event: 27.541 Thread 0x0000026374e65e80 20213   !   3       org.eclipse.jdt.internal.core.index.IndexLocation::<init> (41 bytes)
Event: 27.542 Thread 0x0000026374e65e80 nmethod 20213 0x000002635ccfe410 code [0x000002635ccfe600, 0x000002635ccfe9f0]
Event: 27.545 Thread 0x0000026374e65e80 20215       3       org.eclipse.core.runtime.ListenerList$ListenerListIterator::next (39 bytes)
Event: 27.545 Thread 0x0000026374e65e80 nmethod 20215 0x000002635cd6bd90 code [0x000002635cd6bf40, 0x000002635cd6c198]
Event: 27.605 Thread 0x0000026374e65e80 20220   !   3       org.eclipse.jdt.internal.core.search.indexing.AddJarFileToIndex::execute (1557 bytes)

GC Heap History (20 events):
Event: 17.701 GC heap before
{Heap before GC invocations=58 (full 0):
 garbage-first heap   total 600064K, used 538003K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 111 young (227328K), 10 survivors (20480K)
 Metaspace       used 64653K, committed 66240K, reserved 1114112K
  class space    used 6358K, committed 7104K, reserved 1048576K
}
Event: 17.710 GC heap after
{Heap after GC invocations=59 (full 0):
 garbage-first heap   total 600064K, used 302025K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 13 young (26624K), 13 survivors (26624K)
 Metaspace       used 64653K, committed 66240K, reserved 1114112K
  class space    used 6358K, committed 7104K, reserved 1048576K
}
Event: 19.119 GC heap before
{Heap before GC invocations=59 (full 0):
 garbage-first heap   total 600064K, used 531401K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 124 young (253952K), 13 survivors (26624K)
 Metaspace       used 67307K, committed 69056K, reserved 1114112K
  class space    used 6641K, committed 7424K, reserved 1048576K
}
Event: 19.129 GC heap after
{Heap after GC invocations=60 (full 0):
 garbage-first heap   total 608256K, used 292387K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 10 survivors (20480K)
 Metaspace       used 67307K, committed 69056K, reserved 1114112K
  class space    used 6641K, committed 7424K, reserved 1048576K
}
Event: 19.937 GC heap before
{Heap before GC invocations=60 (full 0):
 garbage-first heap   total 608256K, used 536099K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 129 young (264192K), 10 survivors (20480K)
 Metaspace       used 68024K, committed 69696K, reserved 1114112K
  class space    used 6691K, committed 7424K, reserved 1048576K
}
Event: 19.944 GC heap after
{Heap after GC invocations=61 (full 0):
 garbage-first heap   total 608256K, used 306444K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 7 survivors (14336K)
 Metaspace       used 68024K, committed 69696K, reserved 1114112K
  class space    used 6691K, committed 7424K, reserved 1048576K
}
Event: 21.123 GC heap before
{Heap before GC invocations=61 (full 0):
 garbage-first heap   total 608256K, used 544012K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 123 young (251904K), 7 survivors (14336K)
 Metaspace       used 68186K, committed 69824K, reserved 1114112K
  class space    used 6695K, committed 7424K, reserved 1048576K
}
Event: 21.126 GC heap after
{Heap after GC invocations=62 (full 0):
 garbage-first heap   total 608256K, used 306446K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 7 survivors (14336K)
 Metaspace       used 68186K, committed 69824K, reserved 1114112K
  class space    used 6695K, committed 7424K, reserved 1048576K
}
Event: 22.072 GC heap before
{Heap before GC invocations=62 (full 0):
 garbage-first heap   total 608256K, used 556302K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 129 young (264192K), 7 survivors (14336K)
 Metaspace       used 68264K, committed 69888K, reserved 1114112K
  class space    used 6702K, committed 7424K, reserved 1048576K
}
Event: 22.076 GC heap after
{Heap after GC invocations=63 (full 0):
 garbage-first heap   total 608256K, used 307119K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 7 survivors (14336K)
 Metaspace       used 68264K, committed 69888K, reserved 1114112K
  class space    used 6702K, committed 7424K, reserved 1048576K
}
Event: 23.325 GC heap before
{Heap before GC invocations=64 (full 0):
 garbage-first heap   total 608256K, used 606127K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 119 young (243712K), 7 survivors (14336K)
 Metaspace       used 72916K, committed 74624K, reserved 1179648K
  class space    used 7110K, committed 7872K, reserved 1048576K
}
Event: 23.342 GC heap after
{Heap after GC invocations=65 (full 0):
 garbage-first heap   total 673792K, used 354531K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 17 survivors (34816K)
 Metaspace       used 72916K, committed 74624K, reserved 1179648K
  class space    used 7110K, committed 7872K, reserved 1048576K
}
Event: 24.466 GC heap before
{Heap before GC invocations=65 (full 0):
 garbage-first heap   total 673792K, used 592099K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 132 young (270336K), 17 survivors (34816K)
 Metaspace       used 74106K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K
}
Event: 24.480 GC heap after
{Heap after GC invocations=66 (full 0):
 garbage-first heap   total 679936K, used 341808K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 17 survivors (34816K)
 Metaspace       used 74106K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K
}
Event: 25.220 GC heap before
{Heap before GC invocations=66 (full 0):
 garbage-first heap   total 679936K, used 595760K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 139 young (284672K), 17 survivors (34816K)
 Metaspace       used 74107K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K
}
Event: 25.236 GC heap after
{Heap after GC invocations=67 (full 0):
 garbage-first heap   total 690176K, used 375176K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 18 young (36864K), 18 survivors (36864K)
 Metaspace       used 74107K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K
}
Event: 25.821 GC heap before
{Heap before GC invocations=67 (full 0):
 garbage-first heap   total 690176K, used 600456K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 128 young (262144K), 18 survivors (36864K)
 Metaspace       used 74108K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K
}
Event: 25.835 GC heap after
{Heap after GC invocations=68 (full 0):
 garbage-first heap   total 690176K, used 411020K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 16 survivors (32768K)
 Metaspace       used 74108K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K
}
Event: 26.315 GC heap before
{Heap before GC invocations=68 (full 0):
 garbage-first heap   total 690176K, used 505228K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 63 young (129024K), 16 survivors (32768K)
 Metaspace       used 74108K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K
}
Event: 26.328 GC heap after
{Heap after GC invocations=69 (full 0):
 garbage-first heap   total 690176K, used 446464K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 15 survivors (30720K)
 Metaspace       used 74108K, committed 75840K, reserved 1179648K
  class space    used 7258K, committed 8000K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.008 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.061 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.064 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.064 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.068 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.148 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.172 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.264 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.315 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 3.164 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-2071759690\jna5858489855972179816.dll

Deoptimization events (20 events):
Event: 26.618 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.618 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.629 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.629 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.687 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.687 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.697 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.697 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.708 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.708 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.735 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.735 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.746 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.746 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.841 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.841 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.860 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.860 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0
Event: 26.868 Thread 0x000002637aba2750 DEOPT PACKING pc=0x000002635ee64696 sp=0x00000092ad7fed60
Event: 26.868 Thread 0x000002637aba2750 DEOPT UNPACKING pc=0x0000026364164e42 sp=0x00000092ad7fe240 mode 0

Classes loaded (20 events):
Event: 22.846 Loading class sun/security/util/ArrayUtil
Event: 22.846 Loading class sun/security/util/ArrayUtil done
Event: 22.883 Loading class java/util/zip/CheckedInputStream
Event: 22.884 Loading class java/util/zip/CheckedInputStream done
Event: 22.929 Loading class java/util/zip/GZIPInputStream$1
Event: 22.929 Loading class java/util/zip/GZIPInputStream$1 done
Event: 22.971 Loading class jdk/internal/ref/CleanerImpl$InnocuousThreadFactory
Event: 22.972 Loading class jdk/internal/ref/CleanerImpl$InnocuousThreadFactory done
Event: 23.048 Loading class java/util/HashMap$EntrySpliterator
Event: 23.049 Loading class java/util/HashMap$EntrySpliterator done
Event: 23.124 Loading class java/util/stream/ReduceOps$5
Event: 23.124 Loading class java/util/stream/ReduceOps$5 done
Event: 23.124 Loading class java/util/stream/ReduceOps$CountingSink$OfRef
Event: 23.125 Loading class java/util/stream/ReduceOps$CountingSink
Event: 23.125 Loading class java/util/stream/ReduceOps$CountingSink done
Event: 23.125 Loading class java/util/stream/ReduceOps$CountingSink$OfRef done
Event: 23.186 Loading class java/util/stream/ReduceOps$1
Event: 23.186 Loading class java/util/stream/ReduceOps$1 done
Event: 23.187 Loading class java/util/stream/ReduceOps$1ReducingSink
Event: 23.187 Loading class java/util/stream/ReduceOps$1ReducingSink done

Classes unloaded (10 events):
Event: 4.977 Thread 0x0000026357e10e70 Unloading class 0x00000263011a2c00 'java/lang/invoke/LambdaForm$MH+0x00000263011a2c00'
Event: 4.977 Thread 0x0000026357e10e70 Unloading class 0x00000263011a2800 'java/lang/invoke/LambdaForm$MH+0x00000263011a2800'
Event: 4.977 Thread 0x0000026357e10e70 Unloading class 0x00000263011a2400 'java/lang/invoke/LambdaForm$MH+0x00000263011a2400'
Event: 4.977 Thread 0x0000026357e10e70 Unloading class 0x00000263011a2000 'java/lang/invoke/LambdaForm$MH+0x00000263011a2000'
Event: 4.977 Thread 0x0000026357e10e70 Unloading class 0x00000263011a1c00 'java/lang/invoke/LambdaForm$BMH+0x00000263011a1c00'
Event: 4.977 Thread 0x0000026357e10e70 Unloading class 0x00000263011a1800 'java/lang/invoke/LambdaForm$DMH+0x00000263011a1800'
Event: 4.977 Thread 0x0000026357e10e70 Unloading class 0x00000263011a0800 'java/lang/invoke/LambdaForm$DMH+0x00000263011a0800'
Event: 16.678 Thread 0x0000026357e10e70 Unloading class 0x00000263016b4c00 'java/lang/invoke/LambdaForm$DMH+0x00000263016b4c00'
Event: 16.678 Thread 0x0000026357e10e70 Unloading class 0x00000263016b4800 'java/lang/invoke/LambdaForm$DMH+0x00000263016b4800'
Event: 16.678 Thread 0x0000026357e10e70 Unloading class 0x00000263016b4400 'java/lang/invoke/LambdaForm$DMH+0x00000263016b4400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 23.072 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071e7adc58}> (0x000000071e7adc58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.072 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071e7ae288}> (0x000000071e7ae288) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.073 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071e7aeef0}> (0x000000071e7aeef0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.073 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071e7af520}> (0x000000071e7af520) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.073 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071e7ba280}> (0x000000071e7ba280) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.073 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071e7ba8b0}> (0x000000071e7ba8b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.128 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071dac4800}> (0x000000071dac4800) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.128 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071dac4e30}> (0x000000071dac4e30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.135 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071d7b1100}> (0x000000071d7b1100) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.135 Thread 0x000002637aba6260 Exception <a 'sun/nio/fs/WindowsException'{0x000000071d7b1730}> (0x000000071d7b1730) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.192 Thread 0x000002637d1e0d50 Exception <a 'sun/nio/fs/WindowsException'{0x000000071cc021d0}> (0x000000071cc021d0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.217 Thread 0x000002637aba6260 Exception <a 'java/lang/NoSuchMethodError'{0x000000071cb148c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000071cb148c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 23.241 Thread 0x000002637aba8330 Exception <a 'sun/nio/fs/WindowsException'{0x000000071ca38350}> (0x000000071ca38350) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.242 Thread 0x000002637aba8330 Exception <a 'sun/nio/fs/WindowsException'{0x000000071ca39740}> (0x000000071ca39740) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.257 Thread 0x000002637d1dd8d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000071c68cf18}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000071c68cf18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 23.262 Thread 0x000002637d1dd8d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000071c69ed70}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000071c69ed70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 23.446 Thread 0x000002637d1dd8d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000072865c918}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x000000072865c918) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 23.660 Thread 0x000002637d1dd8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007285932f0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000007285932f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 27.530 Thread 0x000002637aba2750 Exception <a 'sun/nio/fs/WindowsException'{0x000000072dd7ed78}> (0x000000072dd7ed78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 27.544 Thread 0x000002637aba2750 Exception <a 'sun/nio/fs/WindowsException'{0x000000072dd8e650}> (0x000000072dd8e650) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 23.325 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 23.342 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 23.409 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.410 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 24.420 Executing VM operation: Cleanup
Event: 24.421 Executing VM operation: Cleanup done
Event: 24.466 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 24.480 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 25.220 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 25.236 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 25.821 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 25.835 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 26.315 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 26.328 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 26.381 Executing VM operation: G1PauseRemark
Event: 26.399 Executing VM operation: G1PauseRemark done
Event: 26.435 Executing VM operation: G1PauseCleanup
Event: 26.436 Executing VM operation: G1PauseCleanup done
Event: 28.457 Executing VM operation: Cleanup
Event: 28.457 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635ef88110
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635ef88710
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635ef89410
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635ef89810
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635ef8a010
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635ef8af10
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635ef8bc10
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efa5910
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efaa090
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efac990
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efacf90
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efb2210
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efb3a90
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efb4e90
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efb6710
Event: 26.398 Thread 0x0000026357e10e70 flushing osr nmethod 0x000002635efb7490
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635efb8c10
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635f004d90
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635f006490
Event: 26.398 Thread 0x0000026357e10e70 flushing  nmethod 0x000002635f007510

Events (20 events):
Event: 23.128 Thread 0x000002637d1e4860 Thread added: 0x000002637d1e4ef0
Event: 23.129 Thread 0x000002637d1e4ef0 Thread added: 0x000002637d1e1a70
Event: 23.130 Thread 0x000002637d1e1a70 Thread added: 0x000002637d1e6fc0
Event: 23.131 Thread 0x000002637d1e6fc0 Thread added: 0x000002637d1e7ce0
Event: 23.132 Thread 0x000002637d1e7ce0 Thread added: 0x000002637d1e8a00
Event: 23.133 Thread 0x000002637d1e8a00 Thread added: 0x000002637d1e9090
Event: 23.134 Thread 0x000002637d1e9090 Thread added: 0x000002637d1e5c10
Event: 23.136 Thread 0x000002637d1e5c10 Thread added: 0x000002637d1e6930
Event: 23.138 Thread 0x000002637d1e6930 Thread added: 0x000002637d1e62a0
Event: 24.252 Thread 0x0000026375d7af60 Thread exited: 0x0000026375d7af60
Event: 24.275 Thread 0x0000026375cd8020 Thread exited: 0x0000026375cd8020
Event: 24.299 Thread 0x0000026375cc9690 Thread exited: 0x0000026375cc9690
Event: 25.073 Thread 0x000002637d90bb00 Thread exited: 0x000002637d90bb00
Event: 25.077 Thread 0x000002637d877690 Thread exited: 0x000002637d877690
Event: 25.171 Thread 0x000002637dd0e520 Thread exited: 0x000002637dd0e520
Event: 25.505 Thread 0x000002637c351830 Thread exited: 0x000002637c351830
Event: 25.505 Thread 0x000002637b7a84b0 Thread exited: 0x000002637b7a84b0
Event: 25.571 Thread 0x0000026375d8c7a0 Thread exited: 0x0000026375d8c7a0
Event: 25.879 Thread 0x0000026375d7a8c0 Thread exited: 0x0000026375d7a8c0
Event: 27.535 Thread 0x000002637d1ddf60 Thread exited: 0x000002637d1ddf60


Dynamic libraries:
0x00007ff709470000 - 0x00007ff70947e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff830020000 - 0x00007ff830285000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff82f960000 - 0x00007ff82fa29000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff82d700000 - 0x00007ff82dae8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff82d250000 - 0x00007ff82d39b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8221a0000 - 0x00007ff8221b8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff82e8a0000 - 0x00007ff82ea6a000 	C:\WINDOWS\System32\USER32.dll
0x00007ff820da0000 - 0x00007ff820dbe000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff814290000 - 0x00007ff81452a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ff82df30000 - 0x00007ff82dfd9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff82d220000 - 0x00007ff82d247000 	C:\WINDOWS\System32\win32u.dll
0x00007ff82e840000 - 0x00007ff82e86b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff82d5c0000 - 0x00007ff82d6f7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff82d170000 - 0x00007ff82d213000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff82ffa0000 - 0x00007ff82ffd0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff823760000 - 0x00007ff82376c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff820580000 - 0x00007ff82060d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffbf030000 - 0x00007fffbfdc0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff82e030000 - 0x00007ff82e0e3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff82f8b0000 - 0x00007ff82f956000 	C:\WINDOWS\System32\sechost.dll
0x00007ff82f100000 - 0x00007ff82f215000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff82f680000 - 0x00007ff82f6f4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff827650000 - 0x00007ff827685000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff82bd50000 - 0x00007ff82bdae000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8228e0000 - 0x00007ff8228eb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff82bd30000 - 0x00007ff82bd44000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff82bf70000 - 0x00007ff82bf8b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8235f0000 - 0x00007ff8235fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff82aab0000 - 0x00007ff82acf1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff82fb50000 - 0x00007ff82fed5000 	C:\WINDOWS\System32\combase.dll
0x00007ff82f010000 - 0x00007ff82f0f1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff811c90000 - 0x00007ff811cc9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff82d3a0000 - 0x00007ff82d439000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff822340000 - 0x00007ff82234f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8209d0000 - 0x00007ff8209ef000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff82e0f0000 - 0x00007ff82e832000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff82dc40000 - 0x00007ff82ddb4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff82add0000 - 0x00007ff82b628000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff82f780000 - 0x00007ff82f871000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff82f5b0000 - 0x00007ff82f61a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff82d080000 - 0x00007ff82d0af000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff821430000 - 0x00007ff821440000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff822700000 - 0x00007ff82281e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff82c560000 - 0x00007ff82c5ca000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff820980000 - 0x00007ff820996000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff820960000 - 0x00007ff820978000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff820d90000 - 0x00007ff820da0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff810bb0000 - 0x00007ff810bf5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff82f400000 - 0x00007ff82f59e000 	C:\WINDOWS\System32\ole32.dll
0x00007ff82c940000 - 0x00007ff82c95b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff82bf90000 - 0x00007ff82bfca000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff82c600000 - 0x00007ff82c62b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff82d050000 - 0x00007ff82d076000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff82c7a0000 - 0x00007ff82c7ac000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff82b990000 - 0x00007ff82b9c3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff82fb40000 - 0x00007ff82fb4a000 	C:\WINDOWS\System32\NSI.dll
0x00007ff822650000 - 0x00007ff822699000 	C:\Users\<USER>\AppData\Local\Temp\jna-2071759690\jna5858489855972179816.dll
0x00007ff82fee0000 - 0x00007ff82fee8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff8228f0000 - 0x00007ff82290f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff821ef0000 - 0x00007ff821f15000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff82c130000 - 0x00007ff82c166000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-2071759690

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -Xlog:disable --add-modules=javafx.controls,javafx.fxml --module-path=./javafx-sdk-11.0.2/lib -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\75ec01de8fef35e90c2497b506034143\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\75ec01de8fef35e90c2497b506034143\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-500fb749714c7cd36049c5eebcc43b26-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 5                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 20                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\75ec01de8fef35e90c2497b506034143\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4219469824                                {product} {ergonomic}
   size_t MaxNewSize                               = 2531262464                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4219469824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Incredibuild;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\composer;C:\Program Files\nodejs\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Python313\Scripts\;C:\Python313\;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\flutter\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\xampp\php;C:\Users\<USER>\AppData\Local\ComposerSetup\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Python\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\flutter\bin;
USERNAME=win 10-11
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 2 days 15:59 hours
Hyper-V role detected

CPU: total 28 (initial active 28) (14 cores per cpu, 2 threads per core) family 6 model 183 stepping 1 microcode 0x125, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 1
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 2
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 3
  Max Mhz: 2100, Current Mhz: 1453, Mhz Limit: 2100
Processor Information for processor 4
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 5
  Max Mhz: 2100, Current Mhz: 1453, Mhz Limit: 2100
Processor Information for processor 6
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 7
  Max Mhz: 2100, Current Mhz: 1453, Mhz Limit: 2100
Processor Information for processor 8
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 9
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 10
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 11
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 12
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 13
  Max Mhz: 2100, Current Mhz: 1453, Mhz Limit: 2100
Processor Information for processor 14
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 15
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 16
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 17
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 18
  Max Mhz: 2100, Current Mhz: 1540, Mhz Limit: 2100
Processor Information for processor 19
  Max Mhz: 2100, Current Mhz: 1540, Mhz Limit: 2100
Processor Information for processor 20
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 21
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 22
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 23
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 24
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 25
  Max Mhz: 2100, Current Mhz: 1540, Mhz Limit: 2100
Processor Information for processor 26
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 27
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100

Memory: 4k page, system-wide physical 16091M (2657M free)
TotalPageFile size 47939M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 1147M, peak: 1187M
current process commit charge ("private bytes"): 1126M, peak: 1138M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
