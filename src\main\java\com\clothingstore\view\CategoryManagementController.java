package com.clothingstore.view;

import java.net.URL;
import java.sql.SQLException;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

import com.clothingstore.dao.CategoryDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Category;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Dialog;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.Spinner;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.stage.Stage;

/**
 * Controller for Category Management dialog
 */
public class CategoryManagementController implements Initializable {

    @FXML
    private Button btnAddCategory;
    @FXML
    private Button btnRefresh;
    @FXML
    private Button btnReorderCategories;
    @FXML
    private Button btnEditSelected;
    @FXML
    private Button btnDeleteSelected;
    @FXML
    private Button btnClose;

    @FXML
    private Label lblTotalCategories;
    @FXML
    private Label lblActiveCategories;
    @FXML
    private Label lblTotalProducts;
    @FXML
    private Label lblSelectedCategory;

    @FXML
    private TableView<Category> tblCategories;
    @FXML
    private TableColumn<Category, Integer> colOrder;
    @FXML
    private TableColumn<Category, String> colName;
    @FXML
    private TableColumn<Category, String> colDescription;
    @FXML
    private TableColumn<Category, String> colProductCount;
    @FXML
    private TableColumn<Category, String> colActive;
    @FXML
    private TableColumn<Category, String> colCreated;
    @FXML
    private TableColumn<Category, String> colActions;

    @FXML
    private MenuItem menuEdit;
    @FXML
    private MenuItem menuToggleActive;
    @FXML
    private MenuItem menuMoveUp;
    @FXML
    private MenuItem menuMoveDown;
    @FXML
    private MenuItem menuDelete;

    private CategoryDAO categoryDAO;
    private ProductDAO productDAO;
    private ObservableList<Category> categories;

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            categoryDAO = CategoryDAO.getInstance();
            productDAO = ProductDAO.getInstance();
            categories = FXCollections.observableArrayList();

            setupTableColumns();
            setupEventHandlers();
            loadCategories();

        } catch (Exception e) {
            AlertUtil.showError("Initialization Error", "Failed to initialize Category Management: " + e.getMessage());
        }
    }

    private void setupTableColumns() {
        // Display Order
        colOrder.setCellValueFactory(new PropertyValueFactory<>("displayOrder"));

        // Name
        colName.setCellValueFactory(new PropertyValueFactory<>("name"));

        // Description
        colDescription.setCellValueFactory(cellData -> {
            String description = cellData.getValue().getDescription();
            return new SimpleStringProperty(description != null ? description : "");
        });

        // Product Count
        colProductCount.setCellValueFactory(cellData -> {
            try {
                Category category = cellData.getValue();
                int count = productDAO.getProductCountByCategory(category.getName());
                return new SimpleStringProperty(String.valueOf(count));
            } catch (SQLException e) {
                return new SimpleStringProperty("Error");
            }
        });

        // Active Status
        colActive.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().isActive() ? "Yes" : "No"));

        // Created Date
        colCreated.setCellValueFactory(cellData -> {
            if (cellData.getValue().getCreatedAt() != null) {
                return new SimpleStringProperty(cellData.getValue().getCreatedAt().format(dateFormatter));
            }
            return new SimpleStringProperty("");
        });

        // Actions Column
        colActions.setCellFactory(col -> new TableCell<Category, String>() {
            private final Button editBtn = new Button("Edit");
            private final Button deleteBtn = new Button("Delete");

            {
                editBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 10px;");
                deleteBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 10px;");

                editBtn.setOnAction(e -> {
                    Category category = getTableView().getItems().get(getIndex());
                    editCategory(category);
                });

                deleteBtn.setOnAction(e -> {
                    Category category = getTableView().getItems().get(getIndex());
                    deleteCategory(category);
                });
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    HBox buttons = new HBox(5, editBtn, deleteBtn);
                    setGraphic(buttons);
                }
            }
        });

        tblCategories.setItems(categories);
    }

    private void setupEventHandlers() {
        // Table selection
        tblCategories.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            boolean hasSelection = newSelection != null;
            btnEditSelected.setDisable(!hasSelection);
            btnDeleteSelected.setDisable(!hasSelection);

            if (hasSelection) {
                lblSelectedCategory.setText(newSelection.getName());
            } else {
                lblSelectedCategory.setText("None");
            }
        });
    }

    @FXML
    private void handleAddCategory() {
        showCategoryDialog(null);
    }

    @FXML
    private void handleEditCategory() {
        Category selected = tblCategories.getSelectionModel().getSelectedItem();
        if (selected != null) {
            editCategory(selected);
        }
    }

    @FXML
    private void handleDeleteCategory() {
        Category selected = tblCategories.getSelectionModel().getSelectedItem();
        if (selected != null) {
            deleteCategory(selected);
        }
    }

    @FXML
    private void handleToggleActive() {
        Category selected = tblCategories.getSelectionModel().getSelectedItem();
        if (selected != null) {
            try {
                selected.setActive(!selected.isActive());
                categoryDAO.save(selected);
                loadCategories();
                AlertUtil.showInfo("Success", "Category status updated successfully!");
            } catch (SQLException e) {
                AlertUtil.showError("Database Error", "Failed to update category status: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleMoveUp() {
        Category selected = tblCategories.getSelectionModel().getSelectedItem();
        if (selected != null && selected.getDisplayOrder() > 1) {
            moveCategory(selected, -1);
        }
    }

    @FXML
    private void handleMoveDown() {
        Category selected = tblCategories.getSelectionModel().getSelectedItem();
        if (selected != null) {
            moveCategory(selected, 1);
        }
    }

    @FXML
    private void handleReorderCategories() {
        // This could open a separate dialog for drag-and-drop reordering
        AlertUtil.showInfo("Feature Coming Soon", "Drag-and-drop reordering will be available in a future update.\nFor now, use the Move Up/Down options in the context menu.");
    }

    @FXML
    private void handleRefresh() {
        loadCategories();
    }

    @FXML
    private void handleClose() {
        Stage stage = (Stage) btnClose.getScene().getWindow();
        stage.close();
    }

    private void loadCategories() {
        try {
            List<Category> categoryList = categoryDAO.findAllIncludingInactive();
            categories.setAll(categoryList);
            updateStatistics();
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load categories: " + e.getMessage());
        }
    }

    private void updateStatistics() {
        try {
            int totalCategories = categories.size();
            int activeCategories = (int) categories.stream().filter(Category::isActive).count();
            int totalProducts = productDAO.getTotalProductCount();

            lblTotalCategories.setText("Total Categories: " + totalCategories);
            lblActiveCategories.setText("Active: " + activeCategories);
            lblTotalProducts.setText("Total Products: " + totalProducts);

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to update statistics: " + e.getMessage());
        }
    }

    private void showCategoryDialog(Category category) {
        try {
            Dialog<Category> dialog = new Dialog<>();
            dialog.setTitle(category == null ? "Add New Category" : "Edit Category");
            dialog.setHeaderText("Enter category details");

            // Create form
            GridPane grid = new GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));

            TextField nameField = new TextField();
            TextField descriptionField = new TextField();
            Spinner<Integer> orderSpinner = new Spinner<>(1, 100, 1);
            CheckBox activeCheckBox = new CheckBox();

            if (category != null) {
                nameField.setText(category.getName());
                descriptionField.setText(category.getDescription());
                orderSpinner.getValueFactory().setValue(category.getDisplayOrder());
                activeCheckBox.setSelected(category.isActive());
            } else {
                activeCheckBox.setSelected(true);
                orderSpinner.getValueFactory().setValue(categories.size() + 1);
            }

            grid.add(new Label("Name:"), 0, 0);
            grid.add(nameField, 1, 0);
            grid.add(new Label("Description:"), 0, 1);
            grid.add(descriptionField, 1, 1);
            grid.add(new Label("Display Order:"), 0, 2);
            grid.add(orderSpinner, 1, 2);
            grid.add(new Label("Active:"), 0, 3);
            grid.add(activeCheckBox, 1, 3);

            dialog.getDialogPane().setContent(grid);

            ButtonType saveButtonType = new ButtonType("Save", ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);

            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == saveButtonType) {
                    if (nameField.getText().trim().isEmpty()) {
                        AlertUtil.showError("Validation Error", "Category name is required.");
                        return null;
                    }

                    Category result = category != null ? category : new Category();
                    result.setName(nameField.getText().trim());
                    result.setDescription(descriptionField.getText().trim());
                    result.setDisplayOrder(orderSpinner.getValue());
                    result.setActive(activeCheckBox.isSelected());
                    return result;
                }
                return null;
            });

            Optional<Category> result = dialog.showAndWait();
            result.ifPresent(cat -> {
                try {
                    categoryDAO.save(cat);
                    loadCategories();
                    AlertUtil.showSuccess("Success", "Category saved successfully!");
                } catch (SQLException e) {
                    AlertUtil.showError("Database Error", "Failed to save category: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open category dialog: " + e.getMessage());
        }
    }

    private void editCategory(Category category) {
        showCategoryDialog(category);
    }

    private void deleteCategory(Category category) {
        try {
            // Check if category is in use
            int productCount = productDAO.getProductCountByCategory(category.getName());
            if (productCount > 0) {
                AlertUtil.showError("Cannot Delete Category",
                        "This category is currently used by " + productCount + " product(s).\n"
                        + "Please move or delete those products first, or deactivate the category instead.");
                return;
            }

            if (AlertUtil.showConfirmation("Delete Category",
                    "Are you sure you want to delete the category '" + category.getName() + "'?\n"
                    + "This action cannot be undone.")) {

                categoryDAO.delete(category.getId());
                loadCategories();
                AlertUtil.showSuccess("Success", "Category deleted successfully!");
            }

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to delete category: " + e.getMessage());
        }
    }

    private void moveCategory(Category category, int direction) {
        try {
            int newOrder = category.getDisplayOrder() + direction;
            if (newOrder < 1) {
                return;
            }

            category.setDisplayOrder(newOrder);
            categoryDAO.save(category);
            loadCategories();

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to move category: " + e.getMessage());
        }
    }
}
