import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.RefundItem;
import com.clothingstore.model.RefundResult;
import com.clothingstore.service.RefundService;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;

public class TestPartialRefund {
    public static void main(String[] args) {
        try {
            System.out.println("=== Testing Partial Refund Functionality ===");
            
            // Initialize services
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            RefundService refundService = RefundService.getInstance();
            
            // Find a completed transaction to test with
            List<Transaction> completedTransactions = transactionDAO.findByStatus("COMPLETED");
            if (completedTransactions.isEmpty()) {
                System.out.println("No COMPLETED transactions found for testing");
                return;
            }
            
            Transaction testTransaction = completedTransactions.get(0);
            System.out.println("Testing with transaction: " + testTransaction.getTransactionNumber());
            System.out.println("Transaction status: " + testTransaction.getStatus());
            System.out.println("Transaction total: $" + testTransaction.getTotalAmount());
            System.out.println("Number of items: " + testTransaction.getItems().size());
            
            // Create refund items for partial refund (refund first item only)
            List<RefundItem> refundItems = new ArrayList<>();
            if (!testTransaction.getItems().isEmpty()) {
                RefundItem refundItem = new RefundItem(testTransaction.getItems().get(0));
                refundItem.setSelected(true);
                refundItem.setRefundQuantity(1); // Refund only 1 quantity
                refundItem.setReason("Test partial refund");
                refundItems.add(refundItem);
                
                System.out.println("Refund item: " + refundItem.getProductName());
                System.out.println("Original quantity: " + refundItem.getOriginalQuantity());
                System.out.println("Refund quantity: " + refundItem.getRefundQuantity());
                System.out.println("Refund amount: $" + refundItem.getRefundAmount());
            }
            
            // Test partial refund processing
            System.out.println("\n=== Processing Partial Refund ===");
            RefundResult result = refundService.processPartialRefund(
                testTransaction, 
                refundItems, 
                "Test partial refund functionality", 
                "Test User"
            );
            
            if (result.isSuccess()) {
                System.out.println("✓ Partial refund processed successfully!");
                System.out.println("Message: " + result.getMessage());
                if (result.getRefundTransaction() != null) {
                    System.out.println("Refund transaction created: " + result.getRefundTransaction().getTransactionNumber());
                }
                if (result.hasWarnings()) {
                    System.out.println("Warnings: " + result.getWarnings());
                }
            } else {
                System.out.println("✗ Partial refund failed!");
                System.out.println("Error: " + result.getMessage());
                if (result.getErrorCode() != null) {
                    System.out.println("Error code: " + result.getErrorCode());
                }
            }
            
            // Check updated transaction status
            Transaction updatedTransaction = transactionDAO.findById(testTransaction.getId()).orElse(null);
            if (updatedTransaction != null) {
                System.out.println("\n=== Updated Transaction Status ===");
                System.out.println("Status: " + updatedTransaction.getStatus());
                System.out.println("Amount paid: $" + updatedTransaction.getAmountPaid());
                System.out.println("Remaining balance: $" + updatedTransaction.getRemainingBalance());
                System.out.println("Notes: " + updatedTransaction.getNotes());
            }
            
        } catch (Exception e) {
            System.err.println("Error testing partial refund: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
