package com.clothingstore.util;

import java.sql.SQLException;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.service.DatabaseResetService;

/**
 * Simple utility class for resetting database data Provides easy-to-use methods
 * for data management
 */
public class DataResetUtility {

    /**
     * Reset all data to empty state Removes all records but keeps database
     * schema intact
     */
    public static void resetToEmpty() {
        System.out.println("===============================================");
        System.out.println("RESETTING DATABASE TO EMPTY STATE");
        System.out.println("===============================================\n");

        try {
            // Method 1: Using DatabaseManager
            DatabaseManager.getInstance().resetAllData();

            System.out.println("SUCCESS: Database reset to empty state completed");
            System.out.println("All data has been removed from the database");
            System.out.println("Database schema remains intact");

        } catch (SQLException e) {
            System.err.println("ERROR: Failed to reset database: " + e.getMessage());
            System.err.println("SQL State: " + e.getSQLState());
            System.err.println("Error Code: " + e.getErrorCode());
            // Log the full stack trace for debugging
            java.util.logging.Logger.getLogger(DataResetUtility.class.getName())
                    .log(java.util.logging.Level.SEVERE, "Database reset failed", e);
        }
    }

    /**
     * Reset data and populate with demo data Removes all existing data and adds
     * sample records
     */
    public static void resetWithDemoData() {
        System.out.println("===============================================");
        System.out.println("RESETTING DATABASE WITH DEMO DATA");
        System.out.println("===============================================\n");

        try {
            // Method 1: Using DatabaseResetService (more comprehensive)
            DatabaseResetService resetService = DatabaseResetService.getInstance();
            resetService.resetWithDemoData();

            System.out.println("SUCCESS: Database reset with demo data completed");
            System.out.println("Sample products, customers, and transactions created");

        } catch (SQLException e) {
            System.err.println("ERROR: Failed to reset with demo data: " + e.getMessage());
            System.err.println("SQL State: " + e.getSQLState());
            System.err.println("Error Code: " + e.getErrorCode());
            // Log the full stack trace for debugging
            java.util.logging.Logger.getLogger(DataResetUtility.class.getName())
                    .log(java.util.logging.Level.SEVERE, "Database reset with demo data failed", e);
        }
    }

    /**
     * Reset using DatabaseManager with sample data Alternative method using
     * DatabaseManager
     */
    public static void resetWithSampleData() {
        System.out.println("===============================================");
        System.out.println("RESETTING DATABASE WITH SAMPLE DATA");
        System.out.println("===============================================\n");

        try {
            // Method 2: Using DatabaseManager
            DatabaseManager.getInstance().resetWithSampleData();

            System.out.println("SUCCESS: Database reset with sample data completed");

        } catch (SQLException e) {
            System.err.println("ERROR: Failed to reset with sample data: " + e.getMessage());
            System.err.println("SQL State: " + e.getSQLState());
            System.err.println("Error Code: " + e.getErrorCode());
            // Log the full stack trace for debugging
            java.util.logging.Logger.getLogger(DataResetUtility.class.getName())
                    .log(java.util.logging.Level.SEVERE, "Database reset with sample data failed", e);
        }
    }

    /**
     * Show current database statistics
     */
    public static void showDatabaseStats() {
        System.out.println("===============================================");
        System.out.println("CURRENT DATABASE STATISTICS");
        System.out.println("===============================================\n");

        try {
            DatabaseResetService resetService = DatabaseResetService.getInstance();
            DatabaseResetService.DatabaseStats stats = resetService.getDatabaseStats();

            System.out.println("Database Contents:");
            System.out.println("   Products: " + stats.getProductCount());
            System.out.println("   Customers: " + stats.getCustomerCount());
            System.out.println("   Transactions: " + stats.getTransactionCount());
            System.out.println("   Is Empty: " + stats.isEmpty());

        } catch (SQLException e) {
            System.err.println("ERROR: Failed to get database stats: " + e.getMessage());
        }
    }

    /**
     * Main method for command-line usage
     */
    public static void main(String[] args) {
        if (args.length == 0) {
            showUsage();
            return;
        }

        String command = args[0].toLowerCase();

        switch (command) {
            case "empty":
            case "reset":
                resetToEmpty();
                break;

            case "demo":
            case "sample":
                resetWithDemoData();
                break;

            case "stats":
            case "status":
                showDatabaseStats();
                break;

            default:
                System.err.println("Unknown command: " + command);
                showUsage();
        }
    }

    private static void showUsage() {
        System.out.println("Data Reset Utility - Usage:");
        System.out.println("===========================");
        System.out.println("java com.clothingstore.util.DataResetUtility <command>");
        System.out.println();
        System.out.println("Commands:");
        System.out.println("  empty   - Reset database to empty state (remove all data)");
        System.out.println("  demo    - Reset database with demo data");
        System.out.println("  stats   - Show current database statistics");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java com.clothingstore.util.DataResetUtility empty");
        System.out.println("  java com.clothingstore.util.DataResetUtility demo");
        System.out.println("  java com.clothingstore.util.DataResetUtility stats");
    }
}
