package com.clothingstore.service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.Optional;
import java.util.logging.Logger;

import com.clothingstore.dao.PaymentHistoryDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.OutstandingBalanceUpdateResult;
import com.clothingstore.model.PaymentHistory;
import com.clothingstore.model.Transaction;

/**
 * Service for handling Outstanding Balance updates when refunds are processed
 * Ensures proper integration between refund processing and outstanding balance
 * tracking
 */
public class OutstandingBalanceRefundService {

    private static final Logger LOGGER = Logger.getLogger(OutstandingBalanceRefundService.class.getName());
    private static OutstandingBalanceRefundService instance;

    private final TransactionDAO transactionDAO;
    private final PaymentHistoryDAO paymentHistoryDAO;
    private final PaymentHistoryService paymentHistoryService;

    private OutstandingBalanceRefundService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.paymentHistoryDAO = PaymentHistoryDAO.getInstance();
        this.paymentHistoryService = PaymentHistoryService.getInstance();
    }

    public static synchronized OutstandingBalanceRefundService getInstance() {
        if (instance == null) {
            instance = new OutstandingBalanceRefundService();
        }
        return instance;
    }

    /**
     * Process outstanding balance updates for a full refund
     */
    public OutstandingBalanceUpdateResult processFullRefundBalanceUpdate(Long transactionId,
            BigDecimal refundAmount,
            String paymentMethod,
            String cashierName,
            String refundReason) {
        try {
            // Get transaction details
            Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
            if (!transactionOpt.isPresent()) {
                return new OutstandingBalanceUpdateResult(false, "Transaction not found with ID: " + transactionId);
            }

            Transaction transaction = transactionOpt.get();

            // Check if transaction has outstanding balance
            boolean hadOutstandingBalance = transaction.hasOutstandingBalance();
            BigDecimal previousRemainingBalance = transaction.getRemainingBalance();

            // Record refund in payment history (this will update transaction status)
            String refundNotes = "Full refund processed: " + refundReason;
            PaymentHistory refundRecord = paymentHistoryService.recordRefund(
                    transactionId, refundAmount, paymentMethod, cashierName, refundNotes);

            // Get updated transaction after refund processing
            Optional<Transaction> updatedTransactionOpt = transactionDAO.findById(transactionId);
            if (!updatedTransactionOpt.isPresent()) {
                return new OutstandingBalanceUpdateResult(false, "Failed to retrieve updated transaction");
            }

            Transaction updatedTransaction = updatedTransactionOpt.get();
            BigDecimal newRemainingBalance = updatedTransaction.getRemainingBalance();

            // Create result with balance update information
            OutstandingBalanceUpdateResult result = new OutstandingBalanceUpdateResult(true, "Full refund processed successfully");
            result.setTransactionId(transactionId);
            result.setRefundAmount(refundAmount);
            result.setPreviousRemainingBalance(previousRemainingBalance);
            result.setNewRemainingBalance(newRemainingBalance);
            result.setHadOutstandingBalance(hadOutstandingBalance);
            result.setHasOutstandingBalance(updatedTransaction.hasOutstandingBalance());
            result.setPaymentHistoryId(refundRecord.getId());

            LOGGER.info("Full refund balance update completed for transaction " + transactionId
                    + ". Previous balance: " + previousRemainingBalance
                    + ", New balance: " + newRemainingBalance);

            return result;

        } catch (Exception e) {
            LOGGER.severe("Error processing full refund balance update: " + e.getMessage());
            return new OutstandingBalanceUpdateResult(false, "Error processing refund: " + e.getMessage());
        }
    }

    /**
     * Process outstanding balance updates for a partial refund
     */
    public OutstandingBalanceUpdateResult processPartialRefundBalanceUpdate(Long transactionId,
            BigDecimal refundAmount,
            String paymentMethod,
            String cashierName,
            String refundReason) {
        try {
            // Get transaction details
            Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
            if (!transactionOpt.isPresent()) {
                return new OutstandingBalanceUpdateResult(false, "Transaction not found with ID: " + transactionId);
            }

            Transaction transaction = transactionOpt.get();

            // Check if transaction has outstanding balance
            boolean hadOutstandingBalance = transaction.hasOutstandingBalance();
            BigDecimal previousRemainingBalance = transaction.getRemainingBalance();

            // Validate refund amount doesn't exceed what was paid
            BigDecimal totalPaid = paymentHistoryDAO.getTotalAmountPaid(transactionId);
            BigDecimal totalRefunded = paymentHistoryDAO.getTotalAmountRefunded(transactionId);
            BigDecimal availableForRefund;

            // Special handling for COMPLETED transactions without payment history
            if ("COMPLETED".equals(transaction.getStatus()) && totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                // For COMPLETED transactions without payment history, allow refunds up to transaction total
                availableForRefund = transaction.getTotalAmount().subtract(totalRefunded);
                LOGGER.info("COMPLETED transaction " + transactionId + " has no payment history. "
                        + "Using transaction total (" + transaction.getTotalAmount() + ") as available for refund.");
            } else {
                // Standard calculation for transactions with payment history
                availableForRefund = totalPaid.subtract(totalRefunded);
            }

            if (refundAmount.compareTo(availableForRefund) > 0) {
                return new OutstandingBalanceUpdateResult(false,
                        "Refund amount (" + refundAmount + ") exceeds available amount for refund (" + availableForRefund + ")");
            }

            // Record refund in payment history (this will update transaction status and balances)
            String refundNotes = "Partial refund processed: " + refundReason;
            PaymentHistory refundRecord = paymentHistoryService.recordRefund(
                    transactionId, refundAmount, paymentMethod, cashierName, refundNotes);

            // Get updated transaction after refund processing
            Optional<Transaction> updatedTransactionOpt = transactionDAO.findById(transactionId);
            if (!updatedTransactionOpt.isPresent()) {
                return new OutstandingBalanceUpdateResult(false, "Failed to retrieve updated transaction");
            }

            Transaction updatedTransaction = updatedTransactionOpt.get();
            BigDecimal newRemainingBalance = updatedTransaction.getRemainingBalance();

            // Create result with balance update information
            OutstandingBalanceUpdateResult result = new OutstandingBalanceUpdateResult(true, "Partial refund processed successfully");
            result.setTransactionId(transactionId);
            result.setRefundAmount(refundAmount);
            result.setPreviousRemainingBalance(previousRemainingBalance);
            result.setNewRemainingBalance(newRemainingBalance);
            result.setHadOutstandingBalance(hadOutstandingBalance);
            result.setHasOutstandingBalance(updatedTransaction.hasOutstandingBalance());
            result.setPaymentHistoryId(refundRecord.getId());

            // Check if transaction moved from no outstanding balance to having one (refund created new balance)
            if (!hadOutstandingBalance && updatedTransaction.hasOutstandingBalance()) {
                result.addWarning("Refund created new outstanding balance for this transaction");
            }

            LOGGER.info("Partial refund balance update completed for transaction " + transactionId
                    + ". Previous balance: " + previousRemainingBalance
                    + ", New balance: " + newRemainingBalance);

            return result;

        } catch (Exception e) {
            LOGGER.severe("Error processing partial refund balance update: " + e.getMessage());
            return new OutstandingBalanceUpdateResult(false, "Error processing partial refund: " + e.getMessage());
        }
    }

    /**
     * Check if a transaction will have outstanding balance after refund
     */
    public boolean willHaveOutstandingBalanceAfterRefund(Long transactionId, BigDecimal refundAmount) throws SQLException {
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            return false;
        }

        Transaction transaction = transactionOpt.get();
        BigDecimal totalPaid = paymentHistoryDAO.getTotalAmountPaid(transactionId);
        BigDecimal totalRefunded = paymentHistoryDAO.getTotalAmountRefunded(transactionId);
        BigDecimal newTotalRefunded = totalRefunded.add(refundAmount);
        BigDecimal newRunningBalance = totalPaid.subtract(newTotalRefunded);
        BigDecimal newRemainingBalance = transaction.getTotalAmount().subtract(newRunningBalance);

        return newRemainingBalance.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Get current outstanding balance information for a transaction
     */
    public OutstandingBalanceInfo getOutstandingBalanceInfo(Long transactionId) throws SQLException {
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            return null;
        }

        Transaction transaction = transactionOpt.get();
        BigDecimal totalPaid = paymentHistoryDAO.getTotalAmountPaid(transactionId);
        BigDecimal totalRefunded = paymentHistoryDAO.getTotalAmountRefunded(transactionId);
        BigDecimal remainingBalance = transaction.getRemainingBalance();

        return new OutstandingBalanceInfo(
                transactionId,
                transaction.getTotalAmount(),
                totalPaid,
                totalRefunded,
                remainingBalance,
                transaction.hasOutstandingBalance(),
                transaction.getStatus()
        );
    }

    /**
     * Inner class for outstanding balance information
     */
    public static class OutstandingBalanceInfo {

        private final Long transactionId;
        private final BigDecimal totalAmount;
        private final BigDecimal totalPaid;
        private final BigDecimal totalRefunded;
        private final BigDecimal remainingBalance;
        private final boolean hasOutstandingBalance;
        private final String transactionStatus;

        public OutstandingBalanceInfo(Long transactionId, BigDecimal totalAmount, BigDecimal totalPaid,
                BigDecimal totalRefunded, BigDecimal remainingBalance,
                boolean hasOutstandingBalance, String transactionStatus) {
            this.transactionId = transactionId;
            this.totalAmount = totalAmount;
            this.totalPaid = totalPaid;
            this.totalRefunded = totalRefunded;
            this.remainingBalance = remainingBalance;
            this.hasOutstandingBalance = hasOutstandingBalance;
            this.transactionStatus = transactionStatus;
        }

        // Getters
        public Long getTransactionId() {
            return transactionId;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public BigDecimal getTotalPaid() {
            return totalPaid;
        }

        public BigDecimal getTotalRefunded() {
            return totalRefunded;
        }

        public BigDecimal getRemainingBalance() {
            return remainingBalance;
        }

        public boolean hasOutstandingBalance() {
            return hasOutstandingBalance;
        }

        public String getTransactionStatus() {
            return transactionStatus;
        }
    }
}
