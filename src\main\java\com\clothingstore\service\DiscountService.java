package com.clothingstore.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Discount;
import com.clothingstore.model.DiscountType;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

/**
 * Service for handling discount calculations and applications
 */
public class DiscountService {

    private static final Logger LOGGER = Logger.getLogger(DiscountService.class.getName());
    private static DiscountService instance;

    private final List<Discount> availableDiscounts;

    private DiscountService() {
        this.availableDiscounts = new ArrayList<>();
        initializeDefaultDiscounts();
    }

    public static synchronized DiscountService getInstance() {
        if (instance == null) {
            instance = new DiscountService();
        }
        return instance;
    }

    /**
     * Initialize some default discounts for demonstration
     */
    private void initializeDefaultDiscounts() {
        // 10% off everything
        Discount tenPercent = new Discount("10% Off Everything", DiscountType.PERCENTAGE, new BigDecimal("10"));
        tenPercent.setDescription("10% discount on entire purchase");
        tenPercent.setStackable(false);
        availableDiscounts.add(tenPercent);

        // $5 off $50 or more
        Discount fiveDollarsOff = new Discount("$5 Off $50+", DiscountType.FIXED_AMOUNT, new BigDecimal("5"));
        fiveDollarsOff.setDescription("$5 off purchases of $50 or more");
        fiveDollarsOff.setMinimumAmount(new BigDecimal("50"));
        fiveDollarsOff.setStackable(true);
        availableDiscounts.add(fiveDollarsOff);

        // Buy 2 Get 1 Free
        Discount buy2Get1 = new Discount("Buy 2 Get 1 Free", DiscountType.BUY_X_GET_Y, new BigDecimal("100"));
        buy2Get1.setDescription("Buy 2 items, get 1 free");
        buy2Get1.setBuyQuantity(2);
        buy2Get1.setGetQuantity(1);
        buy2Get1.setStackable(false);
        availableDiscounts.add(buy2Get1);

        // Employee discount
        Discount employeeDiscount = new Discount("Employee Discount", DiscountType.EMPLOYEE, new BigDecimal("20"));
        employeeDiscount.setDescription("20% employee discount");
        employeeDiscount.setCustomerGroups("EMPLOYEE");
        employeeDiscount.setStackable(false);
        availableDiscounts.add(employeeDiscount);

        // Bulk discount for 5+ items
        Discount bulkDiscount = new Discount("Bulk Discount", DiscountType.BULK_DISCOUNT, new BigDecimal("15"));
        bulkDiscount.setDescription("15% off when buying 5 or more items");
        bulkDiscount.setMinimumQuantity(5);
        bulkDiscount.setStackable(true);
        availableDiscounts.add(bulkDiscount);
    }

    /**
     * Calculate applicable discounts for a transaction
     */
    public DiscountCalculationResult calculateDiscounts(Transaction transaction, Customer customer, String promoCode) {
        try {
            List<Discount> applicableDiscounts = findApplicableDiscounts(transaction, customer, promoCode);

            if (applicableDiscounts.isEmpty()) {
                return new DiscountCalculationResult(true, "No applicable discounts",
                        BigDecimal.ZERO, new ArrayList<>());
            }

            // Sort discounts by priority (non-stackable first, then by discount amount)
            applicableDiscounts.sort((d1, d2) -> {
                if (d1.isStackable() != d2.isStackable()) {
                    return d1.isStackable() ? 1 : -1; // Non-stackable first
                }
                return calculateDiscountAmount(transaction, d2).compareTo(calculateDiscountAmount(transaction, d1));
            });

            List<AppliedDiscount> appliedDiscounts = new ArrayList<>();
            BigDecimal totalDiscount = BigDecimal.ZERO;
            BigDecimal runningTotal = transaction.getSubtotal();

            for (Discount discount : applicableDiscounts) {
                BigDecimal discountAmount = calculateDiscountAmount(transaction, discount, runningTotal);

                if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                    AppliedDiscount applied = new AppliedDiscount(discount, discountAmount);
                    appliedDiscounts.add(applied);
                    totalDiscount = totalDiscount.add(discountAmount);

                    if (!discount.isStackable()) {
                        break; // Only apply one non-stackable discount
                    }

                    runningTotal = runningTotal.subtract(discountAmount);
                }
            }

            return new DiscountCalculationResult(true, "Discounts calculated successfully",
                    totalDiscount, appliedDiscounts);

        } catch (Exception e) {
            LOGGER.severe("Error calculating discounts: " + e.getMessage());
            return new DiscountCalculationResult(false, "Error calculating discounts: " + e.getMessage(),
                    BigDecimal.ZERO, new ArrayList<>());
        }
    }

    /**
     * Find discounts applicable to a transaction
     */
    private List<Discount> findApplicableDiscounts(Transaction transaction, Customer customer, String promoCode) {
        return availableDiscounts.stream()
                .filter(discount -> isDiscountApplicable(discount, transaction, customer, promoCode))
                .collect(Collectors.toList());
    }

    /**
     * Check if a discount is applicable to a transaction
     */
    private boolean isDiscountApplicable(Discount discount, Transaction transaction, Customer customer, String promoCode) {
        // Check if discount is valid
        if (!discount.isValid()) {
            return false;
        }

        // Check promo code if required
        if (discount.hasPromoCode()) {
            if (promoCode == null || !discount.getPromoCode().equalsIgnoreCase(promoCode.trim())) {
                return false;
            }
        }

        // Check minimum amount
        if (discount.getMinimumAmount() != null) {
            if (transaction.getSubtotal().compareTo(discount.getMinimumAmount()) < 0) {
                return false;
            }
        }

        // Check customer group requirements
        if (discount.getType().requiresCustomerValidation()) {
            if (customer == null) {
                return false;
            }

            if (discount.getCustomerGroups() != null) {
                // In a real implementation, you would check customer groups
                // For now, we'll assume all customers can use non-employee discounts
                if (discount.getType() == DiscountType.EMPLOYEE) {
                    // Would check if customer is an employee
                    return false; // For demo, no employees
                }
            }
        }

        // Check minimum quantity for bulk discounts
        if (discount.getType() == DiscountType.BULK_DISCOUNT) {
            int totalQuantity = transaction.getItems().stream()
                    .mapToInt(TransactionItem::getQuantity)
                    .sum();

            if (totalQuantity < discount.getMinimumQuantity()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Calculate discount amount for a transaction
     */
    private BigDecimal calculateDiscountAmount(Transaction transaction, Discount discount) {
        return calculateDiscountAmount(transaction, discount, transaction.getSubtotal());
    }

    /**
     * Calculate discount amount for a transaction with a specific base amount
     */
    private BigDecimal calculateDiscountAmount(Transaction transaction, Discount discount, BigDecimal baseAmount) {
        BigDecimal discountAmount = BigDecimal.ZERO;

        switch (discount.getType()) {
            case PERCENTAGE:
                discountAmount = baseAmount.multiply(discount.getValue())
                        .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

                // Apply maximum discount limit if set
                if (discount.getMaximumDiscount() != null) {
                    discountAmount = discountAmount.min(discount.getMaximumDiscount());
                }
                break;

            case FIXED_AMOUNT:
                discountAmount = discount.getValue().min(baseAmount);
                break;

            case BUY_X_GET_Y:
                discountAmount = calculateBuyXGetYDiscount(transaction, discount);
                break;

            case BULK_DISCOUNT:
                int totalQuantity = transaction.getItems().stream()
                        .mapToInt(TransactionItem::getQuantity)
                        .sum();

                if (totalQuantity >= discount.getMinimumQuantity()) {
                    discountAmount = baseAmount.multiply(discount.getValue())
                            .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                }
                break;

            case EMPLOYEE:
            case CUSTOMER_GROUP:
            case LOYALTY:
                discountAmount = baseAmount.multiply(discount.getValue())
                        .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                break;

            default:
                LOGGER.warning("Unknown discount type: " + discount.getType());
        }

        return discountAmount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Calculate Buy X Get Y discount
     */
    private BigDecimal calculateBuyXGetYDiscount(Transaction transaction, Discount discount) {
        // For simplicity, apply to the cheapest items
        List<TransactionItem> sortedItems = transaction.getItems().stream()
                .sorted(Comparator.comparing(TransactionItem::getUnitPrice))
                .collect(Collectors.toList());

        int totalQuantity = sortedItems.stream()
                .mapToInt(TransactionItem::getQuantity)
                .sum();

        int eligibleSets = totalQuantity / (discount.getBuyQuantity() + discount.getGetQuantity());
        int freeItems = eligibleSets * discount.getGetQuantity();

        BigDecimal discountAmount = BigDecimal.ZERO;
        int itemsToDiscount = freeItems;

        for (TransactionItem item : sortedItems) {
            if (itemsToDiscount <= 0) {
                break;
            }

            int discountQuantity = Math.min(itemsToDiscount, item.getQuantity());
            BigDecimal itemDiscount = item.getUnitPrice().multiply(new BigDecimal(discountQuantity));
            discountAmount = discountAmount.add(itemDiscount);
            itemsToDiscount -= discountQuantity;
        }

        return discountAmount;
    }

    /**
     * Get all available discounts
     */
    public List<Discount> getAvailableDiscounts() {
        return new ArrayList<>(availableDiscounts);
    }

    /**
     * Add a new discount
     */
    public void addDiscount(Discount discount) {
        availableDiscounts.add(discount);
    }

    /**
     * Remove a discount
     */
    public boolean removeDiscount(Long discountId) {
        return availableDiscounts.removeIf(d -> d.getId() != null && d.getId().equals(discountId));
    }

    /**
     * Find discount by promo code
     */
    public Optional<Discount> findByPromoCode(String promoCode) {
        if (promoCode == null || promoCode.trim().isEmpty()) {
            return Optional.empty();
        }

        return availableDiscounts.stream()
                .filter(d -> d.hasPromoCode() && d.getPromoCode().equalsIgnoreCase(promoCode.trim()))
                .findFirst();
    }
}

/**
 * Result class for discount calculations
 */
class DiscountCalculationResult {

    private final boolean success;
    private final String message;
    private final BigDecimal totalDiscount;
    private final List<AppliedDiscount> appliedDiscounts;

    public DiscountCalculationResult(boolean success, String message, BigDecimal totalDiscount,
            List<AppliedDiscount> appliedDiscounts) {
        this.success = success;
        this.message = message;
        this.totalDiscount = totalDiscount != null ? totalDiscount : BigDecimal.ZERO;
        this.appliedDiscounts = appliedDiscounts != null ? appliedDiscounts : new ArrayList<>();
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public List<AppliedDiscount> getAppliedDiscounts() {
        return appliedDiscounts;
    }

    public boolean hasDiscounts() {
        return !appliedDiscounts.isEmpty();
    }
}

/**
 * Class representing a discount that has been applied to a transaction
 */
class AppliedDiscount {

    private final Discount discount;
    private final BigDecimal amount;

    public AppliedDiscount(Discount discount, BigDecimal amount) {
        this.discount = discount;
        this.amount = amount != null ? amount : BigDecimal.ZERO;
    }

    public Discount getDiscount() {
        return discount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public String getDisplayText() {
        return String.format("%s: -$%.2f", discount.getName(), amount);
    }
}
