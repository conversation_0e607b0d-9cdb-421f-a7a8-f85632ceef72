package com.clothingstore.controller;

import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

import com.clothingstore.model.PurchaseOrder;
import com.clothingstore.model.Supplier;
import com.clothingstore.model.SupplierStatus;
import com.clothingstore.service.PurchaseOrderOperationResult;
import com.clothingstore.service.ReorderSuggestion;
import com.clothingstore.service.SupplierOperationResult;
import com.clothingstore.service.SupplierService;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Node;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Dialog;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;

/**
 * Controller for Supplier Management interface
 */
public class SupplierManagementController implements Initializable {

    // Services
    private SupplierService supplierService;

    // Main container
    @FXML
    private VBox mainContainer;

    // Supplier List Components
    @FXML
    private TableView<Supplier> supplierTable;
    @FXML
    private TableColumn<Supplier, String> companyNameColumn;
    @FXML
    private TableColumn<Supplier, String> contactPersonColumn;
    @FXML
    private TableColumn<Supplier, String> emailColumn;
    @FXML
    private TableColumn<Supplier, String> phoneColumn;
    @FXML
    private TableColumn<Supplier, String> statusColumn;
    @FXML
    private TableColumn<Supplier, String> leadTimeColumn;

    // Supplier Form Components
    @FXML
    private TextField companyNameField;
    @FXML
    private TextField contactPersonField;
    @FXML
    private TextField emailField;
    @FXML
    private TextField phoneField;
    @FXML
    private TextField addressField;
    @FXML
    private TextField cityField;
    @FXML
    private TextField stateField;
    @FXML
    private TextField zipCodeField;
    @FXML
    private TextField countryField;
    @FXML
    private TextField leadTimeDaysField;
    @FXML
    private TextField minimumOrderAmountField;
    @FXML
    private TextField creditLimitField;
    @FXML
    private ComboBox<SupplierStatus> statusComboBox;
    @FXML
    private TextArea notesArea;

    // Purchase Order Components
    @FXML
    private TableView<PurchaseOrder> purchaseOrderTable;
    @FXML
    private TableColumn<PurchaseOrder, String> orderNumberColumn;
    @FXML
    private TableColumn<PurchaseOrder, String> orderStatusColumn;
    @FXML
    private TableColumn<PurchaseOrder, String> orderDateColumn;
    @FXML
    private TableColumn<PurchaseOrder, String> expectedDeliveryColumn;
    @FXML
    private TableColumn<PurchaseOrder, String> totalAmountColumn;

    // Action Buttons
    @FXML
    private Button addSupplierButton;
    @FXML
    private Button editSupplierButton;
    @FXML
    private Button deleteSupplierButton;
    @FXML
    private Button saveSupplierButton;
    @FXML
    private Button cancelSupplierButton;
    @FXML
    private Button createPurchaseOrderButton;
    @FXML
    private Button viewPurchaseOrderButton;
    @FXML
    private Button generateReorderSuggestionsButton;

    // Search and Filter
    @FXML
    private TextField searchField;
    @FXML
    private ComboBox<SupplierStatus> filterStatusComboBox;
    @FXML
    private Button refreshButton;

    // Data
    private ObservableList<Supplier> supplierList;
    private ObservableList<PurchaseOrder> purchaseOrderList;
    private Supplier selectedSupplier;
    private boolean isEditMode = false;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Initialize services
        supplierService = SupplierService.getInstance();

        // Initialize data lists
        supplierList = FXCollections.observableArrayList();
        purchaseOrderList = FXCollections.observableArrayList();

        // Setup table columns
        setupSupplierTableColumns();
        setupPurchaseOrderTableColumns();

        // Setup combo boxes
        setupComboBoxes();

        // Setup event handlers
        setupEventHandlers();

        // Load initial data
        loadSuppliers();

        // Set initial state
        setFormEditMode(false);
    }

    /**
     * Setup supplier table columns
     */
    private void setupSupplierTableColumns() {
        companyNameColumn.setCellValueFactory(new PropertyValueFactory<>("companyName"));
        contactPersonColumn.setCellValueFactory(new PropertyValueFactory<>("contactPerson"));
        emailColumn.setCellValueFactory(new PropertyValueFactory<>("email"));
        phoneColumn.setCellValueFactory(new PropertyValueFactory<>("phone"));

        statusColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getStatus().getDisplayName()));

        leadTimeColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getLeadTimeDays() + " days"));

        supplierTable.setItems(supplierList);
    }

    /**
     * Setup purchase order table columns
     */
    private void setupPurchaseOrderTableColumns() {
        orderNumberColumn.setCellValueFactory(new PropertyValueFactory<>("orderNumber"));

        orderStatusColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getStatus().getDisplayName()));

        orderDateColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getCreatedAt().toLocalDate().toString()));

        expectedDeliveryColumn.setCellValueFactory(cellData -> {
            LocalDateTime expectedDate = cellData.getValue().getExpectedDeliveryDate();
            return new SimpleStringProperty(expectedDate != null ? expectedDate.toLocalDate().toString() : "N/A");
        });

        totalAmountColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty("$" + cellData.getValue().getTotalAmount().toString()));

        purchaseOrderTable.setItems(purchaseOrderList);
    }

    /**
     * Setup combo boxes
     */
    private void setupComboBoxes() {
        // Status combo boxes
        statusComboBox.setItems(FXCollections.observableArrayList(SupplierStatus.values()));
        filterStatusComboBox.setItems(FXCollections.observableArrayList(SupplierStatus.values()));
        filterStatusComboBox.getItems().add(0, null); // Add "All" option

        // Set default values
        statusComboBox.setValue(SupplierStatus.ACTIVE);
    }

    /**
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Table selection handlers
        supplierTable.getSelectionModel().selectedItemProperty().addListener(
                (observable, oldValue, newValue) -> onSupplierSelected(newValue));

        // Search functionality
        searchField.textProperty().addListener((observable, oldValue, newValue) -> filterSuppliers());
        filterStatusComboBox.valueProperty().addListener((observable, oldValue, newValue) -> filterSuppliers());
    }

    /**
     * Handle supplier selection
     */
    private void onSupplierSelected(Supplier supplier) {
        selectedSupplier = supplier;

        if (supplier != null) {
            populateSupplierForm(supplier);
            loadPurchaseOrdersForSupplier(supplier.getId());

            // Enable action buttons
            editSupplierButton.setDisable(false);
            deleteSupplierButton.setDisable(false);
            createPurchaseOrderButton.setDisable(false);
        } else {
            clearSupplierForm();
            purchaseOrderList.clear();

            // Disable action buttons
            editSupplierButton.setDisable(true);
            deleteSupplierButton.setDisable(true);
            createPurchaseOrderButton.setDisable(true);
        }
    }

    /**
     * Load suppliers from service
     */
    private void loadSuppliers() {
        try {
            List<Supplier> suppliers = supplierService.getAllSuppliers();
            supplierList.clear();
            supplierList.addAll(suppliers);
        } catch (Exception e) {
            showErrorAlert("Error Loading Suppliers", "Failed to load suppliers: " + e.getMessage());
        }
    }

    /**
     * Load purchase orders for selected supplier
     */
    private void loadPurchaseOrdersForSupplier(Long supplierId) {
        try {
            List<PurchaseOrder> orders = supplierService.getAllPurchaseOrders().stream()
                    .filter(po -> po.getSupplierId().equals(supplierId))
                    .collect(java.util.stream.Collectors.toList());

            purchaseOrderList.clear();
            purchaseOrderList.addAll(orders);
        } catch (Exception e) {
            showErrorAlert("Error Loading Purchase Orders", "Failed to load purchase orders: " + e.getMessage());
        }
    }

    /**
     * Filter suppliers based on search criteria
     */
    private void filterSuppliers() {
        try {
            String searchText = searchField.getText().toLowerCase();
            SupplierStatus filterStatus = filterStatusComboBox.getValue();

            List<Supplier> allSuppliers = supplierService.getAllSuppliers();
            List<Supplier> filteredSuppliers = allSuppliers.stream()
                    .filter(supplier -> {
                        // Text search filter
                        boolean matchesSearch = searchText.isEmpty()
                                || supplier.getCompanyName().toLowerCase().contains(searchText)
                                || (supplier.getContactPerson() != null && supplier.getContactPerson().toLowerCase().contains(searchText))
                                || (supplier.getEmail() != null && supplier.getEmail().toLowerCase().contains(searchText))
                                || (supplier.getPhone() != null && supplier.getPhone().toLowerCase().contains(searchText));

                        // Status filter
                        boolean matchesStatus = filterStatus == null || supplier.getStatus() == filterStatus;

                        return matchesSearch && matchesStatus;
                    })
                    .collect(java.util.stream.Collectors.toList());

            supplierList.clear();
            supplierList.addAll(filteredSuppliers);
        } catch (Exception e) {
            showErrorAlert("Filter Error", "Failed to filter suppliers: " + e.getMessage());
        }
    }

    /**
     * Populate supplier form with data
     */
    private void populateSupplierForm(Supplier supplier) {
        try {
            companyNameField.setText(supplier.getCompanyName() != null ? supplier.getCompanyName() : "");
            contactPersonField.setText(supplier.getContactPerson() != null ? supplier.getContactPerson() : "");
            emailField.setText(supplier.getEmail() != null ? supplier.getEmail() : "");
            phoneField.setText(supplier.getPhone() != null ? supplier.getPhone() : "");
            addressField.setText(supplier.getAddress() != null ? supplier.getAddress() : "");
            cityField.setText(supplier.getCity() != null ? supplier.getCity() : "");
            stateField.setText(supplier.getState() != null ? supplier.getState() : "");
            zipCodeField.setText(supplier.getZipCode() != null ? supplier.getZipCode() : "");
            countryField.setText(supplier.getCountry() != null ? supplier.getCountry() : "");

            leadTimeDaysField.setText(String.valueOf(supplier.getLeadTimeDays()));

            minimumOrderAmountField.setText(supplier.getMinimumOrderAmount() != null
                    ? supplier.getMinimumOrderAmount().toString() : "0.00");
            creditLimitField.setText(supplier.getCreditLimit() != null
                    ? supplier.getCreditLimit().toString() : "0.00");

            statusComboBox.setValue(supplier.getStatus() != null ? supplier.getStatus() : SupplierStatus.ACTIVE);
            notesArea.setText(supplier.getNotes() != null ? supplier.getNotes() : "");
        } catch (Exception e) {
            showErrorAlert("Form Error", "Failed to populate supplier form: " + e.getMessage());
        }
    }

    /**
     * Clear supplier form
     */
    private void clearSupplierForm() {
        companyNameField.clear();
        contactPersonField.clear();
        emailField.clear();
        phoneField.clear();
        addressField.clear();
        cityField.clear();
        stateField.clear();
        zipCodeField.clear();
        countryField.clear();
        leadTimeDaysField.clear();
        minimumOrderAmountField.clear();
        creditLimitField.clear();
        statusComboBox.setValue(SupplierStatus.ACTIVE);
        notesArea.clear();
    }

    /**
     * Set form edit mode
     */
    private void setFormEditMode(boolean editMode) {
        isEditMode = editMode;

        // Enable/disable form fields
        companyNameField.setDisable(!editMode);
        contactPersonField.setDisable(!editMode);
        emailField.setDisable(!editMode);
        phoneField.setDisable(!editMode);
        addressField.setDisable(!editMode);
        cityField.setDisable(!editMode);
        stateField.setDisable(!editMode);
        zipCodeField.setDisable(!editMode);
        countryField.setDisable(!editMode);
        leadTimeDaysField.setDisable(!editMode);
        minimumOrderAmountField.setDisable(!editMode);
        creditLimitField.setDisable(!editMode);
        statusComboBox.setDisable(!editMode);
        notesArea.setDisable(!editMode);

        // Show/hide action buttons
        saveSupplierButton.setVisible(editMode);
        cancelSupplierButton.setVisible(editMode);
        addSupplierButton.setDisable(editMode);
        editSupplierButton.setDisable(editMode || selectedSupplier == null);
        deleteSupplierButton.setDisable(editMode || selectedSupplier == null);
    }

    // Action Methods
    /**
     * Handle add supplier button click
     */
    @FXML
    private void handleAddSupplier() {
        selectedSupplier = null;
        clearSupplierForm();
        setFormEditMode(true);
        companyNameField.requestFocus();
    }

    /**
     * Handle edit supplier button click
     */
    @FXML
    private void handleEditSupplier() {
        if (selectedSupplier != null) {
            setFormEditMode(true);
            companyNameField.requestFocus();
        }
    }

    /**
     * Handle delete supplier button click
     */
    @FXML
    private void handleDeleteSupplier() {
        if (selectedSupplier == null) {
            return;
        }

        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Confirm Delete");
        confirmAlert.setHeaderText("Delete Supplier");
        confirmAlert.setContentText("Are you sure you want to delete supplier: " + selectedSupplier.getCompanyName() + "?");

        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                SupplierOperationResult deleteResult = supplierService.deleteSupplier(selectedSupplier.getId());
                if (deleteResult.isSuccess()) {
                    showInfoAlert("Success", deleteResult.getMessage());
                    loadSuppliers();
                    clearSupplierForm();
                    selectedSupplier = null;
                } else {
                    showErrorAlert("Delete Failed", deleteResult.getMessage());
                }
            } catch (Exception e) {
                showErrorAlert("Error", "Failed to delete supplier: " + e.getMessage());
            }
        }
    }

    /**
     * Handle save supplier button click
     */
    @FXML
    private void handleSaveSupplier() {
        try {
            // Validate form
            if (!validateSupplierForm()) {
                return;
            }

            // Create or update supplier
            Supplier supplier = selectedSupplier != null ? selectedSupplier : new Supplier();
            populateSupplierFromForm(supplier);

            SupplierOperationResult result;
            if (selectedSupplier == null) {
                result = supplierService.createSupplier(supplier);
            } else {
                result = supplierService.updateSupplier(supplier);
            }

            if (result.isSuccess()) {
                showInfoAlert("Success", result.getMessage());
                loadSuppliers();
                setFormEditMode(false);

                // Select the saved supplier
                selectedSupplier = result.getSupplier();
                supplierTable.getSelectionModel().select(selectedSupplier);
            } else {
                showErrorAlert("Error", result.getMessage());
            }

        } catch (Exception e) {
            showErrorAlert("Error", "Failed to save supplier: " + e.getMessage());
        }
    }

    /**
     * Handle cancel button click
     */
    @FXML
    private void handleCancelSupplier() {
        if (selectedSupplier != null) {
            populateSupplierForm(selectedSupplier);
        } else {
            clearSupplierForm();
        }
        setFormEditMode(false);
    }

    /**
     * Handle refresh button click
     */
    @FXML
    private void handleRefresh() {
        loadSuppliers();
        searchField.clear();
        filterStatusComboBox.setValue(null);
    }

    /**
     * Handle create purchase order button click
     */
    @FXML
    private void handleCreatePurchaseOrder() {
        if (selectedSupplier == null) {
            showWarningAlert("No Selection", "Please select a supplier to create a purchase order.");
            return;
        }

        try {
            // Create a simple purchase order dialog
            Dialog<PurchaseOrder> dialog = createPurchaseOrderDialog(selectedSupplier);
            Optional<PurchaseOrder> result = dialog.showAndWait();

            if (result.isPresent()) {
                PurchaseOrder purchaseOrder = result.get();
                PurchaseOrderOperationResult createResult = supplierService.createPurchaseOrder(
                        selectedSupplier.getId(), "Current User");

                if (createResult.isSuccess()) {
                    showInfoAlert("Success", "Purchase order created: " + createResult.getPurchaseOrder().getOrderNumber());
                    loadPurchaseOrdersForSupplier(selectedSupplier.getId());
                } else {
                    showErrorAlert("Error", createResult.getMessage());
                }
            }

        } catch (Exception e) {
            showErrorAlert("Error", "Failed to create purchase order: " + e.getMessage());
        }
    }

    /**
     * Create purchase order dialog
     */
    private Dialog<PurchaseOrder> createPurchaseOrderDialog(Supplier supplier) {
        Dialog<PurchaseOrder> dialog = new Dialog<>();
        dialog.setTitle("Create Purchase Order");
        dialog.setHeaderText("Create new purchase order for: " + supplier.getCompanyName());

        // Set button types
        ButtonType createButtonType = new ButtonType("Create", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(createButtonType, ButtonType.CANCEL);

        // Create form
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextField deliveryAddressField = new TextField();
        deliveryAddressField.setPromptText("Delivery address");
        TextArea notesArea = new TextArea();
        notesArea.setPromptText("Order notes");
        notesArea.setPrefRowCount(3);
        TextField shippingCostField = new TextField("0.00");
        shippingCostField.setPromptText("Shipping cost");

        grid.add(new Label("Delivery Address:"), 0, 0);
        grid.add(deliveryAddressField, 1, 0);
        grid.add(new Label("Shipping Cost:"), 0, 1);
        grid.add(shippingCostField, 1, 1);
        grid.add(new Label("Notes:"), 0, 2);
        grid.add(notesArea, 1, 2);

        dialog.getDialogPane().setContent(grid);

        // Enable/disable create button
        Node createButton = dialog.getDialogPane().lookupButton(createButtonType);
        createButton.setDisable(false);

        // Convert result
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == createButtonType) {
                PurchaseOrder po = new PurchaseOrder(supplier.getId());
                po.setDeliveryAddress(deliveryAddressField.getText().trim());
                po.setNotes(notesArea.getText().trim());

                try {
                    if (!shippingCostField.getText().trim().isEmpty()) {
                        po.setShippingCost(new BigDecimal(shippingCostField.getText().trim()));
                    }
                } catch (NumberFormatException e) {
                    po.setShippingCost(BigDecimal.ZERO);
                }

                return po;
            }
            return null;
        });

        return dialog;
    }

    /**
     * Handle view purchase order button click
     */
    @FXML
    private void handleViewPurchaseOrder() {
        PurchaseOrder selectedOrder = purchaseOrderTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showWarningAlert("No Selection", "Please select a purchase order to view");
            return;
        }

        // In a real implementation, would open purchase order detail dialog
        showInfoAlert("Purchase Order Details",
                "Order Number: " + selectedOrder.getOrderNumber() + "\n"
                + "Status: " + selectedOrder.getStatus().getDisplayName() + "\n"
                + "Total Amount: $" + selectedOrder.getTotalAmount());
    }

    /**
     * Handle generate reorder suggestions button click
     */
    @FXML
    private void handleGenerateReorderSuggestions() {
        try {
            List<ReorderSuggestion> suggestions = supplierService.generateReorderSuggestions();

            if (suggestions.isEmpty()) {
                showInfoAlert("Reorder Suggestions", "No reorder suggestions at this time");
            } else {
                StringBuilder message = new StringBuilder("Reorder Suggestions:\n\n");
                for (ReorderSuggestion suggestion : suggestions) {
                    message.append(suggestion.getDisplayText()).append("\n");
                }

                showInfoAlert("Reorder Suggestions", message.toString());
            }

        } catch (Exception e) {
            showErrorAlert("Error", "Failed to generate reorder suggestions: " + e.getMessage());
        }
    }

    /**
     * Validate supplier form
     */
    private boolean validateSupplierForm() {
        if (companyNameField.getText().trim().isEmpty()) {
            showErrorAlert("Validation Error", "Company name is required");
            companyNameField.requestFocus();
            return false;
        }

        if (contactPersonField.getText().trim().isEmpty()) {
            showErrorAlert("Validation Error", "Contact person is required");
            contactPersonField.requestFocus();
            return false;
        }

        // Validate email format if provided
        String email = emailField.getText().trim();
        if (!email.isEmpty() && !email.contains("@")) {
            showErrorAlert("Validation Error", "Invalid email format");
            emailField.requestFocus();
            return false;
        }

        // Validate numeric fields
        try {
            if (!leadTimeDaysField.getText().trim().isEmpty()) {
                int leadTime = Integer.parseInt(leadTimeDaysField.getText().trim());
                if (leadTime < 0) {
                    showErrorAlert("Validation Error", "Lead time days must be non-negative");
                    leadTimeDaysField.requestFocus();
                    return false;
                }
            }
        } catch (NumberFormatException e) {
            showErrorAlert("Validation Error", "Lead time days must be a valid number");
            leadTimeDaysField.requestFocus();
            return false;
        }

        try {
            if (!minimumOrderAmountField.getText().trim().isEmpty()) {
                BigDecimal minOrder = new BigDecimal(minimumOrderAmountField.getText().trim());
                if (minOrder.compareTo(BigDecimal.ZERO) < 0) {
                    showErrorAlert("Validation Error", "Minimum order amount must be non-negative");
                    minimumOrderAmountField.requestFocus();
                    return false;
                }
            }
        } catch (NumberFormatException e) {
            showErrorAlert("Validation Error", "Minimum order amount must be a valid number");
            minimumOrderAmountField.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * Populate supplier object from form
     */
    private void populateSupplierFromForm(Supplier supplier) {
        supplier.setCompanyName(companyNameField.getText().trim());
        supplier.setContactPerson(contactPersonField.getText().trim());
        supplier.setEmail(emailField.getText().trim());
        supplier.setPhone(phoneField.getText().trim());
        supplier.setAddress(addressField.getText().trim());
        supplier.setCity(cityField.getText().trim());
        supplier.setState(stateField.getText().trim());
        supplier.setZipCode(zipCodeField.getText().trim());
        supplier.setCountry(countryField.getText().trim());
        supplier.setStatus(statusComboBox.getValue());
        supplier.setNotes(notesArea.getText().trim());

        // Set numeric fields
        try {
            if (!leadTimeDaysField.getText().trim().isEmpty()) {
                supplier.setLeadTimeDays(Integer.parseInt(leadTimeDaysField.getText().trim()));
            }
        } catch (NumberFormatException e) {
            supplier.setLeadTimeDays(0);
        }

        try {
            if (!minimumOrderAmountField.getText().trim().isEmpty()) {
                supplier.setMinimumOrderAmount(new BigDecimal(minimumOrderAmountField.getText().trim()));
            }
        } catch (NumberFormatException e) {
            supplier.setMinimumOrderAmount(BigDecimal.ZERO);
        }

        try {
            if (!creditLimitField.getText().trim().isEmpty()) {
                supplier.setCreditLimit(new BigDecimal(creditLimitField.getText().trim()));
            }
        } catch (NumberFormatException e) {
            supplier.setCreditLimit(BigDecimal.ZERO);
        }
    }

    // Utility Methods
    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showErrorAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showWarningAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
