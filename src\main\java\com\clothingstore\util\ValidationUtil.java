package com.clothingstore.util;

import java.math.BigDecimal;
import java.util.regex.Pattern;

/**
 * Utility class for input validation
 */
public class ValidationUtil {

    // Updated to enforce strict 11-digit phone number format
    private static final Pattern PHONE_PATTERN = Pattern.compile(
            "^[0-9]{11}$"
    );

    private static final Pattern SKU_PATTERN = Pattern.compile(
            "^[A-Z]{3}[0-9]{3}$"
    );

    /**
     * Validates if a string is not null and not empty
     */
    public static boolean isNotEmpty(String value) {
        return value != null && !value.trim().isEmpty();
    }

    /**
     * Validates phone number format - enforces strict 11-digit format Accepts
     * only exactly 11 numeric digits after removing formatting characters
     */
    public static boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }

        // Strip out all formatting characters (spaces, dashes, parentheses, plus signs, dots)
        String digitsOnly = phone.replaceAll("[\\s\\-\\(\\)\\+\\.]", "");

        // Validate that it contains only numeric characters and is exactly 11 digits
        return PHONE_PATTERN.matcher(digitsOnly).matches();
    }

    /**
     * Strips formatting characters from phone number and returns digits only
     * Used for consistent phone number processing
     */
    public static String stripPhoneFormatting(String phone) {
        if (phone == null) {
            return null;
        }
        return phone.replaceAll("[\\s\\-\\(\\)\\+\\.]", "");
    }

    /**
     * Validates SKU format (3 letters + 3 numbers)
     */
    public static boolean isValidSku(String sku) {
        return sku != null && SKU_PATTERN.matcher(sku).matches();
    }

    /**
     * Validates if a number is positive
     */
    public static boolean isPositive(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Validates if a number is non-negative
     */
    public static boolean isNonNegative(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) >= 0;
    }

    /**
     * Validates if an integer is positive
     */
    public static boolean isPositive(Integer value) {
        return value != null && value > 0;
    }

    /**
     * Validates if an integer is non-negative
     */
    public static boolean isNonNegative(Integer value) {
        return value != null && value >= 0;
    }

    /**
     * Validates string length
     */
    public static boolean isValidLength(String value, int minLength, int maxLength) {
        if (value == null) {
            return false;
        }
        int length = value.trim().length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * Validates if a string contains only letters and spaces
     */
    public static boolean isAlphaWithSpaces(String value) {
        return value != null && value.matches("^[a-zA-Z\\s]+$");
    }

    /**
     * Validates if a string is a valid decimal number
     */
    public static boolean isValidDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        try {
            new BigDecimal(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Validates if a string is a valid integer
     */
    public static boolean isValidInteger(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        try {
            Integer.parseInt(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Validates zip code format (5 digits or 5+4 format)
     */
    public static boolean isValidZipCode(String zipCode) {
        return zipCode != null && zipCode.matches("^\\d{5}(-\\d{4})?$");
    }

    /**
     * Validates product name
     */
    public static ValidationResult validateProductName(String name) {
        if (!isNotEmpty(name)) {
            return new ValidationResult(false, "Product name is required");
        }
        if (!isValidLength(name, 2, 100)) {
            return new ValidationResult(false, "Product name must be between 2 and 100 characters");
        }
        return new ValidationResult(true, "");
    }

    /**
     * Validates product SKU
     */
    public static ValidationResult validateProductSku(String sku) {
        if (!isNotEmpty(sku)) {
            return new ValidationResult(false, "SKU is required");
        }
        if (!isValidSku(sku)) {
            return new ValidationResult(false, "SKU must be in format ABC123 (3 letters + 3 numbers)");
        }
        return new ValidationResult(true, "");
    }

    /**
     * Validates product price
     */
    public static ValidationResult validateProductPrice(String priceStr) {
        if (!isNotEmpty(priceStr)) {
            return new ValidationResult(false, "Price is required");
        }
        if (!isValidDecimal(priceStr)) {
            return new ValidationResult(false, "Price must be a valid number");
        }
        BigDecimal price = new BigDecimal(priceStr);
        if (!isPositive(price)) {
            return new ValidationResult(false, "Price must be greater than 0");
        }
        if (price.compareTo(new BigDecimal("10000")) > 0) {
            return new ValidationResult(false, "Price cannot exceed $10,000");
        }
        return new ValidationResult(true, "");
    }

    /**
     * Validates stock quantity
     */
    public static ValidationResult validateStockQuantity(String quantityStr) {
        if (!isNotEmpty(quantityStr)) {
            return new ValidationResult(false, "Stock quantity is required");
        }
        if (!isValidInteger(quantityStr)) {
            return new ValidationResult(false, "Stock quantity must be a valid number");
        }
        int quantity = Integer.parseInt(quantityStr);
        if (!isNonNegative(quantity)) {
            return new ValidationResult(false, "Stock quantity cannot be negative");
        }
        if (quantity > 10000) {
            return new ValidationResult(false, "Stock quantity cannot exceed 10,000");
        }
        return new ValidationResult(true, "");
    }

    /**
     * Validates customer name
     */
    public static ValidationResult validateCustomerName(String firstName, String lastName) {
        if (!isNotEmpty(firstName)) {
            return new ValidationResult(false, "First name is required");
        }
        if (!isNotEmpty(lastName)) {
            return new ValidationResult(false, "Last name is required");
        }
        if (!isAlphaWithSpaces(firstName)) {
            return new ValidationResult(false, "First name can only contain letters and spaces");
        }
        if (!isAlphaWithSpaces(lastName)) {
            return new ValidationResult(false, "Last name can only contain letters and spaces");
        }
        if (!isValidLength(firstName, 1, 50)) {
            return new ValidationResult(false, "First name must be between 1 and 50 characters");
        }
        if (!isValidLength(lastName, 1, 50)) {
            return new ValidationResult(false, "Last name must be between 1 and 50 characters");
        }
        return new ValidationResult(true, "");
    }

    /**
     * Validates customer phone - enforces strict 11-digit format
     */
    public static ValidationResult validateCustomerPhone(String phone) {
        if (!isNotEmpty(phone)) {
            return new ValidationResult(false, "Phone number is required");
        }

        // Strip formatting and check length for specific error messages
        String digitsOnly = stripPhoneFormatting(phone);

        if (digitsOnly == null || digitsOnly.isEmpty()) {
            return new ValidationResult(false, "Phone number is required");
        }

        // Check for non-numeric characters after stripping formatting
        if (!digitsOnly.matches("^[0-9]+$")) {
            return new ValidationResult(false, "Phone number must contain only numeric digits");
        }

        // Check exact length requirement
        if (digitsOnly.length() < 11) {
            return new ValidationResult(false,
                    String.format("Phone number must be exactly 11 digits (current: %d digits)", digitsOnly.length()));
        }

        if (digitsOnly.length() > 11) {
            return new ValidationResult(false,
                    String.format("Phone number must be exactly 11 digits (current: %d digits)", digitsOnly.length()));
        }

        // Final validation using the main method
        if (!isValidPhone(phone)) {
            return new ValidationResult(false, "Phone number must be exactly 11 digits");
        }

        return new ValidationResult(true, "");
    }

    /**
     * Validation result class
     */
    public static class ValidationResult {

        private final boolean valid;
        private final String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }
}
