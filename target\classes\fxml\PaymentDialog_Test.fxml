<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.PaymentDialogController" spacing="15.0" style="-fx-background-color: #f8f9fa;">
   <padding>
      <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
   </padding>
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #2c3e50; -fx-padding: 15; -fx-background-radius: 8;">
         <children>
            <Label text="Process Payment" textFill="WHITE" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblTransactionNumber" text="Transaction: Loading..." textFill="WHITE" style="-fx-font-size: 14px;" />
         </children>
      </HBox>

      <!-- Outstanding Balance Highlight -->
      <VBox spacing="10.0" style="-fx-background-color: #fff3cd; -fx-padding: 15; -fx-border-color: #ffeaa7; -fx-border-width: 2; -fx-border-radius: 8; -fx-background-radius: 8;">
         <children>
            <Label text="Outstanding Balance Information" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #856404;" />
            <HBox spacing="15.0" alignment="CENTER_LEFT">
               <children>
                  <Label text="Amount Still Owed:" style="-fx-font-size: 14px; -fx-font-weight: bold;" />
                  <Label fx:id="lblRemainingBalance" text="Loading..." style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: red;" />
               </children>
            </HBox>
            <HBox spacing="15.0" alignment="CENTER_LEFT">
               <children>
                  <Label text="Total Transaction Amount:" style="-fx-font-size: 12px;" />
                  <Label fx:id="lblTotalAmount" text="Loading..." style="-fx-font-size: 12px; -fx-text-fill: #7f8c8d;" />
               </children>
            </HBox>
            <HBox spacing="15.0" alignment="CENTER_LEFT">
               <children>
                  <Label text="Already Paid:" style="-fx-font-size: 12px;" />
                  <Label fx:id="lblAmountPaid" text="Loading..." style="-fx-font-size: 12px; -fx-text-fill: #27ae60;" />
               </children>
            </HBox>
         </children>
      </VBox>

      <!-- Transaction Details (Collapsed) -->
      <VBox spacing="8.0" style="-fx-background-color: white; -fx-padding: 12; -fx-border-color: #ddd; -fx-border-width: 1; -fx-border-radius: 6; -fx-background-radius: 6;">
         <children>
            <Label text="Transaction Details" style="-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #666;" />
            <HBox spacing="10.0">
               <children>
                  <Label text="Subtotal:" style="-fx-font-size: 11px;" />
                  <Label fx:id="lblSubtotal" text="Loading..." style="-fx-font-size: 11px;" />
               </children>
            </HBox>
            <HBox spacing="10.0">
               <children>
                  <Label text="Discount:" style="-fx-font-size: 11px;" />
                  <Label fx:id="lblDiscount" text="Loading..." style="-fx-font-size: 11px;" />
               </children>
            </HBox>
            <HBox spacing="10.0">
               <children>
                  <Label text="Tax:" style="-fx-font-size: 11px;" />
                  <Label fx:id="lblTax" text="Loading..." style="-fx-font-size: 11px;" />
               </children>
            </HBox>
         </children>
      </VBox>

      <!-- Payment Input Section -->
      <VBox spacing="15.0" style="-fx-background-color: white; -fx-padding: 20; -fx-border-color: #3498db; -fx-border-width: 2; -fx-border-radius: 8; -fx-background-radius: 8;">
         <children>
            <Label text="Make Payment" style="-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" />

            <!-- Payment Method -->
            <HBox spacing="15.0" alignment="CENTER_LEFT">
               <children>
                  <Label text="Payment Method:" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-min-width: 120;" />
                  <ComboBox fx:id="cmbPaymentMethod" prefWidth="150.0" />
               </children>
            </HBox>

            <!-- Payment Amount Input -->
            <VBox spacing="10.0">
               <children>
                  <Label text="Payment Amount:" style="-fx-font-size: 14px; -fx-font-weight: bold;" />
                  <HBox spacing="10.0" alignment="CENTER_LEFT">
                     <children>
                        <TextField fx:id="txtPaymentAmount" prefWidth="150.0" promptText="Enter amount to pay" style="-fx-font-size: 14px;" />
                        <Button fx:id="btnFullPayment" onAction="#handleFullPayment" text="Pay Full Balance" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 12px;" />
                     </children>
                  </HBox>
                  <Label text="Enter the amount you want to pay (cannot exceed remaining balance)" style="-fx-font-size: 11px; -fx-text-fill: #7f8c8d;" />
               </children>
            </VBox>

            <!-- Quick Payment Options -->
            <VBox spacing="8.0">
               <children>
                  <Label text="Quick Payment Options:" style="-fx-font-size: 12px; -fx-font-weight: bold;" />
                  <HBox spacing="8.0">
                     <children>
                        <Button fx:id="btn25Percent" onAction="#handle25Percent" text="25%" style="-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-size: 10px;" />
                        <Button fx:id="btn50Percent" onAction="#handle50Percent" text="50%" style="-fx-background-color: #e67e22; -fx-text-fill: white; -fx-font-size: 10px;" />
                        <Button fx:id="btn75Percent" onAction="#handle75Percent" text="75%" style="-fx-background-color: #d35400; -fx-text-fill: white; -fx-font-size: 10px;" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
      </VBox>

      <!-- Payment Preview -->
      <VBox fx:id="vboxChangeInfo" spacing="10.0" style="-fx-background-color: #d5f4e6; -fx-padding: 15; -fx-border-color: #27ae60; -fx-border-width: 1; -fx-border-radius: 6; -fx-background-radius: 6;" visible="false">
         <children>
            <Label text="Payment Preview" style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #27ae60;" />
            <HBox spacing="15.0">
               <children>
                  <Label fx:id="lblChangeLabel" text="New Remaining Balance:" style="-fx-font-size: 12px; -fx-font-weight: bold;" />
                  <Label fx:id="lblChange" text="$0.00" style="-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: red;" />
               </children>
            </HBox>
         </children>
      </VBox>

      <!-- Payment Type (Hidden - Auto-determined) -->
      <VBox spacing="5.0" visible="false">
         <children>
            <RadioButton fx:id="rbFullPayment" text="Full Payment" />
            <RadioButton fx:id="rbPartialPayment" text="Partial Payment" />
         </children>
      </VBox>

      <!-- Validation Message -->
      <Label fx:id="lblValidationMessage" text="" style="-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: red;" />

      <!-- Notes -->
      <VBox spacing="8.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #ddd; -fx-border-width: 1; -fx-border-radius: 6; -fx-background-radius: 6;">
         <children>
            <Label text="Payment Notes (Optional):" style="-fx-font-size: 12px; -fx-font-weight: bold;" />
            <TextArea fx:id="txtNotes" prefRowCount="2" promptText="Add any notes about this payment..." style="-fx-font-size: 11px;" />
         </children>
      </VBox>

      <!-- Buttons -->
      <HBox spacing="15.0" alignment="CENTER_RIGHT" style="-fx-padding: 10 0 0 0;">
         <children>
            <Button fx:id="btnCancel" onAction="#handleCancel" text="Cancel" style="-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-font-size: 14px; -fx-padding: 10 20;" />
            <Button fx:id="btnProcessPayment" onAction="#handleProcessPayment" text="Process Payment" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold; -fx-padding: 10 20;" />
         </children>
      </HBox>
   </children>
</VBox>
