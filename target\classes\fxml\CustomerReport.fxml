<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.CustomerReportController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="👥 Customer Report">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <DatePicker fx:id="dateFrom" promptText="From Date" />
            <DatePicker fx:id="dateTo" promptText="To Date" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
            <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </HBox>

      <!-- Customer Metrics -->
      <GridPane hgap="15.0" vgap="15.0" styleClass="form-container">
         <columnConstraints>
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         <children>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="0">
               <children>
                  <Label styleClass="metric-title" text="👥 Total Customers" />
                  <Label fx:id="lblTotalCustomers" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="1">
               <children>
                  <Label styleClass="metric-title" text="✅ Active Customers" />
                  <Label fx:id="lblActiveCustomers" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="2">
               <children>
                  <Label styleClass="metric-title" text="🛒 Avg Purchases" />
                  <Label fx:id="lblAvgPurchases" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="3">
               <children>
                  <Label styleClass="metric-title" text="💰 Total Spent" />
                  <Label fx:id="lblTotalSpent" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </GridPane>

      <!-- Customer List -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="Customer Details">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <TextField fx:id="txtSearch" onKeyReleased="#handleSearch" promptText="Search customers..." />
                  <ComboBox fx:id="cmbStatusFilter" onAction="#handleStatusFilter" promptText="All Status" />
               </children>
            </HBox>

            <TableView fx:id="tblCustomers" prefHeight="300.0">
               <columns>
                  <TableColumn fx:id="colCustomerId" prefWidth="80.0" text="ID" />
                  <TableColumn fx:id="colName" prefWidth="150.0" text="Name" />
                  <TableColumn fx:id="colEmail" prefWidth="180.0" text="Email" />
                  <TableColumn fx:id="colPhone" prefWidth="120.0" text="Phone" />
                  <TableColumn fx:id="colStatus" prefWidth="80.0" text="Status" />
                  <TableColumn fx:id="colTotalPurchases" prefWidth="100.0" text="Purchases" />
                  <TableColumn fx:id="colTotalSpent" prefWidth="100.0" text="Total Spent" />
                  <TableColumn fx:id="colLastPurchase" prefWidth="120.0" text="Last Purchase" />
                  <TableColumn fx:id="colActions" prefWidth="100.0" text="Actions" />
               </columns>
            </TableView>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Top Customers -->
      <HBox spacing="15.0" styleClass="form-container">
         <children>
            <VBox spacing="10.0" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="section-title" text="🏆 Top Customers (by Total Spent)">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <TableView fx:id="tblTopCustomers" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="colTopRank" prefWidth="50.0" text="Rank" />
                        <TableColumn fx:id="colTopName" prefWidth="150.0" text="Customer Name" />
                        <TableColumn fx:id="colTopEmail" prefWidth="150.0" text="Email" />
                        <TableColumn fx:id="colTopPurchases" prefWidth="80.0" text="Purchases" />
                        <TableColumn fx:id="colTopSpent" prefWidth="100.0" text="Total Spent" />
                        <TableColumn fx:id="colTopAvgOrder" prefWidth="100.0" text="Avg Order" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
