package com.clothingstore.database;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * Database connection pool for improved performance under concurrent usage
 */
public class ConnectionPool {
    private static final Logger LOGGER = Logger.getLogger(ConnectionPool.class.getName());
    
    private static ConnectionPool instance;
    private final BlockingQueue<Connection> connectionPool;
    private final int maxPoolSize;
    private final String databaseUrl;
    private volatile boolean isShutdown = false;
    
    // Pool configuration
    private static final int DEFAULT_POOL_SIZE = 10;
    private static final int CONNECTION_TIMEOUT_SECONDS = 30;
    
    private ConnectionPool() {
        this.maxPoolSize = DEFAULT_POOL_SIZE;
        this.databaseUrl = "*****************************";
        this.connectionPool = new LinkedBlockingQueue<>(maxPoolSize);
        
        initializePool();
    }
    
    public static synchronized ConnectionPool getInstance() {
        if (instance == null) {
            instance = new ConnectionPool();
        }
        return instance;
    }
    
    private void initializePool() {
        try {
            // Load SQLite driver
            Class.forName("org.sqlite.JDBC");
            
            // Create initial connections
            for (int i = 0; i < maxPoolSize; i++) {
                Connection connection = createNewConnection();
                if (connection != null) {
                    connectionPool.offer(connection);
                }
            }
            
            LOGGER.info("Connection pool initialized with " + connectionPool.size() + " connections");
            
        } catch (ClassNotFoundException e) {
            LOGGER.severe("SQLite JDBC driver not found: " + e.getMessage());
        }
    }
    
    private Connection createNewConnection() {
        try {
            Connection connection = DriverManager.getConnection(databaseUrl);
            
            // Configure connection for optimal performance
            connection.setAutoCommit(true);
            
            // Enable SQLite optimizations
            try (var stmt = connection.createStatement()) {
                // Enable WAL mode for better concurrent access
                stmt.execute("PRAGMA journal_mode=WAL");
                
                // Optimize for read performance
                stmt.execute("PRAGMA synchronous=NORMAL");
                stmt.execute("PRAGMA cache_size=10000");
                stmt.execute("PRAGMA temp_store=MEMORY");
                
                // Enable foreign key constraints
                stmt.execute("PRAGMA foreign_keys=ON");
            }
            
            return connection;
            
        } catch (SQLException e) {
            LOGGER.severe("Failed to create database connection: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Get a connection from the pool
     */
    public Connection getConnection() throws SQLException {
        if (isShutdown) {
            throw new SQLException("Connection pool is shutdown");
        }
        
        try {
            Connection connection = connectionPool.poll(CONNECTION_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            
            if (connection == null) {
                throw new SQLException("Unable to get connection from pool within timeout");
            }
            
            // Validate connection
            if (connection.isClosed() || !connection.isValid(5)) {
                // Create new connection if invalid
                connection = createNewConnection();
                if (connection == null) {
                    throw new SQLException("Unable to create new database connection");
                }
            }
            
            return connection;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new SQLException("Interrupted while waiting for connection", e);
        }
    }
    
    /**
     * Return a connection to the pool
     */
    public void returnConnection(Connection connection) {
        if (connection == null || isShutdown) {
            return;
        }
        
        try {
            if (!connection.isClosed() && connection.isValid(5)) {
                // Reset connection state
                connection.setAutoCommit(true);
                
                // Return to pool
                if (!connectionPool.offer(connection)) {
                    // Pool is full, close the connection
                    connection.close();
                }
            } else {
                // Connection is invalid, close it
                connection.close();
            }
        } catch (SQLException e) {
            LOGGER.warning("Error returning connection to pool: " + e.getMessage());
            try {
                connection.close();
            } catch (SQLException closeEx) {
                LOGGER.warning("Error closing invalid connection: " + closeEx.getMessage());
            }
        }
    }
    
    /**
     * Get pool statistics
     */
    public PoolStats getPoolStats() {
        return new PoolStats(
            connectionPool.size(),
            maxPoolSize,
            maxPoolSize - connectionPool.size()
        );
    }
    
    /**
     * Shutdown the connection pool
     */
    public void shutdown() {
        isShutdown = true;
        
        // Close all connections in pool
        Connection connection;
        while ((connection = connectionPool.poll()) != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                LOGGER.warning("Error closing connection during shutdown: " + e.getMessage());
            }
        }
        
        LOGGER.info("Connection pool shutdown completed");
    }
    
    /**
     * Pool statistics data class
     */
    public static class PoolStats {
        private final int availableConnections;
        private final int maxConnections;
        private final int activeConnections;
        
        public PoolStats(int availableConnections, int maxConnections, int activeConnections) {
            this.availableConnections = availableConnections;
            this.maxConnections = maxConnections;
            this.activeConnections = activeConnections;
        }
        
        public int getAvailableConnections() { return availableConnections; }
        public int getMaxConnections() { return maxConnections; }
        public int getActiveConnections() { return activeConnections; }
        
        @Override
        public String toString() {
            return String.format("Pool Stats: %d/%d available, %d active", 
                availableConnections, maxConnections, activeConnections);
        }
    }
}
