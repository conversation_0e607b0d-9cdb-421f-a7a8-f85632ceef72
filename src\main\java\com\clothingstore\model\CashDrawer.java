package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class for Cash Drawer management
 */
public class CashDrawer {
    private Long id;
    private String drawerNumber;
    private String cashierName;
    private Long cashierId;
    private LocalDateTime openedAt;
    private LocalDateTime closedAt;
    private BigDecimal openingAmount;
    private BigDecimal closingAmount;
    private BigDecimal expectedAmount;
    private BigDecimal actualAmount;
    private BigDecimal variance;
    private BigDecimal totalSales;
    private BigDecimal totalCashSales;
    private BigDecimal totalRefunds;
    private BigDecimal totalCashDrops;
    private BigDecimal totalPayouts;
    private int transactionCount;
    private String status; // OPEN, CLOSED, RECONCILED
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public CashDrawer() {
        this.status = "OPEN";
        this.openingAmount = BigDecimal.ZERO;
        this.closingAmount = BigDecimal.ZERO;
        this.expectedAmount = BigDecimal.ZERO;
        this.actualAmount = BigDecimal.ZERO;
        this.variance = BigDecimal.ZERO;
        this.totalSales = BigDecimal.ZERO;
        this.totalCashSales = BigDecimal.ZERO;
        this.totalRefunds = BigDecimal.ZERO;
        this.totalCashDrops = BigDecimal.ZERO;
        this.totalPayouts = BigDecimal.ZERO;
        this.transactionCount = 0;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public CashDrawer(String drawerNumber, String cashierName, BigDecimal openingAmount) {
        this();
        this.drawerNumber = drawerNumber;
        this.cashierName = cashierName;
        this.openingAmount = openingAmount;
        this.openedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDrawerNumber() {
        return drawerNumber;
    }

    public void setDrawerNumber(String drawerNumber) {
        this.drawerNumber = drawerNumber;
    }

    public String getCashierName() {
        return cashierName;
    }

    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public Long getCashierId() {
        return cashierId;
    }

    public void setCashierId(Long cashierId) {
        this.cashierId = cashierId;
    }

    public LocalDateTime getOpenedAt() {
        return openedAt;
    }

    public void setOpenedAt(LocalDateTime openedAt) {
        this.openedAt = openedAt;
    }

    public LocalDateTime getClosedAt() {
        return closedAt;
    }

    public void setClosedAt(LocalDateTime closedAt) {
        this.closedAt = closedAt;
    }

    public BigDecimal getOpeningAmount() {
        return openingAmount;
    }

    public void setOpeningAmount(BigDecimal openingAmount) {
        this.openingAmount = openingAmount;
    }

    public BigDecimal getClosingAmount() {
        return closingAmount;
    }

    public void setClosingAmount(BigDecimal closingAmount) {
        this.closingAmount = closingAmount;
    }

    public BigDecimal getExpectedAmount() {
        return expectedAmount;
    }

    public void setExpectedAmount(BigDecimal expectedAmount) {
        this.expectedAmount = expectedAmount;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getVariance() {
        return variance;
    }

    public void setVariance(BigDecimal variance) {
        this.variance = variance;
    }

    public BigDecimal getTotalSales() {
        return totalSales;
    }

    public void setTotalSales(BigDecimal totalSales) {
        this.totalSales = totalSales;
    }

    public BigDecimal getTotalCashSales() {
        return totalCashSales;
    }

    public void setTotalCashSales(BigDecimal totalCashSales) {
        this.totalCashSales = totalCashSales;
    }

    public BigDecimal getTotalRefunds() {
        return totalRefunds;
    }

    public void setTotalRefunds(BigDecimal totalRefunds) {
        this.totalRefunds = totalRefunds;
    }

    public BigDecimal getTotalCashDrops() {
        return totalCashDrops;
    }

    public void setTotalCashDrops(BigDecimal totalCashDrops) {
        this.totalCashDrops = totalCashDrops;
    }

    public BigDecimal getTotalPayouts() {
        return totalPayouts;
    }

    public void setTotalPayouts(BigDecimal totalPayouts) {
        this.totalPayouts = totalPayouts;
    }

    public int getTransactionCount() {
        return transactionCount;
    }

    public void setTransactionCount(int transactionCount) {
        this.transactionCount = transactionCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Utility methods
    public void calculateExpectedAmount() {
        this.expectedAmount = openingAmount
            .add(totalCashSales)
            .subtract(totalRefunds)
            .subtract(totalCashDrops)
            .subtract(totalPayouts);
    }

    public void calculateVariance() {
        this.variance = actualAmount.subtract(expectedAmount);
    }

    public boolean isOpen() {
        return "OPEN".equals(status);
    }

    public boolean isClosed() {
        return "CLOSED".equals(status);
    }

    public boolean isReconciled() {
        return "RECONCILED".equals(status);
    }

    public boolean hasVariance() {
        return variance.compareTo(BigDecimal.ZERO) != 0;
    }

    public boolean isOverage() {
        return variance.compareTo(BigDecimal.ZERO) > 0;
    }

    public boolean isShortage() {
        return variance.compareTo(BigDecimal.ZERO) < 0;
    }

    public String getVarianceType() {
        if (isOverage()) {
            return "Overage";
        } else if (isShortage()) {
            return "Shortage";
        } else {
            return "Balanced";
        }
    }

    public void addCashSale(BigDecimal amount) {
        this.totalCashSales = this.totalCashSales.add(amount);
        this.totalSales = this.totalSales.add(amount);
        this.transactionCount++;
        calculateExpectedAmount();
        this.updatedAt = LocalDateTime.now();
    }

    public void addRefund(BigDecimal amount) {
        this.totalRefunds = this.totalRefunds.add(amount);
        calculateExpectedAmount();
        this.updatedAt = LocalDateTime.now();
    }

    public void addCashDrop(BigDecimal amount) {
        this.totalCashDrops = this.totalCashDrops.add(amount);
        calculateExpectedAmount();
        this.updatedAt = LocalDateTime.now();
    }

    public void addPayout(BigDecimal amount) {
        this.totalPayouts = this.totalPayouts.add(amount);
        calculateExpectedAmount();
        this.updatedAt = LocalDateTime.now();
    }

    public void closeDrawer(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
        this.closedAt = LocalDateTime.now();
        this.status = "CLOSED";
        calculateVariance();
        this.updatedAt = LocalDateTime.now();
    }

    public void reconcile() {
        this.status = "RECONCILED";
        this.updatedAt = LocalDateTime.now();
    }

    public String getShiftDuration() {
        if (openedAt == null) return "Unknown";
        
        LocalDateTime endTime = closedAt != null ? closedAt : LocalDateTime.now();
        long hours = java.time.Duration.between(openedAt, endTime).toHours();
        long minutes = java.time.Duration.between(openedAt, endTime).toMinutes() % 60;
        
        return String.format("%d hours, %d minutes", hours, minutes);
    }

    @Override
    public String toString() {
        return String.format("CashDrawer{drawer=%s, cashier=%s, status=%s, variance=%s}", 
                drawerNumber, cashierName, status, variance);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        CashDrawer that = (CashDrawer) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
