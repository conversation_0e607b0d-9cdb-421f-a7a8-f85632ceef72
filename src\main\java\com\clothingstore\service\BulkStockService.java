package com.clothingstore.service;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Product;
import com.clothingstore.model.StockAdjustment;
import com.clothingstore.model.StockAdjustmentType;

/**
 * Service for bulk stock management operations including CSV import/export
 */
public class BulkStockService {

    private static final Logger LOGGER = Logger.getLogger(BulkStockService.class.getName());
    private static BulkStockService instance;

    private final ProductDAO productDAO;
    private final List<StockAdjustment> stockAdjustments;

    // CSV Headers
    private static final String[] EXPORT_HEADERS = {
        "SKU", "Product Name", "Category", "Current Stock", "Min Level", "Max Level",
        "Cost Price", "Retail Price", "Last Updated", "Status"
    };

    private static final String[] IMPORT_HEADERS = {
        "SKU", "New Stock", "Adjustment Type", "Reason", "Unit Cost", "Notes", "Batch Number"
    };

    private BulkStockService() {
        this.productDAO = ProductDAO.getInstance();
        this.stockAdjustments = new ArrayList<>();
    }

    public static synchronized BulkStockService getInstance() {
        if (instance == null) {
            instance = new BulkStockService();
        }
        return instance;
    }

    /**
     * Export current stock levels to CSV
     */
    public BulkOperationResult exportStockToCSV(String filePath, boolean includeInactive) {
        try {
            List<Product> products = productDAO.findAll();

            if (!includeInactive) {
                products = products.stream()
                        .filter(Product::isActive)
                        .collect(Collectors.toList());
            }

            try (FileWriter writer = new FileWriter(filePath); PrintWriter printWriter = new PrintWriter(writer)) {

                // Write headers
                printWriter.println(String.join(",", EXPORT_HEADERS));

                // Write product data
                for (Product product : products) {
                    List<String> row = Arrays.asList(
                            escapeCSV(product.getSku()),
                            escapeCSV(product.getName()),
                            escapeCSV(product.getCategory()),
                            String.valueOf(product.getStockQuantity()),
                            String.valueOf(product.getMinStockLevel()),
                            String.valueOf(product.getReorderQuantity()), // Using reorder quantity as max level
                            product.getCostPrice() != null ? product.getCostPrice().toString() : "0.00",
                            product.getPrice().toString(),
                            product.getUpdatedAt() != null
                            ? product.getUpdatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")) : "",
                            product.isActive() ? "Active" : "Inactive"
                    );

                    printWriter.println(String.join(",", row));
                }
            }

            String message = String.format("Successfully exported %d products to %s", products.size(), filePath);
            LOGGER.info(message);

            return new BulkOperationResult(true, message, products.size(), 0, new ArrayList<>());

        } catch (Exception e) {
            String errorMessage = "Error exporting stock to CSV: " + e.getMessage();
            LOGGER.severe(errorMessage);
            return new BulkOperationResult(false, errorMessage, 0, 0, Arrays.asList(errorMessage));
        }
    }

    /**
     * Import stock adjustments from CSV
     */
    public BulkOperationResult importStockFromCSV(String filePath, String adjustedBy) {
        List<String> errors = new ArrayList<>();
        List<StockAdjustment> adjustments = new ArrayList<>();
        int successCount = 0;
        int errorCount = 0;

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String headerLine = reader.readLine();

            if (headerLine == null) {
                return new BulkOperationResult(false, "CSV file is empty", 0, 1,
                        Arrays.asList("CSV file is empty"));
            }

            // Validate headers
            String[] headers = headerLine.split(",");
            if (!validateImportHeaders(headers)) {
                return new BulkOperationResult(false, "Invalid CSV headers", 0, 1,
                        Arrays.asList("Expected headers: " + String.join(", ", IMPORT_HEADERS)));
            }

            String line;
            int lineNumber = 1;

            while ((line = reader.readLine()) != null) {
                lineNumber++;

                try {
                    StockAdjustment adjustment = parseCSVLine(line, lineNumber, adjustedBy);

                    if (adjustment != null) {
                        // Apply the adjustment
                        boolean success = applyStockAdjustment(adjustment);

                        if (success) {
                            adjustments.add(adjustment);
                            successCount++;
                        } else {
                            errors.add("Line " + lineNumber + ": Failed to apply adjustment for SKU " + adjustment.getProductSku());
                            errorCount++;
                        }
                    }

                } catch (Exception e) {
                    errors.add("Line " + lineNumber + ": " + e.getMessage());
                    errorCount++;
                }
            }

            // Save all successful adjustments
            stockAdjustments.addAll(adjustments);

            String message = String.format("Import completed: %d successful, %d errors", successCount, errorCount);
            boolean success = errorCount == 0;

            return new BulkOperationResult(success, message, successCount, errorCount, errors);

        } catch (Exception e) {
            String errorMessage = "Error reading CSV file: " + e.getMessage();
            LOGGER.severe(errorMessage);
            return new BulkOperationResult(false, errorMessage, 0, 1, Arrays.asList(errorMessage));
        }
    }

    /**
     * Parse a single CSV line into a StockAdjustment
     */
    private StockAdjustment parseCSVLine(String line, int lineNumber, String adjustedBy) throws Exception {
        String[] fields = line.split(",");

        if (fields.length < IMPORT_HEADERS.length) {
            throw new Exception("Insufficient fields in line");
        }

        String sku = fields[0].trim();
        String newStockStr = fields[1].trim();
        String adjustmentTypeStr = fields[2].trim();
        String reason = fields[3].trim();
        String unitCostStr = fields[4].trim();
        String notes = fields[5].trim();
        String batchNumber = fields[6].trim();

        // Validate required fields
        if (sku.isEmpty()) {
            throw new Exception("SKU is required");
        }

        if (newStockStr.isEmpty()) {
            throw new Exception("New Stock quantity is required");
        }

        // Find product by SKU
        Product product = findProductBySku(sku);
        if (product == null) {
            throw new Exception("Product not found with SKU: " + sku);
        }

        // Parse new stock quantity
        int newStock;
        try {
            newStock = Integer.parseInt(newStockStr);
            if (newStock < 0) {
                throw new Exception("New stock quantity cannot be negative");
            }
        } catch (NumberFormatException e) {
            throw new Exception("Invalid new stock quantity: " + newStockStr);
        }

        // Parse adjustment type
        StockAdjustmentType adjustmentType = parseAdjustmentType(adjustmentTypeStr);

        // Calculate adjustment quantity
        int currentStock = product.getStockQuantity();
        int adjustmentQuantity = newStock - currentStock;

        // Create adjustment record
        StockAdjustment adjustment = new StockAdjustment();
        adjustment.setProductId(product.getId());
        adjustment.setProductSku(product.getSku());
        adjustment.setProductName(product.getName());
        adjustment.setAdjustmentType(adjustmentType);
        adjustment.setPreviousQuantity(currentStock);
        adjustment.setAdjustmentQuantity(adjustmentQuantity);
        adjustment.setNewQuantity(newStock);
        adjustment.setReason(reason.isEmpty() ? "Bulk import" : reason);
        adjustment.setNotes(notes);
        adjustment.setBatchNumber(batchNumber);
        adjustment.setAdjustedBy(adjustedBy);

        // Parse unit cost if provided
        if (!unitCostStr.isEmpty()) {
            try {
                BigDecimal unitCost = new BigDecimal(unitCostStr);
                adjustment.setUnitCost(unitCost);
                adjustment.setTotalValue(unitCost.multiply(new BigDecimal(Math.abs(adjustmentQuantity))));
            } catch (NumberFormatException e) {
                // Log warning but continue
                LOGGER.warning("Invalid unit cost on line " + lineNumber + ": " + unitCostStr);
            }
        }

        return adjustment;
    }

    /**
     * Apply a stock adjustment to the product
     */
    private boolean applyStockAdjustment(StockAdjustment adjustment) {
        try {
            Optional<Product> productOpt = productDAO.findById(adjustment.getProductId());

            if (!productOpt.isPresent()) {
                return false;
            }

            Product product = productOpt.get();

            // Update product stock
            product.setStockQuantity(adjustment.getNewQuantity());
            product.setUpdatedAt(LocalDateTime.now());

            // Update cost price if provided
            if (adjustment.getUnitCost() != null) {
                product.setCostPrice(adjustment.getUnitCost());
            }

            // Save product (using save method as update might not be public)
            productDAO.save(product);

            // Set adjustment ID (simulated)
            adjustment.setId((long) (stockAdjustments.size() + 1));

            return true;

        } catch (Exception e) {
            LOGGER.severe("Error applying stock adjustment: " + e.getMessage());
            return false;
        }
    }

    /**
     * Find product by SKU
     */
    private Product findProductBySku(String sku) {
        try {
            List<Product> products = productDAO.findAll();
            return products.stream()
                    .filter(p -> sku.equalsIgnoreCase(p.getSku()))
                    .findFirst()
                    .orElse(null);
        } catch (Exception e) {
            LOGGER.severe("Error finding product by SKU: " + e.getMessage());
            return null;
        }
    }

    /**
     * Parse adjustment type from string
     */
    private StockAdjustmentType parseAdjustmentType(String typeStr) {
        if (typeStr == null || typeStr.trim().isEmpty()) {
            return StockAdjustmentType.BULK_IMPORT;
        }

        // Try exact match first
        for (StockAdjustmentType type : StockAdjustmentType.values()) {
            if (type.getDisplayName().equalsIgnoreCase(typeStr.trim())
                    || type.name().equalsIgnoreCase(typeStr.trim())) {
                return type;
            }
        }

        // Try partial match
        String lowerTypeStr = typeStr.toLowerCase().trim();
        if (lowerTypeStr.contains("manual")) {
            return StockAdjustmentType.MANUAL_ADJUSTMENT;
        }
        if (lowerTypeStr.contains("count")) {
            return StockAdjustmentType.PHYSICAL_COUNT;
        }
        if (lowerTypeStr.contains("damage")) {
            return StockAdjustmentType.DAMAGE;
        }
        if (lowerTypeStr.contains("theft")) {
            return StockAdjustmentType.THEFT;
        }
        if (lowerTypeStr.contains("expired")) {
            return StockAdjustmentType.EXPIRED;
        }
        if (lowerTypeStr.contains("shipment")) {
            return StockAdjustmentType.RECEIVED_SHIPMENT;
        }

        // Default to bulk import
        return StockAdjustmentType.BULK_IMPORT;
    }

    /**
     * Validate CSV import headers
     */
    private boolean validateImportHeaders(String[] headers) {
        if (headers.length < IMPORT_HEADERS.length) {
            return false;
        }

        for (int i = 0; i < IMPORT_HEADERS.length; i++) {
            if (!IMPORT_HEADERS[i].equalsIgnoreCase(headers[i].trim())) {
                return false;
            }
        }

        return true;
    }

    /**
     * Escape CSV field
     */
    private String escapeCSV(String field) {
        if (field == null) {
            return "";
        }

        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }

        return field;
    }

    /**
     * Generate stock adjustment report
     */
    public List<StockAdjustment> getStockAdjustmentHistory(LocalDateTime startDate, LocalDateTime endDate) {
        return stockAdjustments.stream()
                .filter(adj -> isAdjustmentInPeriod(adj, startDate, endDate))
                .sorted((a1, a2) -> a2.getAdjustmentDate().compareTo(a1.getAdjustmentDate()))
                .collect(Collectors.toList());
    }

    /**
     * Check if adjustment is within date period
     */
    private boolean isAdjustmentInPeriod(StockAdjustment adjustment, LocalDateTime startDate, LocalDateTime endDate) {
        LocalDateTime adjDate = adjustment.getAdjustmentDate();

        if (startDate != null && adjDate.isBefore(startDate)) {
            return false;
        }

        if (endDate != null && adjDate.isAfter(endDate)) {
            return false;
        }

        return true;
    }

    /**
     * Export stock adjustment history to CSV
     */
    public BulkOperationResult exportAdjustmentHistory(String filePath, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            List<StockAdjustment> adjustments = getStockAdjustmentHistory(startDate, endDate);

            try (FileWriter writer = new FileWriter(filePath); PrintWriter printWriter = new PrintWriter(writer)) {

                // Write headers
                String[] headers = {"Date", "SKU", "Product Name", "Type", "Previous Qty",
                    "Adjustment", "New Qty", "Reason", "Adjusted By", "Notes"};
                printWriter.println(String.join(",", headers));

                // Write adjustment data
                for (StockAdjustment adj : adjustments) {
                    List<String> row = Arrays.asList(
                            adj.getAdjustmentDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                            escapeCSV(adj.getProductSku()),
                            escapeCSV(adj.getProductName()),
                            escapeCSV(adj.getAdjustmentType().getDisplayName()),
                            String.valueOf(adj.getPreviousQuantity()),
                            String.valueOf(adj.getAdjustmentQuantity()),
                            String.valueOf(adj.getNewQuantity()),
                            escapeCSV(adj.getReason()),
                            escapeCSV(adj.getAdjustedBy()),
                            escapeCSV(adj.getNotes())
                    );

                    printWriter.println(String.join(",", row));
                }
            }

            String message = String.format("Successfully exported %d adjustments to %s", adjustments.size(), filePath);
            return new BulkOperationResult(true, message, adjustments.size(), 0, new ArrayList<>());

        } catch (Exception e) {
            String errorMessage = "Error exporting adjustment history: " + e.getMessage();
            LOGGER.severe(errorMessage);
            return new BulkOperationResult(false, errorMessage, 0, 1, Arrays.asList(errorMessage));
        }
    }

    /**
     * Perform bulk stock adjustment for multiple products
     */
    public BulkOperationResult bulkAdjustStock(List<BulkStockAdjustment> adjustments, String adjustedBy) {
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int errorCount = 0;

        for (BulkStockAdjustment bulkAdj : adjustments) {
            try {
                Optional<Product> productOpt = productDAO.findById(bulkAdj.getProductId());

                if (!productOpt.isPresent()) {
                    errors.add("Product not found with ID: " + bulkAdj.getProductId());
                    errorCount++;
                    continue;
                }

                Product product = productOpt.get();

                // Create stock adjustment record
                StockAdjustment adjustment = new StockAdjustment();
                adjustment.setProductId(product.getId());
                adjustment.setProductSku(product.getSku());
                adjustment.setProductName(product.getName());
                adjustment.setAdjustmentType(bulkAdj.getAdjustmentType());
                adjustment.setPreviousQuantity(product.getStockQuantity());
                adjustment.setAdjustmentQuantity(bulkAdj.getAdjustmentQuantity());
                adjustment.setNewQuantity(product.getStockQuantity() + bulkAdj.getAdjustmentQuantity());
                adjustment.setReason(bulkAdj.getReason());
                adjustment.setNotes(bulkAdj.getNotes());
                adjustment.setAdjustedBy(adjustedBy);

                // Apply adjustment
                if (applyStockAdjustment(adjustment)) {
                    successCount++;
                } else {
                    errors.add("Failed to apply adjustment for product: " + product.getSku());
                    errorCount++;
                }

            } catch (Exception e) {
                errors.add("Error processing adjustment for product ID " + bulkAdj.getProductId() + ": " + e.getMessage());
                errorCount++;
            }
        }

        String message = String.format("Bulk adjustment completed: %d successful, %d errors", successCount, errorCount);
        boolean success = errorCount == 0;

        return new BulkOperationResult(success, message, successCount, errorCount, errors);
    }

    /**
     * Get CSV template for stock import
     */
    public String generateImportTemplate(String filePath) {
        try (FileWriter writer = new FileWriter(filePath); PrintWriter printWriter = new PrintWriter(writer)) {

            // Write headers
            printWriter.println(String.join(",", IMPORT_HEADERS));

            // Write sample data
            printWriter.println("SAMPLE001,50,Manual Adjustment,Physical count correction,12.50,Counted during inventory,BATCH001");
            printWriter.println("SAMPLE002,0,Damage,Water damage from leak,,Damaged beyond repair,");
            printWriter.println("SAMPLE003,100,Received Shipment,New stock arrival,15.00,Weekly delivery,BATCH002");

            return filePath;

        } catch (Exception e) {
            LOGGER.severe("Error generating import template: " + e.getMessage());
            return null;
        }
    }
}

/**
 * Result class for bulk operations
 */
class BulkOperationResult {

    private final boolean success;
    private final String message;
    private final int successCount;
    private final int errorCount;
    private final List<String> errors;

    public BulkOperationResult(boolean success, String message, int successCount, int errorCount, List<String> errors) {
        this.success = success;
        this.message = message;
        this.successCount = successCount;
        this.errorCount = errorCount;
        this.errors = errors != null ? errors : new ArrayList<>();
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public int getErrorCount() {
        return errorCount;
    }

    public List<String> getErrors() {
        return errors;
    }

    public boolean hasErrors() {
        return !errors.isEmpty();
    }

    public int getTotalProcessed() {
        return successCount + errorCount;
    }
}

/**
 * Class for bulk stock adjustment data
 */
class BulkStockAdjustment {

    private Long productId;
    private int adjustmentQuantity;
    private StockAdjustmentType adjustmentType;
    private String reason;
    private String notes;

    public BulkStockAdjustment(Long productId, int adjustmentQuantity, StockAdjustmentType adjustmentType, String reason) {
        this.productId = productId;
        this.adjustmentQuantity = adjustmentQuantity;
        this.adjustmentType = adjustmentType;
        this.reason = reason;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public int getAdjustmentQuantity() {
        return adjustmentQuantity;
    }

    public void setAdjustmentQuantity(int adjustmentQuantity) {
        this.adjustmentQuantity = adjustmentQuantity;
    }

    public StockAdjustmentType getAdjustmentType() {
        return adjustmentType;
    }

    public void setAdjustmentType(StockAdjustmentType adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
}
