<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.MonthlySalesReportController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="📊 Monthly Sales Report">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <ComboBox fx:id="cmbMonth" onAction="#handleMonthChange" promptText="Select Month" />
            <ComboBox fx:id="cmbYear" onAction="#handleYearChange" promptText="Select Year" />
            <Button fx:id="btnCurrentMonth" onAction="#handleCurrentMonth" text="📅 Current Month" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
            <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </HBox>

      <!-- Monthly Summary -->
      <GridPane hgap="15.0" vgap="15.0" styleClass="form-container">
         <columnConstraints>
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         <rowConstraints>
            <RowConstraints />
            <RowConstraints />
         </rowConstraints>
         <children>
            <VBox spacing="5.0" styleClass="metric-card success" GridPane.columnIndex="0" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="metric-title" text="💰 Total Revenue" />
                  <Label fx:id="lblTotalRevenue" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card info" GridPane.columnIndex="1" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="metric-title" text="🛒 Total Transactions" />
                  <Label fx:id="lblTotalTransactions" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card primary" GridPane.columnIndex="2" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="metric-title" text="📊 Avg Daily Sales" />
                  <Label fx:id="lblAvgDailySales" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card warning" GridPane.columnIndex="3" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="metric-title" text="👥 Unique Customers" />
                  <Label fx:id="lblUniqueCustomers" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card secondary" GridPane.columnIndex="0" GridPane.rowIndex="1">
               <children>
                  <Label styleClass="metric-title" text="📈 Growth vs Last Month" />
                  <Label fx:id="lblGrowthRate" styleClass="metric-value" text="0%" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="1" GridPane.rowIndex="1">
               <children>
                  <Label styleClass="metric-title" text="🏆 Best Day" />
                  <Label fx:id="lblBestDay" styleClass="metric-value" text="-" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="2" GridPane.rowIndex="1">
               <children>
                  <Label styleClass="metric-title" text="💎 Best Day Sales" />
                  <Label fx:id="lblBestDaySales" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="3" GridPane.rowIndex="1">
               <children>
                  <Label styleClass="metric-title" text="📦 Items Sold" />
                  <Label fx:id="lblTotalItemsSold" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </GridPane>

      <!-- Daily Breakdown -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="section-title" text="📅 Daily Sales Breakdown">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            <TableView fx:id="tblDailySales" prefHeight="250.0">
               <columns>
                  <TableColumn fx:id="colDate" prefWidth="100.0" text="Date" />
                  <TableColumn fx:id="colDayOfWeek" prefWidth="100.0" text="Day" />
                  <TableColumn fx:id="colDailyTransactions" prefWidth="100.0" text="Transactions" />
                  <TableColumn fx:id="colDailySales" prefWidth="120.0" text="Sales" />
                  <TableColumn fx:id="colDailyAvg" prefWidth="120.0" text="Avg Transaction" />
                  <TableColumn fx:id="colDailyCustomers" prefWidth="100.0" text="Customers" />
                  <TableColumn fx:id="colDailyItems" prefWidth="100.0" text="Items Sold" />
               </columns>
            </TableView>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Top Products and Categories -->
      <HBox spacing="15.0" styleClass="form-container">
         <children>
            <VBox spacing="10.0" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="section-title" text="🏆 Top Products This Month">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <TableView fx:id="tblTopProducts" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="colProductRank" prefWidth="40.0" text="Rank" />
                        <TableColumn fx:id="colProductName" prefWidth="150.0" text="Product" />
                        <TableColumn fx:id="colQuantitySold" prefWidth="80.0" text="Qty Sold" />
                        <TableColumn fx:id="colProductRevenue" prefWidth="100.0" text="Revenue" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
            <VBox spacing="10.0" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="section-title" text="📊 Top Categories This Month">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <TableView fx:id="tblTopCategories" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="colCategoryRank" prefWidth="40.0" text="Rank" />
                        <TableColumn fx:id="colCategoryName" prefWidth="120.0" text="Category" />
                        <TableColumn fx:id="colCategoryItems" prefWidth="80.0" text="Items Sold" />
                        <TableColumn fx:id="colCategoryRevenue" prefWidth="100.0" text="Revenue" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
