package com.clothingstore.util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Logger;

import com.clothingstore.database.DatabaseManager;

/**
 * Database schema updater for detailed refund item tracking Creates tables to
 * track individual product refunds with quantities
 */
public class RefundItemTrackingSchemaUpdater {

    private static final Logger LOGGER = Logger.getLogger(RefundItemTrackingSchemaUpdater.class.getName());

    /**
     * Update database schema to support detailed refund item tracking
     */
    public static void updateSchemaForRefundItemTracking() throws SQLException {
        LOGGER.info("Starting refund item tracking database schema updates...");

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Create refund item tracking table
            createRefundItemTrackingTable(conn);

            // Add refund tracking columns to transaction_items table
            addRefundTrackingColumnsToTransactionItems(conn);

            // Create indexes for performance
            createRefundTrackingIndexes(conn);

            LOGGER.info("Refund item tracking database schema updates completed successfully");
        }
    }

    /**
     * Create the refund_item_tracking table to track detailed refund history
     */
    private static void createRefundItemTrackingTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS refund_item_tracking ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                + "original_transaction_id INTEGER NOT NULL, "
                + "original_transaction_item_id INTEGER NOT NULL, "
                + "refund_transaction_id INTEGER NOT NULL, "
                + "product_id INTEGER NOT NULL, "
                + "product_name TEXT NOT NULL, "
                + "product_sku TEXT, "
                + "original_quantity INTEGER NOT NULL, "
                + "refunded_quantity INTEGER NOT NULL, "
                + "unit_price DECIMAL(10,2) NOT NULL, "
                + "refund_amount DECIMAL(10,2) NOT NULL, "
                + "refund_reason TEXT, "
                + "cashier_name TEXT, "
                + "refund_date DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "FOREIGN KEY (original_transaction_id) REFERENCES transactions (id), "
                + "FOREIGN KEY (original_transaction_item_id) REFERENCES transaction_items (id), "
                + "FOREIGN KEY (refund_transaction_id) REFERENCES transactions (id), "
                + "FOREIGN KEY (product_id) REFERENCES products (id)"
                + ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            LOGGER.info("Created refund_item_tracking table");
        }
    }

    /**
     * Add refund tracking columns to transaction_items table
     */
    private static void addRefundTrackingColumnsToTransactionItems(Connection conn) throws SQLException {
        // Add total_refunded_quantity column
        if (!columnExists(conn, "transaction_items", "total_refunded_quantity")) {
            LOGGER.info("Adding total_refunded_quantity column to transaction_items table");
            try (PreparedStatement stmt = conn.prepareStatement(
                    "ALTER TABLE transaction_items ADD COLUMN total_refunded_quantity INTEGER DEFAULT 0")) {
                stmt.executeUpdate();
                LOGGER.info("Successfully added total_refunded_quantity column");
            }
        } else {
            LOGGER.info("total_refunded_quantity column already exists");
        }

        // Add remaining_quantity column (calculated field for convenience)
        if (!columnExists(conn, "transaction_items", "remaining_quantity")) {
            LOGGER.info("Adding remaining_quantity column to transaction_items table");
            try (PreparedStatement stmt = conn.prepareStatement(
                    "ALTER TABLE transaction_items ADD COLUMN remaining_quantity INTEGER DEFAULT 0")) {
                stmt.executeUpdate();
                LOGGER.info("Successfully added remaining_quantity column");
            }
        } else {
            LOGGER.info("remaining_quantity column already exists");
        }

        // Update existing records to set remaining_quantity = quantity - total_refunded_quantity
        updateExistingRemainingQuantities(conn);
    }

    /**
     * Update existing transaction items to calculate remaining quantities
     */
    private static void updateExistingRemainingQuantities(Connection conn) throws SQLException {
        String sql = "UPDATE transaction_items SET "
                + "total_refunded_quantity = COALESCE(total_refunded_quantity, 0), "
                + "remaining_quantity = quantity - COALESCE(total_refunded_quantity, 0) "
                + "WHERE remaining_quantity IS NULL OR remaining_quantity = 0";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            int updated = stmt.executeUpdate();
            LOGGER.info("Updated remaining quantities for " + updated + " transaction items");
        }
    }

    /**
     * Create indexes for refund tracking performance
     */
    private static void createRefundTrackingIndexes(Connection conn) throws SQLException {
        String[] indexes = {
            "CREATE INDEX IF NOT EXISTS idx_refund_item_tracking_original_transaction "
            + "ON refund_item_tracking (original_transaction_id)",
            "CREATE INDEX IF NOT EXISTS idx_refund_item_tracking_original_item "
            + "ON refund_item_tracking (original_transaction_item_id)",
            "CREATE INDEX IF NOT EXISTS idx_refund_item_tracking_product "
            + "ON refund_item_tracking (product_id)",
            "CREATE INDEX IF NOT EXISTS idx_refund_item_tracking_refund_date "
            + "ON refund_item_tracking (refund_date)",
            "CREATE INDEX IF NOT EXISTS idx_transaction_items_refund_tracking "
            + "ON transaction_items (total_refunded_quantity, remaining_quantity)"
        };

        try (Statement stmt = conn.createStatement()) {
            for (String indexSql : indexes) {
                stmt.execute(indexSql);
            }
            LOGGER.info("Created refund tracking performance indexes");
        }
    }

    /**
     * Check if a column exists in a table
     */
    private static boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
        String sql = "PRAGMA table_info(" + tableName + ")";
        try (PreparedStatement stmt = conn.prepareStatement(sql); ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                if (columnName.equals(rs.getString("name"))) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * Verify refund item tracking schema is properly set up
     */
    public static boolean verifyRefundItemTrackingSchema() {
        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Check if refund_item_tracking table exists
            if (!tableExists(conn, "refund_item_tracking")) {
                LOGGER.severe("refund_item_tracking table does not exist");
                return false;
            }

            // Check if required columns exist in transaction_items
            if (!columnExists(conn, "transaction_items", "total_refunded_quantity")) {
                LOGGER.severe("total_refunded_quantity column missing from transaction_items");
                return false;
            }

            if (!columnExists(conn, "transaction_items", "remaining_quantity")) {
                LOGGER.severe("remaining_quantity column missing from transaction_items");
                return false;
            }

            LOGGER.info("Refund item tracking schema verification passed");
            return true;

        } catch (SQLException e) {
            LOGGER.severe("Error verifying refund item tracking schema: " + e.getMessage());
            return false;
        }
    }

    /**
     * Check if a table exists
     */
    private static boolean tableExists(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, tableName);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        }
    }
}
