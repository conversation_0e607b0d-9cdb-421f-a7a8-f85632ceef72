package com.clothingstore.model;

import java.time.LocalDateTime;

/**
 * Category model class representing product categories in the inventory
 */
public class Category {
    private Long id;
    private String name;
    private String description;
    private int displayOrder;
    private boolean active;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public Category() {
        this.active = true;
        this.displayOrder = 0;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public Category(String name) {
        this();
        this.name = name;
    }

    public Category(String name, String description) {
        this(name);
        this.description = description;
    }

    public Category(String name, String description, int displayOrder) {
        this(name, description);
        this.displayOrder = displayOrder;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.updatedAt = LocalDateTime.now();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }

    public int getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(int displayOrder) {
        this.displayOrder = displayOrder;
        this.updatedAt = LocalDateTime.now();
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
        this.updatedAt = LocalDateTime.now();
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Utility methods
    @Override
    public String toString() {
        return name;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Category category = (Category) obj;
        return id != null ? id.equals(category.id) : category.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    /**
     * Check if this category can be deleted
     * @return true if the category can be safely deleted
     */
    public boolean canBeDeleted() {
        // Categories can be deleted if they're not active or if no products use them
        // This will be checked by the DAO when attempting deletion
        return true;
    }

    /**
     * Get display name for UI components
     * @return formatted display name
     */
    public String getDisplayName() {
        if (description != null && !description.trim().isEmpty()) {
            return name + " - " + description;
        }
        return name;
    }

    /**
     * Create a copy of this category
     * @return new Category instance with same data (except ID)
     */
    public Category copy() {
        Category copy = new Category();
        copy.setName(this.name);
        copy.setDescription(this.description);
        copy.setDisplayOrder(this.displayOrder);
        copy.setActive(this.active);
        return copy;
    }
}
