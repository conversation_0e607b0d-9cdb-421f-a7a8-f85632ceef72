package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Data Access Object for Sales Analytics operations
 */
public class SalesAnalyticsDAO {
    private static final Logger LOGGER = Logger.getLogger(SalesAnalyticsDAO.class.getName());
    private static SalesAnalyticsDAO instance;

    private SalesAnalyticsDAO() {
        // Private constructor for singleton
    }

    public static synchronized SalesAnalyticsDAO getInstance() {
        if (instance == null) {
            instance = new SalesAnalyticsDAO();
        }
        return instance;
    }

    /**
     * Get sales summary for a date range
     */
    public SalesSummary getSalesSummary(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT " +
                "COUNT(*) as transaction_count, " +
                "SUM(total) as total_revenue, " +
                "AVG(total) as avg_transaction_value, " +
                "SUM(CASE WHEN payment_method = 'CASH' THEN total ELSE 0 END) as cash_sales, " +
                "SUM(CASE WHEN payment_method = 'CARD' THEN total ELSE 0 END) as card_sales " +
                "FROM transactions WHERE transaction_date BETWEEN ? AND ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return new SalesSummary(
                            rs.getInt("transaction_count"),
                            rs.getBigDecimal("total_revenue"),
                            rs.getBigDecimal("avg_transaction_value"),
                            rs.getBigDecimal("cash_sales"),
                            rs.getBigDecimal("card_sales")
                    );
                }
            }
        }
        return new SalesSummary(0, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
    }

    /**
     * Get daily sales data for a date range
     */
    public List<DailySalesData> getDailySalesData(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT " +
                "DATE(transaction_date) as sale_date, " +
                "COUNT(*) as transaction_count, " +
                "SUM(total) as total_revenue, " +
                "AVG(total) as avg_transaction_value " +
                "FROM transactions " +
                "WHERE transaction_date BETWEEN ? AND ? " +
                "GROUP BY DATE(transaction_date) " +
                "ORDER BY sale_date";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            List<DailySalesData> dailyData = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    dailyData.add(new DailySalesData(
                            rs.getDate("sale_date").toLocalDate(),
                            rs.getInt("transaction_count"),
                            rs.getBigDecimal("total_revenue"),
                            rs.getBigDecimal("avg_transaction_value")
                    ));
                }
            }
            return dailyData;
        }
    }

    /**
     * Get top selling products
     */
    public List<TopProductData> getTopSellingProducts(LocalDateTime startDate, LocalDateTime endDate, int limit) throws SQLException {
        String sql = "SELECT " +
                "p.name as product_name, " +
                "p.sku as product_sku, " +
                "SUM(ti.quantity) as total_quantity, " +
                "SUM(ti.line_total) as total_revenue, " +
                "COUNT(DISTINCT t.id) as transaction_count " +
                "FROM transaction_items ti " +
                "JOIN transactions t ON ti.transaction_id = t.id " +
                "JOIN products p ON ti.product_id = p.id " +
                "WHERE t.transaction_date BETWEEN ? AND ? " +
                "GROUP BY p.id, p.name, p.sku " +
                "ORDER BY total_revenue DESC " +
                "LIMIT ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));
            pstmt.setInt(3, limit);

            List<TopProductData> topProducts = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    topProducts.add(new TopProductData(
                            rs.getString("product_name"),
                            rs.getString("product_sku"),
                            rs.getInt("total_quantity"),
                            rs.getBigDecimal("total_revenue"),
                            rs.getInt("transaction_count")
                    ));
                }
            }
            return topProducts;
        }
    }

    /**
     * Get sales by category
     */
    public List<CategorySalesData> getSalesByCategory(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT " +
                "COALESCE(p.category, 'Uncategorized') as category, " +
                "SUM(ti.quantity) as total_quantity, " +
                "SUM(ti.line_total) as total_revenue, " +
                "COUNT(DISTINCT t.id) as transaction_count " +
                "FROM transaction_items ti " +
                "JOIN transactions t ON ti.transaction_id = t.id " +
                "JOIN products p ON ti.product_id = p.id " +
                "WHERE t.transaction_date BETWEEN ? AND ? " +
                "GROUP BY p.category " +
                "ORDER BY total_revenue DESC";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            List<CategorySalesData> categoryData = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    categoryData.add(new CategorySalesData(
                            rs.getString("category"),
                            rs.getInt("total_quantity"),
                            rs.getBigDecimal("total_revenue"),
                            rs.getInt("transaction_count")
                    ));
                }
            }
            return categoryData;
        }
    }

    /**
     * Get hourly sales distribution
     */
    public Map<Integer, BigDecimal> getHourlySalesDistribution(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT " +
                "CAST(strftime('%H', transaction_date) AS INTEGER) as hour, " +
                "SUM(total) as total_revenue " +
                "FROM transactions " +
                "WHERE transaction_date BETWEEN ? AND ? " +
                "GROUP BY CAST(strftime('%H', transaction_date) AS INTEGER) " +
                "ORDER BY hour";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            Map<Integer, BigDecimal> hourlyData = new HashMap<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    hourlyData.put(rs.getInt("hour"), rs.getBigDecimal("total_revenue"));
                }
            }
            return hourlyData;
        }
    }

    /**
     * Get payment method distribution
     */
    public Map<String, PaymentMethodData> getPaymentMethodDistribution(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT " +
                "payment_method, " +
                "COUNT(*) as transaction_count, " +
                "SUM(total) as total_amount " +
                "FROM transactions " +
                "WHERE transaction_date BETWEEN ? AND ? " +
                "GROUP BY payment_method " +
                "ORDER BY total_amount DESC";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            Map<String, PaymentMethodData> paymentData = new HashMap<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    paymentData.put(rs.getString("payment_method"), 
                        new PaymentMethodData(
                            rs.getString("payment_method"),
                            rs.getInt("transaction_count"),
                            rs.getBigDecimal("total_amount")
                        ));
                }
            }
            return paymentData;
        }
    }

    /**
     * Get customer purchase frequency
     */
    public List<CustomerFrequencyData> getCustomerPurchaseFrequency(LocalDateTime startDate, LocalDateTime endDate, int limit) throws SQLException {
        String sql = "SELECT " +
                "c.first_name || ' ' || c.last_name as customer_name, " +
                "COUNT(*) as purchase_count, " +
                "SUM(t.total) as total_spent, " +
                "AVG(t.total) as avg_order_value " +
                "FROM transactions t " +
                "JOIN customers c ON t.customer_id = c.id " +
                "WHERE t.transaction_date BETWEEN ? AND ? " +
                "GROUP BY c.id, customer_name " +
                "ORDER BY total_spent DESC " +
                "LIMIT ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));
            pstmt.setInt(3, limit);

            List<CustomerFrequencyData> customerData = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    customerData.add(new CustomerFrequencyData(
                            rs.getString("customer_name"),
                            rs.getInt("purchase_count"),
                            rs.getBigDecimal("total_spent"),
                            rs.getBigDecimal("avg_order_value")
                    ));
                }
            }
            return customerData;
        }
    }

    // Data classes for analytics results
    public static class SalesSummary {
        private final int transactionCount;
        private final BigDecimal totalRevenue;
        private final BigDecimal avgTransactionValue;
        private final BigDecimal cashSales;
        private final BigDecimal cardSales;

        public SalesSummary(int transactionCount, BigDecimal totalRevenue, BigDecimal avgTransactionValue, 
                           BigDecimal cashSales, BigDecimal cardSales) {
            this.transactionCount = transactionCount;
            this.totalRevenue = totalRevenue != null ? totalRevenue : BigDecimal.ZERO;
            this.avgTransactionValue = avgTransactionValue != null ? avgTransactionValue : BigDecimal.ZERO;
            this.cashSales = cashSales != null ? cashSales : BigDecimal.ZERO;
            this.cardSales = cardSales != null ? cardSales : BigDecimal.ZERO;
        }

        // Getters
        public int getTransactionCount() { return transactionCount; }
        public BigDecimal getTotalRevenue() { return totalRevenue; }
        public BigDecimal getAvgTransactionValue() { return avgTransactionValue; }
        public BigDecimal getCashSales() { return cashSales; }
        public BigDecimal getCardSales() { return cardSales; }
    }

    public static class DailySalesData {
        private final LocalDate date;
        private final int transactionCount;
        private final BigDecimal totalRevenue;
        private final BigDecimal avgTransactionValue;

        public DailySalesData(LocalDate date, int transactionCount, BigDecimal totalRevenue, BigDecimal avgTransactionValue) {
            this.date = date;
            this.transactionCount = transactionCount;
            this.totalRevenue = totalRevenue != null ? totalRevenue : BigDecimal.ZERO;
            this.avgTransactionValue = avgTransactionValue != null ? avgTransactionValue : BigDecimal.ZERO;
        }

        // Getters
        public LocalDate getDate() { return date; }
        public int getTransactionCount() { return transactionCount; }
        public BigDecimal getTotalRevenue() { return totalRevenue; }
        public BigDecimal getAvgTransactionValue() { return avgTransactionValue; }
    }

    public static class TopProductData {
        private final String productName;
        private final String productSku;
        private final int totalQuantity;
        private final BigDecimal totalRevenue;
        private final int transactionCount;

        public TopProductData(String productName, String productSku, int totalQuantity, 
                             BigDecimal totalRevenue, int transactionCount) {
            this.productName = productName;
            this.productSku = productSku;
            this.totalQuantity = totalQuantity;
            this.totalRevenue = totalRevenue != null ? totalRevenue : BigDecimal.ZERO;
            this.transactionCount = transactionCount;
        }

        // Getters
        public String getProductName() { return productName; }
        public String getProductSku() { return productSku; }
        public int getTotalQuantity() { return totalQuantity; }
        public BigDecimal getTotalRevenue() { return totalRevenue; }
        public int getTransactionCount() { return transactionCount; }
    }

    public static class CategorySalesData {
        private final String category;
        private final int totalQuantity;
        private final BigDecimal totalRevenue;
        private final int transactionCount;

        public CategorySalesData(String category, int totalQuantity, BigDecimal totalRevenue, int transactionCount) {
            this.category = category;
            this.totalQuantity = totalQuantity;
            this.totalRevenue = totalRevenue != null ? totalRevenue : BigDecimal.ZERO;
            this.transactionCount = transactionCount;
        }

        // Getters
        public String getCategory() { return category; }
        public int getTotalQuantity() { return totalQuantity; }
        public BigDecimal getTotalRevenue() { return totalRevenue; }
        public int getTransactionCount() { return transactionCount; }
    }

    public static class PaymentMethodData {
        private final String paymentMethod;
        private final int transactionCount;
        private final BigDecimal totalAmount;

        public PaymentMethodData(String paymentMethod, int transactionCount, BigDecimal totalAmount) {
            this.paymentMethod = paymentMethod;
            this.transactionCount = transactionCount;
            this.totalAmount = totalAmount != null ? totalAmount : BigDecimal.ZERO;
        }

        // Getters
        public String getPaymentMethod() { return paymentMethod; }
        public int getTransactionCount() { return transactionCount; }
        public BigDecimal getTotalAmount() { return totalAmount; }
    }

    public static class CustomerFrequencyData {
        private final String customerName;
        private final int purchaseCount;
        private final BigDecimal totalSpent;
        private final BigDecimal avgOrderValue;

        public CustomerFrequencyData(String customerName, int purchaseCount, BigDecimal totalSpent, BigDecimal avgOrderValue) {
            this.customerName = customerName;
            this.purchaseCount = purchaseCount;
            this.totalSpent = totalSpent != null ? totalSpent : BigDecimal.ZERO;
            this.avgOrderValue = avgOrderValue != null ? avgOrderValue : BigDecimal.ZERO;
        }

        // Getters
        public String getCustomerName() { return customerName; }
        public int getPurchaseCount() { return purchaseCount; }
        public BigDecimal getTotalSpent() { return totalSpent; }
        public BigDecimal getAvgOrderValue() { return avgOrderValue; }
    }
}
