package com.clothingstore.model;

/**
 * Result class for refund validation operations
 */
public class RefundValidationResult {
    private boolean valid;
    private String errorMessage;
    private String warningMessage;

    public RefundValidationResult() {}

    public RefundValidationResult(boolean valid, String errorMessage) {
        this.valid = valid;
        this.errorMessage = errorMessage;
    }

    public RefundValidationResult(boolean valid, String errorMessage, String warningMessage) {
        this.valid = valid;
        this.errorMessage = errorMessage;
        this.warningMessage = warningMessage;
    }

    // Getters and Setters
    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getWarningMessage() {
        return warningMessage;
    }

    public void setWarningMessage(String warningMessage) {
        this.warningMessage = warningMessage;
    }

    public boolean hasWarning() {
        return warningMessage != null && !warningMessage.trim().isEmpty();
    }

    @Override
    public String toString() {
        return String.format("RefundValidationResult{valid=%s, errorMessage='%s', warningMessage='%s'}", 
                           valid, errorMessage, warningMessage);
    }
}
