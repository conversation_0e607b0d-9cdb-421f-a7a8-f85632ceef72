<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.CategoryManagementController" spacing="15.0">
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="15.0">
         <children>
            <Label text="Category Management">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnAddCategory" onAction="#handleAddCategory" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold;" text="+ Add Category" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #3498db; -fx-text-fill: white;" text="Refresh" />
         </children>
      </HBox>

      <!-- Category Statistics -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1;">
         <children>
            <Label fx:id="lblTotalCategories" text="Total Categories: 0" />
            <Label fx:id="lblActiveCategories" text="Active: 0" />
            <Label fx:id="lblTotalProducts" text="Total Products: 0" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnReorderCategories" onAction="#handleReorderCategories" style="-fx-background-color: #f39c12; -fx-text-fill: white;" text="Reorder Categories" />
         </children>
      </HBox>

      <!-- Categories Table -->
      <TableView fx:id="tblCategories" VBox.vgrow="ALWAYS">
         <columns>
            <TableColumn fx:id="colOrder" prefWidth="60.0" text="Order" />
            <TableColumn fx:id="colName" prefWidth="150.0" text="Category Name" />
            <TableColumn fx:id="colDescription" prefWidth="200.0" text="Description" />
            <TableColumn fx:id="colProductCount" prefWidth="100.0" text="Products" />
            <TableColumn fx:id="colActive" prefWidth="80.0" text="Active" />
            <TableColumn fx:id="colCreated" prefWidth="120.0" text="Created" />
            <TableColumn fx:id="colActions" prefWidth="150.0" text="Actions" />
         </columns>
         <contextMenu>
            <ContextMenu>
               <items>
                  <MenuItem fx:id="menuEdit" onAction="#handleEditCategory" text="Edit Category" />
                  <MenuItem fx:id="menuToggleActive" onAction="#handleToggleActive" text="Toggle Active/Inactive" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuMoveUp" onAction="#handleMoveUp" text="Move Up" />
                  <MenuItem fx:id="menuMoveDown" onAction="#handleMoveDown" text="Move Down" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuDelete" onAction="#handleDeleteCategory" text="Delete Category" />
               </items>
            </ContextMenu>
         </contextMenu>
      </TableView>

      <!-- Bottom Actions -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label text="Selected Category:" />
            <Label fx:id="lblSelectedCategory" text="None" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnEditSelected" disable="true" onAction="#handleEditCategory" style="-fx-background-color: #3498db; -fx-text-fill: white;" text="Edit" />
            <Button fx:id="btnDeleteSelected" disable="true" onAction="#handleDeleteCategory" style="-fx-background-color: #e74c3c; -fx-text-fill: white;" text="Delete" />
            <Button fx:id="btnClose" onAction="#handleClose" text="Close" />
         </children>
      </HBox>
   </children>
</VBox>
