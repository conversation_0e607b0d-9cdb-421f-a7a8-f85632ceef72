package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

import com.clothingstore.model.TaxRate;
import com.clothingstore.model.TaxType;
import com.clothingstore.service.TaxService;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;

/**
 * Controller for Tax Management interface
 */
public class TaxManagementController implements Initializable {

    // FXML Components - Table and Selection
    @FXML
    private TableView<TaxRate> taxRateTable;
    @FXML
    private TableColumn<TaxRate, String> nameColumn;
    @FXML
    private TableColumn<TaxRate, String> typeColumn;
    @FXML
    private TableColumn<TaxRate, String> rateColumn;
    @FXML
    private TableColumn<TaxRate, String> jurisdictionColumn;
    @FXML
    private TableColumn<TaxRate, String> statusColumn;
    @FXML
    private TableColumn<TaxRate, String> validityColumn;

    // FXML Components - Form Fields
    @FXML
    private TextField nameField;
    @FXML
    private TextArea descriptionField;
    @FXML
    private ComboBox<TaxType> typeComboBox;
    @FXML
    private TextField rateField;
    @FXML
    private TextField jurisdictionField;
    @FXML
    private TextField applicableCategoriesField;
    @FXML
    private TextField exemptCategoriesField;
    @FXML
    private TextField minimumAmountField;
    @FXML
    private TextField maximumAmountField;
    @FXML
    private DatePicker effectiveDatePicker;
    @FXML
    private DatePicker expirationDatePicker;
    @FXML
    private CheckBox activeCheckBox;

    // FXML Components - Buttons
    @FXML
    private Button addTaxRateButton;
    @FXML
    private Button editTaxRateButton;
    @FXML
    private Button deleteTaxRateButton;
    @FXML
    private Button saveTaxRateButton;
    @FXML
    private Button cancelTaxRateButton;
    @FXML
    private Button testTaxRateButton;

    // FXML Components - Search and Filter
    @FXML
    private TextField searchField;
    @FXML
    private ComboBox<String> statusFilter;
    @FXML
    private ComboBox<TaxType> typeFilter;

    // FXML Components - Info Labels
    @FXML
    private Label taxRateCountLabel;
    @FXML
    private Label selectionInfoLabel;

    // Tax Exempt Customers Section
    @FXML
    private TableView<String> exemptCustomersTable;
    @FXML
    private TableColumn<String, String> exemptCustomerColumn;
    @FXML
    private TextField exemptCustomerField;
    @FXML
    private Button addExemptCustomerButton;
    @FXML
    private Button removeExemptCustomerButton;

    // Data and Services
    private TaxService taxService;
    private ObservableList<TaxRate> allTaxRates;
    private ObservableList<TaxRate> filteredTaxRates;
    private ObservableList<String> exemptCustomers;
    private TaxRate selectedTaxRate;
    private boolean isEditMode = false;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateFormatter;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeServices();
        initializeFormatters();
        initializeTableColumns();
        initializeComboBoxes();
        initializeEventHandlers();
        loadTaxRates();
        loadExemptCustomers();
        setFormEditMode(false);
    }

    private void initializeServices() {
        taxService = TaxService.getInstance();
        allTaxRates = FXCollections.observableArrayList();
        filteredTaxRates = FXCollections.observableArrayList();
        exemptCustomers = FXCollections.observableArrayList();
        taxRateTable.setItems(filteredTaxRates);
        exemptCustomersTable.setItems(exemptCustomers);
    }

    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy");
    }

    private void initializeTableColumns() {
        nameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));

        typeColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getType().getDisplayName()));

        rateColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getRate() + "%"));

        jurisdictionColumn.setCellValueFactory(cellData -> {
            String jurisdiction = cellData.getValue().getJurisdiction();
            return new SimpleStringProperty(jurisdiction != null ? jurisdiction : "All");
        });

        statusColumn.setCellValueFactory(cellData -> {
            TaxRate taxRate = cellData.getValue();
            String status;
            if (!taxRate.isActive()) {
                status = "Inactive";
            } else if (taxRate.isValid()) {
                status = "Active";
            } else {
                status = "Invalid";
            }
            return new SimpleStringProperty(status);
        });

        validityColumn.setCellValueFactory(cellData -> {
            TaxRate taxRate = cellData.getValue();
            String validity = "Always Valid";
            if (taxRate.getEffectiveDate() != null && taxRate.getExpirationDate() != null) {
                validity = taxRate.getEffectiveDate().format(dateFormatter) + " - "
                        + taxRate.getExpirationDate().format(dateFormatter);
            } else if (taxRate.getExpirationDate() != null) {
                validity = "Until " + taxRate.getExpirationDate().format(dateFormatter);
            }
            return new SimpleStringProperty(validity);
        });

        // Exempt customers table
        exemptCustomerColumn.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue()));

        // Table selection handlers
        taxRateTable.getSelectionModel().selectedItemProperty().addListener(
                (obs, oldSelection, newSelection) -> {
                    selectedTaxRate = newSelection;
                    updateSelectionInfo();
                    updateFormFromSelection();
                    updateButtonStates();
                });
    }

    private void initializeComboBoxes() {
        // Type ComboBox
        typeComboBox.setItems(FXCollections.observableArrayList(TaxType.values()));

        // Status Filter
        statusFilter.setItems(FXCollections.observableArrayList(
                "All", "Active", "Inactive", "Invalid"));
        statusFilter.setValue("All");

        // Type Filter
        typeFilter.setItems(FXCollections.observableArrayList(TaxType.values()));
        typeFilter.setPromptText("All Types");
    }

    private void initializeEventHandlers() {
        // Search field
        searchField.textProperty().addListener((obs, oldText, newText) -> applyFilters());

        // Filter ComboBoxes
        statusFilter.setOnAction(e -> applyFilters());
        typeFilter.setOnAction(e -> applyFilters());
    }

    private void loadTaxRates() {
        try {
            List<TaxRate> taxRates = taxService.getAllTaxRates();
            allTaxRates.setAll(taxRates);
            applyFilters();
            updateTaxRateCount();
        } catch (Exception e) {
            AlertUtil.showError("Load Error", "Failed to load tax rates: " + e.getMessage());
        }
    }

    private void loadExemptCustomers() {
        try {
            // Note: This would need to be implemented in TaxService
            // For now, we'll show a placeholder
            exemptCustomers.clear();
            exemptCustomers.add("555-0123-4567");
            exemptCustomers.add("tax-exempt-customer-id");
        } catch (Exception e) {
            AlertUtil.showError("Load Error", "Failed to load exempt customers: " + e.getMessage());
        }
    }

    private void applyFilters() {
        String searchText = searchField.getText().toLowerCase().trim();
        String statusFilterValue = statusFilter.getValue();
        TaxType typeFilterValue = typeFilter.getValue();

        filteredTaxRates.setAll(allTaxRates.stream()
                .filter(taxRate -> {
                    // Search filter
                    if (!searchText.isEmpty()) {
                        return taxRate.getName().toLowerCase().contains(searchText)
                                || taxRate.getDescription().toLowerCase().contains(searchText)
                                || (taxRate.getJurisdiction() != null
                                && taxRate.getJurisdiction().toLowerCase().contains(searchText));
                    }
                    return true;
                })
                .filter(taxRate -> {
                    // Status filter
                    if (!"All".equals(statusFilterValue)) {
                        switch (statusFilterValue) {
                            case "Active":
                                return taxRate.isActive() && taxRate.isValid();
                            case "Inactive":
                                return !taxRate.isActive();
                            case "Invalid":
                                return !taxRate.isValid();
                        }
                    }
                    return true;
                })
                .filter(taxRate -> {
                    // Type filter
                    return typeFilterValue == null || taxRate.getType() == typeFilterValue;
                })
                .toList());

        updateTaxRateCount();
    }

    private void updateTaxRateCount() {
        int total = allTaxRates.size();
        int filtered = filteredTaxRates.size();

        if (filtered == total) {
            taxRateCountLabel.setText("Showing: " + total + " tax rates");
        } else {
            taxRateCountLabel.setText("Showing: " + filtered + " of " + total + " tax rates");
        }
    }

    private void updateSelectionInfo() {
        if (selectedTaxRate == null) {
            selectionInfoLabel.setText("Select a tax rate for details");
        } else {
            String info = String.format("Selected: %s (%s)",
                    selectedTaxRate.getName(),
                    selectedTaxRate.getType().getDisplayName());
            selectionInfoLabel.setText(info);
        }
    }

    private void updateFormFromSelection() {
        if (selectedTaxRate == null) {
            clearForm();
            return;
        }

        nameField.setText(selectedTaxRate.getName());
        descriptionField.setText(selectedTaxRate.getDescription());
        typeComboBox.setValue(selectedTaxRate.getType());
        rateField.setText(selectedTaxRate.getRate().toString());
        jurisdictionField.setText(selectedTaxRate.getJurisdiction());
        applicableCategoriesField.setText(selectedTaxRate.getApplicableCategories());
        exemptCategoriesField.setText(selectedTaxRate.getExemptCategories());

        if (selectedTaxRate.getMinimumAmount() != null) {
            minimumAmountField.setText(selectedTaxRate.getMinimumAmount().toString());
        }
        if (selectedTaxRate.getMaximumAmount() != null) {
            maximumAmountField.setText(selectedTaxRate.getMaximumAmount().toString());
        }

        if (selectedTaxRate.getEffectiveDate() != null) {
            effectiveDatePicker.setValue(selectedTaxRate.getEffectiveDate().toLocalDate());
        }
        if (selectedTaxRate.getExpirationDate() != null) {
            expirationDatePicker.setValue(selectedTaxRate.getExpirationDate().toLocalDate());
        }

        activeCheckBox.setSelected(selectedTaxRate.isActive());
    }

    private void clearForm() {
        nameField.clear();
        descriptionField.clear();
        typeComboBox.setValue(null);
        rateField.clear();
        jurisdictionField.clear();
        applicableCategoriesField.clear();
        exemptCategoriesField.clear();
        minimumAmountField.clear();
        maximumAmountField.clear();
        effectiveDatePicker.setValue(null);
        expirationDatePicker.setValue(null);
        activeCheckBox.setSelected(true);
    }

    private void updateButtonStates() {
        boolean hasSelection = selectedTaxRate != null;
        editTaxRateButton.setDisable(!hasSelection || isEditMode);
        deleteTaxRateButton.setDisable(!hasSelection || isEditMode);
        testTaxRateButton.setDisable(!hasSelection);
    }

    private void setFormEditMode(boolean editMode) {
        isEditMode = editMode;

        // Form fields
        nameField.setDisable(!editMode);
        descriptionField.setDisable(!editMode);
        typeComboBox.setDisable(!editMode);
        rateField.setDisable(!editMode);
        jurisdictionField.setDisable(!editMode);
        applicableCategoriesField.setDisable(!editMode);
        exemptCategoriesField.setDisable(!editMode);
        minimumAmountField.setDisable(!editMode);
        maximumAmountField.setDisable(!editMode);
        effectiveDatePicker.setDisable(!editMode);
        expirationDatePicker.setDisable(!editMode);
        activeCheckBox.setDisable(!editMode);

        // Buttons
        saveTaxRateButton.setVisible(editMode);
        cancelTaxRateButton.setVisible(editMode);
        addTaxRateButton.setDisable(editMode);

        updateButtonStates();
    }

    // Event Handlers
    @FXML
    private void handleAddTaxRate() {
        selectedTaxRate = null;
        clearForm();
        setFormEditMode(true);
        nameField.requestFocus();
    }

    @FXML
    private void handleEditTaxRate() {
        if (selectedTaxRate != null) {
            setFormEditMode(true);
            nameField.requestFocus();
        }
    }

    @FXML
    private void handleDeleteTaxRate() {
        if (selectedTaxRate == null) {
            return;
        }

        if (AlertUtil.showConfirmation("Delete Tax Rate",
                "Are you sure you want to delete the tax rate '" + selectedTaxRate.getName() + "'?")) {
            try {
                taxService.removeTaxRate(selectedTaxRate.getId());
                loadTaxRates();
                AlertUtil.showInfo("Success", "Tax rate deleted successfully.");
            } catch (Exception e) {
                AlertUtil.showError("Delete Error", "Failed to delete tax rate: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleSaveTaxRate() {
        try {
            if (!validateForm()) {
                return;
            }

            TaxRate taxRate = selectedTaxRate != null ? selectedTaxRate : new TaxRate();
            populateTaxRateFromForm(taxRate);

            taxService.addTaxRate(taxRate);
            loadTaxRates();
            setFormEditMode(false);

            // Select the saved tax rate
            taxRateTable.getSelectionModel().select(taxRate);

            AlertUtil.showInfo("Success", "Tax rate saved successfully.");

        } catch (Exception e) {
            AlertUtil.showError("Save Error", "Failed to save tax rate: " + e.getMessage());
        }
    }

    @FXML
    private void handleCancelTaxRate() {
        setFormEditMode(false);
        updateFormFromSelection();
    }

    @FXML
    private void handleTestTaxRate() {
        if (selectedTaxRate == null) {
            return;
        }

        // Show tax rate test dialog
        String testInfo = String.format(
                "Tax Rate Test Information:\n\n"
                + "Name: %s\n"
                + "Type: %s\n"
                + "Rate: %s%%\n"
                + "Jurisdiction: %s\n"
                + "Status: %s\n"
                + "Valid: %s\n"
                + "Applicable Categories: %s\n"
                + "Exempt Categories: %s",
                selectedTaxRate.getName(),
                selectedTaxRate.getType().getDisplayName(),
                selectedTaxRate.getRate(),
                selectedTaxRate.getJurisdiction() != null ? selectedTaxRate.getJurisdiction() : "All",
                selectedTaxRate.isActive() ? "Active" : "Inactive",
                selectedTaxRate.isValid() ? "Yes" : "No",
                selectedTaxRate.getApplicableCategories() != null ? selectedTaxRate.getApplicableCategories() : "All",
                selectedTaxRate.getExemptCategories() != null ? selectedTaxRate.getExemptCategories() : "None"
        );

        AlertUtil.showInfo("Tax Rate Test", testInfo);
    }

    @FXML
    private void handleAddExemptCustomer() {
        String customerInfo = exemptCustomerField.getText().trim();
        if (!customerInfo.isEmpty()) {
            if (!exemptCustomers.contains(customerInfo)) {
                exemptCustomers.add(customerInfo);
                exemptCustomerField.clear();
                AlertUtil.showInfo("Success", "Customer added to tax exempt list.");
            } else {
                AlertUtil.showWarning("Duplicate Entry", "Customer is already in the tax exempt list.");
            }
        }
    }

    @FXML
    private void handleRemoveExemptCustomer() {
        String selected = exemptCustomersTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            exemptCustomers.remove(selected);
            AlertUtil.showInfo("Success", "Customer removed from tax exempt list.");
        }
    }

    @FXML
    private void handleClearFilters() {
        searchField.clear();
        statusFilter.setValue("All");
        typeFilter.setValue(null);
        applyFilters();
    }

    private boolean validateForm() {
        if (nameField.getText().trim().isEmpty()) {
            AlertUtil.showWarning("Validation Error", "Tax rate name is required.");
            nameField.requestFocus();
            return false;
        }

        if (typeComboBox.getValue() == null) {
            AlertUtil.showWarning("Validation Error", "Tax type is required.");
            typeComboBox.requestFocus();
            return false;
        }

        try {
            BigDecimal rate = new BigDecimal(rateField.getText().trim());
            if (rate.compareTo(BigDecimal.ZERO) < 0 || rate.compareTo(new BigDecimal("100")) > 0) {
                AlertUtil.showWarning("Validation Error", "Tax rate must be between 0 and 100.");
                rateField.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            AlertUtil.showWarning("Validation Error", "Invalid tax rate.");
            rateField.requestFocus();
            return false;
        }

        return true;
    }

    private void populateTaxRateFromForm(TaxRate taxRate) {
        taxRate.setName(nameField.getText().trim());
        taxRate.setDescription(descriptionField.getText().trim());
        taxRate.setType(typeComboBox.getValue());
        taxRate.setRate(new BigDecimal(rateField.getText().trim()));
        taxRate.setJurisdiction(jurisdictionField.getText().trim().isEmpty() ? null : jurisdictionField.getText().trim());
        taxRate.setApplicableCategories(applicableCategoriesField.getText().trim().isEmpty() ? null : applicableCategoriesField.getText().trim());
        taxRate.setExemptCategories(exemptCategoriesField.getText().trim().isEmpty() ? null : exemptCategoriesField.getText().trim());

        if (!minimumAmountField.getText().trim().isEmpty()) {
            taxRate.setMinimumAmount(new BigDecimal(minimumAmountField.getText().trim()));
        }
        if (!maximumAmountField.getText().trim().isEmpty()) {
            taxRate.setMaximumAmount(new BigDecimal(maximumAmountField.getText().trim()));
        }

        if (effectiveDatePicker.getValue() != null) {
            taxRate.setEffectiveDate(effectiveDatePicker.getValue().atStartOfDay());
        }
        if (expirationDatePicker.getValue() != null) {
            taxRate.setExpirationDate(expirationDatePicker.getValue().atTime(23, 59, 59));
        }

        taxRate.setActive(activeCheckBox.isSelected());
        taxRate.setUpdatedAt(LocalDateTime.now());
    }
}
