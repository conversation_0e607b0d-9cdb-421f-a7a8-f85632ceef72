
import java.math.BigDecimal;
import java.util.List;

import com.clothingstore.dao.PaymentHistoryDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.PaymentHistory;
import com.clothingstore.model.Transaction;

public class TestPaymentHistory {

    public static void main(String[] args) {
        try {
            System.out.println("=== Testing Payment History for Completed Transactions ===");

            // Initialize DAOs
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            PaymentHistoryDAO paymentHistoryDAO = PaymentHistoryDAO.getInstance();

            // Find completed transactions
            List<Transaction> completedTransactions = transactionDAO.findByStatus("COMPLETED");
            if (completedTransactions.isEmpty()) {
                System.out.println("No COMPLETED transactions found");
                return;
            }

            Transaction testTransaction = completedTransactions.get(0);
            System.out.println("Testing transaction: " + testTransaction.getTransactionNumber());
            System.out.println("Transaction ID: " + testTransaction.getId());
            System.out.println("Transaction status: " + testTransaction.getStatus());
            System.out.println("Transaction total: $" + testTransaction.getTotalAmount());
            System.out.println("Amount paid: $" + testTransaction.getAmountPaid());
            System.out.println("Remaining balance: $" + testTransaction.getRemainingBalance());

            // Check payment history
            System.out.println("\n=== Payment History Analysis ===");
            List<PaymentHistory> paymentHistory = paymentHistoryDAO.findByTransactionId(testTransaction.getId());
            System.out.println("Payment history records: " + paymentHistory.size());

            BigDecimal totalPaid = paymentHistoryDAO.getTotalAmountPaid(testTransaction.getId());
            BigDecimal totalRefunded = paymentHistoryDAO.getTotalAmountRefunded(testTransaction.getId());
            BigDecimal availableForRefund = totalPaid.subtract(totalRefunded);

            System.out.println("Total paid (from payment history): $" + totalPaid);
            System.out.println("Total refunded (from payment history): $" + totalRefunded);
            System.out.println("Available for refund: $" + availableForRefund);

            if (paymentHistory.isEmpty()) {
                System.out.println("WARNING: No payment history records found for this COMPLETED transaction!");
                System.out.println("This explains why partial refund is failing.");
                System.out.println("For COMPLETED transactions, we should allow refunds up to the transaction total amount.");
            } else {
                System.out.println("\nPayment History Details:");
                for (PaymentHistory payment : paymentHistory) {
                    System.out.println("- " + payment.getPaymentMethod() + ": $" + payment.getPaymentAmount()
                            + " (" + payment.getPaymentType() + ") - " + payment.getPaymentDate());
                }
            }

            // Test with multiple completed transactions
            System.out.println("\n=== Testing Multiple Completed Transactions ===");
            int transactionsWithoutPaymentHistory = 0;
            int transactionsWithPaymentHistory = 0;

            for (int i = 0; i < Math.min(5, completedTransactions.size()); i++) {
                Transaction tx = completedTransactions.get(i);
                List<PaymentHistory> history = paymentHistoryDAO.findByTransactionId(tx.getId());
                BigDecimal paid = paymentHistoryDAO.getTotalAmountPaid(tx.getId());

                System.out.println("Transaction " + tx.getTransactionNumber()
                        + ": Total=$" + tx.getTotalAmount()
                        + ", Paid=$" + paid
                        + ", History records=" + history.size());

                if (history.isEmpty() || paid.compareTo(BigDecimal.ZERO) == 0) {
                    transactionsWithoutPaymentHistory++;
                } else {
                    transactionsWithPaymentHistory++;
                }
            }

            System.out.println("\nSummary:");
            System.out.println("Transactions without payment history: " + transactionsWithoutPaymentHistory);
            System.out.println("Transactions with payment history: " + transactionsWithPaymentHistory);

        } catch (Exception e) {
            System.err.println("Error testing payment history: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
