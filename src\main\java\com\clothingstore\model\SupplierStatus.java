package com.clothingstore.model;

/**
 * Enumeration for supplier status
 */
public enum SupplierStatus {
    ACTIVE("Active", "Supplier is active and available for orders"),
    INACTIVE("Inactive", "Supplier is temporarily inactive"),
    SUSPENDED("Suspended", "Supplier is suspended due to issues"),
    BLACKLISTED("Blacklisted", "Supplier is blacklisted and cannot be used"),
    PENDING_APPROVAL("Pending Approval", "New supplier pending approval"),
    UNDER_REVIEW("Under Review", "Supplier is under review for compliance");

    private final String displayName;
    private final String description;

    SupplierStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Check if supplier can receive orders
     */
    public boolean canReceiveOrders() {
        return this == ACTIVE;
    }

    /**
     * Check if supplier is in good standing
     */
    public boolean isInGoodStanding() {
        return this == ACTIVE || this == PENDING_APPROVAL;
    }
}
