package com.clothingstore.model;

import java.math.BigDecimal;

/**
 * Enumeration for customer loyalty tiers
 */
public enum LoyaltyTier {
    BRONZE("Bronze", new BigDecimal("0"), new BigDecimal("2"), new BigDecimal("0"), 
           "Entry level tier with basic benefits"),
    SILVER("Silver", new BigDecimal("500"), new BigDecimal("5"), new BigDecimal("5"), 
           "Mid-level tier with enhanced rewards and discounts"),
    GOLD("Gold", new BigDecimal("1500"), new BigDecimal("8"), new BigDecimal("10"), 
           "Premium tier with excellent benefits and priority service"),
    PLATINUM("Platinum", new BigDecimal("3000"), new BigDecimal("12"), new BigDecimal("15"), 
             "Elite tier with maximum rewards and exclusive perks");

    private final String displayName;
    private final BigDecimal minimumSpend; // Minimum lifetime spend to qualify
    private final BigDecimal pointsMultiplier; // Points earned per dollar spent
    private final BigDecimal discountPercentage; // Automatic discount percentage
    private final String description;

    LoyaltyTier(String displayName, BigDecimal minimumSpend, BigDecimal pointsMultiplier, 
                BigDecimal discountPercentage, String description) {
        this.displayName = displayName;
        this.minimumSpend = minimumSpend;
        this.pointsMultiplier = pointsMultiplier;
        this.discountPercentage = discountPercentage;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public BigDecimal getMinimumSpend() {
        return minimumSpend;
    }

    public BigDecimal getPointsMultiplier() {
        return pointsMultiplier;
    }

    public BigDecimal getDiscountPercentage() {
        return discountPercentage;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Determine loyalty tier based on lifetime spend
     */
    public static LoyaltyTier getTierBySpend(BigDecimal lifetimeSpend) {
        if (lifetimeSpend == null) {
            return BRONZE;
        }

        if (lifetimeSpend.compareTo(PLATINUM.getMinimumSpend()) >= 0) {
            return PLATINUM;
        } else if (lifetimeSpend.compareTo(GOLD.getMinimumSpend()) >= 0) {
            return GOLD;
        } else if (lifetimeSpend.compareTo(SILVER.getMinimumSpend()) >= 0) {
            return SILVER;
        } else {
            return BRONZE;
        }
    }

    /**
     * Get next tier for progression
     */
    public LoyaltyTier getNextTier() {
        switch (this) {
            case BRONZE:
                return SILVER;
            case SILVER:
                return GOLD;
            case GOLD:
                return PLATINUM;
            case PLATINUM:
            default:
                return null; // Already at highest tier
        }
    }

    /**
     * Calculate amount needed to reach next tier
     */
    public BigDecimal getAmountToNextTier(BigDecimal currentSpend) {
        LoyaltyTier nextTier = getNextTier();
        if (nextTier == null || currentSpend == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal amountNeeded = nextTier.getMinimumSpend().subtract(currentSpend);
        return amountNeeded.compareTo(BigDecimal.ZERO) > 0 ? amountNeeded : BigDecimal.ZERO;
    }

    /**
     * Check if tier offers automatic discounts
     */
    public boolean hasAutomaticDiscount() {
        return discountPercentage.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Get tier benefits description
     */
    public String getBenefitsDescription() {
        StringBuilder benefits = new StringBuilder();
        benefits.append("• ").append(pointsMultiplier).append("x points per dollar spent\n");
        
        if (hasAutomaticDiscount()) {
            benefits.append("• ").append(discountPercentage).append("% automatic discount\n");
        }
        
        switch (this) {
            case SILVER:
                benefits.append("• Birthday bonus points\n");
                benefits.append("• Early access to sales\n");
                break;
            case GOLD:
                benefits.append("• Birthday bonus points\n");
                benefits.append("• Early access to sales\n");
                benefits.append("• Free shipping on all orders\n");
                benefits.append("• Priority customer service\n");
                break;
            case PLATINUM:
                benefits.append("• Birthday bonus points\n");
                benefits.append("• Early access to sales\n");
                benefits.append("• Free shipping on all orders\n");
                benefits.append("• Priority customer service\n");
                benefits.append("• Exclusive member events\n");
                benefits.append("• Personal shopping assistance\n");
                break;
        }
        
        return benefits.toString();
    }
}
