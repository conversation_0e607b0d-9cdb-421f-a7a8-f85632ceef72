package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.InventoryMovement;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object for Inventory Movement operations Provides specialized
 * queries for tracking items sold/processed and items returned/refunded
 */
public class InventoryMovementDAO {

    private static InventoryMovementDAO instance;

    private InventoryMovementDAO() {
    }

    public static synchronized InventoryMovementDAO getInstance() {
        if (instance == null) {
            instance = new InventoryMovementDAO();
        }
        return instance;
    }

    /**
     * Get all items sold/processed through completed transactions
     *
     * @param startDate Start date for the report period
     * @param endDate End date for the report period
     * @return List of inventory movements representing sold items
     */
    public List<InventoryMovement> getItemsSoldProcessed(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT t.transaction_number, t.transaction_date, t.status, t.payment_method, "
                + "t.cashier_name, t.notes, t.customer_id, "
                + "COALESCE(c.first_name || ' ' || c.last_name, 'Walk-in') as customer_name, "
                + "ti.product_id, p.name as product_name, p.sku as product_sku, "
                + "p.category, p.brand, ti.quantity, ti.unit_price, ti.line_total, "
                + "t.created_at, t.updated_at "
                + "FROM transactions t "
                + "JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "JOIN products p ON ti.product_id = p.id "
                + "LEFT JOIN customers c ON t.customer_id = c.id "
                + "WHERE t.status = 'COMPLETED' "
                + "AND t.transaction_date BETWEEN ? AND ? "
                + "ORDER BY t.transaction_date DESC, t.transaction_number, p.name";

        List<InventoryMovement> movements = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    InventoryMovement movement = mapResultSetToMovement(rs, "SOLD");
                    movements.add(movement);
                }
            }
        }

        return movements;
    }

    /**
     * Get all items returned/refunded to inventory
     *
     * @param startDate Start date for the report period
     * @param endDate End date for the report period
     * @return List of inventory movements representing returned items
     */
    public List<InventoryMovement> getItemsReturnedRefunded(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT t.transaction_number, t.transaction_date, t.status, t.payment_method, "
                + "t.cashier_name, t.notes, t.customer_id, "
                + "COALESCE(c.first_name || ' ' || c.last_name, 'Walk-in') as customer_name, "
                + "ti.product_id, p.name as product_name, p.sku as product_sku, "
                + "p.category, p.brand, ti.quantity, ti.unit_price, ti.line_total, "
                + "t.created_at, t.updated_at "
                + "FROM transactions t "
                + "JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "JOIN products p ON ti.product_id = p.id "
                + "LEFT JOIN customers c ON t.customer_id = c.id "
                + "WHERE (t.status = 'REFUNDED' OR t.status = 'PARTIALLY_REFUNDED' OR t.status = 'CANCELLED') "
                + "AND t.transaction_date BETWEEN ? AND ? "
                + "ORDER BY t.transaction_date DESC, t.transaction_number, p.name";

        List<InventoryMovement> movements = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    String movementType = determineReturnMovementType(rs.getString("status"));
                    InventoryMovement movement = mapResultSetToMovement(rs, movementType);
                    movements.add(movement);
                }
            }
        }

        return movements;
    }

    /**
     * Get inventory movement summary statistics
     *
     * @param startDate Start date for the report period
     * @param endDate End date for the report period
     * @return InventoryMovementSummary with aggregated data
     */
    public InventoryMovementSummary getMovementSummary(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        InventoryMovementSummary summary = new InventoryMovementSummary();

        // Get sold items summary
        String soldSql = "SELECT COUNT(ti.id) as item_count, SUM(ti.quantity) as total_quantity, "
                + "SUM(ti.line_total) as total_value "
                + "FROM transactions t "
                + "JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "WHERE t.status = 'COMPLETED' "
                + "AND t.transaction_date BETWEEN ? AND ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(soldSql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    summary.setSoldItemCount(rs.getInt("item_count"));
                    summary.setSoldQuantity(rs.getInt("total_quantity"));
                    summary.setSoldValue(rs.getBigDecimal("total_value"));
                }
            }
        }

        // Get returned items summary
        String returnedSql = "SELECT COUNT(ti.id) as item_count, SUM(ti.quantity) as total_quantity, "
                + "SUM(ti.line_total) as total_value "
                + "FROM transactions t "
                + "JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "WHERE (t.status = 'REFUNDED' OR t.status = 'PARTIALLY_REFUNDED' OR t.status = 'CANCELLED') "
                + "AND t.transaction_date BETWEEN ? AND ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(returnedSql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    summary.setReturnedItemCount(rs.getInt("item_count"));
                    summary.setReturnedQuantity(rs.getInt("total_quantity"));
                    summary.setReturnedValue(rs.getBigDecimal("total_value"));
                }
            }
        }

        return summary;
    }

    /**
     * Get top products by movement type
     *
     * @param startDate Start date for the report period
     * @param endDate End date for the report period
     * @param movementType "SOLD" or "RETURNED"
     * @param limit Number of top products to return
     * @return List of top products by movement
     */
    public List<ProductMovementSummary> getTopProductsByMovement(LocalDateTime startDate, LocalDateTime endDate,
            String movementType, int limit) throws SQLException {
        String statusCondition;
        if ("SOLD".equals(movementType)) {
            statusCondition = "t.status = 'COMPLETED'";
        } else {
            statusCondition = "(t.status = 'REFUNDED' OR t.status = 'PARTIALLY_REFUNDED' OR t.status = 'CANCELLED')";
        }

        String sql = "SELECT p.name as product_name, p.sku as product_sku, p.category, "
                + "SUM(ti.quantity) as total_quantity, SUM(ti.line_total) as total_value, "
                + "COUNT(DISTINCT t.id) as transaction_count "
                + "FROM transactions t "
                + "JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "JOIN products p ON ti.product_id = p.id "
                + "WHERE " + statusCondition + " "
                + "AND t.transaction_date BETWEEN ? AND ? "
                + "GROUP BY p.id, p.name, p.sku, p.category "
                + "ORDER BY total_quantity DESC "
                + "LIMIT ?";

        List<ProductMovementSummary> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));
            pstmt.setInt(3, limit);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    ProductMovementSummary product = new ProductMovementSummary();
                    product.setProductName(rs.getString("product_name"));
                    product.setProductSku(rs.getString("product_sku"));
                    product.setCategory(rs.getString("category"));
                    product.setTotalQuantity(rs.getInt("total_quantity"));
                    product.setTotalValue(rs.getBigDecimal("total_value"));
                    product.setTransactionCount(rs.getInt("transaction_count"));
                    product.setMovementType(movementType);
                    products.add(product);
                }
            }
        }

        return products;
    }

    /**
     * Map ResultSet to InventoryMovement object
     */
    private InventoryMovement mapResultSetToMovement(ResultSet rs, String movementType) throws SQLException {
        InventoryMovement movement = new InventoryMovement();

        movement.setTransactionNumber(rs.getString("transaction_number"));
        movement.setMovementDate(rs.getTimestamp("transaction_date").toLocalDateTime());
        movement.setMovementType(movementType);

        movement.setProductId(rs.getLong("product_id"));
        movement.setProductName(rs.getString("product_name"));
        movement.setProductSku(rs.getString("product_sku"));
        movement.setCategory(rs.getString("category"));
        movement.setBrand(rs.getString("brand"));

        movement.setQuantity(rs.getInt("quantity"));
        movement.setUnitPrice(rs.getBigDecimal("unit_price"));
        movement.setLineTotal(rs.getBigDecimal("line_total"));

        // Handle NULL customer_id properly
        Object customerIdObj = rs.getObject("customer_id");
        if (customerIdObj != null) {
            movement.setCustomerId(((Number) customerIdObj).longValue());
        } else {
            movement.setCustomerId(null);
        }
        movement.setCustomerName(rs.getString("customer_name"));

        movement.setStatus(rs.getString("status"));
        movement.setPaymentMethod(rs.getString("payment_method"));
        movement.setCashierName(rs.getString("cashier_name"));
        movement.setNotes(rs.getString("notes"));

        // Set reason based on movement type and notes
        if (movement.isReturnedMovement()) {
            movement.setReason(determineReturnReason(rs.getString("status"), rs.getString("notes")));
        }

        movement.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        movement.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());

        return movement;
    }

    /**
     * Determine movement type for returns based on transaction status
     */
    private String determineReturnMovementType(String status) {
        switch (status) {
            case "REFUNDED":
                return "REFUNDED";
            case "PARTIALLY_REFUNDED":
                return "REFUNDED";
            case "CANCELLED":
                return "CANCELLED";
            default:
                return "RETURNED";
        }
    }

    /**
     * Determine return reason based on status and notes
     */
    private String determineReturnReason(String status, String notes) {
        if ("REFUNDED".equals(status) || "PARTIALLY_REFUNDED".equals(status)) {
            return notes != null && !notes.trim().isEmpty() ? notes : "Customer refund request";
        } else if ("CANCELLED".equals(status)) {
            return notes != null && !notes.trim().isEmpty() ? notes : "Transaction cancelled";
        }
        return "Return to inventory";
    }

    /**
     * Summary class for inventory movement statistics
     */
    public static class InventoryMovementSummary {

        private int soldItemCount;
        private int soldQuantity;
        private java.math.BigDecimal soldValue = java.math.BigDecimal.ZERO;

        private int returnedItemCount;
        private int returnedQuantity;
        private java.math.BigDecimal returnedValue = java.math.BigDecimal.ZERO;

        // Getters and setters
        public int getSoldItemCount() {
            return soldItemCount;
        }

        public void setSoldItemCount(int soldItemCount) {
            this.soldItemCount = soldItemCount;
        }

        public int getSoldQuantity() {
            return soldQuantity;
        }

        public void setSoldQuantity(int soldQuantity) {
            this.soldQuantity = soldQuantity;
        }

        public java.math.BigDecimal getSoldValue() {
            return soldValue;
        }

        public void setSoldValue(java.math.BigDecimal soldValue) {
            this.soldValue = soldValue;
        }

        public int getReturnedItemCount() {
            return returnedItemCount;
        }

        public void setReturnedItemCount(int returnedItemCount) {
            this.returnedItemCount = returnedItemCount;
        }

        public int getReturnedQuantity() {
            return returnedQuantity;
        }

        public void setReturnedQuantity(int returnedQuantity) {
            this.returnedQuantity = returnedQuantity;
        }

        public java.math.BigDecimal getReturnedValue() {
            return returnedValue;
        }

        public void setReturnedValue(java.math.BigDecimal returnedValue) {
            this.returnedValue = returnedValue;
        }

        public int getNetQuantity() {
            return soldQuantity - returnedQuantity;
        }

        public java.math.BigDecimal getNetValue() {
            return soldValue.subtract(returnedValue);
        }

        public double getReturnRate() {
            return soldQuantity > 0 ? (double) returnedQuantity / soldQuantity * 100 : 0.0;
        }
    }

    /**
     * Summary class for product movement data
     */
    public static class ProductMovementSummary {

        private String productName;
        private String productSku;
        private String category;
        private int totalQuantity;
        private java.math.BigDecimal totalValue = java.math.BigDecimal.ZERO;
        private int transactionCount;
        private String movementType;

        // Getters and setters
        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getProductSku() {
            return productSku;
        }

        public void setProductSku(String productSku) {
            this.productSku = productSku;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public int getTotalQuantity() {
            return totalQuantity;
        }

        public void setTotalQuantity(int totalQuantity) {
            this.totalQuantity = totalQuantity;
        }

        public java.math.BigDecimal getTotalValue() {
            return totalValue;
        }

        public void setTotalValue(java.math.BigDecimal totalValue) {
            this.totalValue = totalValue;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        public void setTransactionCount(int transactionCount) {
            this.transactionCount = transactionCount;
        }

        public String getMovementType() {
            return movementType;
        }

        public void setMovementType(String movementType) {
            this.movementType = movementType;
        }
    }
}
