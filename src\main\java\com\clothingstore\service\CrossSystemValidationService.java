package com.clothingstore.service;

import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.PaymentHistory;
import com.clothingstore.util.ValidationUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service for validating data consistency across Transaction History, 
 * Outstanding Balances, and Payment History systems
 */
public class CrossSystemValidationService {
    
    private final TransactionDAO transactionDAO;
    private final PaymentHistoryService paymentHistoryService;
    private final RefundTrackingIntegrationService refundTrackingService;
    
    public CrossSystemValidationService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.paymentHistoryService = PaymentHistoryService.getInstance();
        this.refundTrackingService = RefundTrackingIntegrationService.getInstance();
    }
    
    /**
     * Comprehensive validation result for cross-system data consistency
     */
    public static class CrossSystemValidationResult {
        private boolean isValid;
        private List<String> errors;
        private List<String> warnings;
        private List<ValidationDetail> details;
        private ValidationSummary summary;
        
        public CrossSystemValidationResult() {
            this.errors = new ArrayList<>();
            this.warnings = new ArrayList<>();
            this.details = new ArrayList<>();
            this.summary = new ValidationSummary();
        }
        
        // Getters and setters
        public boolean isValid() { return isValid; }
        public void setValid(boolean valid) { this.isValid = valid; }
        
        public List<String> getErrors() { return errors; }
        public void addError(String error) { this.errors.add(error); }
        
        public List<String> getWarnings() { return warnings; }
        public void addWarning(String warning) { this.warnings.add(warning); }
        
        public List<ValidationDetail> getDetails() { return details; }
        public void addDetail(ValidationDetail detail) { this.details.add(detail); }
        
        public ValidationSummary getSummary() { return summary; }
        public void setSummary(ValidationSummary summary) { this.summary = summary; }
    }
    
    /**
     * Detailed validation information for specific checks
     */
    public static class ValidationDetail {
        private String checkType;
        private String transactionId;
        private String description;
        private boolean passed;
        private String expectedValue;
        private String actualValue;
        private String severity; // ERROR, WARNING, INFO
        
        public ValidationDetail(String checkType, String transactionId, String description, 
                              boolean passed, String expectedValue, String actualValue, String severity) {
            this.checkType = checkType;
            this.transactionId = transactionId;
            this.description = description;
            this.passed = passed;
            this.expectedValue = expectedValue;
            this.actualValue = actualValue;
            this.severity = severity;
        }
        
        // Getters
        public String getCheckType() { return checkType; }
        public String getTransactionId() { return transactionId; }
        public String getDescription() { return description; }
        public boolean isPassed() { return passed; }
        public String getExpectedValue() { return expectedValue; }
        public String getActualValue() { return actualValue; }
        public String getSeverity() { return severity; }
    }
    
    /**
     * Summary of validation results
     */
    public static class ValidationSummary {
        private int totalTransactionsChecked;
        private int transactionsWithErrors;
        private int transactionsWithWarnings;
        private int balanceDiscrepancies;
        private int paymentHistoryInconsistencies;
        private int refundTrackingIssues;
        private LocalDateTime validationTimestamp;
        
        public ValidationSummary() {
            this.validationTimestamp = LocalDateTime.now();
        }
        
        // Getters and setters
        public int getTotalTransactionsChecked() { return totalTransactionsChecked; }
        public void setTotalTransactionsChecked(int total) { this.totalTransactionsChecked = total; }
        
        public int getTransactionsWithErrors() { return transactionsWithErrors; }
        public void setTransactionsWithErrors(int errors) { this.transactionsWithErrors = errors; }
        
        public int getTransactionsWithWarnings() { return transactionsWithWarnings; }
        public void setTransactionsWithWarnings(int warnings) { this.transactionsWithWarnings = warnings; }
        
        public int getBalanceDiscrepancies() { return balanceDiscrepancies; }
        public void setBalanceDiscrepancies(int discrepancies) { this.balanceDiscrepancies = discrepancies; }
        
        public int getPaymentHistoryInconsistencies() { return paymentHistoryInconsistencies; }
        public void setPaymentHistoryInconsistencies(int inconsistencies) { this.paymentHistoryInconsistencies = inconsistencies; }
        
        public int getRefundTrackingIssues() { return refundTrackingIssues; }
        public void setRefundTrackingIssues(int issues) { this.refundTrackingIssues = issues; }
        
        public LocalDateTime getValidationTimestamp() { return validationTimestamp; }
    }
    
    /**
     * Validate data consistency for a specific transaction
     */
    public CrossSystemValidationResult validateTransaction(Long transactionId) {
        CrossSystemValidationResult result = new CrossSystemValidationResult();
        
        try {
            Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
            if (transactionOpt.isEmpty()) {
                result.addError("Transaction not found: " + transactionId);
                result.setValid(false);
                return result;
            }
            
            Transaction transaction = transactionOpt.get();
            result.getSummary().setTotalTransactionsChecked(1);
            
            // Perform all validation checks
            validateTransactionBalances(transaction, result);
            validatePaymentHistoryConsistency(transaction, result);
            validateRefundTrackingIntegrity(transaction, result);
            validateOutstandingBalanceStatus(transaction, result);
            
            // Determine overall validity
            result.setValid(result.getErrors().isEmpty());
            
            // Update summary counts
            if (!result.getErrors().isEmpty()) {
                result.getSummary().setTransactionsWithErrors(1);
            }
            if (!result.getWarnings().isEmpty()) {
                result.getSummary().setTransactionsWithWarnings(1);
            }
            
        } catch (Exception e) {
            result.addError("Validation failed: " + e.getMessage());
            result.setValid(false);
        }
        
        return result;
    }
    
    /**
     * Validate data consistency for all transactions with outstanding balances
     */
    public CrossSystemValidationResult validateAllOutstandingBalances() {
        CrossSystemValidationResult result = new CrossSystemValidationResult();
        
        try {
            List<Transaction> outstandingTransactions = transactionDAO.findWithOutstandingBalances();
            result.getSummary().setTotalTransactionsChecked(outstandingTransactions.size());
            
            int errorsCount = 0;
            int warningsCount = 0;
            int balanceDiscrepancies = 0;
            int paymentInconsistencies = 0;
            int refundIssues = 0;
            
            for (Transaction transaction : outstandingTransactions) {
                CrossSystemValidationResult transactionResult = validateTransaction(transaction.getId());
                
                // Aggregate results
                result.getErrors().addAll(transactionResult.getErrors());
                result.getWarnings().addAll(transactionResult.getWarnings());
                result.getDetails().addAll(transactionResult.getDetails());
                
                // Count specific issue types
                if (!transactionResult.getErrors().isEmpty()) {
                    errorsCount++;
                }
                if (!transactionResult.getWarnings().isEmpty()) {
                    warningsCount++;
                }
                
                // Count specific validation types
                for (ValidationDetail detail : transactionResult.getDetails()) {
                    if (!detail.isPassed()) {
                        switch (detail.getCheckType()) {
                            case "BALANCE_CONSISTENCY":
                                balanceDiscrepancies++;
                                break;
                            case "PAYMENT_HISTORY":
                                paymentInconsistencies++;
                                break;
                            case "REFUND_TRACKING":
                                refundIssues++;
                                break;
                        }
                    }
                }
            }
            
            // Update summary
            result.getSummary().setTransactionsWithErrors(errorsCount);
            result.getSummary().setTransactionsWithWarnings(warningsCount);
            result.getSummary().setBalanceDiscrepancies(balanceDiscrepancies);
            result.getSummary().setPaymentHistoryInconsistencies(paymentInconsistencies);
            result.getSummary().setRefundTrackingIssues(refundIssues);
            
            result.setValid(result.getErrors().isEmpty());
            
        } catch (Exception e) {
            result.addError("Bulk validation failed: " + e.getMessage());
            result.setValid(false);
        }
        
        return result;
    }
    
    /**
     * Validate transaction balance calculations
     */
    private void validateTransactionBalances(Transaction transaction, CrossSystemValidationResult result) {
        try {
            // Get payment history total
            BigDecimal paymentHistoryTotal = paymentHistoryService.getTotalAmountPaid(transaction.getId());
            BigDecimal transactionAmountPaid = transaction.getAmountPaid();
            
            // Check balance consistency (allow small rounding differences)
            BigDecimal difference = paymentHistoryTotal.subtract(transactionAmountPaid).abs();
            boolean balancesMatch = difference.compareTo(new BigDecimal("0.01")) <= 0;
            
            ValidationDetail balanceDetail = new ValidationDetail(
                "BALANCE_CONSISTENCY",
                transaction.getId().toString(),
                "Transaction amount paid vs payment history total",
                balancesMatch,
                transactionAmountPaid.toString(),
                paymentHistoryTotal.toString(),
                balancesMatch ? "INFO" : "ERROR"
            );
            
            result.addDetail(balanceDetail);
            
            if (!balancesMatch) {
                result.addError(String.format(
                    "Balance mismatch for transaction %s: Transaction shows $%.2f paid, Payment history shows $%.2f",
                    transaction.getId(), transactionAmountPaid.doubleValue(), paymentHistoryTotal.doubleValue()
                ));
            }
            
            // Validate remaining balance calculation
            BigDecimal calculatedRemaining = transaction.getTotalAmount().subtract(paymentHistoryTotal);
            BigDecimal transactionRemaining = transaction.getRemainingBalance();
            
            boolean remainingBalanceCorrect = calculatedRemaining.subtract(transactionRemaining).abs()
                .compareTo(new BigDecimal("0.01")) <= 0;
            
            if (!remainingBalanceCorrect) {
                result.addWarning(String.format(
                    "Remaining balance calculation inconsistency for transaction %s: Expected $%.2f, Found $%.2f",
                    transaction.getId(), calculatedRemaining.doubleValue(), transactionRemaining.doubleValue()
                ));
            }
            
        } catch (Exception e) {
            result.addError("Balance validation failed for transaction " + transaction.getId() + ": " + e.getMessage());
        }
    }

    /**
     * Validate payment history consistency
     */
    private void validatePaymentHistoryConsistency(Transaction transaction, CrossSystemValidationResult result) {
        try {
            List<PaymentHistory> paymentHistory = paymentHistoryService.getPaymentHistory(transaction.getId());

            // Check for duplicate payment records
            for (int i = 0; i < paymentHistory.size(); i++) {
                for (int j = i + 1; j < paymentHistory.size(); j++) {
                    PaymentHistory payment1 = paymentHistory.get(i);
                    PaymentHistory payment2 = paymentHistory.get(j);

                    // Check for potential duplicates (same amount, method, and close timestamps)
                    if (payment1.getPaymentAmount().equals(payment2.getPaymentAmount()) &&
                        payment1.getPaymentMethod().equals(payment2.getPaymentMethod()) &&
                        Math.abs(payment1.getPaymentDate().toEpochSecond(java.time.ZoneOffset.UTC) -
                                payment2.getPaymentDate().toEpochSecond(java.time.ZoneOffset.UTC)) < 60) {

                        result.addWarning(String.format(
                            "Potential duplicate payment records for transaction %s: $%.2f via %s",
                            transaction.getId(), payment1.getPaymentAmount().doubleValue(), payment1.getPaymentMethod()
                        ));
                    }
                }
            }

            // Validate running balance calculations
            BigDecimal runningTotal = BigDecimal.ZERO;
            for (PaymentHistory payment : paymentHistory) {
                runningTotal = runningTotal.add(payment.getPaymentAmount());

                BigDecimal expectedRemaining = transaction.getTotalAmount().subtract(runningTotal);
                BigDecimal actualRemaining = payment.getRemainingBalance();

                boolean runningBalanceCorrect = expectedRemaining.subtract(actualRemaining).abs()
                    .compareTo(new BigDecimal("0.01")) <= 0;

                ValidationDetail runningBalanceDetail = new ValidationDetail(
                    "PAYMENT_HISTORY",
                    transaction.getId().toString(),
                    "Running balance calculation for payment on " + payment.getPaymentDate(),
                    runningBalanceCorrect,
                    expectedRemaining.toString(),
                    actualRemaining.toString(),
                    runningBalanceCorrect ? "INFO" : "WARNING"
                );

                result.addDetail(runningBalanceDetail);

                if (!runningBalanceCorrect) {
                    result.addWarning(String.format(
                        "Running balance inconsistency in payment history for transaction %s on %s: Expected $%.2f, Found $%.2f",
                        transaction.getId(), payment.getPaymentDate(), expectedRemaining.doubleValue(), actualRemaining.doubleValue()
                    ));
                }
            }

            // Check payment sequence integrity
            for (int i = 1; i < paymentHistory.size(); i++) {
                PaymentHistory current = paymentHistory.get(i);
                PaymentHistory previous = paymentHistory.get(i - 1);

                if (current.getPaymentDate().isBefore(previous.getPaymentDate())) {
                    result.addWarning(String.format(
                        "Payment history chronological order issue for transaction %s: Payment on %s appears before payment on %s",
                        transaction.getId(), current.getPaymentDate(), previous.getPaymentDate()
                    ));
                }
            }

        } catch (Exception e) {
            result.addError("Payment history validation failed for transaction " + transaction.getId() + ": " + e.getMessage());
        }
    }

    /**
     * Validate refund tracking integrity
     */
    private void validateRefundTrackingIntegrity(Transaction transaction, CrossSystemValidationResult result) {
        try {
            // Get refund summary from integration service
            var refundSummary = refundTrackingService.getRefundSummary(transaction.getId());

            // Validate refund amounts don't exceed payments
            BigDecimal totalPaid = paymentHistoryService.getTotalAmountPaid(transaction.getId());
            BigDecimal totalRefunded = refundSummary.getTotalRefunded();

            if (totalRefunded.compareTo(totalPaid) > 0) {
                result.addError(String.format(
                    "Refund amount exceeds total payments for transaction %s: Refunded $%.2f, Paid $%.2f",
                    transaction.getId(), totalRefunded.doubleValue(), totalPaid.doubleValue()
                ));
            }

            // Check refund status consistency
            boolean hasRefunds = totalRefunded.compareTo(BigDecimal.ZERO) > 0;
            boolean isPartiallyRefunded = "PARTIALLY_REFUNDED".equals(transaction.getStatus());
            boolean isFullyRefunded = "REFUNDED".equals(transaction.getStatus());

            ValidationDetail refundStatusDetail = new ValidationDetail(
                "REFUND_TRACKING",
                transaction.getId().toString(),
                "Refund status consistency",
                (hasRefunds && (isPartiallyRefunded || isFullyRefunded)) || (!hasRefunds && !isPartiallyRefunded && !isFullyRefunded),
                hasRefunds ? "PARTIALLY_REFUNDED or REFUNDED" : "Not refund status",
                transaction.getStatus(),
                "INFO"
            );

            result.addDetail(refundStatusDetail);

            if (hasRefunds && !isPartiallyRefunded && !isFullyRefunded) {
                result.addWarning(String.format(
                    "Transaction %s has refunds ($%.2f) but status is '%s' instead of PARTIALLY_REFUNDED or REFUNDED",
                    transaction.getId(), totalRefunded.doubleValue(), transaction.getStatus()
                ));
            }

            // Validate available refund amount (calculated as net amount paid)
            BigDecimal availableForRefund = refundSummary.getNetAmountPaid();
            BigDecimal expectedAvailable = totalPaid.subtract(totalRefunded);

            if (availableForRefund.subtract(expectedAvailable).abs().compareTo(new BigDecimal("0.01")) > 0) {
                result.addWarning(String.format(
                    "Available refund amount calculation inconsistency for transaction %s: Expected $%.2f, Found $%.2f",
                    transaction.getId(), expectedAvailable.doubleValue(), availableForRefund.doubleValue()
                ));
            }

        } catch (Exception e) {
            result.addError("Refund tracking validation failed for transaction " + transaction.getId() + ": " + e.getMessage());
        }
    }

    /**
     * Validate outstanding balance status
     */
    private void validateOutstandingBalanceStatus(Transaction transaction, CrossSystemValidationResult result) {
        try {
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            String status = transaction.getStatus();

            // Check status consistency with remaining balance
            boolean hasOutstandingBalance = remainingBalance.compareTo(new BigDecimal("0.01")) > 0;
            boolean statusIndicatesOutstanding = "PARTIAL_PAYMENT".equals(status) ||
                                               "PENDING".equals(status) ||
                                               "PENDING_COMPLETION".equals(status) ||
                                               (transaction.isInstallmentTransaction() &&
                                                ("PENDING".equals(transaction.getInstallmentStatus()) ||
                                                 "IN_PROGRESS".equals(transaction.getInstallmentStatus())));

            ValidationDetail statusDetail = new ValidationDetail(
                "OUTSTANDING_BALANCE_STATUS",
                transaction.getId().toString(),
                "Outstanding balance status consistency",
                (hasOutstandingBalance && statusIndicatesOutstanding) || (!hasOutstandingBalance && !statusIndicatesOutstanding),
                hasOutstandingBalance ? "Outstanding status" : "Completed status",
                status,
                "INFO"
            );

            result.addDetail(statusDetail);

            if (hasOutstandingBalance && !statusIndicatesOutstanding) {
                result.addError(String.format(
                    "Transaction %s has outstanding balance ($%.2f) but status '%s' doesn't indicate outstanding payment",
                    transaction.getId(), remainingBalance.doubleValue(), status
                ));
            }

            if (!hasOutstandingBalance && statusIndicatesOutstanding) {
                result.addWarning(String.format(
                    "Transaction %s has no outstanding balance but status '%s' indicates outstanding payment",
                    transaction.getId(), status
                ));
            }

            // Validate installment-specific consistency
            if (transaction.isInstallmentTransaction()) {
                String installmentStatus = transaction.getInstallmentStatus();
                boolean installmentComplete = "COMPLETED".equals(installmentStatus);

                if (!hasOutstandingBalance && !installmentComplete) {
                    result.addWarning(String.format(
                        "Installment transaction %s is fully paid but installment status is '%s' instead of COMPLETED",
                        transaction.getId(), installmentStatus
                    ));
                }
            }

        } catch (Exception e) {
            result.addError("Outstanding balance status validation failed for transaction " + transaction.getId() + ": " + e.getMessage());
        }
    }

    /**
     * Quick validation check for critical data consistency issues
     */
    public boolean hasDataConsistencyIssues(Long transactionId) {
        try {
            CrossSystemValidationResult result = validateTransaction(transactionId);
            return !result.isValid();
        } catch (Exception e) {
            return true; // Assume issues if validation fails
        }
    }

    /**
     * Get validation summary for dashboard display
     */
    public ValidationSummary getValidationSummary() {
        CrossSystemValidationResult result = validateAllOutstandingBalances();
        return result.getSummary();
    }
}
