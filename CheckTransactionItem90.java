import com.clothingstore.database.DatabaseManager;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class CheckTransactionItem90 {
    public static void main(String[] args) {
        try {
            System.out.println("=== CHECKING TRANSACTION ITEM 90 ===");
            
            try (Connection conn = DatabaseManager.getInstance().getConnection()) {
                
                // Check if transaction item 90 exists
                String itemSql = "SELECT id, transaction_id, product_id, quantity, unit_price FROM transaction_items WHERE id = 90";
                try (PreparedStatement stmt = conn.prepareStatement(itemSql); ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        System.out.println("Transaction item 90 found:");
                        System.out.printf("  ID: %d, TXN_ID: %d, Product_ID: %d, Qty: %d, Price: %.2f%n",
                            rs.getLong("id"),
                            rs.getLong("transaction_id"),
                            rs.getLong("product_id"),
                            rs.getInt("quantity"),
                            rs.getDouble("unit_price")
                        );
                    } else {
                        System.out.println("Transaction item 90 NOT FOUND!");
                    }
                }
                
                // Check what items transaction 42 (TXN249981) currently has
                System.out.println("\nCurrent items for transaction 42 (TXN249981):");
                String txnItemsSql = "SELECT id, product_id, quantity, unit_price FROM transaction_items WHERE transaction_id = 42";
                try (PreparedStatement stmt = conn.prepareStatement(txnItemsSql); ResultSet rs = stmt.executeQuery()) {
                    System.out.println("Item_ID | Product_ID | Quantity | Price");
                    System.out.println("----------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%d | %d | %d | %.2f%n",
                            rs.getLong("id"),
                            rs.getLong("product_id"),
                            rs.getInt("quantity"),
                            rs.getDouble("unit_price")
                        );
                    }
                }
                
            }
            
        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
