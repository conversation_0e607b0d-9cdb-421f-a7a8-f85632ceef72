package com.clothingstore.test;

import com.clothingstore.service.ProfitAnalysisService;
import com.clothingstore.service.ProfitAnalysisService.ProfitMetrics;
import com.clothingstore.service.ProfitAnalysisService.CategoryProfitData;
import com.clothingstore.database.OptimizedProfitQueries;
import com.clothingstore.database.OptimizedProfitQueries.ProfitQueryResult;
import com.clothingstore.database.OptimizedProfitQueries.CategoryProfitResult;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Test the optimized profit analysis system with real database data
 */
public class OptimizedProfitAnalysisTest {

    public static void main(String[] args) {
        System.out.println("=== OPTIMIZED PROFIT ANALYSIS TEST ===");
        System.out.println("Testing with real database data using optimized queries");
        System.out.println();

        try {
            // Initialize services
            ProfitAnalysisService profitService = new ProfitAnalysisService();
            OptimizedProfitQueries optimizedQueries = new OptimizedProfitQueries();

            // Test date range - last 30 days
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(30);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            System.out.println("Date Range: " + startDate.format(formatter) + " to " + endDate.format(formatter));
            System.out.println();

            // Test 1: Direct optimized query
            System.out.println("=== DIRECT OPTIMIZED QUERY TEST ===");
            ProfitQueryResult directResult = optimizedQueries.calculateProfitMetrics(startDate, endDate);
            System.out.println("Direct Query Results:");
            System.out.println("  Transactions: " + directResult.transactionCount);
            System.out.println("  Items Sold: " + directResult.totalItemsSold);
            System.out.println("  Revenue: $" + String.format("%.2f", directResult.totalRevenue));
            System.out.println("  Cost: $" + String.format("%.2f", directResult.totalCost));
            System.out.println("  Profit: $" + String.format("%.2f", (directResult.totalRevenue - directResult.totalCost)));
            System.out.println();

            // Test 2: Service layer with optimized queries
            System.out.println("=== SERVICE LAYER TEST ===");
            ProfitMetrics serviceResult = profitService.calculateProfitMetrics(startDate, endDate);
            System.out.println("Service Layer Results:");
            System.out.println("  Transactions: " + serviceResult.getTransactionCount());
            System.out.println("  Items Sold: " + serviceResult.getTotalItemsSold());
            System.out.println("  Revenue: " + serviceResult.getFormattedRevenue());
            System.out.println("  Cost: " + serviceResult.getFormattedCost());
            System.out.println("  Profit: " + serviceResult.getFormattedProfit());
            System.out.println("  Profit Margin: " + serviceResult.getFormattedProfitMargin());
            System.out.println();

            // Test 3: Category breakdown with optimized queries
            System.out.println("=== CATEGORY BREAKDOWN TEST ===");
            List<CategoryProfitResult> directCategories = optimizedQueries.calculateCategoryProfitBreakdown(startDate, endDate);
            System.out.println("Direct Category Query Results:");
            for (CategoryProfitResult category : directCategories) {
                double profit = category.revenue - category.cost;
                double margin = category.revenue > 0 ? (profit / category.revenue * 100) : 0.0;
                System.out.println("  " + category.category + ":");
                System.out.println("    Revenue: $" + String.format("%.2f", category.revenue));
                System.out.println("    Cost: $" + String.format("%.2f", category.cost));
                System.out.println("    Profit: $" + String.format("%.2f", profit));
                System.out.println("    Margin: " + String.format("%.1f", margin) + "%");
                System.out.println("    Items Sold: " + category.itemsSold);
                System.out.println("    Transactions: " + category.transactionCount);
                System.out.println();
            }

            // Test 4: Service layer category breakdown
            System.out.println("=== SERVICE CATEGORY BREAKDOWN TEST ===");
            List<CategoryProfitData> serviceCategories = profitService.calculateCategoryProfitBreakdown(startDate, endDate);
            System.out.println("Service Category Results:");
            for (CategoryProfitData category : serviceCategories) {
                System.out.println("  " + category.getCategoryName() + ":");
                System.out.println("    Revenue: " + category.getFormattedRevenue());
                System.out.println("    Cost: " + category.getFormattedCost());
                System.out.println("    Profit: " + category.getFormattedProfit());
                System.out.println("    Margin: " + category.getFormattedProfitMargin());
                System.out.println("    Items Sold: " + category.getItemsSold());
                System.out.println("    Transactions: " + category.getTransactionCount());
                System.out.println();
            }

            // Test 5: Performance comparison
            System.out.println("=== PERFORMANCE TEST ===");
            
            // Test optimized query performance
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < 10; i++) {
                profitService.calculateProfitMetrics(startDate, endDate);
            }
            long optimizedTime = System.currentTimeMillis() - startTime;
            
            System.out.println("10 optimized queries took: " + optimizedTime + "ms");
            System.out.println("Average per query: " + (optimizedTime / 10.0) + "ms");
            System.out.println();

            // Test 6: Cache effectiveness
            System.out.println("=== CACHE TEST ===");
            startTime = System.currentTimeMillis();
            profitService.calculateProfitMetrics(startDate, endDate); // Should use cache
            long cachedTime = System.currentTimeMillis() - startTime;
            System.out.println("Cached query took: " + cachedTime + "ms");
            System.out.println();

            // Validation
            System.out.println("=== VALIDATION ===");
            boolean dataMatches = Math.abs(directResult.totalRevenue - serviceResult.getRevenue()) < 0.01;
            System.out.println("Direct vs Service data matches: " + (dataMatches ? "✓ PASS" : "✗ FAIL"));
            
            boolean hasCategories = !serviceCategories.isEmpty();
            System.out.println("Category data available: " + (hasCategories ? "✓ PASS" : "✗ FAIL"));
            
            boolean performanceGood = optimizedTime < 1000; // Should be under 1 second for 10 queries
            System.out.println("Performance acceptable: " + (performanceGood ? "✓ PASS" : "✗ FAIL"));
            
            System.out.println();
            System.out.println("✓ OPTIMIZED PROFIT ANALYSIS TEST COMPLETE");
            System.out.println("✓ Real database data integration working");
            System.out.println("✓ Optimized queries performing well");
            System.out.println("✓ Category breakdown functional");

        } catch (Exception e) {
            System.err.println("Test failed with exception: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
