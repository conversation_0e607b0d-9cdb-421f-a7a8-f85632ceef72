package com.clothingstore.test;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Simple diagnostic tool to test database connection and data availability
 */
public class SimpleDatabaseDiagnostic {

    private static final String DB_URL = "*****************************";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void main(String[] args) {
        System.out.println("=== SIMPLE DATABASE DIAGNOSTIC ===");
        System.out.println("Testing database connection and data availability");
        System.out.println();

        try {
            // Test 1: Basic database connection
            testDatabaseConnection();
            
            // Test 2: Check table structure and data
            checkDatabaseStructure();
            
            // Test 3: Test profit analysis query manually
            testProfitAnalysisQuery();
            
            System.out.println("\n=== DIAGNOSTIC COMPLETE ===");
            
        } catch (Exception e) {
            System.err.println("Diagnostic failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testDatabaseConnection() {
        System.out.println("1. TESTING BASIC DATABASE CONNECTION");
        System.out.println("-----------------------------------");
        
        try {
            Class.forName("org.sqlite.JDBC");
            try (Connection conn = DriverManager.getConnection(DB_URL)) {
                System.out.println("✓ Database connection successful");
                System.out.println("  Database URL: " + DB_URL);
                System.out.println("  Connection valid: " + conn.isValid(5));
                System.out.println();
            }
        } catch (Exception e) {
            System.err.println("✗ Database connection failed: " + e.getMessage());
            System.out.println();
        }
    }

    private static void checkDatabaseStructure() {
        System.out.println("2. CHECKING DATABASE STRUCTURE AND DATA");
        System.out.println("---------------------------------------");
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // Check tables exist
            String[] tables = {"products", "customers", "transactions", "transaction_items"};
            for (String table : tables) {
                if (tableExists(conn, table)) {
                    int count = getTableRowCount(conn, table);
                    System.out.println("✓ Table '" + table + "' exists with " + count + " rows");
                } else {
                    System.out.println("✗ Table '" + table + "' does not exist");
                }
            }
            
            // Check specific data for profit analysis
            System.out.println("\nDATA ANALYSIS:");
            
            // Products with cost_price
            String productQuery = "SELECT COUNT(*) as total, COUNT(cost_price) as with_cost FROM products WHERE active = 1";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(productQuery)) {
                if (rs.next()) {
                    System.out.println("  Products: " + rs.getInt("total") + " total, " + rs.getInt("with_cost") + " with cost_price");
                }
            }
            
            // Transactions by status
            String transactionQuery = "SELECT status, COUNT(*) as count FROM transactions GROUP BY status";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(transactionQuery)) {
                System.out.println("  Transactions by status:");
                while (rs.next()) {
                    System.out.println("    " + rs.getString("status") + ": " + rs.getInt("count"));
                }
            }
            
            // Transaction items
            String itemQuery = "SELECT COUNT(*) as count FROM transaction_items";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(itemQuery)) {
                if (rs.next()) {
                    System.out.println("  Transaction items: " + rs.getInt("count"));
                }
            }
            
            // Sample transaction data
            String sampleQuery = "SELECT t.id, t.transaction_number, t.status, t.total_amount, " +
                    "t.transaction_date, COUNT(ti.id) as item_count " +
                    "FROM transactions t " +
                    "LEFT JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "GROUP BY t.id " +
                    "ORDER BY t.transaction_date DESC LIMIT 5";
            
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(sampleQuery)) {
                System.out.println("\n  Recent transactions:");
                while (rs.next()) {
                    System.out.println("    " + rs.getString("transaction_number") + 
                            " | " + rs.getString("status") + 
                            " | $" + rs.getDouble("total_amount") + 
                            " | " + rs.getInt("item_count") + " items" +
                            " | " + rs.getString("transaction_date"));
                }
            }
            
            // Check products with categories
            String categoryQuery = "SELECT category, COUNT(*) as count FROM products WHERE active = 1 GROUP BY category";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(categoryQuery)) {
                System.out.println("\n  Products by category:");
                while (rs.next()) {
                    System.out.println("    " + rs.getString("category") + ": " + rs.getInt("count"));
                }
            }
            
            System.out.println();
            
        } catch (SQLException e) {
            System.err.println("✗ Database structure check failed: " + e.getMessage());
            System.out.println();
        }
    }

    private static void testProfitAnalysisQuery() {
        System.out.println("3. TESTING PROFIT ANALYSIS QUERIES");
        System.out.println("----------------------------------");
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // Test date range - last 30 days
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(30);
            
            System.out.println("Date range: " + startDate.format(FORMATTER) + " to " + endDate.format(FORMATTER));
            
            // Test the exact query used by OptimizedProfitQueries
            String profitQuery = "SELECT " +
                    "COUNT(DISTINCT t.id) as transaction_count, " +
                    "SUM(ti.quantity) as total_items_sold, " +
                    "SUM(ti.line_total) as total_revenue, " +
                    "SUM(p.cost_price * ti.quantity) as total_cost " +
                    "FROM transactions t " +
                    "INNER JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "INNER JOIN products p ON ti.product_id = p.id " +
                    "WHERE t.status = 'COMPLETED' " +
                    "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) " +
                    "AND t.transaction_date BETWEEN ? AND ? " +
                    "AND p.cost_price IS NOT NULL";
            
            try (PreparedStatement stmt = conn.prepareStatement(profitQuery)) {
                stmt.setString(1, startDate.format(FORMATTER));
                stmt.setString(2, endDate.format(FORMATTER));
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        System.out.println("\nProfit Metrics Query Result:");
                        System.out.println("  Transactions: " + rs.getInt("transaction_count"));
                        System.out.println("  Items Sold: " + rs.getInt("total_items_sold"));
                        System.out.println("  Revenue: $" + String.format("%.2f", rs.getDouble("total_revenue")));
                        System.out.println("  Cost: $" + String.format("%.2f", rs.getDouble("total_cost")));
                        double profit = rs.getDouble("total_revenue") - rs.getDouble("total_cost");
                        System.out.println("  Profit: $" + String.format("%.2f", profit));
                    } else {
                        System.out.println("✗ No data returned from profit query");
                    }
                }
            }
            
            // Test category breakdown query
            String categoryQuery = "SELECT " +
                    "COALESCE(p.category, 'Uncategorized') as category, " +
                    "COUNT(DISTINCT t.id) as transaction_count, " +
                    "SUM(ti.quantity) as items_sold, " +
                    "SUM(ti.line_total) as revenue, " +
                    "SUM(p.cost_price * ti.quantity) as cost " +
                    "FROM transactions t " +
                    "INNER JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "INNER JOIN products p ON ti.product_id = p.id " +
                    "WHERE t.status = 'COMPLETED' " +
                    "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) " +
                    "AND t.transaction_date BETWEEN ? AND ? " +
                    "AND p.cost_price IS NOT NULL " +
                    "GROUP BY p.category " +
                    "ORDER BY (SUM(ti.line_total) - SUM(p.cost_price * ti.quantity)) DESC";
            
            try (PreparedStatement stmt = conn.prepareStatement(categoryQuery)) {
                stmt.setString(1, startDate.format(FORMATTER));
                stmt.setString(2, endDate.format(FORMATTER));
                
                try (ResultSet rs = stmt.executeQuery()) {
                    System.out.println("\nCategory Breakdown Query Result:");
                    int categoryCount = 0;
                    while (rs.next()) {
                        categoryCount++;
                        double profit = rs.getDouble("revenue") - rs.getDouble("cost");
                        System.out.println("  " + rs.getString("category") + ":");
                        System.out.println("    Revenue: $" + String.format("%.2f", rs.getDouble("revenue")));
                        System.out.println("    Cost: $" + String.format("%.2f", rs.getDouble("cost")));
                        System.out.println("    Profit: $" + String.format("%.2f", profit));
                        System.out.println("    Items: " + rs.getInt("items_sold"));
                        System.out.println("    Transactions: " + rs.getInt("transaction_count"));
                    }
                    
                    if (categoryCount == 0) {
                        System.out.println("  ✗ No categories found");
                    } else {
                        System.out.println("  ✓ Found " + categoryCount + " categories");
                    }
                }
            }
            
            // Test if there are any transactions at all in the date range
            String testQuery = "SELECT COUNT(*) as count FROM transactions WHERE transaction_date BETWEEN ? AND ?";
            try (PreparedStatement stmt = conn.prepareStatement(testQuery)) {
                stmt.setString(1, startDate.format(FORMATTER));
                stmt.setString(2, endDate.format(FORMATTER));
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        System.out.println("\n  Transactions in date range: " + rs.getInt("count"));
                    }
                }
            }
            
            System.out.println();
            
        } catch (SQLException e) {
            System.err.println("✗ Profit analysis query test failed: " + e.getMessage());
            e.printStackTrace();
            System.out.println();
        }
    }

    private static boolean tableExists(Connection conn, String tableName) throws SQLException {
        DatabaseMetaData meta = conn.getMetaData();
        try (ResultSet rs = meta.getTables(null, null, tableName, new String[]{"TABLE"})) {
            return rs.next();
        }
    }

    private static int getTableRowCount(Connection conn, String tableName) throws SQLException {
        String query = "SELECT COUNT(*) FROM " + tableName;
        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(query)) {
            return rs.next() ? rs.getInt(1) : 0;
        }
    }
}
