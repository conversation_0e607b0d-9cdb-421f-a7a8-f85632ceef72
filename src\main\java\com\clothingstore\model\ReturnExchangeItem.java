package com.clothingstore.model;

import java.math.BigDecimal;

/**
 * Model class for individual items in a Return/Exchange transaction
 */
public class ReturnExchangeItem {
    private Long id;
    private Long returnExchangeId;
    private Long originalTransactionItemId;
    private Long productId;
    private String productName;
    private String productSku;
    private int originalQuantity;
    private int returnQuantity;
    private BigDecimal unitPrice;
    private BigDecimal lineTotal;
    private String condition; // NEW, USED, DAMAGED
    private String reason;
    private String action; // REFUND, EXCHANGE, STORE_CREDIT
    private Long exchangeProductId;
    private String exchangeProductName;
    private int exchangeQuantity;
    private BigDecimal exchangeUnitPrice;
    private BigDecimal exchangeLineTotal;

    // Constructors
    public ReturnExchangeItem() {
        this.condition = "NEW";
        this.action = "REFUND";
    }

    public ReturnExchangeItem(Long returnExchangeId, Long productId, String productName, 
                             int originalQuantity, int returnQuantity, BigDecimal unitPrice) {
        this();
        this.returnExchangeId = returnExchangeId;
        this.productId = productId;
        this.productName = productName;
        this.originalQuantity = originalQuantity;
        this.returnQuantity = returnQuantity;
        this.unitPrice = unitPrice;
        calculateLineTotal();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReturnExchangeId() {
        return returnExchangeId;
    }

    public void setReturnExchangeId(Long returnExchangeId) {
        this.returnExchangeId = returnExchangeId;
    }

    public Long getOriginalTransactionItemId() {
        return originalTransactionItemId;
    }

    public void setOriginalTransactionItemId(Long originalTransactionItemId) {
        this.originalTransactionItemId = originalTransactionItemId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductSku() {
        return productSku;
    }

    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }

    public int getOriginalQuantity() {
        return originalQuantity;
    }

    public void setOriginalQuantity(int originalQuantity) {
        this.originalQuantity = originalQuantity;
    }

    public int getReturnQuantity() {
        return returnQuantity;
    }

    public void setReturnQuantity(int returnQuantity) {
        this.returnQuantity = returnQuantity;
        calculateLineTotal();
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        calculateLineTotal();
    }

    public BigDecimal getLineTotal() {
        return lineTotal;
    }

    public void setLineTotal(BigDecimal lineTotal) {
        this.lineTotal = lineTotal;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Long getExchangeProductId() {
        return exchangeProductId;
    }

    public void setExchangeProductId(Long exchangeProductId) {
        this.exchangeProductId = exchangeProductId;
    }

    public String getExchangeProductName() {
        return exchangeProductName;
    }

    public void setExchangeProductName(String exchangeProductName) {
        this.exchangeProductName = exchangeProductName;
    }

    public int getExchangeQuantity() {
        return exchangeQuantity;
    }

    public void setExchangeQuantity(int exchangeQuantity) {
        this.exchangeQuantity = exchangeQuantity;
        calculateExchangeLineTotal();
    }

    public BigDecimal getExchangeUnitPrice() {
        return exchangeUnitPrice;
    }

    public void setExchangeUnitPrice(BigDecimal exchangeUnitPrice) {
        this.exchangeUnitPrice = exchangeUnitPrice;
        calculateExchangeLineTotal();
    }

    public BigDecimal getExchangeLineTotal() {
        return exchangeLineTotal;
    }

    public void setExchangeLineTotal(BigDecimal exchangeLineTotal) {
        this.exchangeLineTotal = exchangeLineTotal;
    }

    // Utility methods
    private void calculateLineTotal() {
        if (unitPrice != null && returnQuantity > 0) {
            this.lineTotal = unitPrice.multiply(new BigDecimal(returnQuantity));
        } else {
            this.lineTotal = BigDecimal.ZERO;
        }
    }

    private void calculateExchangeLineTotal() {
        if (exchangeUnitPrice != null && exchangeQuantity > 0) {
            this.exchangeLineTotal = exchangeUnitPrice.multiply(new BigDecimal(exchangeQuantity));
        } else {
            this.exchangeLineTotal = BigDecimal.ZERO;
        }
    }

    public boolean isRefund() {
        return "REFUND".equals(action);
    }

    public boolean isExchange() {
        return "EXCHANGE".equals(action);
    }

    public boolean isStoreCredit() {
        return "STORE_CREDIT".equals(action);
    }

    public boolean isNew() {
        return "NEW".equals(condition);
    }

    public boolean isUsed() {
        return "USED".equals(condition);
    }

    public boolean isDamaged() {
        return "DAMAGED".equals(condition);
    }

    public boolean isPartialReturn() {
        return returnQuantity < originalQuantity;
    }

    public boolean isFullReturn() {
        return returnQuantity == originalQuantity;
    }

    public BigDecimal getRefundAmount() {
        if (isRefund()) {
            // Apply condition-based refund percentage
            BigDecimal refundPercentage = getRefundPercentage();
            return lineTotal.multiply(refundPercentage);
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal getRefundPercentage() {
        switch (condition) {
            case "NEW": return BigDecimal.ONE; // 100%
            case "USED": return new BigDecimal("0.80"); // 80%
            case "DAMAGED": return new BigDecimal("0.50"); // 50%
            default: return BigDecimal.ONE;
        }
    }

    public BigDecimal getExchangeDifference() {
        if (isExchange() && exchangeLineTotal != null) {
            return exchangeLineTotal.subtract(lineTotal);
        }
        return BigDecimal.ZERO;
    }

    public String getDisplayCondition() {
        switch (condition) {
            case "NEW": return "New";
            case "USED": return "Used";
            case "DAMAGED": return "Damaged";
            default: return condition;
        }
    }

    public String getDisplayAction() {
        switch (action) {
            case "REFUND": return "Refund";
            case "EXCHANGE": return "Exchange";
            case "STORE_CREDIT": return "Store Credit";
            default: return action;
        }
    }

    @Override
    public String toString() {
        return String.format("ReturnExchangeItem{product=%s, qty=%d/%d, action=%s}", 
                productName, returnQuantity, originalQuantity, action);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ReturnExchangeItem that = (ReturnExchangeItem) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
