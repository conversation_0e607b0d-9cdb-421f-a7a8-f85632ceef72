package com.clothingstore.view;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.text.NumberFormat;
import java.util.ResourceBundle;

import com.clothingstore.model.Transaction;
import com.clothingstore.service.PaymentHistoryService;

import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.RadioButton;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.control.ToggleGroup;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Controller for the enhanced payment dialog
 */
public class PaymentDialogController implements Initializable {

    @FXML
    private Label lblTransactionNumber;
    @FXML
    private Label lblSubtotal;
    @FXML
    private Label lblDiscount;
    @FXML
    private Label lblTax;
    @FXML
    private Label lblTotalAmount;
    @FXML
    private Label lblAmountPaidLabel;
    @FXML
    private Label lblAmountPaid;
    @FXML
    private Label lblRemainingBalanceLabel;
    @FXML
    private Label lblRemainingBalance;

    @FXML
    private ComboBox<String> cmbPaymentMethod;
    @FXML
    private TextField txtPaymentAmount;
    @FXML
    private Button btnFullPayment;
    @FXML
    private RadioButton rbFullPayment;
    @FXML
    private RadioButton rbPartialPayment;

    @FXML
    private VBox vboxChangeInfo;
    @FXML
    private Label lblChangeLabel;
    @FXML
    private Label lblChange;
    @FXML
    private Label lblValidationMessage;
    @FXML
    private TextArea txtNotes;

    @FXML
    private Button btnCancel;
    @FXML
    private Button btnProcessPayment;
    @FXML
    private Button btn25Percent;
    @FXML
    private Button btn50Percent;
    @FXML
    private Button btn75Percent;

    private Transaction transaction;
    private boolean paymentProcessed = false;
    private BigDecimal paymentAmount = BigDecimal.ZERO;
    private String selectedPaymentMethod;
    private boolean isPartialPayment = false;

    private final NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
    private ToggleGroup paymentTypeGroup;
    private PaymentHistoryService paymentHistoryService;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("DEBUG: PaymentDialogController.initialize() called");
        try {
            // Initialize services
            paymentHistoryService = PaymentHistoryService.getInstance();

            setupPaymentMethods();
            System.out.println("DEBUG: Payment methods setup complete");

            setupPaymentTypeToggle();
            System.out.println("DEBUG: Payment type toggle setup complete");

            setupEventHandlers();
            System.out.println("DEBUG: Event handlers setup complete");

            setupValidation();
            System.out.println("DEBUG: Validation setup complete");

            System.out.println("DEBUG: PaymentDialogController initialization successful");
        } catch (Exception e) {
            System.err.println("ERROR: Exception during PaymentDialogController initialization:");
            e.printStackTrace();
            throw e;
        }
    }

    private void setupPaymentMethods() {
        cmbPaymentMethod.setItems(FXCollections.observableArrayList(
                "CASH", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "GIFT_CARD"
        ));
        cmbPaymentMethod.setValue("CASH");
    }

    private void setupPaymentTypeToggle() {
        paymentTypeGroup = new ToggleGroup();
        rbFullPayment.setToggleGroup(paymentTypeGroup);
        rbPartialPayment.setToggleGroup(paymentTypeGroup);

        rbFullPayment.setSelected(true);
    }

    private void setupEventHandlers() {
        // Payment amount text field
        txtPaymentAmount.textProperty().addListener((obs, oldVal, newVal) -> {
            validateAndUpdatePayment();
        });

        // Payment method selection
        cmbPaymentMethod.setOnAction(e -> updatePaymentMethodDisplay());

        // Payment type radio buttons
        rbFullPayment.setOnAction(e -> handlePaymentTypeChange());
        rbPartialPayment.setOnAction(e -> handlePaymentTypeChange());
    }

    private void setupValidation() {
        // Initially hide validation message and change info
        lblValidationMessage.setVisible(false);
        vboxChangeInfo.setVisible(false);

        // Hide amount paid and remaining balance initially
        lblAmountPaidLabel.setVisible(false);
        lblAmountPaid.setVisible(false);
        lblRemainingBalanceLabel.setVisible(false);
        lblRemainingBalance.setVisible(false);
    }

    public void setTransaction(Transaction transaction) {
        System.out.println("DEBUG: setTransaction called with: " + transaction);
        if (transaction != null) {
            System.out.println("DEBUG: Transaction number: " + transaction.getTransactionNumber());
            System.out.println("DEBUG: Transaction remaining balance: " + transaction.getRemainingBalance());
        }
        this.transaction = transaction;
        try {
            updateTransactionDisplay();
            System.out.println("DEBUG: Transaction display updated successfully");
        } catch (Exception e) {
            System.err.println("ERROR: Exception in updateTransactionDisplay:");
            e.printStackTrace();
            throw e;
        }
    }

    private void updateTransactionDisplay() {
        if (transaction == null) {
            return;
        }

        lblTransactionNumber.setText("Transaction: " + transaction.getTransactionNumber());
        lblSubtotal.setText(currencyFormat.format(transaction.getSubtotal()));
        lblDiscount.setText(currencyFormat.format(transaction.getDiscountAmount()));
        lblTax.setText(currencyFormat.format(transaction.getTaxAmount()));
        lblTotalAmount.setText(currencyFormat.format(transaction.getTotalAmount()));

        // Show existing payment info if this is a partial payment transaction
        BigDecimal amountPaid = transaction.getAmountPaid();
        if (amountPaid.compareTo(BigDecimal.ZERO) > 0) {
            lblAmountPaidLabel.setVisible(true);
            lblAmountPaid.setVisible(true);
            lblAmountPaid.setText(currencyFormat.format(amountPaid));

            BigDecimal remainingBalance = transaction.getRemainingBalance();
            if (remainingBalance.compareTo(BigDecimal.ZERO) > 0) {
                lblRemainingBalanceLabel.setVisible(true);
                lblRemainingBalance.setVisible(true);
                lblRemainingBalance.setText(currencyFormat.format(remainingBalance));

                // Set default payment amount to remaining balance
                txtPaymentAmount.setText(remainingBalance.toString());
            }
        } else {
            // New transaction - set default to full amount
            txtPaymentAmount.setText(transaction.getTotalAmount().toString());
        }

        validateAndUpdatePayment();
    }

    @FXML
    private void handleFullPayment() {
        if (transaction != null) {
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            txtPaymentAmount.setText(remainingBalance.toString());
            rbFullPayment.setSelected(true);
            validateAndUpdatePayment();
        }
    }

    @FXML
    private void handle25Percent() {
        if (transaction != null) {
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            BigDecimal amount = remainingBalance.multiply(new BigDecimal("0.25"));
            txtPaymentAmount.setText(amount.setScale(2, RoundingMode.HALF_UP).toString());
            rbPartialPayment.setSelected(true);
            validateAndUpdatePayment();
        }
    }

    @FXML
    private void handle50Percent() {
        if (transaction != null) {
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            BigDecimal amount = remainingBalance.multiply(new BigDecimal("0.50"));
            txtPaymentAmount.setText(amount.setScale(2, RoundingMode.HALF_UP).toString());
            rbPartialPayment.setSelected(true);
            validateAndUpdatePayment();
        }
    }

    @FXML
    private void handle75Percent() {
        if (transaction != null) {
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            BigDecimal amount = remainingBalance.multiply(new BigDecimal("0.75"));
            txtPaymentAmount.setText(amount.setScale(2, RoundingMode.HALF_UP).toString());
            rbPartialPayment.setSelected(true);
            validateAndUpdatePayment();
        }
    }

    @FXML
    private void handleCustomAmount() {
        // Clear the field and focus on it for custom input
        txtPaymentAmount.clear();
        txtPaymentAmount.requestFocus();
        rbPartialPayment.setSelected(true);
    }

    private void handlePaymentTypeChange() {
        validateAndUpdatePayment();
    }

    private void updatePaymentMethodDisplay() {
        // Update UI based on payment method if needed
        validateAndUpdatePayment();
    }

    private void validateAndUpdatePayment() {
        if (transaction == null) {
            return;
        }

        try {
            // Clear previous validation messages
            lblValidationMessage.setVisible(false);
            vboxChangeInfo.setVisible(false);

            // Parse payment amount
            String amountText = txtPaymentAmount.getText().trim();
            if (amountText.isEmpty()) {
                paymentAmount = BigDecimal.ZERO;
                btnProcessPayment.setDisable(true);
                return;
            }

            paymentAmount = new BigDecimal(amountText);
            BigDecimal remainingBalance = transaction.getRemainingBalance();

            // Enhanced validation for Outstanding Balances
            if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                showValidationError("Payment amount must be greater than zero");
                return;
            }

            // For Outstanding Balances, prevent overpayment
            if (paymentAmount.compareTo(remainingBalance) > 0) {
                showValidationError(String.format("Payment amount cannot exceed remaining balance of %s",
                        currencyFormat.format(remainingBalance)));
                return;
            }

            // Minimum payment validation (e.g., at least $0.01)
            BigDecimal minimumPayment = new BigDecimal("0.01");
            if (paymentAmount.compareTo(minimumPayment) < 0) {
                showValidationError(String.format("Minimum payment amount is %s",
                        currencyFormat.format(minimumPayment)));
                return;
            }

            // Show payment preview
            if (paymentAmount.compareTo(remainingBalance) == 0) {
                // Full payment - transaction will be completed
                lblChangeLabel.setText("Transaction Status:");
                lblChange.setText("WILL BE COMPLETED");
                lblChange.setStyle("-fx-font-weight: bold; -fx-text-fill: #27ae60;");
                vboxChangeInfo.setStyle("-fx-background-color: #d5f4e6; -fx-padding: 15; -fx-border-color: #27ae60; -fx-border-width: 1; -fx-border-radius: 6; -fx-background-radius: 6;");
                vboxChangeInfo.setVisible(true);
                isPartialPayment = false;
                rbFullPayment.setSelected(true);
            } else {
                // Partial payment - show new remaining balance
                BigDecimal newRemainingBalance = remainingBalance.subtract(paymentAmount);
                lblChangeLabel.setText("New Remaining Balance:");
                lblChange.setText(currencyFormat.format(newRemainingBalance));
                lblChange.setStyle("-fx-font-weight: bold; -fx-text-fill: #e74c3c;");
                vboxChangeInfo.setStyle("-fx-background-color: #fff3cd; -fx-padding: 15; -fx-border-color: #ffeaa7; -fx-border-width: 1; -fx-border-radius: 6; -fx-background-radius: 6;");
                vboxChangeInfo.setVisible(true);
                isPartialPayment = true;
                rbPartialPayment.setSelected(true);
            }

            btnProcessPayment.setDisable(false);

        } catch (NumberFormatException e) {
            showValidationError("Please enter a valid numeric amount (e.g., 25.50)");
        }
    }

    private void showValidationError(String message) {
        lblValidationMessage.setText(message);
        lblValidationMessage.setVisible(true);
        lblValidationMessage.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;");
        btnProcessPayment.setDisable(true);
        vboxChangeInfo.setVisible(false);
    }

    private void clearValidationError() {
        lblValidationMessage.setText("");
        lblValidationMessage.setVisible(false);
    }

    @FXML
    private void handleProcessPayment() {
        if (transaction == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        selectedPaymentMethod = cmbPaymentMethod.getValue();
        if (selectedPaymentMethod == null) {
            showValidationError("Please select a payment method");
            return;
        }

        try {
            // Process the payment based on type
            if (isPartialPayment) {
                transaction.processPartialPayment(paymentAmount);
            } else {
                transaction.processFullPayment(paymentAmount);
            }

            // Set payment method and notes
            transaction.setPaymentMethod(selectedPaymentMethod);
            String notes = txtNotes.getText().trim();
            if (!notes.isEmpty()) {
                String existingNotes = transaction.getNotes();
                if (existingNotes != null && !existingNotes.isEmpty()) {
                    transaction.setNotes(existingNotes + "\n" + notes);
                } else {
                    transaction.setNotes(notes);
                }
            }

            paymentProcessed = true;
            closeDialog();

        } catch (Exception e) {
            showValidationError("Payment processing error: " + e.getMessage());
        }
    }

    @FXML
    private void handleCancel() {
        closeDialog();
    }

    private void closeDialog() {
        Stage stage = (Stage) btnCancel.getScene().getWindow();
        stage.close();
    }

    public boolean isPaymentProcessed() {
        return paymentProcessed;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public String getSelectedPaymentMethod() {
        return selectedPaymentMethod;
    }

    public boolean isPartialPayment() {
        return isPartialPayment;
    }

    /**
     * Record payment history for the processed payment
     * This should be called after the transaction is saved to the database
     */
    public void recordPaymentHistory(Long transactionId) {
        if (paymentProcessed && paymentHistoryService != null && transactionId != null) {
            try {
                String cashierName = "System"; // In a real system, this would be the logged-in user
                String notes = "Payment processed via POS system";
                if (txtNotes.getText() != null && !txtNotes.getText().trim().isEmpty()) {
                    notes = txtNotes.getText().trim();
                }

                paymentHistoryService.recordPayment(transactionId, paymentAmount, selectedPaymentMethod, cashierName, notes);
                System.out.println("DEBUG: Payment history recorded for transaction " + transactionId);
            } catch (Exception e) {
                System.err.println("WARNING: Failed to record payment history: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * Get payment details for external history recording
     */
    public PaymentDetails getPaymentDetails() {
        if (paymentProcessed) {
            return new PaymentDetails(paymentAmount, selectedPaymentMethod, txtNotes.getText());
        }
        return null;
    }

    /**
     * Helper class to hold payment details
     */
    public static class PaymentDetails {
        private final BigDecimal amount;
        private final String paymentMethod;
        private final String notes;

        public PaymentDetails(BigDecimal amount, String paymentMethod, String notes) {
            this.amount = amount;
            this.paymentMethod = paymentMethod;
            this.notes = notes;
        }

        public BigDecimal getAmount() { return amount; }
        public String getPaymentMethod() { return paymentMethod; }
        public String getNotes() { return notes; }
    }

    public BigDecimal getChangeAmount() {
        if (transaction != null && paymentAmount.compareTo(transaction.getRemainingBalance()) > 0) {
            return paymentAmount.subtract(transaction.getRemainingBalance());
        }
        return BigDecimal.ZERO;
    }

    /**
     * Pre-select partial payment mode for installment payments
     */
    public void setPartialPaymentMode(boolean partialMode) {
        if (partialMode) {
            rbPartialPayment.setSelected(true);
            rbFullPayment.setSelected(false);

            // Set a default partial payment amount (e.g., 50% of total)
            if (transaction != null) {
                BigDecimal remainingBalance = transaction.getRemainingBalance();
                BigDecimal defaultPartialAmount = remainingBalance.multiply(new BigDecimal("0.5"));
                txtPaymentAmount.setText(defaultPartialAmount.setScale(2, RoundingMode.HALF_UP).toString());

                // Update the display to show this is a partial payment
                validateAndUpdatePayment();
            }
        } else {
            rbFullPayment.setSelected(true);
            rbPartialPayment.setSelected(false);
        }
    }

    /**
     * Set a custom partial payment amount
     */
    public void setCustomPartialAmount(BigDecimal amount) {
        if (transaction != null && amount != null) {
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            if (amount.compareTo(remainingBalance) < 0) {
                rbPartialPayment.setSelected(true);
                rbFullPayment.setSelected(false);
                txtPaymentAmount.setText(amount.setScale(2, RoundingMode.HALF_UP).toString());
                validateAndUpdatePayment();
            }
        }
    }

    /**
     * Get transaction details for Outstanding Balances integration
     */
    public String getTransactionSummary() {
        if (transaction != null && isPartialPayment) {
            BigDecimal remainingBalance = transaction.getRemainingBalance().subtract(paymentAmount);
            return String.format("Transaction %s: Partial payment of %s processed. Remaining balance: %s",
                    transaction.getTransactionNumber(),
                    NumberFormat.getCurrencyInstance().format(paymentAmount),
                    NumberFormat.getCurrencyInstance().format(remainingBalance));
        }
        return "";
    }
}
