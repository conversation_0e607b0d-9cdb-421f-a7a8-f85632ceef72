package com.clothingstore.model;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * TransactionItem model class representing individual items in a transaction
 */
public class TransactionItem {

    private Long id;
    private Long transactionId;
    private Transaction transaction;
    private Long productId;
    private Product product;
    private int quantity;
    private BigDecimal unitPrice;
    private BigDecimal lineTotal;
    private BigDecimal discountAmount;
    private String notes;
    private BigDecimal discount;

    // Constructors
    public TransactionItem() {
        this.quantity = 1;
        this.discountAmount = BigDecimal.ZERO;
    }

    public TransactionItem(Product product, int quantity) {
        this();
        this.product = product;
        this.productId = product.getId();
        this.quantity = quantity;
        this.unitPrice = product.getPrice();
        calculateLineTotal();
    }

    public TransactionItem(Product product, int quantity, BigDecimal unitPrice) {
        this();
        this.product = product;
        this.productId = product.getId();
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        calculateLineTotal();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
        this.transactionId = transaction != null ? transaction.getId() : null;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
        this.productId = product != null ? product.getId() : null;
        if (product != null && unitPrice == null) {
            this.unitPrice = product.getPrice();
        }
        calculateLineTotal();
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
        calculateLineTotal();
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        calculateLineTotal();
    }

    public BigDecimal getLineTotal() {
        return lineTotal;
    }

    public void setLineTotal(BigDecimal lineTotal) {
        this.lineTotal = lineTotal;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
        calculateLineTotal();
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    // Business methods
    private void calculateLineTotal() {
        if (unitPrice != null && quantity > 0) {
            BigDecimal subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
            lineTotal = subtotal.subtract(discountAmount != null ? discountAmount : BigDecimal.ZERO);
        } else {
            lineTotal = BigDecimal.ZERO;
        }
    }

    public void applyDiscount(BigDecimal discount) {
        this.discountAmount = discount;
        calculateLineTotal();
    }

    public void applyPercentageDiscount(double percentage) {
        if (percentage < 0 || percentage > 100) {
            throw new IllegalArgumentException("Discount percentage must be between 0 and 100");
        }
        BigDecimal subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
        this.discountAmount = subtotal.multiply(BigDecimal.valueOf(percentage / 100));
        calculateLineTotal();
    }

    public BigDecimal getSubtotal() {
        return unitPrice != null ? unitPrice.multiply(BigDecimal.valueOf(quantity)) : BigDecimal.ZERO;
    }

    public double getDiscountPercentage() {
        BigDecimal subtotal = getSubtotal();
        if (subtotal.compareTo(BigDecimal.ZERO) > 0 && discountAmount != null) {
            return discountAmount.divide(subtotal, 4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100;
        }
        return 0.0;
    }

    // Helper methods for display
    public String getProductName() {
        return product != null ? product.getName() : "Unknown Product";
    }

    public String getProductSku() {
        return product != null ? product.getSku() : "";
    }

    public String getProductSize() {
        return product != null ? product.getSize() : "";
    }

    public String getProductColor() {
        return product != null ? product.getColor() : "";
    }

    public String getDisplayInfo() {
        StringBuilder sb = new StringBuilder();
        if (product != null) {
            sb.append(product.getName());
            if (product.getSize() != null) {
                sb.append(" (Size: ").append(product.getSize()).append(")");
            }
            if (product.getColor() != null) {
                sb.append(" (Color: ").append(product.getColor()).append(")");
            }
        }
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TransactionItem that = (TransactionItem) o;
        return Objects.equals(id, that.id)
                && Objects.equals(transactionId, that.transactionId)
                && Objects.equals(productId, that.productId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, transactionId, productId);
    }

    @Override
    public String toString() {
        return String.format("TransactionItem{id=%d, product='%s', quantity=%d, unitPrice=%s, lineTotal=%s}",
                id, getProductName(), quantity, unitPrice, lineTotal);
    }
}
