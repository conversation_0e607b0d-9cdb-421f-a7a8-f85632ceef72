
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.database.ConnectionPool;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;

public class PerformanceAnalysis {

    public static void main(String[] args) {
        System.out.println("=== CLOTHING STORE PERFORMANCE ANALYSIS ===");
        System.out.println("Analyzing application performance characteristics...\n");

        try {
            // Initialize database
            DatabaseManager.getInstance();

            // 1. Database Performance Analysis
            analyzeDatabasePerformance();

            // 2. Memory Usage Analysis
            analyzeMemoryUsage();

            // 3. Query Performance Analysis
            analyzeQueryPerformance();

            // 4. Connection Pool Analysis
            analyzeConnectionPool();

            // 5. Data Loading Performance
            analyzeDataLoadingPerformance();

            // 6. Identify Performance Bottlenecks
            identifyBottlenecks();

            System.out.println("\n=== PERFORMANCE ANALYSIS COMPLETE ===");

        } catch (Exception e) {
            System.err.println("Performance analysis failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void analyzeDatabasePerformance() {
        System.out.println("1. DATABASE PERFORMANCE ANALYSIS");
        System.out.println("----------------------------------------");

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Check database size
            Statement stmt = conn.createStatement();

            // Get table sizes
            String[] tables = {"transactions", "transaction_items", "products", "customers", "payment_history"};
            for (String table : tables) {
                try {
                    ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as count FROM " + table);
                    if (rs.next()) {
                        int count = rs.getInt("count");
                        System.out.println("  " + table + ": " + count + " records");
                    }
                } catch (SQLException e) {
                    System.out.println("  " + table + ": Error - " + e.getMessage());
                }
            }

            // Check database file size
            ResultSet rs = stmt.executeQuery("PRAGMA page_count");
            if (rs.next()) {
                int pageCount = rs.getInt(1);
                rs = stmt.executeQuery("PRAGMA page_size");
                if (rs.next()) {
                    int pageSize = rs.getInt(1);
                    long dbSize = (long) pageCount * pageSize;
                    System.out.println("  Database size: " + (dbSize / 1024 / 1024) + " MB");
                }
            }

            // Check SQLite optimizations
            rs = stmt.executeQuery("PRAGMA journal_mode");
            if (rs.next()) {
                System.out.println("  Journal mode: " + rs.getString(1));
            }

            rs = stmt.executeQuery("PRAGMA synchronous");
            if (rs.next()) {
                System.out.println("  Synchronous mode: " + rs.getInt(1));
            }

        } catch (SQLException e) {
            System.err.println("Database analysis error: " + e.getMessage());
        }
    }

    private static void analyzeMemoryUsage() {
        System.out.println("\n2. MEMORY USAGE ANALYSIS");
        System.out.println("----------------------------------------");

        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        System.out.println("  Total Memory: " + (totalMemory / 1024 / 1024) + " MB");
        System.out.println("  Used Memory: " + (usedMemory / 1024 / 1024) + " MB");
        System.out.println("  Free Memory: " + (freeMemory / 1024 / 1024) + " MB");
        System.out.println("  Max Memory: " + (maxMemory / 1024 / 1024) + " MB");
        System.out.println("  Memory Usage: " + ((usedMemory * 100) / totalMemory) + "%");

        // Check for potential memory issues
        if (usedMemory > (maxMemory * 0.8)) {
            System.out.println("  ⚠ WARNING: High memory usage detected!");
        }
        if (freeMemory < (totalMemory * 0.2)) {
            System.out.println("  ⚠ WARNING: Low free memory!");
        }
    }

    private static void analyzeQueryPerformance() {
        System.out.println("\n3. QUERY PERFORMANCE ANALYSIS");
        System.out.println("----------------------------------------");

        try {
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            ProductDAO productDAO = ProductDAO.getInstance();
            CustomerDAO customerDAO = CustomerDAO.getInstance();

            // Test transaction loading performance
            long startTime = System.currentTimeMillis();
            List<Transaction> transactions = transactionDAO.findAll();
            long transactionLoadTime = System.currentTimeMillis() - startTime;
            System.out.println("  Transaction loading: " + transactionLoadTime + "ms (" + transactions.size() + " records)");

            // Test product loading performance
            startTime = System.currentTimeMillis();
            List<Product> products = productDAO.findAll();
            long productLoadTime = System.currentTimeMillis() - startTime;
            System.out.println("  Product loading: " + productLoadTime + "ms (" + products.size() + " records)");

            // Test customer loading performance
            startTime = System.currentTimeMillis();
            List<Customer> customers = customerDAO.findAll();
            long customerLoadTime = System.currentTimeMillis() - startTime;
            System.out.println("  Customer loading: " + customerLoadTime + "ms (" + customers.size() + " records)");

            // Test outstanding balance query
            startTime = System.currentTimeMillis();
            List<Transaction> outstandingTransactions = transactionDAO.findWithOutstandingBalances();
            long outstandingLoadTime = System.currentTimeMillis() - startTime;
            System.out.println("  Outstanding balances: " + outstandingLoadTime + "ms (" + outstandingTransactions.size() + " records)");

            // Performance warnings
            if (transactionLoadTime > 1000) {
                System.out.println("  ⚠ WARNING: Slow transaction loading (>1s)");
            }
            if (productLoadTime > 500) {
                System.out.println("  ⚠ WARNING: Slow product loading (>500ms)");
            }

        } catch (Exception e) {
            System.err.println("Query performance analysis error: " + e.getMessage());
        }
    }

    private static void analyzeConnectionPool() {
        System.out.println("\n4. CONNECTION POOL ANALYSIS");
        System.out.println("----------------------------------------");

        try {
            ConnectionPool pool = ConnectionPool.getInstance();

            // Test connection acquisition speed
            long startTime = System.currentTimeMillis();
            Connection conn = pool.getConnection();
            long acquisitionTime = System.currentTimeMillis() - startTime;
            System.out.println("  Connection acquisition: " + acquisitionTime + "ms");

            // Test connection validity
            boolean isValid = conn.isValid(5);
            System.out.println("  Connection validity: " + (isValid ? "Valid" : "Invalid"));

            pool.returnConnection(conn);

            // Performance warnings
            if (acquisitionTime > 100) {
                System.out.println("  ⚠ WARNING: Slow connection acquisition (>100ms)");
            }

        } catch (Exception e) {
            System.err.println("Connection pool analysis error: " + e.getMessage());
        }
    }

    private static void analyzeDataLoadingPerformance() {
        System.out.println("\n5. DATA LOADING PERFORMANCE ANALYSIS");
        System.out.println("----------------------------------------");

        try {
            TransactionDAO transactionDAO = TransactionDAO.getInstance();

            // Test individual transaction loading (potential N+1 issue)
            long startTime = System.currentTimeMillis();
            List<Transaction> transactions = transactionDAO.findAll();
            long totalLoadTime = System.currentTimeMillis() - startTime;

            if (!transactions.isEmpty()) {
                double avgLoadTime = (double) totalLoadTime / transactions.size();
                System.out.println("  Average transaction load time: " + String.format("%.2f", avgLoadTime) + "ms per record");

                // Check for N+1 query pattern
                if (avgLoadTime > 10) {
                    System.out.println("  ⚠ WARNING: Potential N+1 query issue detected!");
                    System.out.println("    Consider using JOIN queries to load related data");
                }
            }

        } catch (Exception e) {
            System.err.println("Data loading analysis error: " + e.getMessage());
        }
    }

    private static void identifyBottlenecks() {
        System.out.println("\n6. PERFORMANCE BOTTLENECK IDENTIFICATION");
        System.out.println("----------------------------------------");

        System.out.println("Potential Performance Issues:");

        // Check memory usage
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();

        if (usedMemory > (maxMemory * 0.7)) {
            System.out.println("  ⚠ HIGH MEMORY USAGE: Consider increasing heap size or optimizing memory usage");
        }

        // Database-related bottlenecks
        System.out.println("  • Transaction loading uses individual queries for items and customers (N+1 pattern)");
        System.out.println("  • No database indexes on frequently queried columns");
        System.out.println("  • In-memory services (Supplier, Loyalty) may cause memory issues with large datasets");

        // UI-related bottlenecks
        System.out.println("  • FXML loading happens on UI thread (blocking)");
        System.out.println("  • Large transaction lists loaded synchronously");
        System.out.println("  • No pagination for large datasets");

        System.out.println("\nRecommended Optimizations:");
        System.out.println("  1. Implement JOIN queries to reduce N+1 issues");
        System.out.println("  2. Add database indexes on frequently queried columns");
        System.out.println("  3. Implement pagination for large data sets");
        System.out.println("  4. Use background threads for data loading");
        System.out.println("  5. Implement caching for frequently accessed data");
        System.out.println("  6. Optimize memory usage in service classes");
    }
}
