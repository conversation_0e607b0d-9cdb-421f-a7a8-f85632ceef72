package com.clothingstore.util;

import javafx.application.Platform;

/**
 * Utility class for JavaFX thread management and UI updates. Helps prevent "Not
 * on FX application thread" exceptions.
 */
public class FXThreadUtil {

    /**
     * Ensures that the given runnable is executed on the JavaFX Application
     * Thread. If already on the FX thread, executes immediately. Otherwise,
     * schedules for later execution.
     *
     * @param runnable The code to execute on the FX thread
     */
    public static void runOnFXThread(Runnable runnable) {
        if (Platform.isFxApplicationThread()) {
            runnable.run();
        } else {
            Platform.runLater(runnable);
        }
    }

    /**
     * Safely updates UI components by ensuring execution on the FX thread. This
     * is a convenience method for UI update operations.
     *
     * @param uiUpdate The UI update code to execute
     */
    public static void updateUI(Runnable uiUpdate) {
        runOnFXThread(uiUpdate);
    }

    /**
     * Executes a task on a background thread and then updates the UI on the FX
     * thread. Useful for database operations followed by UI updates.
     *
     * @param backgroundTask The task to run in the background
     * @param uiUpdate The UI update to run after the background task completes
     */
    public static void runInBackground(Runnable backgroundTask, Runnable uiUpdate) {
        Thread backgroundThread = new Thread(() -> {
            try {
                backgroundTask.run();
                runOnFXThread(uiUpdate);
            } catch (Exception e) {
                runOnFXThread(() -> {
                    System.err.println("Background task failed: " + e.getMessage());
                    e.printStackTrace();
                });
            }
        });
        backgroundThread.setDaemon(true);
        backgroundThread.start();
    }

    /**
     * Safely executes a database operation and updates UI. Handles exceptions
     * and ensures proper thread management.
     *
     * @param databaseOperation The database operation to perform
     * @param onSuccess UI update to perform on success
     * @param onError Error handler (optional, can be null)
     */
    public static void safeDBOperation(Runnable databaseOperation, Runnable onSuccess, Runnable onError) {
        runInBackground(
                databaseOperation,
                () -> {
                    try {
                        onSuccess.run();
                    } catch (Exception e) {
                        if (onError != null) {
                            onError.run();
                        } else {
                            System.err.println("UI Error: Failed to update interface: " + e.getMessage());
                            e.printStackTrace();
                        }
                    }
                }
        );
    }

    /**
     * Checks if the current thread is the JavaFX Application Thread.
     *
     * @return true if on FX thread, false otherwise
     */
    public static boolean isOnFXThread() {
        return Platform.isFxApplicationThread();
    }

    /**
     * Logs thread information for debugging purposes.
     *
     * @param operation Description of the operation being performed
     */
    public static void logThreadInfo(String operation) {
        String threadName = Thread.currentThread().getName();
        boolean isFXThread = Platform.isFxApplicationThread();
        System.out.println(String.format("[THREAD] %s - Thread: %s, FX Thread: %s",
                operation, threadName, isFXThread));
    }

    /**
     * Safely shuts down the JavaFX application. Ensures proper cleanup and
     * thread termination.
     */
    public static void safeExit() {
        runOnFXThread(() -> {
            Platform.exit();
            System.exit(0);
        });
    }

    /**
     * Creates a thread-safe wrapper for UI update methods. Use this to wrap
     * methods that update UI components.
     *
     * @param originalMethod The original method that updates UI
     * @return A thread-safe version of the method
     */
    public static Runnable makeThreadSafe(Runnable originalMethod) {
        return () -> runOnFXThread(originalMethod);
    }

    /**
     * Executes multiple UI updates atomically on the FX thread. All updates
     * will be executed together in a single FX thread cycle.
     *
     * @param updates Array of UI update operations
     */
    public static void batchUIUpdates(Runnable... updates) {
        runOnFXThread(() -> {
            for (Runnable update : updates) {
                try {
                    update.run();
                } catch (Exception e) {
                    System.err.println("Error in batch UI update: " + e.getMessage());
                }
            }
        });
    }

    /**
     * Delays execution of a UI update by the specified milliseconds. Useful for
     * animations or timed UI changes.
     *
     * @param delayMs Delay in milliseconds
     * @param uiUpdate The UI update to execute after the delay
     */
    public static void delayedUIUpdate(long delayMs, Runnable uiUpdate) {
        Thread delayThread = new Thread(() -> {
            try {
                Thread.sleep(delayMs);
                runOnFXThread(uiUpdate);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        delayThread.setDaemon(true);
        delayThread.start();
    }
}
