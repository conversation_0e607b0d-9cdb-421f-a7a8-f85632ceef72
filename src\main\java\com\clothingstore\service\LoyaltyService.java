package com.clothingstore.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.LoyaltyAccount;
import com.clothingstore.model.LoyaltyTier;
import com.clothingstore.model.LoyaltyTransaction;
import com.clothingstore.model.LoyaltyTransactionType;

/**
 * Service for managing customer loyalty program
 */
public class LoyaltyService {

    private static final Logger LOGGER = Logger.getLogger(LoyaltyService.class.getName());
    private static LoyaltyService instance;

    // In-memory storage for demo - in real app would use database
    private final Map<Long, LoyaltyAccount> loyaltyAccounts;
    private final List<LoyaltyTransaction> allTransactions;
    private final CustomerDAO customerDAO;

    // Loyalty program configuration
    private static final int POINTS_PER_DOLLAR_BASE = 1;
    private static final int POINTS_FOR_REDEMPTION_MINIMUM = 100;
    private static final BigDecimal POINTS_TO_DOLLAR_RATIO = new BigDecimal("100"); // 100 points = $1

    private LoyaltyService() {
        this.loyaltyAccounts = new HashMap<>();
        this.allTransactions = new ArrayList<>();
        this.customerDAO = CustomerDAO.getInstance();
    }

    public static synchronized LoyaltyService getInstance() {
        if (instance == null) {
            instance = new LoyaltyService();
        }
        return instance;
    }

    /**
     * Create or get loyalty account for customer
     */
    public LoyaltyAccount getOrCreateLoyaltyAccount(Long customerId) {
        try {
            LoyaltyAccount account = loyaltyAccounts.get(customerId);

            if (account == null) {
                // Create new loyalty account
                account = new LoyaltyAccount(customerId);
                account.setId((long) (loyaltyAccounts.size() + 1));
                loyaltyAccounts.put(customerId, account);

                LOGGER.info("Created new loyalty account for customer: " + customerId);
            }

            return account;

        } catch (Exception e) {
            LOGGER.severe("Error getting/creating loyalty account: " + e.getMessage());
            return null;
        }
    }

    /**
     * Process purchase and award points
     */
    public LoyaltyProcessingResult processPurchase(Long customerId, BigDecimal purchaseAmount, String transactionRef) {
        try {
            LoyaltyAccount account = getOrCreateLoyaltyAccount(customerId);

            if (account == null) {
                return new LoyaltyProcessingResult(false, "Could not access loyalty account", 0, null);
            }

            // Calculate points to award
            int pointsEarned = account.calculatePointsForPurchase(purchaseAmount);

            // Add purchase to lifetime spend
            account.addPurchase(purchaseAmount);

            // Award points
            String description = String.format("Purchase - %s", transactionRef != null ? transactionRef : "Transaction");
            account.addPoints(pointsEarned, description);

            // Check for tier upgrade
            LoyaltyTier previousTier = account.getCurrentTier();
            LoyaltyTier newTier = LoyaltyTier.getTierBySpend(account.getLifetimeSpend());

            boolean tierUpgraded = false;
            if (newTier != previousTier) {
                account.setCurrentTier(newTier);
                account.setTierAchievedDate(LocalDateTime.now());
                tierUpgraded = true;

                // Award bonus points for tier upgrade
                int bonusPoints = getBonusPointsForTierUpgrade(newTier);
                if (bonusPoints > 0) {
                    account.addPoints(bonusPoints, "Tier upgrade bonus - " + newTier.getDisplayName());
                    pointsEarned += bonusPoints;
                }
            }

            // Save transaction
            LoyaltyTransaction transaction = new LoyaltyTransaction(account.getId(),
                    LoyaltyTransactionType.EARNED, pointsEarned, description);
            transaction.setReferenceNumber(transactionRef);
            transaction.setId((long) (allTransactions.size() + 1));
            allTransactions.add(transaction);

            String message = String.format("Earned %d points. Current balance: %d points",
                    pointsEarned, account.getCurrentPoints());

            if (tierUpgraded) {
                message += String.format(". Congratulations! You've been upgraded to %s tier!",
                        newTier.getDisplayName());
            }

            return new LoyaltyProcessingResult(true, message, pointsEarned, account);

        } catch (Exception e) {
            LOGGER.severe("Error processing purchase for loyalty: " + e.getMessage());
            return new LoyaltyProcessingResult(false, "Error processing loyalty points", 0, null);
        }
    }

    /**
     * Redeem points for discount
     */
    public LoyaltyRedemptionResult redeemPoints(Long customerId, int pointsToRedeem, String description) {
        try {
            LoyaltyAccount account = loyaltyAccounts.get(customerId);

            if (account == null) {
                return new LoyaltyRedemptionResult(false, "Loyalty account not found", BigDecimal.ZERO);
            }

            if (pointsToRedeem < POINTS_FOR_REDEMPTION_MINIMUM) {
                return new LoyaltyRedemptionResult(false,
                        String.format("Minimum redemption is %d points", POINTS_FOR_REDEMPTION_MINIMUM),
                        BigDecimal.ZERO);
            }

            if (!account.hasSufficientPoints(pointsToRedeem)) {
                return new LoyaltyRedemptionResult(false,
                        String.format("Insufficient points. Available: %d, Required: %d",
                                account.getCurrentPoints(), pointsToRedeem),
                        BigDecimal.ZERO);
            }

            // Calculate redemption value
            BigDecimal redemptionValue = new BigDecimal(pointsToRedeem)
                    .divide(POINTS_TO_DOLLAR_RATIO, 2, RoundingMode.HALF_UP);

            // Redeem points
            boolean success = account.redeemPoints(pointsToRedeem, description);

            if (success) {
                // Save transaction
                LoyaltyTransaction transaction = new LoyaltyTransaction(account.getId(),
                        LoyaltyTransactionType.REDEEMED, -pointsToRedeem, description);
                transaction.setId((long) (allTransactions.size() + 1));
                allTransactions.add(transaction);

                String message = String.format("Redeemed %d points for $%.2f discount. Remaining balance: %d points",
                        pointsToRedeem, redemptionValue, account.getCurrentPoints());

                return new LoyaltyRedemptionResult(true, message, redemptionValue);
            } else {
                return new LoyaltyRedemptionResult(false, "Failed to redeem points", BigDecimal.ZERO);
            }

        } catch (Exception e) {
            LOGGER.severe("Error redeeming points: " + e.getMessage());
            return new LoyaltyRedemptionResult(false, "Error processing redemption", BigDecimal.ZERO);
        }
    }

    /**
     * Get loyalty account by customer ID
     */
    public LoyaltyAccount getLoyaltyAccount(Long customerId) {
        return loyaltyAccounts.get(customerId);
    }

    /**
     * Get transaction history for an account
     */
    public List<LoyaltyTransaction> getTransactionHistory(Long customerId, int limit) {
        LoyaltyAccount account = loyaltyAccounts.get(customerId);

        if (account == null) {
            return new ArrayList<>();
        }

        return allTransactions.stream()
                .filter(t -> t.getAccountId().equals(account.getId()))
                .sorted((t1, t2) -> t2.getTransactionDate().compareTo(t1.getTransactionDate()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * Calculate automatic tier discount for customer
     */
    public BigDecimal calculateTierDiscount(Long customerId, BigDecimal purchaseAmount) {
        LoyaltyAccount account = loyaltyAccounts.get(customerId);

        if (account == null || purchaseAmount == null || purchaseAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        LoyaltyTier tier = account.getCurrentTier();
        if (!tier.hasAutomaticDiscount()) {
            return BigDecimal.ZERO;
        }

        return purchaseAmount.multiply(tier.getDiscountPercentage())
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

    /**
     * Get bonus points for tier upgrade
     */
    private int getBonusPointsForTierUpgrade(LoyaltyTier newTier) {
        switch (newTier) {
            case SILVER:
                return 100;
            case GOLD:
                return 250;
            case PLATINUM:
                return 500;
            default:
                return 0;
        }
    }

    /**
     * Get loyalty program statistics
     */
    public LoyaltyProgramStats getProgramStats() {
        int totalAccounts = loyaltyAccounts.size();
        int activeAccounts = (int) loyaltyAccounts.values().stream()
                .filter(LoyaltyAccount::isActive)
                .count();

        Map<LoyaltyTier, Long> tierDistribution = loyaltyAccounts.values().stream()
                .collect(Collectors.groupingBy(LoyaltyAccount::getCurrentTier, Collectors.counting()));

        int totalPointsEarned = loyaltyAccounts.values().stream()
                .mapToInt(LoyaltyAccount::getLifetimePointsEarned)
                .sum();

        int totalPointsRedeemed = loyaltyAccounts.values().stream()
                .mapToInt(LoyaltyAccount::getLifetimePointsRedeemed)
                .sum();

        BigDecimal totalLifetimeSpend = loyaltyAccounts.values().stream()
                .map(LoyaltyAccount::getLifetimeSpend)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return new LoyaltyProgramStats(totalAccounts, activeAccounts, tierDistribution,
                totalPointsEarned, totalPointsRedeemed, totalLifetimeSpend);
    }

    /**
     * Get top loyalty customers
     */
    public List<LoyaltyAccount> getTopCustomers(int limit) {
        return loyaltyAccounts.values().stream()
                .filter(LoyaltyAccount::isActive)
                .sorted((a1, a2) -> a2.getLifetimeSpend().compareTo(a1.getLifetimeSpend()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * Award bonus points for special events
     */
    public boolean awardBonusPoints(Long customerId, int points, String reason) {
        try {
            LoyaltyAccount account = loyaltyAccounts.get(customerId);

            if (account == null) {
                return false;
            }

            account.addPoints(points, "Bonus: " + reason);

            // Save transaction
            LoyaltyTransaction transaction = new LoyaltyTransaction(account.getId(),
                    LoyaltyTransactionType.BONUS, points, "Bonus: " + reason);
            transaction.setId((long) (allTransactions.size() + 1));
            allTransactions.add(transaction);

            return true;

        } catch (Exception e) {
            LOGGER.severe("Error awarding bonus points: " + e.getMessage());
            return false;
        }
    }
}

/**
 * Result class for loyalty processing operations
 */
class LoyaltyProcessingResult {

    private final boolean success;
    private final String message;
    private final int pointsEarned;
    private final LoyaltyAccount account;

    public LoyaltyProcessingResult(boolean success, String message, int pointsEarned, LoyaltyAccount account) {
        this.success = success;
        this.message = message;
        this.pointsEarned = pointsEarned;
        this.account = account;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public int getPointsEarned() {
        return pointsEarned;
    }

    public LoyaltyAccount getAccount() {
        return account;
    }
}

/**
 * Result class for loyalty redemption operations
 */
class LoyaltyRedemptionResult {

    private final boolean success;
    private final String message;
    private final BigDecimal redemptionValue;

    public LoyaltyRedemptionResult(boolean success, String message, BigDecimal redemptionValue) {
        this.success = success;
        this.message = message;
        this.redemptionValue = redemptionValue;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public BigDecimal getRedemptionValue() {
        return redemptionValue;
    }
}

/**
 * Class for loyalty program statistics
 */
class LoyaltyProgramStats {

    private final int totalAccounts;
    private final int activeAccounts;
    private final Map<LoyaltyTier, Long> tierDistribution;
    private final int totalPointsEarned;
    private final int totalPointsRedeemed;
    private final BigDecimal totalLifetimeSpend;

    public LoyaltyProgramStats(int totalAccounts, int activeAccounts, Map<LoyaltyTier, Long> tierDistribution,
            int totalPointsEarned, int totalPointsRedeemed, BigDecimal totalLifetimeSpend) {
        this.totalAccounts = totalAccounts;
        this.activeAccounts = activeAccounts;
        this.tierDistribution = tierDistribution;
        this.totalPointsEarned = totalPointsEarned;
        this.totalPointsRedeemed = totalPointsRedeemed;
        this.totalLifetimeSpend = totalLifetimeSpend;
    }

    public int getTotalAccounts() {
        return totalAccounts;
    }

    public int getActiveAccounts() {
        return activeAccounts;
    }

    public Map<LoyaltyTier, Long> getTierDistribution() {
        return tierDistribution;
    }

    public int getTotalPointsEarned() {
        return totalPointsEarned;
    }

    public int getTotalPointsRedeemed() {
        return totalPointsRedeemed;
    }

    public BigDecimal getTotalLifetimeSpend() {
        return totalLifetimeSpend;
    }

    public int getCurrentPointsOutstanding() {
        return totalPointsEarned - totalPointsRedeemed;
    }

    public BigDecimal getAverageSpendPerAccount() {
        if (totalAccounts == 0) {
            return BigDecimal.ZERO;
        }
        return totalLifetimeSpend.divide(new BigDecimal(totalAccounts), 2, RoundingMode.HALF_UP);
    }
}
