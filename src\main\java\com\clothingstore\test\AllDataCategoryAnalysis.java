package com.clothingstore.test;

import com.clothingstore.database.OptimizedProfitQueries;
import com.clothingstore.database.OptimizedProfitQueries.CategoryProfitResult;
import com.clothingstore.database.OptimizedProfitQueries.ProductProfitResult;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Category Analysis using ALL available data from database
 */
public class AllDataCategoryAnalysis {

    public static void main(String[] args) {
        System.out.println("================================================================================");
        System.out.println("COMPLETE CATEGORY ANALYSIS - ALL DATABASE DATA");
        System.out.println("================================================================================");
        System.out.println("Analysis Date: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();

        try {
            // Initialize queries
            OptimizedProfitQueries queries = new OptimizedProfitQueries();

            // Use a very wide date range to capture all data
            LocalDateTime startDate = LocalDateTime.of(2020, 1, 1, 0, 0, 0); // Start from 2020
            LocalDateTime endDate = LocalDateTime.of(2030, 12, 31, 23, 59, 59); // End in 2030

            System.out.println("[INFO] Analyzing ALL transaction data from " + startDate.toLocalDate() + " to " + endDate.toLocalDate());
            System.out.println();

            // 1. COMPLETE CATEGORY ANALYSIS
            System.out.println("[ANALYSIS] COMPLETE CATEGORY PERFORMANCE");
            System.out.println("----------------------------------------------------------------");
            
            List<CategoryProfitResult> allResults = queries.calculateCategoryProfitBreakdown(startDate, endDate);
            
            if (allResults.isEmpty()) {
                System.out.println("No category data found in database!");
                System.out.println("This could mean:");
                System.out.println("- No completed transactions exist");
                System.out.println("- Products don't have cost_price set");
                System.out.println("- Transaction dates are outside expected range");
                return;
            }
            
            System.out.println("Found " + allResults.size() + " categories with transaction data:");
            System.out.println();
            
            // Sort by profit descending
            allResults.sort((a, b) -> Double.compare((b.revenue - b.cost), (a.revenue - a.cost)));
            
            double grandTotalRevenue = 0;
            double grandTotalCost = 0;
            int grandTotalItems = 0;
            int grandTotalTransactions = 0;
            
            for (int i = 0; i < allResults.size(); i++) {
                CategoryProfitResult result = allResults.get(i);
                double profit = result.revenue - result.cost;
                double margin = result.revenue > 0 ? (profit / result.revenue) * 100 : 0;
                double avgTransactionValue = result.transactionCount > 0 ? 
                    result.revenue / result.transactionCount : 0;
                double revenuePerItem = result.itemsSold > 0 ? 
                    result.revenue / result.itemsSold : 0;
                
                // Add to grand totals
                grandTotalRevenue += result.revenue;
                grandTotalCost += result.cost;
                grandTotalItems += result.itemsSold;
                grandTotalTransactions += result.transactionCount;
                
                String rank = String.valueOf(i + 1);
                if (i == 0) rank = "1st PLACE";
                else if (i == 1) rank = "2nd PLACE";
                else if (i == 2) rank = "3rd PLACE";
                else rank = "#" + (i + 1);
                
                System.out.println("[" + rank + "] " + result.category.toUpperCase());
                System.out.println("   Revenue: $" + String.format("%.2f", result.revenue));
                System.out.println("   Cost: $" + String.format("%.2f", result.cost));
                System.out.println("   Profit: $" + String.format("%.2f", profit));
                System.out.println("   Margin: " + String.format("%.2f%%", margin));
                System.out.println("   Items Sold: " + result.itemsSold);
                System.out.println("   Transactions: " + result.transactionCount);
                System.out.println("   Avg Transaction: $" + String.format("%.2f", avgTransactionValue));
                System.out.println("   Revenue per Item: $" + String.format("%.2f", revenuePerItem));
                
                // Performance assessment
                System.out.print("   Assessment: ");
                if (margin > 40) {
                    System.out.println("EXCELLENT - Premium performance");
                } else if (margin > 25) {
                    System.out.println("GOOD - Healthy margins");
                } else if (margin > 15) {
                    System.out.println("MODERATE - Room for improvement");
                } else {
                    System.out.println("LOW - Needs immediate attention");
                }
                System.out.println();
            }
            
            // Grand totals
            double grandTotalProfit = grandTotalRevenue - grandTotalCost;
            double overallMargin = grandTotalRevenue > 0 ? (grandTotalProfit / grandTotalRevenue) * 100 : 0;
            
            System.out.println("[TOTALS] OVERALL BUSINESS PERFORMANCE");
            System.out.println("----------------------------------------------------------------");
            System.out.println("Total Revenue: $" + String.format("%.2f", grandTotalRevenue));
            System.out.println("Total Cost: $" + String.format("%.2f", grandTotalCost));
            System.out.println("Total Profit: $" + String.format("%.2f", grandTotalProfit));
            System.out.println("Overall Margin: " + String.format("%.2f%%", overallMargin));
            System.out.println("Total Items Sold: " + grandTotalItems);
            System.out.println("Total Transactions: " + grandTotalTransactions);
            System.out.println("Active Categories: " + allResults.size());
            System.out.println("Avg Items per Transaction: " + 
                String.format("%.1f", grandTotalTransactions > 0 ? (double)grandTotalItems / grandTotalTransactions : 0));

            // 2. PRODUCT-LEVEL BREAKDOWN
            System.out.println("\n[PRODUCTS] DETAILED PRODUCT BREAKDOWN BY CATEGORY");
            System.out.println("----------------------------------------------------------------");
            
            List<ProductProfitResult> productResults = queries.calculateProductProfitBreakdown(startDate, endDate, 100, 0);
            
            if (!productResults.isEmpty()) {
                // Group products by category
                Map<String, List<ProductProfitResult>> productsByCategory = productResults.stream()
                    .collect(Collectors.groupingBy(p -> p.category));
                
                for (Map.Entry<String, List<ProductProfitResult>> entry : productsByCategory.entrySet()) {
                    String category = entry.getKey();
                    List<ProductProfitResult> products = entry.getValue();
                    
                    System.out.println("\n[CATEGORY] " + category.toUpperCase());
                    System.out.println("------------------------------");
                    
                    // Sort products by profit descending
                    products.sort((a, b) -> Double.compare((b.revenue - b.cost), (a.revenue - a.cost)));
                    
                    double categoryRevenue = 0;
                    double categoryCost = 0;
                    int categoryItems = 0;
                    
                    for (ProductProfitResult product : products) {
                        double profit = product.revenue - product.cost;
                        double margin = product.revenue > 0 ? (profit / product.revenue) * 100 : 0;
                        
                        categoryRevenue += product.revenue;
                        categoryCost += product.cost;
                        categoryItems += product.quantitySold;
                        
                        System.out.println("   " + product.productName + " (SKU: " + product.sku + ")");
                        System.out.println("      Revenue: $" + String.format("%.2f", product.revenue) + 
                            " | Profit: $" + String.format("%.2f", profit) + 
                            " | Margin: " + String.format("%.1f%%", margin));
                        System.out.println("      Qty Sold: " + product.quantitySold + 
                            " | Transactions: " + product.transactionCount);
                        System.out.println();
                    }
                    
                    // Category summary
                    double categoryProfit = categoryRevenue - categoryCost;
                    double categoryMargin = categoryRevenue > 0 ? (categoryProfit / categoryRevenue) * 100 : 0;
                    
                    System.out.println("   [CATEGORY SUMMARY]");
                    System.out.println("   Products: " + products.size());
                    System.out.println("   Total Revenue: $" + String.format("%.2f", categoryRevenue));
                    System.out.println("   Total Profit: $" + String.format("%.2f", categoryProfit));
                    System.out.println("   Category Margin: " + String.format("%.2f%%", categoryMargin));
                    System.out.println("   Items Sold: " + categoryItems);
                }
            } else {
                System.out.println("No product-level data found.");
            }

            // 3. STRATEGIC INSIGHTS
            System.out.println("\n[INSIGHTS] STRATEGIC BUSINESS INSIGHTS");
            System.out.println("----------------------------------------------------------------");
            
            if (allResults.size() >= 2) {
                CategoryProfitResult topCategory = allResults.get(0);
                CategoryProfitResult bottomCategory = allResults.get(allResults.size() - 1);
                
                double topProfit = topCategory.revenue - topCategory.cost;
                double bottomProfit = bottomCategory.revenue - bottomCategory.cost;
                double topMargin = topCategory.revenue > 0 ? (topProfit / topCategory.revenue) * 100 : 0;
                double bottomMargin = bottomCategory.revenue > 0 ? (bottomProfit / bottomCategory.revenue) * 100 : 0;
                
                System.out.println("PERFORMANCE ANALYSIS:");
                System.out.println("* CHAMPION: " + topCategory.category + " leads with $" + 
                    String.format("%.2f", topProfit) + " profit (" + String.format("%.1f%%", topMargin) + " margin)");
                System.out.println("* CHALLENGER: " + bottomCategory.category + " trails with $" + 
                    String.format("%.2f", bottomProfit) + " profit (" + String.format("%.1f%%", bottomMargin) + " margin)");
                
                // Performance gap analysis
                double profitGap = topProfit - bottomProfit;
                double marginGap = topMargin - bottomMargin;
                
                System.out.println("* PERFORMANCE GAP: $" + String.format("%.2f", profitGap) + 
                    " profit difference, " + String.format("%.1f", marginGap) + " percentage points margin gap");
                
                // Market share analysis
                double topShare = (topCategory.revenue / grandTotalRevenue) * 100;
                System.out.println("* MARKET DOMINANCE: " + topCategory.category + " represents " + 
                    String.format("%.1f%%", topShare) + " of total revenue");
                
                System.out.println("\nACTIONABLE RECOMMENDATIONS:");
                System.out.println("1. DOUBLE DOWN: Increase inventory and marketing for " + topCategory.category);
                System.out.println("2. INVESTIGATE: Analyze why " + bottomCategory.category + " underperforms");
                System.out.println("3. OPTIMIZE: Review pricing strategy for low-margin categories");
                System.out.println("4. EXPAND: Consider new products in high-performing categories");
                
                if (overallMargin > 40) {
                    System.out.println("5. PREMIUM POSITIONING: Your " + String.format("%.1f%%", overallMargin) + 
                        " margin supports premium market positioning");
                } else if (overallMargin > 25) {
                    System.out.println("5. SOLID FOUNDATION: Your " + String.format("%.1f%%", overallMargin) + 
                        " margin provides good business foundation");
                } else {
                    System.out.println("5. MARGIN IMPROVEMENT: Focus on increasing your " + String.format("%.1f%%", overallMargin) + 
                        " overall margin through cost optimization");
                }
            }

            System.out.println("\n================================================================================");
            System.out.println("[SUCCESS] COMPLETE CATEGORY ANALYSIS FINISHED");
            System.out.println("Data Source: Real database transactions with cost analysis");
            System.out.println("Categories Analyzed: " + allResults.size());
            System.out.println("Total Business Value: $" + String.format("%.2f", grandTotalProfit) + " profit");
            System.out.println("================================================================================");

        } catch (Exception e) {
            System.err.println("[ERROR] Category analysis failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
