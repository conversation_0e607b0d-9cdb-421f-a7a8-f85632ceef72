<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox fx:id="mainContainer" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.ModernReportsController">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 20; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;">
         <children>
            <Label text="Modern Reports Dashboard" textFill="#2c3e50">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label text="Comprehensive Business Analytics &amp; Reporting" textFill="#6c757d">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
            <Button onAction="#refreshAllReports" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 8 16;" text="Refresh All" />
         </children>
      </HBox>

      <!-- Tab Pane for Different Report Categories -->
      <TabPane fx:id="reportTabPane" tabClosingPolicy="UNAVAILABLE" VBox.vgrow="ALWAYS">
         
         <!-- Sales Reports Tab -->
         <Tab fx:id="salesTab" text="Sales Reports">
            <content>
               <VBox fx:id="salesContent" spacing="15.0">
                  <padding>
                     <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                  </padding>
                  <children>
                     <!-- Sales Statistics Cards -->
                     <HBox fx:id="salesStatsCards" alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <!-- Stats cards will be populated programmatically -->
                     </HBox>

                     <!-- Sales Controls -->
                     <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <children>
                           <Label text="Date Range:" />
                           <DatePicker fx:id="salesStartDate" promptText="Start Date" />
                           <Label text="to" />
                           <DatePicker fx:id="salesEndDate" promptText="End Date" />
                           <Button onAction="#setTodayRange" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 10px;" text="Today" />
                           <Button onAction="#setThisWeekRange" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 10px;" text="This Week" />
                           <Button onAction="#setThisMonthRange" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 10px;" text="This Month" />
                           <ComboBox fx:id="salesReportType" prefWidth="180.0" promptText="Report Type" />
                           <Button onAction="#generateSalesReport" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Generate" />
                           <Button onAction="#refreshSalesReport" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Refresh" />
                           <Region HBox.hgrow="ALWAYS" />
                           <ProgressIndicator fx:id="salesProgress" maxHeight="20.0" maxWidth="20.0" visible="false" />
                        </children>
                     </HBox>

                     <!-- Sales Search and Filter -->
                     <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #e9ecef; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;">
                        <children>
                           <Label text="Search:" />
                           <TextField fx:id="salesSearchField" prefWidth="200.0" promptText="Search transactions..." />
                           <Label text="Filter:" />
                           <ComboBox fx:id="salesFilterCombo" prefWidth="150.0" />
                           <Region HBox.hgrow="ALWAYS" />
                           <Button onAction="#exportSalesReport" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Export CSV" />
                           <Button onAction="#printSalesReport" style="-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Print" />
                        </children>
                     </HBox>

                     <!-- Sales Summary -->
                     <Label fx:id="salesSummaryLabel" style="-fx-background-color: #e3f2fd; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;" text="Summary: Select date range and generate report" textFill="#1976d2">
                        <font>
                           <Font name="System Bold" size="12.0" />
                        </font>
                     </Label>

                     <!-- Sales Table -->
                     <TableView fx:id="salesTable" VBox.vgrow="ALWAYS" style="-fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8;">
                        <placeholder>
                           <Label text="No sales data available. Please generate a report." />
                        </placeholder>
                     </TableView>
                  </children>
               </VBox>
            </content>
         </Tab>

         <!-- Inventory Reports Tab -->
         <Tab fx:id="inventoryTab" text="Inventory Reports">
            <content>
               <VBox fx:id="inventoryContent" spacing="15.0">
                  <padding>
                     <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                  </padding>
                  <children>
                     <!-- Inventory Statistics Cards -->
                     <HBox fx:id="inventoryStatsCards" alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <!-- Stats cards will be populated programmatically -->
                     </HBox>

                     <!-- Inventory Controls -->
                     <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <children>
                           <Label text="Report Type:" />
                           <ComboBox fx:id="inventoryReportType" prefWidth="200.0" promptText="Select Report Type" />
                           <Button onAction="#generateInventoryReport" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Generate" />
                           <Button onAction="#refreshInventoryReport" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Refresh" />
                           <Region HBox.hgrow="ALWAYS" />
                           <ProgressIndicator fx:id="inventoryProgress" maxHeight="20.0" maxWidth="20.0" visible="false" />
                        </children>
                     </HBox>

                     <!-- Inventory Search and Filter -->
                     <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #e9ecef; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;">
                        <children>
                           <Label text="Search:" />
                           <TextField fx:id="inventorySearchField" prefWidth="200.0" promptText="Search products..." />
                           <Label text="Filter:" />
                           <ComboBox fx:id="inventoryFilterCombo" prefWidth="150.0" />
                           <Region HBox.hgrow="ALWAYS" />
                           <Button onAction="#exportInventoryReport" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Export CSV" />
                           <Button onAction="#printInventoryReport" style="-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Print" />
                        </children>
                     </HBox>

                     <!-- Inventory Summary -->
                     <Label fx:id="inventorySummaryLabel" style="-fx-background-color: #f3e5f5; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;" text="Summary: Generate inventory report to view statistics" textFill="#7b1fa2">
                        <font>
                           <Font name="System Bold" size="12.0" />
                        </font>
                     </Label>

                     <!-- Inventory Table -->
                     <TableView fx:id="inventoryTable" VBox.vgrow="ALWAYS" style="-fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8;">
                        <placeholder>
                           <Label text="No inventory data available. Please generate a report." />
                        </placeholder>
                     </TableView>
                  </children>
               </VBox>
            </content>
         </Tab>

         <!-- Customer Reports Tab -->
         <Tab fx:id="customerTab" text="Customer Reports">
            <content>
               <VBox fx:id="customerContent" spacing="15.0">
                  <padding>
                     <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                  </padding>
                  <children>
                     <!-- Customer Statistics Cards -->
                     <HBox fx:id="customerStatsCards" alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <!-- Stats cards will be populated programmatically -->
                     </HBox>

                     <!-- Customer Controls -->
                     <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <children>
                           <Label text="Date Range:" />
                           <DatePicker fx:id="customerStartDate" promptText="Start Date" />
                           <Label text="to" />
                           <DatePicker fx:id="customerEndDate" promptText="End Date" />
                           <ComboBox fx:id="customerReportType" prefWidth="200.0" promptText="Report Type" />
                           <Button onAction="#generateCustomerReport" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Generate" />
                           <Button onAction="#refreshCustomerReport" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Refresh" />
                           <Region HBox.hgrow="ALWAYS" />
                           <ProgressIndicator fx:id="customerProgress" maxHeight="20.0" maxWidth="20.0" visible="false" />
                        </children>
                     </HBox>

                     <!-- Customer Search and Filter -->
                     <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #e9ecef; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;">
                        <children>
                           <Label text="Search:" />
                           <TextField fx:id="customerSearchField" prefWidth="200.0" promptText="Search customers..." />
                           <Label text="Filter:" />
                           <ComboBox fx:id="customerFilterCombo" prefWidth="150.0" />
                           <Region HBox.hgrow="ALWAYS" />
                           <Button onAction="#exportCustomerReport" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Export CSV" />
                           <Button onAction="#printCustomerReport" style="-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Print" />
                        </children>
                     </HBox>

                     <!-- Customer Summary -->
                     <Label fx:id="customerSummaryLabel" style="-fx-background-color: #e8f5e8; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;" text="Summary: Generate customer report to view analytics" textFill="#2e7d32">
                        <font>
                           <Font name="System Bold" size="12.0" />
                        </font>
                     </Label>

                     <!-- Customer Table -->
                     <TableView fx:id="customerTable" VBox.vgrow="ALWAYS" style="-fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8;">
                        <placeholder>
                           <Label text="No customer data available. Please generate a report." />
                        </placeholder>
                     </TableView>
                  </children>
               </VBox>
            </content>
         </Tab>

         <!-- Financial Reports Tab -->
         <Tab fx:id="financialTab" text="Financial Reports">
            <content>
               <VBox fx:id="financialContent" spacing="15.0">
                  <padding>
                     <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                  </padding>
                  <children>
                     <!-- Financial Statistics Cards -->
                     <HBox fx:id="financialStatsCards" alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <!-- Stats cards will be populated programmatically -->
                     </HBox>

                     <!-- Financial Controls -->
                     <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <children>
                           <Label text="Date Range:" />
                           <DatePicker fx:id="financialStartDate" promptText="Start Date" />
                           <Label text="to" />
                           <DatePicker fx:id="financialEndDate" promptText="End Date" />
                           <ComboBox fx:id="financialReportType" prefWidth="180.0" promptText="Report Type" />
                           <Button onAction="#generateFinancialReport" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Generate" />
                           <Button onAction="#refreshFinancialReport" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Refresh" />
                           <Region HBox.hgrow="ALWAYS" />
                           <ProgressIndicator fx:id="financialProgress" maxHeight="20.0" maxWidth="20.0" visible="false" />
                        </children>
                     </HBox>

                     <!-- Financial Search and Filter -->
                     <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #e9ecef; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;">
                        <children>
                           <Label text="Search:" />
                           <TextField fx:id="financialSearchField" prefWidth="200.0" promptText="Search financial data..." />
                           <Label text="Filter:" />
                           <ComboBox fx:id="financialFilterCombo" prefWidth="150.0" />
                           <Region HBox.hgrow="ALWAYS" />
                           <Button onAction="#exportFinancialReport" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Export CSV" />
                           <Button onAction="#printFinancialReport" style="-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="Print" />
                        </children>
                     </HBox>

                     <!-- Financial Summary -->
                     <Label fx:id="financialSummaryLabel" style="-fx-background-color: #fff3e0; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;" text="Summary: Generate financial report to view analysis" textFill="#f57c00">
                        <font>
                           <Font name="System Bold" size="12.0" />
                        </font>
                     </Label>

                     <!-- Financial Table -->
                     <TableView fx:id="financialTable" VBox.vgrow="ALWAYS" style="-fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8;">
                        <placeholder>
                           <Label text="No financial data available. Please generate a report." />
                        </placeholder>
                     </TableView>
                  </children>
               </VBox>
            </content>
         </Tab>

         <!-- Returns & Refunds Tab -->
         <Tab fx:id="returnsTab" text="Returns &amp; Refunds">
            <content>
               <VBox fx:id="returnsContent" spacing="15.0">
                  <padding>
                     <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                  </padding>
                  <children>
                     <!-- Returns Statistics Cards -->
                     <HBox fx:id="returnsStatsCards" alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <!-- Stats cards will be populated programmatically -->
                     </HBox>

                     <!-- Returns Controls -->
                     <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;">
                        <children>
                           <Label text="Date Range:" />
                           <DatePicker fx:id="returnsStartDate" promptText="Start Date" />
                           <Label text="to" />
                           <DatePicker fx:id="returnsEndDate" promptText="End Date" />
                           <Label text="Report Type:" />
                           <ComboBox fx:id="returnsReportType" prefWidth="200.0" promptText="Select Report Type" />
                           <Button onAction="#generateReturnsReport" style="-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5; -fx-padding: 8 16;" text="Generate Report" />
                           <Region HBox.hgrow="ALWAYS" />
                           <TextField fx:id="returnsSearchField" promptText="Search returns..." />
                           <ComboBox fx:id="returnsFilterCombo" promptText="Filter" />
                        </children>
                     </HBox>

                     <!-- Returns Summary -->
                     <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #fff3cd; -fx-padding: 10; -fx-border-color: #ffeaa7; -fx-border-width: 1; -fx-border-radius: 5; -fx-background-radius: 5;">
                        <children>
                           <Label fx:id="returnsSummaryLabel" text="Returns Summary: Loading..." textFill="#856404">
                              <font>
                                 <Font name="System Bold" size="12.0" />
                              </font>
                           </Label>
                           <Region HBox.hgrow="ALWAYS" />
                           <ProgressIndicator fx:id="returnsProgress" maxHeight="20.0" maxWidth="20.0" visible="false" />
                        </children>
                     </HBox>

                     <!-- Returns Table -->
                     <TableView fx:id="returnsTable" VBox.vgrow="ALWAYS">
                        <placeholder>
                           <Label text="No return data available. Generate a report to view returns and refunds." />
                        </placeholder>
                     </TableView>
                  </children>
               </VBox>
            </content>
         </Tab>
      </TabPane>
   </children>
</VBox>
