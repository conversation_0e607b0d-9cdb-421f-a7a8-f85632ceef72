@echo off
title Clothing Store - JavaFX GUI
echo ========================================
echo    CLOTHING STORE MANAGEMENT SYSTEM
echo         JavaFX GUI Application
echo ========================================
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 11 or higher
    pause
    exit /b 1
)

REM Check if JavaFX SDK exists
if not exist "javafx-sdk-17.0.2\lib" (
    echo ERROR: JavaFX SDK not found!
    echo Please ensure javafx-sdk-17.0.2 folder exists in current directory
    echo Download from: https://openjfx.io/
    pause
    exit /b 1
)

REM Check if compiled classes exist
if not exist "target\classes" (
    echo ERROR: Compiled classes not found!
    echo Please run: javac -cp "lib\sqlite-jdbc-3.50.1.0.jar" -d target\classes src\main\java\com\clothingstore\*.java src\main\java\com\clothingstore\*\*.java
    pause
    exit /b 1
)

REM Check if SQLite JDBC exists
if not exist "lib\sqlite-jdbc-3.50.1.0.jar" (
    echo ERROR: SQLite JDBC driver not found!
    echo Please ensure lib\sqlite-jdbc-3.50.1.0.jar exists
    pause
    exit /b 1
)

echo Starting JavaFX GUI Application...
echo.

java --module-path "javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml -cp "target\classes;lib\sqlite-jdbc-3.50.1.0.jar" com.clothingstore.ClothingStoreApp

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Application failed to start
    echo Check the error messages above
    pause
)
