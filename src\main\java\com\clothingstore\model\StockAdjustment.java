package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing a stock adjustment record
 */
public class StockAdjustment {
    
    private Long id;
    private Long productId;
    private String productSku;
    private String productName;
    private StockAdjustmentType adjustmentType;
    private int previousQuantity;
    private int adjustmentQuantity;
    private int newQuantity;
    private String reason;
    private String notes;
    private String batchNumber;
    private String referenceNumber;
    private BigDecimal unitCost;
    private BigDecimal totalValue;
    private String adjustedBy;
    private LocalDateTime adjustmentDate;
    private LocalDateTime createdAt;
    
    // Constructors
    public StockAdjustment() {
        this.adjustmentDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
    }
    
    public StockAdjustment(Long productId, StockAdjustmentType type, int adjustmentQuantity, String reason) {
        this();
        this.productId = productId;
        this.adjustmentType = type;
        this.adjustmentQuantity = adjustmentQuantity;
        this.reason = reason;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getProductId() {
        return productId;
    }
    
    public void setProductId(Long productId) {
        this.productId = productId;
    }
    
    public String getProductSku() {
        return productSku;
    }
    
    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public StockAdjustmentType getAdjustmentType() {
        return adjustmentType;
    }
    
    public void setAdjustmentType(StockAdjustmentType adjustmentType) {
        this.adjustmentType = adjustmentType;
    }
    
    public int getPreviousQuantity() {
        return previousQuantity;
    }
    
    public void setPreviousQuantity(int previousQuantity) {
        this.previousQuantity = previousQuantity;
    }
    
    public int getAdjustmentQuantity() {
        return adjustmentQuantity;
    }
    
    public void setAdjustmentQuantity(int adjustmentQuantity) {
        this.adjustmentQuantity = adjustmentQuantity;
    }
    
    public int getNewQuantity() {
        return newQuantity;
    }
    
    public void setNewQuantity(int newQuantity) {
        this.newQuantity = newQuantity;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public String getBatchNumber() {
        return batchNumber;
    }
    
    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }
    
    public String getReferenceNumber() {
        return referenceNumber;
    }
    
    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
    
    public BigDecimal getUnitCost() {
        return unitCost;
    }
    
    public void setUnitCost(BigDecimal unitCost) {
        this.unitCost = unitCost;
    }
    
    public BigDecimal getTotalValue() {
        return totalValue;
    }
    
    public void setTotalValue(BigDecimal totalValue) {
        this.totalValue = totalValue;
    }
    
    public String getAdjustedBy() {
        return adjustedBy;
    }
    
    public void setAdjustedBy(String adjustedBy) {
        this.adjustedBy = adjustedBy;
    }
    
    public LocalDateTime getAdjustmentDate() {
        return adjustmentDate;
    }
    
    public void setAdjustmentDate(LocalDateTime adjustmentDate) {
        this.adjustmentDate = adjustmentDate;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    // Utility methods
    public boolean isIncrease() {
        return adjustmentQuantity > 0;
    }
    
    public boolean isDecrease() {
        return adjustmentQuantity < 0;
    }
    
    public int getAbsoluteAdjustment() {
        return Math.abs(adjustmentQuantity);
    }
    
    public String getAdjustmentDescription() {
        StringBuilder desc = new StringBuilder();
        
        if (isIncrease()) {
            desc.append("+").append(adjustmentQuantity);
        } else {
            desc.append(adjustmentQuantity);
        }
        
        desc.append(" (").append(previousQuantity).append(" → ").append(newQuantity).append(")");
        
        if (reason != null && !reason.trim().isEmpty()) {
            desc.append(" - ").append(reason);
        }
        
        return desc.toString();
    }
    
    public String getDisplayText() {
        return String.format("%s: %s %s", 
                productSku != null ? productSku : "Unknown SKU",
                getAdjustmentDescription(),
                adjustmentType.getDisplayName());
    }
    
    @Override
    public String toString() {
        return String.format("StockAdjustment{id=%d, productId=%d, type=%s, qty=%d}", 
                id, productId, adjustmentType, adjustmentQuantity);
    }
}
