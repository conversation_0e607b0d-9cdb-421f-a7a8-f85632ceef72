<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.AdvancedReportsController">
   <children>
      <!-- Header Section -->
      <VBox spacing="15.0" styleClass="header-section">
         <children>
            <!-- Title and Controls -->
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="page-title" text="Advanced Reporting Suite" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="exportAllButton" onAction="#handleExportAll" styleClass="secondary-button" text="📊 Export All" />
                  <Button fx:id="generateReportsButton" onAction="#generateAllReports" styleClass="primary-button" text="🔄 Generate Reports" />
               </children>
            </HBox>
            
            <!-- Date Range Controls -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="📅 Period:" />
                  <ComboBox fx:id="reportPeriodCombo" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="From:" />
                  <DatePicker fx:id="startDatePicker" prefWidth="130.0" styleClass="form-field" />
                  <Label styleClass="filter-label" text="To:" />
                  <DatePicker fx:id="endDatePicker" prefWidth="130.0" styleClass="form-field" />
                  <Region HBox.hgrow="ALWAYS" />
                  <VBox alignment="CENTER_RIGHT" spacing="2.0">
                     <children>
                        <Label fx:id="reportPeriodLabel" styleClass="info-label" text="Report Period: --" />
                        <Label fx:id="lastUpdatedLabel" styleClass="info-label-small" text="Last Updated: --" />
                     </children>
                  </VBox>
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </VBox>

      <!-- Reports Tabs -->
      <TabPane fx:id="reportsTabPane" VBox.vgrow="ALWAYS">
         <tabs>
            <!-- Product Performance Tab -->
            <Tab fx:id="productPerformanceTab" text="📈 Product Performance" closable="false">
               <content>
                  <ScrollPane fitToWidth="true">
                     <content>
                        <VBox spacing="20.0">
                           <children>
                              <!-- Charts Row -->
                              <HBox spacing="15.0">
                                 <children>
                                    <!-- Top Products Chart -->
                                    <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label styleClass="chart-title" text="🏆 Top Products by Revenue" />
                                          <BarChart fx:id="topProductsChart" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                             <xAxis>
                                                <CategoryAxis side="BOTTOM" />
                                             </xAxis>
                                             <yAxis>
                                                <NumberAxis side="LEFT" />
                                             </yAxis>
                                          </BarChart>
                                       </children>
                                    </VBox>
                                    
                                    <!-- Category Performance Chart -->
                                    <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label styleClass="chart-title" text="🥧 Category Performance" />
                                          <PieChart fx:id="categoryPerformanceChart" prefHeight="300.0" VBox.vgrow="ALWAYS" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                              
                              <!-- Product Performance Table -->
                              <VBox spacing="10.0" styleClass="table-container">
                                 <children>
                                    <Label styleClass="section-title" text="📊 Detailed Product Performance" />
                                    <TableView fx:id="productPerformanceTable" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                       <columns>
                                          <TableColumn fx:id="productNameColumn" prefWidth="200.0" text="Product Name" />
                                          <TableColumn fx:id="unitsSoldColumn" prefWidth="100.0" text="Units Sold" />
                                          <TableColumn fx:id="revenueColumn" prefWidth="120.0" text="Revenue" />
                                          <TableColumn fx:id="profitColumn" prefWidth="120.0" text="Profit" />
                                          <TableColumn fx:id="marginColumn" prefWidth="100.0" text="Margin %" />
                                       </columns>
                                    </TableView>
                                 </children>
                              </VBox>
                           </children>
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
                           </padding>
                        </VBox>
                     </content>
                  </ScrollPane>
               </content>
            </Tab>

            <!-- Customer Analytics Tab -->
            <Tab fx:id="customerAnalyticsTab" text="👥 Customer Analytics" closable="false">
               <content>
                  <ScrollPane fitToWidth="true">
                     <content>
                        <VBox spacing="20.0">
                           <children>
                              <!-- Customer Charts Row -->
                              <HBox spacing="15.0">
                                 <children>
                                    <!-- Top Customers Chart -->
                                    <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label styleClass="chart-title" text="👑 Top Customers by Spending" />
                                          <BarChart fx:id="topCustomersChart" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                             <xAxis>
                                                <CategoryAxis side="BOTTOM" />
                                             </xAxis>
                                             <yAxis>
                                                <NumberAxis side="LEFT" />
                                             </yAxis>
                                          </BarChart>
                                       </children>
                                    </VBox>
                                    
                                    <!-- Customer Trends Chart -->
                                    <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label styleClass="chart-title" text="📈 Customer Purchase Trends" />
                                          <LineChart fx:id="customerTrendsChart" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                             <xAxis>
                                                <CategoryAxis side="BOTTOM" />
                                             </xAxis>
                                             <yAxis>
                                                <NumberAxis side="LEFT" />
                                             </yAxis>
                                          </LineChart>
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                              
                              <!-- Customer Analytics Table -->
                              <VBox spacing="10.0" styleClass="table-container">
                                 <children>
                                    <Label styleClass="section-title" text="📊 Detailed Customer Analytics" />
                                    <TableView fx:id="customerAnalyticsTable" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                       <columns>
                                          <TableColumn fx:id="customerNameColumn" prefWidth="180.0" text="Customer Name" />
                                          <TableColumn fx:id="totalSpentColumn" prefWidth="120.0" text="Total Spent" />
                                          <TableColumn fx:id="transactionCountColumn" prefWidth="100.0" text="Orders" />
                                          <TableColumn fx:id="avgOrderValueColumn" prefWidth="120.0" text="Avg Order Value" />
                                          <TableColumn fx:id="lastPurchaseColumn" prefWidth="120.0" text="Last Purchase" />
                                       </columns>
                                    </TableView>
                                 </children>
                              </VBox>
                           </children>
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
                           </padding>
                        </VBox>
                     </content>
                  </ScrollPane>
               </content>
            </Tab>

            <!-- Profit/Loss Tab -->
            <Tab fx:id="profitLossTab" text="💰 Profit &amp; Loss" closable="false">
               <content>
                  <ScrollPane fitToWidth="true">
                     <content>
                        <VBox spacing="20.0">
                           <children>
                              <!-- P&L Summary Cards -->
                              <HBox spacing="15.0" styleClass="summary-section">
                                 <children>
                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card success">
                                       <children>
                                          <Label styleClass="summary-title" text="Total Revenue" />
                                          <Label fx:id="totalRevenueLabel" styleClass="summary-value" text="$0.00" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>

                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card warning">
                                       <children>
                                          <Label styleClass="summary-title" text="Total Cost" />
                                          <Label fx:id="totalCostLabel" styleClass="summary-value" text="$0.00" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>

                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card info">
                                       <children>
                                          <Label styleClass="summary-title" text="Gross Profit" />
                                          <Label fx:id="grossProfitLabel" styleClass="summary-value" text="$0.00" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>

                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card">
                                       <children>
                                          <Label styleClass="summary-title" text="Profit Margin" />
                                          <Label fx:id="profitMarginLabel" styleClass="summary-value" text="0.0%" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>

                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card">
                                       <children>
                                          <Label styleClass="summary-title" text="Transactions" />
                                          <Label fx:id="transactionCountLabel" styleClass="summary-value" text="0" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>

                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card">
                                       <children>
                                          <Label styleClass="summary-title" text="Avg Transaction" />
                                          <Label fx:id="avgTransactionValueLabel" styleClass="summary-value" text="$0.00" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>
                                 </children>
                              </HBox>
                              
                              <!-- Profit Charts Row -->
                              <HBox spacing="15.0">
                                 <children>
                                    <!-- Daily Profit Trend -->
                                    <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label styleClass="chart-title" text="Daily Profit Trend" />
                                          <AreaChart fx:id="profitTrendChart" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                             <xAxis>
                                                <CategoryAxis side="BOTTOM" />
                                             </xAxis>
                                             <yAxis>
                                                <NumberAxis side="LEFT" />
                                             </yAxis>
                                          </AreaChart>
                                       </children>
                                    </VBox>

                                    <!-- Monthly Profit Comparison -->
                                    <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label styleClass="chart-title" text="Monthly Profit Comparison" />
                                          <BarChart fx:id="monthlyProfitChart" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                             <xAxis>
                                                <CategoryAxis side="BOTTOM" />
                                             </xAxis>
                                             <yAxis>
                                                <NumberAxis side="LEFT" />
                                             </yAxis>
                                          </BarChart>
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                           </children>
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
                           </padding>
                        </VBox>
                     </content>
                  </ScrollPane>
               </content>
            </Tab>

            <!-- Inventory Valuation Tab -->
            <Tab fx:id="inventoryValuationTab" text="Inventory Valuation" closable="false">
               <content>
                  <ScrollPane fitToWidth="true">
                     <content>
                        <VBox spacing="20.0">
                           <children>
                              <!-- Inventory Summary Cards -->
                              <HBox spacing="15.0" styleClass="summary-section">
                                 <children>
                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card warning">
                                       <children>
                                          <Label styleClass="summary-title" text="Total Inventory Cost" />
                                          <Label fx:id="totalInventoryCostLabel" styleClass="summary-value" text="$0.00" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>

                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card success">
                                       <children>
                                          <Label styleClass="summary-title" text="Total Inventory Value" />
                                          <Label fx:id="totalInventoryValueLabel" styleClass="summary-value" text="$0.00" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>

                                    <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card info">
                                       <children>
                                          <Label styleClass="summary-title" text="Potential Profit" />
                                          <Label fx:id="potentialProfitLabel" styleClass="summary-value" text="$0.00" />
                                       </children>
                                       <HBox.hgrow>ALWAYS</HBox.hgrow>
                                    </VBox>
                                 </children>
                              </HBox>
                              
                              <!-- Inventory Content Row -->
                              <HBox spacing="15.0">
                                 <children>
                                    <!-- Inventory Valuation Table -->
                                    <VBox spacing="10.0" styleClass="table-container" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label styleClass="section-title" text="Detailed Inventory Valuation" />
                                          <TableView fx:id="inventoryValuationTable" prefHeight="400.0" VBox.vgrow="ALWAYS">
                                             <columns>
                                                <TableColumn fx:id="itemNameColumn" prefWidth="200.0" text="Item Name" />
                                                <TableColumn fx:id="quantityColumn" prefWidth="80.0" text="Qty" />
                                                <TableColumn fx:id="costPriceColumn" prefWidth="100.0" text="Cost Price" />
                                                <TableColumn fx:id="sellingPriceColumn" prefWidth="100.0" text="Selling Price" />
                                                <TableColumn fx:id="totalCostColumn" prefWidth="120.0" text="Total Cost" />
                                                <TableColumn fx:id="totalValueColumn" prefWidth="120.0" text="Total Value" />
                                             </columns>
                                          </TableView>
                                       </children>
                                    </VBox>

                                    <!-- Inventory Distribution Chart -->
                                    <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="NEVER" prefWidth="400.0">
                                       <children>
                                          <Label styleClass="chart-title" text="Inventory Distribution" />
                                          <PieChart fx:id="inventoryDistributionChart" prefHeight="400.0" VBox.vgrow="ALWAYS" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                           </children>
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
                           </padding>
                        </VBox>
                     </content>
                  </ScrollPane>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </children>
</VBox>
