<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.CustomerDialogController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="12.0" styleClass="header-section">
         <children>
            <Label fx:id="lblDialogTitle" styleClass="section-title" text="Add New Customer">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
         </padding>
      </HBox>

      <!-- Customer Form - Optimized layout -->
      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="15.0">
               <children>
                  <!-- Personal Information Section -->
                  <VBox spacing="10.0" styleClass="form-section">
                     <children>
                        <Label styleClass="section-title" text="Personal Information">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        
                        <GridPane hgap="15.0" vgap="12.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="120.0" prefWidth="130.0" />
                              <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                              <ColumnConstraints hgrow="NEVER" minWidth="80.0" prefWidth="90.0" />
                              <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" />
                           </columnConstraints>
                           <children>
                              <Label text="First Name:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <TextField fx:id="txtFirstName" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                              <Label text="Last Name:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                              <TextField fx:id="txtLastName" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                              <Label text="Phone:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <TextField fx:id="txtPhone" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              <Label text="Date of Birth:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                              <DatePicker fx:id="dpDateOfBirth" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                           </children>
                        </GridPane>
                     </children>
                     <padding>
                        <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
                     </padding>
                  </VBox>

                  <!-- Address Information Section -->
                  <VBox spacing="10.0" styleClass="form-section">
                     <children>
                        <Label styleClass="section-title" text="Address Information">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        
                        <GridPane hgap="15.0" vgap="12.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="120.0" prefWidth="130.0" />
                              <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                           </columnConstraints>
                           <children>
                              <Label text="Street Address:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <TextField fx:id="txtAddress" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                              <Label text="City:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <HBox spacing="10.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                 <children>
                                    <TextField fx:id="txtCity" HBox.hgrow="ALWAYS" />
                                    <Label text="State:" />
                                    <TextField fx:id="txtState" prefWidth="80.0" />
                                    <Label text="ZIP:" />
                                    <TextField fx:id="txtZipCode" prefWidth="100.0" />
                                 </children>
                              </HBox>

                              <Label text="Country:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                              <TextField fx:id="txtCountry" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                           </children>
                        </GridPane>
                     </children>
                     <padding>
                        <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
                     </padding>
                  </VBox>

                  <!-- Account Information Section -->
                  <VBox spacing="10.0" styleClass="form-section">
                     <children>
                        <Label styleClass="section-title" text="Account Information">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        
                        <GridPane hgap="15.0" vgap="12.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="120.0" prefWidth="130.0" />
                              <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                              <ColumnConstraints hgrow="NEVER" minWidth="80.0" prefWidth="90.0" />
                              <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" />
                           </columnConstraints>
                           <children>
                              <Label text="Customer Group:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <ComboBox fx:id="cmbCustomerGroup" prefWidth="180.0" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                              <Label text="Status:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                              <ComboBox fx:id="cmbStatus" prefWidth="120.0" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                              <Label text="Loyalty Points:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <TextField fx:id="txtLoyaltyPoints" text="0" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              <Label text="Credit Limit:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                              <TextField fx:id="txtCreditLimit" text="0.00" GridPane.columnIndex="3" GridPane.rowIndex="1" />

                              <Label text="Notes:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                              <TextArea fx:id="txtNotes" prefRowCount="3" GridPane.columnIndex="1" GridPane.rowIndex="2" GridPane.columnSpan="3" />
                           </children>
                        </GridPane>
                     </children>
                     <padding>
                        <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
                     </padding>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>

      <!-- Action Buttons -->
      <HBox alignment="CENTER_RIGHT" spacing="12.0" styleClass="form-container">
         <children>
            <Button fx:id="btnCancel" mnemonicParsing="false" onAction="#handleCancel" styleClass="secondary-button" text="Cancel" />
            <Button fx:id="btnSave" mnemonicParsing="false" onAction="#handleSave" styleClass="primary-button" text="Save Customer" />
         </children>
         <padding>
            <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
         </padding>
      </HBox>
   </children>
</VBox>
