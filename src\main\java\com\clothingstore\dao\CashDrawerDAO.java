package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.CashDrawer;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * Data Access Object for Cash Drawer operations
 */
public class CashDrawerDAO {
    private static final Logger LOGGER = Logger.getLogger(CashDrawerDAO.class.getName());
    private static CashDrawerDAO instance;

    private CashDrawerDAO() {
        // Private constructor for singleton
    }

    public static synchronized CashDrawerDAO getInstance() {
        if (instance == null) {
            instance = new CashDrawerDAO();
        }
        return instance;
    }

    /**
     * Create a new cash drawer
     */
    public CashDrawer create(CashDrawer cashDrawer) throws SQLException {
        String sql = "INSERT INTO cash_drawers (drawer_number, cashier_name, cashier_id, " +
                "opening_amount, total_sales, total_cash_sales, total_refunds, " +
                "total_cash_drops, total_payouts, transaction_count, notes) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, cashDrawer.getDrawerNumber());
            pstmt.setString(2, cashDrawer.getCashierName());
            pstmt.setObject(3, cashDrawer.getCashierId());
            pstmt.setBigDecimal(4, cashDrawer.getOpeningAmount());
            pstmt.setBigDecimal(5, cashDrawer.getTotalSales());
            pstmt.setBigDecimal(6, cashDrawer.getTotalCashSales());
            pstmt.setBigDecimal(7, cashDrawer.getTotalRefunds());
            pstmt.setBigDecimal(8, cashDrawer.getTotalCashDrops());
            pstmt.setBigDecimal(9, cashDrawer.getTotalPayouts());
            pstmt.setInt(10, cashDrawer.getTransactionCount());
            pstmt.setString(11, cashDrawer.getNotes());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating cash drawer failed, no rows affected.");
            }

            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    cashDrawer.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating cash drawer failed, no ID obtained.");
                }
            }

            // Calculate expected amount
            cashDrawer.calculateExpectedAmount();
            updateExpectedAmount(cashDrawer.getId(), cashDrawer.getExpectedAmount());

            LOGGER.info("Created cash drawer: " + cashDrawer.getDrawerNumber());
            return cashDrawer;
        }
    }

    /**
     * Find cash drawer by ID
     */
    public CashDrawer findById(Long id) throws SQLException {
        String sql = "SELECT * FROM cash_drawers WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToCashDrawer(rs);
                }
            }
        }
        return null;
    }

    /**
     * Find current open cash drawer for a cashier
     */
    public CashDrawer findOpenDrawerByCashier(String cashierName) throws SQLException {
        String sql = "SELECT * FROM cash_drawers WHERE cashier_name = ? AND status = 'OPEN' ORDER BY opened_at DESC LIMIT 1";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, cashierName);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToCashDrawer(rs);
                }
            }
        }
        return null;
    }

    /**
     * Find all cash drawers with optional filters
     */
    public List<CashDrawer> findAll(String status, String cashierName, LocalDateTime startDate, 
                                   LocalDateTime endDate) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT * FROM cash_drawers WHERE 1=1");
        List<Object> params = new ArrayList<>();

        if (status != null && !status.equals("All")) {
            sql.append(" AND status = ?");
            params.add(status);
        }

        if (cashierName != null && !cashierName.trim().isEmpty()) {
            sql.append(" AND cashier_name LIKE ?");
            params.add("%" + cashierName.trim() + "%");
        }

        if (startDate != null) {
            sql.append(" AND opened_at >= ?");
            params.add(Timestamp.valueOf(startDate));
        }

        if (endDate != null) {
            sql.append(" AND opened_at <= ?");
            params.add(Timestamp.valueOf(endDate));
        }

        sql.append(" ORDER BY opened_at DESC");

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                pstmt.setObject(i + 1, params.get(i));
            }

            List<CashDrawer> cashDrawers = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    cashDrawers.add(mapResultSetToCashDrawer(rs));
                }
            }
            return cashDrawers;
        }
    }

    /**
     * Update cash drawer totals
     */
    public void updateTotals(Long id, BigDecimal totalSales, BigDecimal totalCashSales, 
                           BigDecimal totalRefunds, BigDecimal totalCashDrops, 
                           BigDecimal totalPayouts, int transactionCount) throws SQLException {
        String sql = "UPDATE cash_drawers SET total_sales = ?, total_cash_sales = ?, " +
                "total_refunds = ?, total_cash_drops = ?, total_payouts = ?, " +
                "transaction_count = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setBigDecimal(1, totalSales);
            pstmt.setBigDecimal(2, totalCashSales);
            pstmt.setBigDecimal(3, totalRefunds);
            pstmt.setBigDecimal(4, totalCashDrops);
            pstmt.setBigDecimal(5, totalPayouts);
            pstmt.setInt(6, transactionCount);
            pstmt.setLong(7, id);

            pstmt.executeUpdate();
            
            // Update expected amount
            CashDrawer drawer = findById(id);
            if (drawer != null) {
                drawer.calculateExpectedAmount();
                updateExpectedAmount(id, drawer.getExpectedAmount());
            }

            LOGGER.info("Updated cash drawer totals: " + id);
        }
    }

    /**
     * Close cash drawer
     */
    public void closeDrawer(Long id, BigDecimal actualAmount) throws SQLException {
        String sql = "UPDATE cash_drawers SET status = 'CLOSED', actual_amount = ?, " +
                "variance = ?, closed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            // Calculate variance
            CashDrawer drawer = findById(id);
            BigDecimal variance = BigDecimal.ZERO;
            if (drawer != null) {
                variance = actualAmount.subtract(drawer.getExpectedAmount());
            }

            pstmt.setBigDecimal(1, actualAmount);
            pstmt.setBigDecimal(2, variance);
            pstmt.setLong(3, id);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Closing cash drawer failed, no rows affected.");
            }

            LOGGER.info("Closed cash drawer: " + id);
        }
    }

    /**
     * Reconcile cash drawer
     */
    public void reconcileDrawer(Long id) throws SQLException {
        String sql = "UPDATE cash_drawers SET status = 'RECONCILED', " +
                "updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Reconciling cash drawer failed, no rows affected.");
            }

            LOGGER.info("Reconciled cash drawer: " + id);
        }
    }

    /**
     * Update expected amount
     */
    private void updateExpectedAmount(Long id, BigDecimal expectedAmount) throws SQLException {
        String sql = "UPDATE cash_drawers SET expected_amount = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setBigDecimal(1, expectedAmount);
            pstmt.setLong(2, id);
            pstmt.executeUpdate();
        }
    }

    /**
     * Update notes
     */
    public void updateNotes(Long id, String notes) throws SQLException {
        String sql = "UPDATE cash_drawers SET notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, notes);
            pstmt.setLong(2, id);
            pstmt.executeUpdate();

            LOGGER.info("Updated cash drawer notes: " + id);
        }
    }

    /**
     * Delete cash drawer
     */
    public void delete(Long id) throws SQLException {
        String sql = "DELETE FROM cash_drawers WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows == 0) {
                throw new SQLException("Deleting cash drawer failed, no rows affected.");
            }

            LOGGER.info("Deleted cash drawer: " + id);
        }
    }

    private CashDrawer mapResultSetToCashDrawer(ResultSet rs) throws SQLException {
        CashDrawer cashDrawer = new CashDrawer();
        cashDrawer.setId(rs.getLong("id"));
        cashDrawer.setDrawerNumber(rs.getString("drawer_number"));
        cashDrawer.setCashierName(rs.getString("cashier_name"));
        cashDrawer.setCashierId(rs.getObject("cashier_id", Long.class));
        cashDrawer.setOpeningAmount(rs.getBigDecimal("opening_amount"));
        cashDrawer.setClosingAmount(rs.getBigDecimal("closing_amount"));
        cashDrawer.setExpectedAmount(rs.getBigDecimal("expected_amount"));
        cashDrawer.setActualAmount(rs.getBigDecimal("actual_amount"));
        cashDrawer.setVariance(rs.getBigDecimal("variance"));
        cashDrawer.setTotalSales(rs.getBigDecimal("total_sales"));
        cashDrawer.setTotalCashSales(rs.getBigDecimal("total_cash_sales"));
        cashDrawer.setTotalRefunds(rs.getBigDecimal("total_refunds"));
        cashDrawer.setTotalCashDrops(rs.getBigDecimal("total_cash_drops"));
        cashDrawer.setTotalPayouts(rs.getBigDecimal("total_payouts"));
        cashDrawer.setTransactionCount(rs.getInt("transaction_count"));
        cashDrawer.setStatus(rs.getString("status"));
        cashDrawer.setNotes(rs.getString("notes"));

        Timestamp openedAt = rs.getTimestamp("opened_at");
        if (openedAt != null) {
            cashDrawer.setOpenedAt(openedAt.toLocalDateTime());
        }

        Timestamp closedAt = rs.getTimestamp("closed_at");
        if (closedAt != null) {
            cashDrawer.setClosedAt(closedAt.toLocalDateTime());
        }

        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            cashDrawer.setCreatedAt(createdAt.toLocalDateTime());
        }

        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            cashDrawer.setUpdatedAt(updatedAt.toLocalDateTime());
        }

        return cashDrawer;
    }
}
