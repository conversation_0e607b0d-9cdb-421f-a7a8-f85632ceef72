package com.clothingstore;

import com.clothingstore.controller.ProductDialogController;
import com.clothingstore.model.Product;

/**
 * Simple test to verify ProductDialogController functionality
 */
public class ProductDialogTest {

    public static void main(String[] args) {
        System.out.println("Testing ProductDialogController...");

        try {
            // Test 1: Create controller instance
            System.out.println("1. Creating ProductDialogController instance...");
            ProductDialogController controller = new ProductDialogController();
            System.out.println("   SUCCESS: Controller created");

            // Test 2: Test with null product (new product scenario)
            System.out.println("2. Testing with null product (new product)...");
            controller.setProduct(null);
            System.out.println("   SUCCESS: Null product handled");

            // Test 3: Test with existing product
            System.out.println("3. Testing with existing product...");
            Product testProduct = new Product();
            testProduct.setName("Test Product");
            testProduct.setSku("TEST-001");
            testProduct.setCategory("Test Category");
            controller.setProduct(testProduct);
            System.out.println("   SUCCESS: Existing product handled");

            System.out.println("\nProductDialogController basic functionality test PASSED!");

        } catch (Exception e) {
            System.out.println("ERROR: ProductDialogController test failed");
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        }

        // Test FXML loading
        System.out.println("\nTesting FXML resource loading...");
        try {
            java.net.URL fxmlUrl = ProductDialogTest.class.getResource("/fxml/ProductDialog.fxml");
            if (fxmlUrl != null) {
                System.out.println("SUCCESS: ProductDialog.fxml found at: " + fxmlUrl);
            } else {
                System.out.println("ERROR: ProductDialog.fxml not found");
            }
        } catch (Exception e) {
            System.out.println("ERROR: Failed to check FXML resource: " + e.getMessage());
        }

        System.out.println("\nTest completed.");
    }
}
