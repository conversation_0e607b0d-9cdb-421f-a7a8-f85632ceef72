import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.dao.PaymentHistoryDAO;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.PaymentHistory;
import java.util.List;

public class TestTransactionHistoryDisplay {
    public static void main(String[] args) {
        try {
            System.out.println("=== Testing Transaction History Display After Partial Refund ===");
            
            // Initialize DAOs
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            PaymentHistoryDAO paymentHistoryDAO = PaymentHistoryDAO.getInstance();
            
            // Find partially refunded transactions
            List<Transaction> partiallyRefundedTransactions = transactionDAO.findByStatus("PARTIALLY_REFUNDED");
            if (partiallyRefundedTransactions.isEmpty()) {
                System.out.println("No PARTIALLY_REFUNDED transactions found");
                return;
            }
            
            System.out.println("Found " + partiallyRefundedTransactions.size() + " partially refunded transactions:");
            System.out.println();
            
            for (Transaction transaction : partiallyRefundedTransactions) {
                System.out.println("=== Transaction: " + transaction.getTransactionNumber() + " ===");
                System.out.println("ID: " + transaction.getId());
                System.out.println("Status: " + transaction.getStatus());
                System.out.println("Total Amount: $" + transaction.getTotalAmount());
                System.out.println("Amount Paid: $" + transaction.getAmountPaid());
                System.out.println("Refunded Amount: $" + transaction.getRefundedAmount());
                System.out.println("Remaining Balance: $" + transaction.getRemainingBalance());
                System.out.println("Customer: " + (transaction.getCustomer() != null ? transaction.getCustomer().getName() : "N/A"));
                System.out.println("Date: " + transaction.getTransactionDate());
                System.out.println("Notes: " + (transaction.getNotes() != null ? transaction.getNotes() : "None"));
                
                // Check payment history
                List<PaymentHistory> paymentHistory = paymentHistoryDAO.findByTransactionId(transaction.getId());
                System.out.println("\nPayment History (" + paymentHistory.size() + " records):");
                for (PaymentHistory payment : paymentHistory) {
                    String type = payment.getPaymentType().toString();
                    String amount = payment.getPaymentAmount().toString();
                    String method = payment.getPaymentMethod();
                    String date = payment.getPaymentDate().toString();
                    
                    System.out.println("  - " + type + ": $" + amount + " via " + method + " on " + date);
                    if (payment.getNotes() != null) {
                        System.out.println("    Notes: " + payment.getNotes());
                    }
                }
                
                // Verify calculations
                System.out.println("\nVerification:");
                System.out.println("  Total - Refunded = Remaining?");
                System.out.println("  $" + transaction.getTotalAmount() + " - $" + transaction.getRefundedAmount() + 
                                 " = $" + transaction.getTotalAmount().subtract(transaction.getRefundedAmount()));
                System.out.println("  Expected remaining: $" + transaction.getRemainingBalance());
                
                boolean calculationCorrect = transaction.getTotalAmount().subtract(transaction.getRefundedAmount())
                    .compareTo(transaction.getRemainingBalance()) == 0;
                System.out.println("  Calculation correct: " + (calculationCorrect ? "✓ YES" : "✗ NO"));
                
                System.out.println();
            }
            
            // Test filtering by status (simulating Transaction History page filter)
            System.out.println("=== Testing Status Filter (Transaction History Page) ===");
            String[] statusesToTest = {"COMPLETED", "PARTIALLY_REFUNDED", "PARTIAL_PAYMENT", "REFUNDED"};
            
            for (String status : statusesToTest) {
                List<Transaction> transactions = transactionDAO.findByStatus(status);
                System.out.println(status + ": " + transactions.size() + " transactions");
            }
            
        } catch (Exception e) {
            System.err.println("Error testing transaction history display: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
