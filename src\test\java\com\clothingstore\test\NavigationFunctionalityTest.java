package com.clothingstore.test;

import java.io.File;

/**
 * Test class to verify navigation functionality
 */
public class NavigationFunctionalityTest {
    
    public static void main(String[] args) {
        System.out.println("=== Navigation Functionality Test ===");
        
        // Test 1: Check if all FXML files exist
        System.out.println("\n1. Testing FXML File Existence:");
        testFXMLFileExistence();
        
        // Test 2: Check if all controller classes exist
        System.out.println("\n2. Testing Controller Class Existence:");
        testControllerClassExistence();
        
        // Test 3: Check NavigationUtil
        System.out.println("\n3. Testing NavigationUtil:");
        testNavigationUtil();
        
        System.out.println("\n=== Navigation Test Complete ===");
    }
    
    private static void testFXMLFileExistence() {
        String[] fxmlFiles = {
            "Dashboard.fxml",
            "PointOfSaleNew.fxml", 
            "ProductManagement.fxml",
            "CustomerManagement.fxml",
            "TransactionHistory.fxml",
            "SalesReport.fxml",
            "Settings.fxml",
            "LowStockReport.fxml",
            "InventoryReport.fxml",
            "CustomerReport.fxml",
            "DailySalesReport.fxml",
            "MonthlySalesReport.fxml",
            "ProfitReport.fxml",
            "MainWindow.fxml"
        };
        
        String fxmlPath = "src/main/resources/fxml/";
        int foundCount = 0;
        
        for (String fxmlFile : fxmlFiles) {
            File file = new File(fxmlPath + fxmlFile);
            if (file.exists()) {
                System.out.println("✓ " + fxmlFile + " - EXISTS");
                foundCount++;
            } else {
                System.out.println("✗ " + fxmlFile + " - MISSING");
            }
        }
        
        System.out.println("FXML Files: " + foundCount + "/" + fxmlFiles.length + " found");
    }
    
    private static void testControllerClassExistence() {
        String[] controllerClasses = {
            "DashboardController",
            "SimplePOSController",
            "ProductManagementController", 
            "CustomerManagementController",
            "TransactionHistoryController",
            "SalesReportController",
            "SettingsController",
            "LowStockReportController",
            "InventoryReportController",
            "CustomerReportController",
            "DailySalesReportController",
            "MonthlySalesReportController",
            "ProfitReportController",
            "MainWindowController"
        };
        
        String controllerPath = "src/main/java/com/clothingstore/view/";
        int foundCount = 0;
        
        for (String controllerClass : controllerClasses) {
            File file = new File(controllerPath + controllerClass + ".java");
            if (file.exists()) {
                System.out.println("✓ " + controllerClass + " - EXISTS");
                foundCount++;
            } else {
                System.out.println("✗ " + controllerClass + " - MISSING");
            }
        }
        
        System.out.println("Controller Classes: " + foundCount + "/" + controllerClasses.length + " found");
    }
    
    private static void testNavigationUtil() {
        File navigationUtil = new File("src/main/java/com/clothingstore/util/NavigationUtil.java");
        if (navigationUtil.exists()) {
            System.out.println("✓ NavigationUtil.java - EXISTS");
        } else {
            System.out.println("✗ NavigationUtil.java - MISSING");
        }
        
        // Test if NavigationUtil can be loaded
        try {
            Class.forName("com.clothingstore.util.NavigationUtil");
            System.out.println("✓ NavigationUtil class can be loaded");
        } catch (ClassNotFoundException e) {
            System.out.println("✗ NavigationUtil class cannot be loaded: " + e.getMessage());
        }
    }
}
