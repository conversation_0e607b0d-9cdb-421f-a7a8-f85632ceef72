<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox fx:id="mainContainer" styleClass="dashboard-container" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.EnhancedDashboardController">
   <children>
      <!-- Header Section with Theme Toggle -->
      <HBox fx:id="headerContainer" alignment="CENTER_LEFT" spacing="20.0" styleClass="dashboard-header">
         <children>
            <VBox spacing="5.0">
               <children>
                  <Label styleClass="dashboard-title" text="Profit Analysis Dashboard">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
                  <Label styleClass="dashboard-subtitle" text="Advanced Business Intelligence &amp; Analytics">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Label>
               </children>
            </VBox>
            <Region HBox.hgrow="ALWAYS" />
            <ToggleButton fx:id="themeToggle" styleClass="theme-toggle" text="Dark Theme" />
         </children>
         <padding>
            <Insets bottom="20.0" left="30.0" right="30.0" top="20.0" />
         </padding>
      </HBox>

      <!-- Controls Section -->
      <VBox spacing="20.0" styleClass="controls-section">
         <children>
            <!-- Date Range and Preset Buttons -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="date-controls">
               <children>
                  <Label styleClass="control-label" text="Date Range:">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <DatePicker fx:id="startDatePicker" prefWidth="140.0" styleClass="date-picker" />
                  <Label styleClass="control-label" text="to" />
                  <DatePicker fx:id="endDatePicker" prefWidth="140.0" styleClass="date-picker" />
                  <Button fx:id="generateReportButton" styleClass="primary-button" text="Generate Dashboard" />
                  <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
               </children>
            </HBox>
            
            <!-- Preset Date Buttons -->
            <HBox fx:id="presetButtonsContainer" alignment="CENTER_LEFT" spacing="10.0" styleClass="preset-buttons">
               <children>
                  <Label styleClass="control-label" text="Quick Select:">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
               </children>
            </HBox>
            
            <!-- Export Controls -->
            <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="export-controls">
               <children>
                  <Label styleClass="control-label" text="Export:">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Button fx:id="exportCSVButton" styleClass="export-button csv-button" text="Export CSV" />
                  <Button fx:id="exportPDFButton" styleClass="export-button pdf-button" text="Export PDF" />
               </children>
            </HBox>
            
            <!-- Status Label -->
            <Label fx:id="statusLabel" styleClass="status-label" text="Select date range and click Generate Dashboard" />
         </children>
         <padding>
            <Insets bottom="10.0" left="30.0" right="30.0" top="10.0" />
         </padding>
      </VBox>

      <!-- Metrics Grid -->
      <GridPane fx:id="metricsGrid" hgap="20.0" styleClass="metrics-grid" vgap="15.0">
         <columnConstraints>
            <ColumnConstraints percentWidth="25.0" />
            <ColumnConstraints percentWidth="25.0" />
            <ColumnConstraints percentWidth="25.0" />
            <ColumnConstraints percentWidth="25.0" />
         </columnConstraints>
         <padding>
            <Insets bottom="20.0" left="30.0" right="30.0" top="20.0" />
         </padding>
      </GridPane>

      <!-- Charts Section -->
      <TabPane fx:id="chartsTabPane" styleClass="charts-tabpane" tabClosingPolicy="UNAVAILABLE" VBox.vgrow="ALWAYS">
         <tabs>
            <Tab fx:id="profitTrendTab" styleClass="chart-tab" text="Profit Trends">
               <content>
                  <VBox fx:id="profitTrendContainer" styleClass="chart-container">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
            <Tab fx:id="categoryBreakdownTab" styleClass="chart-tab" text="Category Breakdown">
               <content>
                  <VBox fx:id="categoryBreakdownContainer" styleClass="chart-container">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
            <Tab fx:id="comparisonTab" styleClass="chart-tab" text="Period Comparison">
               <content>
                  <VBox fx:id="comparisonContainer" styleClass="chart-container">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </children>
</VBox>
