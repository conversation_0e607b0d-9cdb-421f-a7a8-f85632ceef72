package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing a customer group with pricing and benefits
 */
public class CustomerGroup {
    
    private Long id;
    private String groupCode;
    private String groupName;
    private String description;
    private CustomerGroupType groupType;
    private BigDecimal discountPercentage;
    private BigDecimal minimumOrderAmount;
    private BigDecimal creditLimit;
    private int paymentTermsDays;
    private boolean taxExempt;
    private boolean allowBackorders;
    private boolean requireApproval;
    private String benefits;
    private int priority; // Higher number = higher priority
    private boolean active;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructors
    public CustomerGroup() {
        this.discountPercentage = BigDecimal.ZERO;
        this.minimumOrderAmount = BigDecimal.ZERO;
        this.creditLimit = BigDecimal.ZERO;
        this.paymentTermsDays = 0;
        this.taxExempt = false;
        this.allowBackorders = false;
        this.requireApproval = false;
        this.priority = 1;
        this.active = true;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public CustomerGroup(String groupCode, String groupName, CustomerGroupType groupType) {
        this();
        this.groupCode = groupCode;
        this.groupName = groupName;
        this.groupType = groupType;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getGroupCode() {
        return groupCode;
    }
    
    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    
    public String getGroupName() {
        return groupName;
    }
    
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public CustomerGroupType getGroupType() {
        return groupType;
    }
    
    public void setGroupType(CustomerGroupType groupType) {
        this.groupType = groupType;
    }
    
    public BigDecimal getDiscountPercentage() {
        return discountPercentage;
    }
    
    public void setDiscountPercentage(BigDecimal discountPercentage) {
        this.discountPercentage = discountPercentage;
    }
    
    public BigDecimal getMinimumOrderAmount() {
        return minimumOrderAmount;
    }
    
    public void setMinimumOrderAmount(BigDecimal minimumOrderAmount) {
        this.minimumOrderAmount = minimumOrderAmount;
    }
    
    public BigDecimal getCreditLimit() {
        return creditLimit;
    }
    
    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }
    
    public int getPaymentTermsDays() {
        return paymentTermsDays;
    }
    
    public void setPaymentTermsDays(int paymentTermsDays) {
        this.paymentTermsDays = paymentTermsDays;
    }
    
    public boolean isTaxExempt() {
        return taxExempt;
    }
    
    public void setTaxExempt(boolean taxExempt) {
        this.taxExempt = taxExempt;
    }
    
    public boolean isAllowBackorders() {
        return allowBackorders;
    }
    
    public void setAllowBackorders(boolean allowBackorders) {
        this.allowBackorders = allowBackorders;
    }
    
    public boolean isRequireApproval() {
        return requireApproval;
    }
    
    public void setRequireApproval(boolean requireApproval) {
        this.requireApproval = requireApproval;
    }
    
    public String getBenefits() {
        return benefits;
    }
    
    public void setBenefits(String benefits) {
        this.benefits = benefits;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public void setPriority(int priority) {
        this.priority = priority;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Business methods
    
    /**
     * Calculate discounted price for this customer group
     */
    public BigDecimal calculateDiscountedPrice(BigDecimal originalPrice) {
        if (originalPrice == null || discountPercentage == null || 
            discountPercentage.compareTo(BigDecimal.ZERO) == 0) {
            return originalPrice;
        }
        
        BigDecimal discountAmount = originalPrice.multiply(discountPercentage)
                .divide(new BigDecimal("100"));
        
        return originalPrice.subtract(discountAmount);
    }
    
    /**
     * Check if order amount meets minimum requirement
     */
    public boolean meetsMinimumOrder(BigDecimal orderAmount) {
        if (minimumOrderAmount == null || minimumOrderAmount.compareTo(BigDecimal.ZERO) == 0) {
            return true; // No minimum requirement
        }
        
        return orderAmount != null && orderAmount.compareTo(minimumOrderAmount) >= 0;
    }
    
    /**
     * Check if customer has available credit
     */
    public boolean hasAvailableCredit(BigDecimal currentBalance, BigDecimal requestedAmount) {
        if (creditLimit == null || creditLimit.compareTo(BigDecimal.ZERO) == 0) {
            return true; // No credit limit
        }
        
        BigDecimal balance = currentBalance != null ? currentBalance : BigDecimal.ZERO;
        BigDecimal amount = requestedAmount != null ? requestedAmount : BigDecimal.ZERO;
        
        return balance.add(amount).compareTo(creditLimit) <= 0;
    }
    
    /**
     * Get available credit amount
     */
    public BigDecimal getAvailableCredit(BigDecimal currentBalance) {
        if (creditLimit == null || creditLimit.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.valueOf(Double.MAX_VALUE); // Unlimited
        }
        
        BigDecimal balance = currentBalance != null ? currentBalance : BigDecimal.ZERO;
        BigDecimal available = creditLimit.subtract(balance);
        
        return available.compareTo(BigDecimal.ZERO) > 0 ? available : BigDecimal.ZERO;
    }
    
    /**
     * Get payment terms description
     */
    public String getPaymentTermsDescription() {
        if (paymentTermsDays <= 0) {
            return "Cash on Delivery";
        } else {
            return "Net " + paymentTermsDays + " days";
        }
    }
    
    /**
     * Get group benefits as list
     */
    public String[] getBenefitsList() {
        if (benefits == null || benefits.trim().isEmpty()) {
            return new String[0];
        }
        
        return benefits.split("\n");
    }
    
    /**
     * Get display name with type
     */
    public String getDisplayName() {
        StringBuilder display = new StringBuilder();
        
        if (groupName != null && !groupName.trim().isEmpty()) {
            display.append(groupName);
        } else {
            display.append("Unknown Group");
        }
        
        if (groupType != null) {
            display.append(" (").append(groupType.getDisplayName()).append(")");
        }
        
        return display.toString();
    }
    
    /**
     * Get summary of group benefits
     */
    public String getBenefitsSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (discountPercentage != null && discountPercentage.compareTo(BigDecimal.ZERO) > 0) {
            summary.append("• ").append(discountPercentage).append("% discount\n");
        }
        
        if (creditLimit != null && creditLimit.compareTo(BigDecimal.ZERO) > 0) {
            summary.append("• Credit limit: $").append(creditLimit).append("\n");
        }
        
        if (paymentTermsDays > 0) {
            summary.append("• Payment terms: ").append(getPaymentTermsDescription()).append("\n");
        }
        
        if (taxExempt) {
            summary.append("• Tax exempt\n");
        }
        
        if (allowBackorders) {
            summary.append("• Backorders allowed\n");
        }
        
        if (benefits != null && !benefits.trim().isEmpty()) {
            summary.append("• Additional benefits available\n");
        }
        
        return summary.length() > 0 ? summary.toString() : "Standard pricing and terms";
    }
    
    @Override
    public String toString() {
        return String.format("CustomerGroup{id=%d, code='%s', name='%s', type=%s}", 
                id, groupCode, groupName, groupType);
    }
}
