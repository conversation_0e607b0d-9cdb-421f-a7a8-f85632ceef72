package com.clothingstore.model;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Setting model class representing configurable system parameters
 */
public class Setting {
    private Long id;
    private String key;
    private String value;
    private String description;
    private String category;
    private String dataType; // STRING, INTEGER, DECIMAL, BOOLEAN
    private String defaultValue;
    private boolean isRequired;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public Setting() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.isRequired = false;
        this.dataType = "STRING";
    }

    public Setting(String key, String value) {
        this();
        this.key = key;
        this.value = value;
    }

    public Setting(String key, String value, String description, String category) {
        this(key, value);
        this.description = description;
        this.category = category;
    }

    public Setting(String key, String value, String description, String category, String dataType) {
        this(key, value, description, category);
        this.dataType = dataType;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getKey() { return key; }
    public void setKey(String key) { this.key = key; }

    public String getValue() { return value; }
    public void setValue(String value) { 
        this.value = value;
        this.updatedAt = LocalDateTime.now();
    }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public String getDataType() { return dataType; }
    public void setDataType(String dataType) { this.dataType = dataType; }

    public String getDefaultValue() { return defaultValue; }
    public void setDefaultValue(String defaultValue) { this.defaultValue = defaultValue; }

    public boolean isRequired() { return isRequired; }
    public void setRequired(boolean required) { isRequired = required; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    // Utility methods for type conversion
    public String getStringValue() {
        return value != null ? value : defaultValue;
    }

    public int getIntValue() {
        try {
            String val = value != null ? value : defaultValue;
            return val != null ? Integer.parseInt(val) : 0;
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public double getDoubleValue() {
        try {
            String val = value != null ? value : defaultValue;
            return val != null ? Double.parseDouble(val) : 0.0;
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    public boolean getBooleanValue() {
        String val = value != null ? value : defaultValue;
        return val != null && ("true".equalsIgnoreCase(val) || "1".equals(val) || "yes".equalsIgnoreCase(val));
    }

    public java.math.BigDecimal getDecimalValue() {
        try {
            String val = value != null ? value : defaultValue;
            return val != null ? new java.math.BigDecimal(val) : java.math.BigDecimal.ZERO;
        } catch (NumberFormatException e) {
            return java.math.BigDecimal.ZERO;
        }
    }

    // Validation methods
    public boolean isValid() {
        if (isRequired && (value == null || value.trim().isEmpty())) {
            return false;
        }

        if (value == null || value.trim().isEmpty()) {
            return true; // Optional empty values are valid
        }

        switch (dataType.toUpperCase()) {
            case "INTEGER":
                try {
                    Integer.parseInt(value);
                    return true;
                } catch (NumberFormatException e) {
                    return false;
                }
            case "DECIMAL":
                try {
                    new java.math.BigDecimal(value);
                    return true;
                } catch (NumberFormatException e) {
                    return false;
                }
            case "BOOLEAN":
                return "true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value) ||
                       "1".equals(value) || "0".equals(value) ||
                       "yes".equalsIgnoreCase(value) || "no".equalsIgnoreCase(value);
            case "STRING":
            default:
                return true;
        }
    }

    public String getValidationError() {
        if (isRequired && (value == null || value.trim().isEmpty())) {
            return "This setting is required";
        }

        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        switch (dataType.toUpperCase()) {
            case "INTEGER":
                try {
                    Integer.parseInt(value);
                    return null;
                } catch (NumberFormatException e) {
                    return "Value must be a valid integer";
                }
            case "DECIMAL":
                try {
                    new java.math.BigDecimal(value);
                    return null;
                } catch (NumberFormatException e) {
                    return "Value must be a valid decimal number";
                }
            case "BOOLEAN":
                if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value) ||
                    "1".equals(value) || "0".equals(value) ||
                    "yes".equalsIgnoreCase(value) || "no".equalsIgnoreCase(value)) {
                    return null;
                } else {
                    return "Value must be true/false, yes/no, or 1/0";
                }
            case "STRING":
            default:
                return null;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Setting setting = (Setting) o;
        return Objects.equals(id, setting.id) && Objects.equals(key, setting.key);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, key);
    }

    @Override
    public String toString() {
        return String.format("Setting{id=%d, key='%s', value='%s', category='%s', dataType='%s'}",
                id, key, value, category, dataType);
    }
}
