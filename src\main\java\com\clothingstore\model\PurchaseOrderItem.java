package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing an item in a purchase order
 */
public class PurchaseOrderItem {
    
    private Long id;
    private Long purchaseOrderId;
    private Long productId;
    private Product product;
    private String productSku;
    private String productName;
    private String description;
    private int quantityOrdered;
    private int quantityReceived;
    private BigDecimal unitCost;
    private BigDecimal lineTotal;
    private String notes;
    private LocalDateTime expectedDate;
    private LocalDateTime receivedDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructors
    public PurchaseOrderItem() {
        this.quantityOrdered = 0;
        this.quantityReceived = 0;
        this.unitCost = BigDecimal.ZERO;
        this.lineTotal = BigDecimal.ZERO;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public PurchaseOrderItem(Long productId, int quantity, BigDecimal unitCost) {
        this();
        this.productId = productId;
        this.quantityOrdered = quantity;
        this.unitCost = unitCost;
        calculateLineTotal();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getPurchaseOrderId() {
        return purchaseOrderId;
    }
    
    public void setPurchaseOrderId(Long purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
    }
    
    public Long getProductId() {
        return productId;
    }
    
    public void setProductId(Long productId) {
        this.productId = productId;
    }
    
    public Product getProduct() {
        return product;
    }
    
    public void setProduct(Product product) {
        this.product = product;
        if (product != null) {
            this.productId = product.getId();
            this.productSku = product.getSku();
            this.productName = product.getName();
        }
    }
    
    public String getProductSku() {
        return productSku;
    }
    
    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public int getQuantityOrdered() {
        return quantityOrdered;
    }
    
    public void setQuantityOrdered(int quantityOrdered) {
        this.quantityOrdered = quantityOrdered;
        calculateLineTotal();
    }
    
    public int getQuantityReceived() {
        return quantityReceived;
    }
    
    public void setQuantityReceived(int quantityReceived) {
        this.quantityReceived = quantityReceived;
        this.updatedAt = LocalDateTime.now();
        
        if (quantityReceived > 0 && receivedDate == null) {
            this.receivedDate = LocalDateTime.now();
        }
    }
    
    public BigDecimal getUnitCost() {
        return unitCost;
    }
    
    public void setUnitCost(BigDecimal unitCost) {
        this.unitCost = unitCost;
        calculateLineTotal();
    }
    
    public BigDecimal getLineTotal() {
        return lineTotal;
    }
    
    public void setLineTotal(BigDecimal lineTotal) {
        this.lineTotal = lineTotal;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public LocalDateTime getExpectedDate() {
        return expectedDate;
    }
    
    public void setExpectedDate(LocalDateTime expectedDate) {
        this.expectedDate = expectedDate;
    }
    
    public LocalDateTime getReceivedDate() {
        return receivedDate;
    }
    
    public void setReceivedDate(LocalDateTime receivedDate) {
        this.receivedDate = receivedDate;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Business methods
    
    /**
     * Calculate line total based on quantity and unit cost
     */
    public void calculateLineTotal() {
        if (unitCost != null) {
            this.lineTotal = unitCost.multiply(new BigDecimal(quantityOrdered));
        } else {
            this.lineTotal = BigDecimal.ZERO;
        }
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Get quantity still pending
     */
    public int getQuantityPending() {
        return quantityOrdered - quantityReceived;
    }
    
    /**
     * Check if item is fully received
     */
    public boolean isFullyReceived() {
        return quantityReceived >= quantityOrdered;
    }
    
    /**
     * Check if item is partially received
     */
    public boolean isPartiallyReceived() {
        return quantityReceived > 0 && quantityReceived < quantityOrdered;
    }
    
    /**
     * Check if item is not received
     */
    public boolean isNotReceived() {
        return quantityReceived == 0;
    }
    
    /**
     * Get receiving status
     */
    public String getReceivingStatus() {
        if (isFullyReceived()) {
            return "Fully Received";
        } else if (isPartiallyReceived()) {
            return "Partially Received";
        } else {
            return "Not Received";
        }
    }
    
    /**
     * Receive quantity of this item
     */
    public boolean receiveQuantity(int quantity) {
        if (quantity <= 0 || quantity > getQuantityPending()) {
            return false;
        }
        
        this.quantityReceived += quantity;
        this.updatedAt = LocalDateTime.now();
        
        if (this.receivedDate == null) {
            this.receivedDate = LocalDateTime.now();
        }
        
        return true;
    }
    
    /**
     * Get display text for this item
     */
    public String getDisplayText() {
        StringBuilder display = new StringBuilder();
        
        if (productSku != null && !productSku.trim().isEmpty()) {
            display.append(productSku).append(" - ");
        }
        
        if (productName != null && !productName.trim().isEmpty()) {
            display.append(productName);
        } else {
            display.append("Unknown Product");
        }
        
        display.append(" (Qty: ").append(quantityOrdered);
        
        if (quantityReceived > 0) {
            display.append(", Received: ").append(quantityReceived);
        }
        
        display.append(")");
        
        return display.toString();
    }
    
    @Override
    public String toString() {
        return String.format("PurchaseOrderItem{id=%d, productId=%d, qty=%d, unitCost=%s}", 
                id, productId, quantityOrdered, unitCost);
    }
}
