package com.clothingstore.model;

/**
 * Helper class for tracking product sales data
 */
public class ProductSalesData {
    private String productName;
    private String sku;
    private int totalQuantity;
    private double totalRevenue;
    private int transactionCount;

    public ProductSalesData(String productName, String sku) {
        this.productName = productName;
        this.sku = sku;
        this.totalQuantity = 0;
        this.totalRevenue = 0.0;
        this.transactionCount = 0;
    }

    public void addSale(int quantity, double revenue) {
        this.totalQuantity += quantity;
        this.totalRevenue += revenue;
        this.transactionCount++;
    }

    // Getters
    public String getProductName() {
        return productName;
    }

    public String getSku() {
        return sku;
    }

    public int getTotalQuantity() {
        return totalQuantity;
    }

    public double getTotalRevenue() {
        return totalRevenue;
    }

    public int getTransactionCount() {
        return transactionCount;
    }

    public double getAveragePrice() {
        return totalQuantity > 0 ? totalRevenue / totalQuantity : 0.0;
    }

    public double getAverageTransactionValue() {
        return transactionCount > 0 ? totalRevenue / transactionCount : 0.0;
    }

    @Override
    public String toString() {
        return String.format("ProductSalesData{productName='%s', sku='%s', totalQuantity=%d, totalRevenue=%.2f, transactionCount=%d}", 
                           productName, sku, totalQuantity, totalRevenue, transactionCount);
    }
}
