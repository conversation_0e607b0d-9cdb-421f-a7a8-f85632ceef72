package com.clothingstore.service;

import com.clothingstore.model.PurchaseOrder;

/**
 * Result class for purchase order operations
 */
public class PurchaseOrderOperationResult {

    private final boolean success;
    private final String message;
    private final PurchaseOrder purchaseOrder;

    public PurchaseOrderOperationResult(boolean success, String message, PurchaseOrder purchaseOrder) {
        this.success = success;
        this.message = message;
        this.purchaseOrder = purchaseOrder;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public PurchaseOrder getPurchaseOrder() {
        return purchaseOrder;
    }

    @Override
    public String toString() {
        return String.format("PurchaseOrderOperationResult{success=%s, message='%s', purchaseOrder=%s}",
                success, message, purchaseOrder != null ? purchaseOrder.getOrderNumber() : "null");
    }
}
