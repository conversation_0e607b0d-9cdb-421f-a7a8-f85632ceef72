package com.clothingstore.service;

import com.clothingstore.model.Payment;
import java.math.BigDecimal;

/**
 * Result class for single payment processing
 */
public class PaymentResult {
    
    private final boolean success;
    private final String message;
    private final Payment payment;
    private final BigDecimal change;
    
    public PaymentResult(boolean success, String message, Payment payment) {
        this(success, message, payment, BigDecimal.ZERO);
    }
    
    public PaymentResult(boolean success, String message, Payment payment, BigDecimal change) {
        this.success = success;
        this.message = message;
        this.payment = payment;
        this.change = change != null ? change : BigDecimal.ZERO;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public Payment getPayment() {
        return payment;
    }
    
    public BigDecimal getChange() {
        return change;
    }
    
    public boolean hasChange() {
        return change.compareTo(BigDecimal.ZERO) > 0;
    }
}
