<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.InventoryMovementReportControllerMinimal">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label text="Inventory Movement Report (Minimal)" textFill="#2c3e50">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnRefresh" mnemonicParsing="false" onAction="#handleRefresh" text="Refresh" />
            <Button fx:id="btnExport" mnemonicParsing="false" onAction="#handleExport" text="Export" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <Separator />

      <!-- Date Range Section -->
      <HBox alignment="CENTER_LEFT" spacing="15.0">
         <children>
            <Label text="Date Range:" />
            <DatePicker fx:id="dpStartDate" promptText="Start Date" />
            <Label text="to" />
            <DatePicker fx:id="dpEndDate" promptText="End Date" />
            <Button fx:id="btnGenerateReport" mnemonicParsing="false" onAction="#handleGenerateReport" text="Generate Report" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <Separator />

      <!-- Summary Statistics Section -->
      <HBox spacing="20.0">
         <children>
            <!-- Items Sold/Processed Statistics -->
            <VBox alignment="CENTER" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
               <children>
                  <Label text="Items Sold/Processed" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Label text="Count:" />
                  <Label fx:id="lblSoldItemCount" text="0" textFill="#27ae60">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Quantity:" />
                  <Label fx:id="lblSoldQuantity" text="0" textFill="#27ae60">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Value:" />
                  <Label fx:id="lblSoldValue" text="$0.00" textFill="#27ae60">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
               </children>
            </VBox>

            <!-- Items Returned/Refunded Statistics -->
            <VBox alignment="CENTER" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
               <children>
                  <Label text="Items Returned/Refunded" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Label text="Count:" />
                  <Label fx:id="lblReturnedItemCount" text="0" textFill="#e74c3c">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Quantity:" />
                  <Label fx:id="lblReturnedQuantity" text="0" textFill="#e74c3c">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Value:" />
                  <Label fx:id="lblReturnedValue" text="$0.00" textFill="#e74c3c">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
               </children>
            </VBox>

            <!-- Net Movement Statistics -->
            <VBox alignment="CENTER" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
               <children>
                  <Label text="Net Movement" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Label text="Net Quantity:" />
                  <Label fx:id="lblNetQuantity" text="0" textFill="#34495e">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Net Value:" />
                  <Label fx:id="lblNetValue" text="$0.00" textFill="#34495e">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Return Rate:" />
                  <Label fx:id="lblReturnRate" text="0.00%" textFill="#f39c12">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <Separator />

      <!-- Placeholder for Tables -->
      <VBox VBox.vgrow="ALWAYS">
         <children>
            <Label text="Inventory Movement Data Tables" style="-fx-font-size: 16px; -fx-font-weight: bold;">
               <VBox.margin>
                  <Insets bottom="10.0" left="20.0" right="20.0" top="20.0" />
               </VBox.margin>
            </Label>
            <Label text="This is a minimal version to test FXML loading." style="-fx-font-size: 12px;">
               <VBox.margin>
                  <Insets bottom="10.0" left="20.0" right="20.0" top="5.0" />
               </VBox.margin>
            </Label>
            <Label text="Click the buttons above to test functionality." style="-fx-font-size: 12px;">
               <VBox.margin>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="5.0" />
               </VBox.margin>
            </Label>
         </children>
      </VBox>
   </children>
</VBox>
