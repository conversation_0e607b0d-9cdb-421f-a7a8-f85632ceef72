package com.clothingstore.service;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Service for managing customer groups and group-based pricing
 */
public class CustomerGroupService {

    private static final Logger LOGGER = Logger.getLogger(CustomerGroupService.class.getName());
    private static CustomerGroupService instance;

    // In-memory storage for demo - in real app would use database
    private final Map<Long, CustomerGroup> customerGroups;
    private final Map<Long, Long> customerGroupAssignments; // customerId -> groupId
    private final CustomerDAO customerDAO;

    private long nextGroupId = 1;

    private CustomerGroupService() {
        this.customerGroups = new HashMap<>();
        this.customerGroupAssignments = new HashMap<>();
        this.customerDAO = CustomerDAO.getInstance();
        initializeDefaultGroups();
    }

    public static synchronized CustomerGroupService getInstance() {
        if (instance == null) {
            instance = new CustomerGroupService();
        }
        return instance;
    }

    /**
     * Initialize default customer groups
     */
    private void initializeDefaultGroups() {
        // Create default groups for each type
        for (CustomerGroupType type : CustomerGroupType.values()) {
            CustomerGroup group = type.createDefaultGroup();
            group.setId(nextGroupId++);
            customerGroups.put(group.getId(), group);
        }

        LOGGER.info("Initialized " + customerGroups.size() + " default customer groups");
    }

    /**
     * Create a new customer group
     */
    public CustomerGroupOperationResult createCustomerGroup(CustomerGroup group) {
        try {
            // Validate group data
            String validationError = validateCustomerGroup(group);
            if (validationError != null) {
                return new CustomerGroupOperationResult(false, validationError, null);
            }

            // Check for duplicate group code
            if (isGroupCodeExists(group.getGroupCode())) {
                return new CustomerGroupOperationResult(false, "Group code already exists", null);
            }

            // Set ID and save
            group.setId(nextGroupId++);
            group.setCreatedAt(LocalDateTime.now());
            group.setUpdatedAt(LocalDateTime.now());

            customerGroups.put(group.getId(), group);

            LOGGER.info("Created customer group: " + group.getDisplayName());
            return new CustomerGroupOperationResult(true, "Customer group created successfully", group);

        } catch (Exception e) {
            LOGGER.severe("Error creating customer group: " + e.getMessage());
            return new CustomerGroupOperationResult(false, "Error creating group: " + e.getMessage(), null);
        }
    }

    /**
     * Update an existing customer group
     */
    public CustomerGroupOperationResult updateCustomerGroup(CustomerGroup group) {
        try {
            if (group.getId() == null || !customerGroups.containsKey(group.getId())) {
                return new CustomerGroupOperationResult(false, "Customer group not found", null);
            }

            // Validate group data
            String validationError = validateCustomerGroup(group);
            if (validationError != null) {
                return new CustomerGroupOperationResult(false, validationError, null);
            }

            group.setUpdatedAt(LocalDateTime.now());
            customerGroups.put(group.getId(), group);

            LOGGER.info("Updated customer group: " + group.getDisplayName());
            return new CustomerGroupOperationResult(true, "Customer group updated successfully", group);

        } catch (Exception e) {
            LOGGER.severe("Error updating customer group: " + e.getMessage());
            return new CustomerGroupOperationResult(false, "Error updating group: " + e.getMessage(), null);
        }
    }

    /**
     * Get customer group by ID
     */
    public CustomerGroup getCustomerGroup(Long groupId) {
        return customerGroups.get(groupId);
    }

    /**
     * Get all customer groups
     */
    public List<CustomerGroup> getAllCustomerGroups() {
        return customerGroups.values().stream()
                .sorted(Comparator.comparing(CustomerGroup::getPriority).reversed()
                        .thenComparing(CustomerGroup::getGroupName))
                .collect(Collectors.toList());
    }

    /**
     * Get active customer groups
     */
    public List<CustomerGroup> getActiveCustomerGroups() {
        return customerGroups.values().stream()
                .filter(CustomerGroup::isActive)
                .sorted(Comparator.comparing(CustomerGroup::getPriority).reversed()
                        .thenComparing(CustomerGroup::getGroupName))
                .collect(Collectors.toList());
    }

    /**
     * Get customer groups by type
     */
    public List<CustomerGroup> getCustomerGroupsByType(CustomerGroupType type) {
        return customerGroups.values().stream()
                .filter(group -> group.getGroupType() == type && group.isActive())
                .collect(Collectors.toList());
    }

    /**
     * Assign customer to a group
     */
    public CustomerGroupOperationResult assignCustomerToGroup(Long customerId, Long groupId) {
        try {
            // Validate customer exists
            Optional<Customer> customerOpt = customerDAO.findById(customerId);
            if (!customerOpt.isPresent()) {
                return new CustomerGroupOperationResult(false, "Customer not found", null);
            }

            // Validate group exists
            CustomerGroup group = customerGroups.get(groupId);
            if (group == null) {
                return new CustomerGroupOperationResult(false, "Customer group not found", null);
            }

            if (!group.isActive()) {
                return new CustomerGroupOperationResult(false, "Customer group is not active", null);
            }

            // Check if group requires approval
            if (group.isRequireApproval()) {
                // In a real system, this would create a pending approval request
                LOGGER.info("Group assignment requires approval: " + group.getGroupName());
            }

            // Assign customer to group
            customerGroupAssignments.put(customerId, groupId);

            String message = String.format("Customer assigned to group: %s", group.getGroupName());
            LOGGER.info(message);

            return new CustomerGroupOperationResult(true, message, group);

        } catch (Exception e) {
            LOGGER.severe("Error assigning customer to group: " + e.getMessage());
            return new CustomerGroupOperationResult(false, "Error assigning customer: " + e.getMessage(), null);
        }
    }

    /**
     * Remove customer from group
     */
    public CustomerGroupOperationResult removeCustomerFromGroup(Long customerId) {
        try {
            Long previousGroupId = customerGroupAssignments.remove(customerId);

            if (previousGroupId == null) {
                return new CustomerGroupOperationResult(false, "Customer was not assigned to any group", null);
            }

            CustomerGroup previousGroup = customerGroups.get(previousGroupId);
            String message = String.format("Customer removed from group: %s",
                    previousGroup != null ? previousGroup.getGroupName() : "Unknown");

            LOGGER.info(message);
            return new CustomerGroupOperationResult(true, message, null);

        } catch (Exception e) {
            LOGGER.severe("Error removing customer from group: " + e.getMessage());
            return new CustomerGroupOperationResult(false, "Error removing customer: " + e.getMessage(), null);
        }
    }

    /**
     * Get customer's assigned group
     */
    public CustomerGroup getCustomerGroupForCustomer(Long customerId) {
        Long groupId = customerGroupAssignments.get(customerId);

        if (groupId == null) {
            // Return default regular group
            return getDefaultGroup(CustomerGroupType.REGULAR);
        }

        return customerGroups.get(groupId);
    }

    /**
     * Calculate price for customer based on their group
     */
    public BigDecimal calculateCustomerPrice(Long customerId, BigDecimal originalPrice) {
        CustomerGroup group = getCustomerGroup(customerId);

        if (group == null) {
            return originalPrice;
        }

        return group.calculateDiscountedPrice(originalPrice);
    }

    /**
     * Check if customer can place order based on group rules
     */
    public OrderEligibilityResult checkOrderEligibility(Long customerId, BigDecimal orderAmount,
            BigDecimal currentBalance) {
        CustomerGroup group = getCustomerGroup(customerId);

        if (group == null) {
            return new OrderEligibilityResult(true, "No group restrictions", null);
        }

        // Check minimum order amount
        if (!group.meetsMinimumOrder(orderAmount)) {
            String message = String.format("Order amount $%.2f is below minimum $%.2f for %s group",
                    orderAmount, group.getMinimumOrderAmount(), group.getGroupName());
            return new OrderEligibilityResult(false, message, group);
        }

        // Check credit limit
        if (!group.hasAvailableCredit(currentBalance, orderAmount)) {
            String message = String.format("Order would exceed credit limit for %s group", group.getGroupName());
            return new OrderEligibilityResult(false, message, group);
        }

        return new OrderEligibilityResult(true, "Order approved for " + group.getGroupName(), group);
    }

    /**
     * Get group statistics
     */
    public CustomerGroupStatistics getGroupStatistics(Long groupId) {
        CustomerGroup group = customerGroups.get(groupId);
        if (group == null) {
            return null;
        }

        // Count customers in this group
        long customerCount = customerGroupAssignments.values().stream()
                .filter(gId -> gId.equals(groupId))
                .count();

        // In a real system, would calculate revenue, orders, etc. from database
        return new CustomerGroupStatistics(group, (int) customerCount, BigDecimal.ZERO, 0);
    }

    /**
     * Get default group by type
     */
    public CustomerGroup getDefaultGroup(CustomerGroupType type) {
        return customerGroups.values().stream()
                .filter(group -> group.getGroupType() == type)
                .findFirst()
                .orElse(null);
    }

    /**
     * Suggest group upgrade for customer
     */
    public CustomerGroupUpgradeSuggestion suggestGroupUpgrade(Long customerId, BigDecimal lifetimeSpend,
            int totalOrders) {
        CustomerGroup currentGroup = getCustomerGroup(customerId);

        if (currentGroup == null) {
            return null;
        }

        CustomerGroupType upgradeType = currentGroup.getGroupType().getUpgradePath();

        if (upgradeType == null) {
            return null; // No upgrade path available
        }

        CustomerGroup suggestedGroup = getDefaultGroup(upgradeType);

        if (suggestedGroup == null) {
            return null;
        }

        // Simple upgrade criteria
        boolean qualifies = false;
        String reason = "";

        switch (upgradeType) {
            case VIP:
                qualifies = lifetimeSpend.compareTo(new BigDecimal("1000")) >= 0 || totalOrders >= 10;
                reason = "Based on your purchase history, you qualify for VIP status";
                break;
            case LOYALTY:
                qualifies = lifetimeSpend.compareTo(new BigDecimal("2500")) >= 0 || totalOrders >= 25;
                reason = "Your loyalty qualifies you for our premium loyalty program";
                break;
            default:
                qualifies = lifetimeSpend.compareTo(new BigDecimal("500")) >= 0;
                reason = "You may be eligible for an upgraded customer group";
        }

        if (qualifies) {
            return new CustomerGroupUpgradeSuggestion(currentGroup, suggestedGroup, reason, true);
        }

        return null;
    }

    /**
     * Validate customer group data
     */
    private String validateCustomerGroup(CustomerGroup group) {
        if (group.getGroupCode() == null || group.getGroupCode().trim().isEmpty()) {
            return "Group code is required";
        }

        if (group.getGroupName() == null || group.getGroupName().trim().isEmpty()) {
            return "Group name is required";
        }

        if (group.getGroupType() == null) {
            return "Group type is required";
        }

        if (group.getDiscountPercentage() != null
                && (group.getDiscountPercentage().compareTo(BigDecimal.ZERO) < 0
                || group.getDiscountPercentage().compareTo(new BigDecimal("100")) > 0)) {
            return "Discount percentage must be between 0 and 100";
        }

        return null; // Valid
    }

    /**
     * Check if group code exists
     */
    private boolean isGroupCodeExists(String groupCode) {
        return customerGroups.values().stream()
                .anyMatch(group -> groupCode.equalsIgnoreCase(group.getGroupCode()));
    }
}

/**
 * Result class for customer group operations
 */
class CustomerGroupOperationResult {

    private final boolean success;
    private final String message;
    private final CustomerGroup group;

    public CustomerGroupOperationResult(boolean success, String message, CustomerGroup group) {
        this.success = success;
        this.message = message;
        this.group = group;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public CustomerGroup getGroup() {
        return group;
    }
}

/**
 * Result class for order eligibility checks
 */
class OrderEligibilityResult {

    private final boolean eligible;
    private final String message;
    private final CustomerGroup group;

    public OrderEligibilityResult(boolean eligible, String message, CustomerGroup group) {
        this.eligible = eligible;
        this.message = message;
        this.group = group;
    }

    public boolean isEligible() {
        return eligible;
    }

    public String getMessage() {
        return message;
    }

    public CustomerGroup getGroup() {
        return group;
    }
}

/**
 * Class for customer group statistics
 */
class CustomerGroupStatistics {

    private final CustomerGroup group;
    private final int customerCount;
    private final BigDecimal totalRevenue;
    private final int totalOrders;

    public CustomerGroupStatistics(CustomerGroup group, int customerCount,
            BigDecimal totalRevenue, int totalOrders) {
        this.group = group;
        this.customerCount = customerCount;
        this.totalRevenue = totalRevenue;
        this.totalOrders = totalOrders;
    }

    public CustomerGroup getGroup() {
        return group;
    }

    public int getCustomerCount() {
        return customerCount;
    }

    public BigDecimal getTotalRevenue() {
        return totalRevenue;
    }

    public int getTotalOrders() {
        return totalOrders;
    }

    public BigDecimal getAverageOrderValue() {
        if (totalOrders == 0) {
            return BigDecimal.ZERO;
        }
        return totalRevenue.divide(new BigDecimal(totalOrders), 2, java.math.RoundingMode.HALF_UP);
    }

    public BigDecimal getRevenuePerCustomer() {
        if (customerCount == 0) {
            return BigDecimal.ZERO;
        }
        return totalRevenue.divide(new BigDecimal(customerCount), 2, java.math.RoundingMode.HALF_UP);
    }
}

/**
 * Class for group upgrade suggestions
 */
class CustomerGroupUpgradeSuggestion {

    private final CustomerGroup currentGroup;
    private final CustomerGroup suggestedGroup;
    private final String reason;
    private final boolean qualifies;

    public CustomerGroupUpgradeSuggestion(CustomerGroup currentGroup, CustomerGroup suggestedGroup,
            String reason, boolean qualifies) {
        this.currentGroup = currentGroup;
        this.suggestedGroup = suggestedGroup;
        this.reason = reason;
        this.qualifies = qualifies;
    }

    public CustomerGroup getCurrentGroup() {
        return currentGroup;
    }

    public CustomerGroup getSuggestedGroup() {
        return suggestedGroup;
    }

    public String getReason() {
        return reason;
    }

    public boolean isQualifies() {
        return qualifies;
    }

    public String getUpgradeMessage() {
        return String.format("Upgrade from %s to %s: %s",
                currentGroup.getGroupName(), suggestedGroup.getGroupName(), reason);
    }

    public BigDecimal getPotentialSavings() {
        // Calculate potential savings based on discount difference
        BigDecimal currentDiscount = currentGroup.getDiscountPercentage();
        BigDecimal newDiscount = suggestedGroup.getDiscountPercentage();

        return newDiscount.subtract(currentDiscount);
    }
}
