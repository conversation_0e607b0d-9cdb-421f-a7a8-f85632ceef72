<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.MultiplePaymentDialogController">
   <children>
      <!-- Header -->
      <VBox styleClass="dialog-header">
         <children>
            <Label styleClass="dialog-title" text="Multiple Payment Methods">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Label fx:id="lblTransactionNumber" styleClass="dialog-subtitle" text="Transaction: TXN-001" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </VBox>

      <!-- Transaction Summary -->
      <GridPane hgap="10.0" vgap="8.0" styleClass="summary-section">
         <columnConstraints>
            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         <children>
            <Label text="Total Amount:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
            <Label fx:id="lblTotalAmount" styleClass="amount-label" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="0" />
            
            <Label text="Total Paid:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
            <Label fx:id="lblTotalPaid" styleClass="amount-label" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            
            <Label text="Remaining:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
            <Label fx:id="lblRemainingBalance" styleClass="amount-label" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="2" />
         </children>
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </GridPane>

      <!-- Status -->
      <HBox alignment="CENTER" styleClass="status-section">
         <children>
            <Label fx:id="lblStatus" styleClass="status-label" text="⏳ Awaiting Payment" />
         </children>
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="5.0" />
         </padding>
      </HBox>

      <!-- Change Information (hidden by default) -->
      <VBox fx:id="vboxChangeInfo" alignment="CENTER" styleClass="change-info" visible="false">
         <children>
            <Label fx:id="lblChange" styleClass="change-label" text="Change: $0.00">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="5.0" />
         </padding>
      </VBox>

      <!-- Payment Entry Section -->
      <VBox styleClass="payment-entry-section">
         <children>
            <Label styleClass="section-title" text="Add Payment Method">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            
            <GridPane hgap="10.0" vgap="10.0">
               <columnConstraints>
                  <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                  <ColumnConstraints hgrow="ALWAYS" />
                  <ColumnConstraints hgrow="NEVER" />
                  <ColumnConstraints hgrow="NEVER" />
               </columnConstraints>
               <children>
                  <Label text="Payment Method:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                  <ComboBox fx:id="cmbPaymentMethod" maxWidth="Infinity" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                  
                  <Label text="Amount:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                  <TextField fx:id="txtPaymentAmount" promptText="Enter amount" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                  <Button fx:id="btnAddPayment" onAction="#handleAddPayment" styleClass="add-button" text="Add" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                  <Button onAction="#handleQuickAmount" styleClass="quick-button" text="Remaining" GridPane.columnIndex="3" GridPane.rowIndex="1" />
               </children>
            </GridPane>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </VBox>

      <!-- Payments Table -->
      <VBox styleClass="payments-table-section" VBox.vgrow="ALWAYS">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="Payment Methods Added">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnRemovePayment" onAction="#handleRemovePayment" styleClass="remove-button" text="Remove Selected" />
               </children>
            </HBox>
            
            <TableView fx:id="tblPayments" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="colPaymentMethod" prefWidth="200.0" text="Payment Method" />
                  <TableColumn fx:id="colAmount" prefWidth="150.0" text="Amount" />
               </columns>
               <columnResizePolicy>
                  <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TableView>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="5.0" />
         </padding>
      </VBox>

      <!-- Action Buttons -->
      <HBox alignment="CENTER" spacing="15.0" styleClass="button-section">
         <children>
            <Button fx:id="btnCancel" onAction="#handleCancel" styleClass="cancel-button" text="Cancel" />
            <Button fx:id="btnCompleteTransaction" onAction="#handleCompleteTransaction" styleClass="complete-button" text="Complete Transaction" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </children>
</VBox>
