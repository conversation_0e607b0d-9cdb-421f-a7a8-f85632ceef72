package com.clothingstore.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Customer model class for managing customer information and loyalty program
 */
public class Customer {

    private Long id;
    private String firstName;
    private String lastName;
    private String phone;
    private String address;
    private String city;
    private String state;
    private String zipCode;
    private LocalDate dateOfBirth;
    private String gender;
    private LocalDateTime registrationDate;
    private boolean active;
    private int loyaltyPoints;
    private LocalDateTime lastPurchaseDate;
    private double totalSpent;
    private int totalPurchases;
    private String membershipLevel;

    // Constructors
    public Customer() {
        this.active = true;
        this.registrationDate = LocalDateTime.now();
        this.loyaltyPoints = 0;
        this.totalSpent = 0.0;
        this.totalPurchases = 0;
        this.membershipLevel = "Standard";
    }

    public Customer(String firstName, String lastName, String phone) {
        this();
        this.firstName = firstName;
        this.lastName = lastName;
        this.phone = phone;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFullName() {
        return (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public LocalDateTime getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(LocalDateTime registrationDate) {
        this.registrationDate = registrationDate;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public int getLoyaltyPoints() {
        return loyaltyPoints;
    }

    public void setLoyaltyPoints(int loyaltyPoints) {
        this.loyaltyPoints = loyaltyPoints;
    }

    public LocalDateTime getLastPurchaseDate() {
        return lastPurchaseDate;
    }

    public void setLastPurchaseDate(LocalDateTime lastPurchaseDate) {
        this.lastPurchaseDate = lastPurchaseDate;
    }

    public double getTotalSpent() {
        return totalSpent;
    }

    public void setTotalSpent(double totalSpent) {
        this.totalSpent = totalSpent;
    }

    public int getTotalPurchases() {
        return totalPurchases;
    }

    public void setTotalPurchases(int totalPurchases) {
        this.totalPurchases = totalPurchases;
    }

    // Business methods
    public void addLoyaltyPoints(int points) {
        this.loyaltyPoints += points;
    }

    public boolean redeemLoyaltyPoints(int points) {
        if (loyaltyPoints >= points) {
            this.loyaltyPoints -= points;
            return true;
        }
        return false;
    }

    public void addPurchase(double amount) {
        this.totalSpent += amount;
        this.totalPurchases++;
        this.lastPurchaseDate = LocalDateTime.now();

        // Add loyalty points (1 point per dollar spent)
        addLoyaltyPoints((int) amount);
    }

    /**
     * Get discount percentage - MANUAL ONLY: No automatic discounts Discounts
     * must be manually applied during transaction
     */
    public double getDiscountPercentage() {
        return 0.0; // No automatic discounts - manual application only
    }

    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (address != null) {
            sb.append(address);
        }
        if (city != null) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append(city);
        }
        if (state != null) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append(state);
        }
        if (zipCode != null) {
            if (sb.length() > 0) {
                sb.append(" ");
            }
            sb.append(zipCode);
        }
        return sb.toString();
    }

    // Convenience methods for UI compatibility
    public String getName() {
        return getFullName();
    }

    public String getStatus() {
        return active ? "Active" : "Inactive";
    }

    public LocalDate getBirthDate() {
        return getDateOfBirth();
    }

    /**
     * Get membership level - configurable membership system
     */
    public String getMembershipLevel() {
        return membershipLevel != null ? membershipLevel : "Standard";
    }

    /**
     * Set membership level - configurable membership levels
     */
    public void setMembershipLevel(String membershipLevel) {
        this.membershipLevel = membershipLevel;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Customer customer = (Customer) o;
        return Objects.equals(id, customer.id) && Objects.equals(phone, customer.phone);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, phone);
    }

    @Override
    public String toString() {
        return String.format("Customer{id=%d, name='%s', phone='%s', points=%d}",
                id, getFullName(), phone, loyaltyPoints);
    }
}
