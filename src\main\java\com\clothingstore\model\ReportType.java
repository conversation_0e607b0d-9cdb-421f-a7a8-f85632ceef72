package com.clothingstore.model;

/**
 * Enumeration for different types of reports
 */
public enum ReportType {
    SALES_SUMMARY("Sales Summary", "Overview of sales performance"),
    PRODUCT_PERFORMANCE("Product Performance", "Analysis of product sales and inventory"),
    CUSTOMER_ANALYSIS("Customer Analysis", "Customer behavior and demographics"),
    INVENTORY_VALUATION("Inventory Valuation", "Current inventory value and costing"),
    PROFIT_LOSS("Profit & Loss", "Financial performance statement"),
    TAX_REPORT("Tax Report", "Tax collection and liability report"),
    DAILY_SALES("Daily Sales", "Day-by-day sales breakdown"),
    MONTHLY_SALES("Monthly Sales", "Month-by-month sales analysis"),
    CATEGORY_PERFORMANCE("Category Performance", "Sales performance by product category"),
    LOW_STOCK_REPORT("Low Stock Report", "Products requiring reorder"),
    TOP_CUSTOMERS("Top Customers", "Best customers by purchase volume"),
    PAYMENT_METHOD_ANALYSIS("Payment Method Analysis", "Breakdown of payment methods used"),
    HOURLY_SALES("Hourly Sales", "Sales patterns by hour of day"),
    SEASONAL_TRENDS("Seasonal Trends", "Seasonal sales patterns and trends"),
    RETURN_ANALYSIS("Return Analysis", "Product returns and refund analysis");

    private final String displayName;
    private final String description;

    ReportType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Check if this report type requires date range
     */
    public boolean requiresDateRange() {
        return this != LOW_STOCK_REPORT && this != INVENTORY_VALUATION;
    }

    /**
     * Check if this report type supports export
     */
    public boolean supportsExport() {
        return true; // All reports support export
    }

    /**
     * Get default date range in days for this report type
     */
    public int getDefaultDateRangeDays() {
        switch (this) {
            case DAILY_SALES:
            case HOURLY_SALES:
                return 7; // Last 7 days
            case MONTHLY_SALES:
            case SEASONAL_TRENDS:
                return 365; // Last year
            case SALES_SUMMARY:
            case PRODUCT_PERFORMANCE:
            case CUSTOMER_ANALYSIS:
            default:
                return 30; // Last 30 days
        }
    }
}
