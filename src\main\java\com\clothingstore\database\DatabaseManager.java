package com.clothingstore.database;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * Database Manager class for handling SQLite database connections and
 * initialization
 */
public class DatabaseManager {

    private static DatabaseManager instance;
    private static final String DATABASE_NAME = "clothing_store.db";
    private static final String DATABASE_URL = "jdbc:sqlite:" + DATABASE_NAME;
    private Connection connection;

    private DatabaseManager() {
    }

    public static synchronized DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }

    public Connection getConnection() throws SQLException {
        if (connection == null || connection.isClosed()) {
            try {
                Class.forName("org.sqlite.JDBC");
                connection = DriverManager.getConnection(DATABASE_URL);
                connection.setAutoCommit(true);
            } catch (ClassNotFoundException e) {
                throw new SQLException("SQLite JDBC driver not found", e);
            }
        }
        return connection;
    }

    public void initializeDatabase() throws SQLException {
        try (Connection conn = getConnection()) {
            createTables(conn);
            // Note: Sample data insertion removed to prevent automatic data creation
            // Use insertSampleData() method explicitly if sample data is needed
        }
    }

    private void createTables(Connection conn) throws SQLException {
        // Create Products table with supplier relationship
        String createProductsTable = "CREATE TABLE IF NOT EXISTS products ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                + "sku TEXT UNIQUE NOT NULL, "
                + "barcode TEXT UNIQUE, "
                + "name TEXT NOT NULL, "
                + "description TEXT, "
                + "category TEXT NOT NULL, "
                + "brand TEXT, "
                + "color TEXT, "
                + "size TEXT, "
                + "price DECIMAL(10,2) NOT NULL, "
                + "cost_price DECIMAL(10,2), "
                + "stock_quantity INTEGER NOT NULL DEFAULT 0, "
                + "min_stock_level INTEGER DEFAULT 5, "
                + "supplier_id INTEGER, "
                + "supplier_name TEXT, "
                + "supplier_code TEXT, "
                + "image_url TEXT, "
                + "active BOOLEAN DEFAULT 1, "
                + "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP"
                + ")";

        // Create Customers table (without membership system and email)
        String createCustomersTable = "CREATE TABLE IF NOT EXISTS customers ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                + "first_name TEXT NOT NULL, "
                + "last_name TEXT NOT NULL, "
                + "phone TEXT, "
                + "address TEXT, "
                + "city TEXT, "
                + "state TEXT, "
                + "zip_code TEXT, "
                + "date_of_birth DATE, "
                + "gender TEXT, "
                + "registration_date DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "active BOOLEAN DEFAULT 1, "
                + "loyalty_points INTEGER DEFAULT 0, "
                + "last_purchase_date DATETIME, "
                + "total_spent DECIMAL(10,2) DEFAULT 0.00, "
                + "total_purchases INTEGER DEFAULT 0"
                + ")";

        // Create Transactions table
        String createTransactionsTable = "CREATE TABLE IF NOT EXISTS transactions ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                + "transaction_number TEXT UNIQUE NOT NULL, "
                + "customer_id INTEGER, "
                + "transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "subtotal DECIMAL(10,2) NOT NULL, "
                + "tax_amount DECIMAL(10,2) DEFAULT 0.00, "
                + // Tax removed but column kept for compatibility
                "discount_amount DECIMAL(10,2) DEFAULT 0.00, "
                + "total_amount DECIMAL(10,2) NOT NULL, "
                + "payment_method TEXT NOT NULL, "
                + "status TEXT DEFAULT 'COMPLETED', "
                + "notes TEXT, "
                + "cashier_name TEXT, "
                + "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "FOREIGN KEY (customer_id) REFERENCES customers(id)"
                + ")";

        // Create Transaction Items table
        String createTransactionItemsTable = "CREATE TABLE IF NOT EXISTS transaction_items ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                + "transaction_id INTEGER NOT NULL, "
                + "product_id INTEGER NOT NULL, "
                + "quantity INTEGER NOT NULL, "
                + "unit_price DECIMAL(10,2) NOT NULL, "
                + "line_total DECIMAL(10,2) NOT NULL, "
                + "discount_amount DECIMAL(10,2) DEFAULT 0.00, "
                + "notes TEXT, "
                + "FOREIGN KEY (transaction_id) REFERENCES transactions(id), "
                + "FOREIGN KEY (product_id) REFERENCES products(id)"
                + ")";

        // Create Settings table
        String createSettingsTable = "CREATE TABLE IF NOT EXISTS settings ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                + "key TEXT UNIQUE NOT NULL, "
                + "value TEXT, "
                + "description TEXT, "
                + "category TEXT, "
                + "data_type TEXT DEFAULT 'STRING', "
                + "default_value TEXT, "
                + "is_required BOOLEAN DEFAULT 0, "
                + "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP"
                + ")";

        // Create Categories table
        String createCategoriesTable = "CREATE TABLE IF NOT EXISTS categories ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                + "name TEXT UNIQUE NOT NULL, "
                + "description TEXT, "
                + "display_order INTEGER DEFAULT 0, "
                + "active BOOLEAN DEFAULT 1, "
                + "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP"
                + ")";

        // Create indexes for better performance
        String[] indexes = {
            "CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
            "CREATE INDEX IF NOT EXISTS idx_products_active ON products(active)",
            "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction ON transaction_items(transaction_id)",
            "CREATE INDEX IF NOT EXISTS idx_transaction_items_product ON transaction_items(product_id)"
        };

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(createProductsTable);
            stmt.execute(createCustomersTable);
            stmt.execute(createTransactionsTable);
            stmt.execute(createTransactionItemsTable);
            stmt.execute(createSettingsTable);
            stmt.execute(createCategoriesTable);

            for (String index : indexes) {
                stmt.execute(index);
            }

            // Perform database migration to remove membership columns if they exist
            performDatabaseMigration(stmt);

            // Add supplier columns to products table if they don't exist
            addSupplierColumnsIfNotExists(stmt);

            // Add membership level column back for customer filtering
            addMembershipLevelColumnIfNotExists(stmt);
        }
    }

    private void performDatabaseMigration(Statement stmt) throws SQLException {
        try {
            // Check if membership_level column exists in customers table
            ResultSet rs = stmt.executeQuery("PRAGMA table_info(customers)");
            boolean hasMembershipLevel = false;

            while (rs.next()) {
                String columnName = rs.getString("name");
                if ("membership_level".equals(columnName)) {
                    hasMembershipLevel = true;
                    break;
                }
            }
            rs.close();

            // If membership_level column exists, remove it
            if (hasMembershipLevel) {
                System.out.println("Performing database migration: removing membership system...");

                // SQLite doesn't support DROP COLUMN, so we need to recreate the table
                stmt.execute("CREATE TABLE customers_new ("
                        + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                        + "first_name TEXT NOT NULL, "
                        + "last_name TEXT NOT NULL, "
                        + "phone TEXT, "
                        + "address TEXT, "
                        + "city TEXT, "
                        + "state TEXT, "
                        + "zip_code TEXT, "
                        + "date_of_birth DATE, "
                        + "gender TEXT, "
                        + "registration_date DATETIME DEFAULT CURRENT_TIMESTAMP, "
                        + "active BOOLEAN DEFAULT 1, "
                        + "loyalty_points INTEGER DEFAULT 0, "
                        + "last_purchase_date DATETIME, "
                        + "total_spent DECIMAL(10,2) DEFAULT 0.00, "
                        + "total_purchases INTEGER DEFAULT 0"
                        + ")");

                // Copy data from old table to new table (excluding membership_level and email)
                stmt.execute("INSERT INTO customers_new (id, first_name, last_name, phone, address, city, state, zip_code, "
                        + "date_of_birth, gender, registration_date, active, loyalty_points, last_purchase_date, total_spent, total_purchases) "
                        + "SELECT id, first_name, last_name, phone, address, city, state, zip_code, "
                        + "date_of_birth, gender, registration_date, active, loyalty_points, last_purchase_date, total_spent, total_purchases "
                        + "FROM customers");

                // Drop old table and rename new table
                stmt.execute("DROP TABLE customers");
                stmt.execute("ALTER TABLE customers_new RENAME TO customers");

                System.out.println("Database migration completed: membership system removed");
            }

            // Add barcode column to products table if it doesn't exist
            addBarcodeColumnIfNotExists(stmt);

            // Add low stock management columns
            addLowStockManagementColumns(stmt);

            // Migrate existing categories to categories table
            migrateCategoriesData(stmt);

            // Initialize default settings if settings table is empty
            initializeDefaultSettings(stmt);

        } catch (SQLException e) {
            System.err.println("Database migration failed: " + e.getMessage());
            // Don't throw the exception to prevent application startup failure
        }
    }

    private void addBarcodeColumnIfNotExists(Statement stmt) throws SQLException {
        try {
            // Check if barcode column exists in products table
            ResultSet rs = stmt.executeQuery("PRAGMA table_info(products)");
            boolean hasBarcodeColumn = false;

            while (rs.next()) {
                String columnName = rs.getString("name");
                if ("barcode".equals(columnName)) {
                    hasBarcodeColumn = true;
                    break;
                }
            }
            rs.close();

            // If barcode column doesn't exist, add it
            if (!hasBarcodeColumn) {
                System.out.println("Adding barcode column to products table...");
                stmt.execute("ALTER TABLE products ADD COLUMN barcode TEXT");

                // Create index for barcode column
                stmt.execute("CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)");

                System.out.println("Barcode column added successfully");
            }
        } catch (SQLException e) {
            System.err.println("Failed to add barcode column: " + e.getMessage());
            // Don't throw the exception to prevent application startup failure
        }
    }

    private void addLowStockManagementColumns(Statement stmt) throws SQLException {
        try {
            // Check existing columns in products table
            ResultSet rs = stmt.executeQuery("PRAGMA table_info(products)");
            boolean hasReorderQuantity = false;
            boolean hasLowStockAlertEnabled = false;

            while (rs.next()) {
                String columnName = rs.getString("name");
                if ("reorder_quantity".equals(columnName)) {
                    hasReorderQuantity = true;
                } else if ("low_stock_alert_enabled".equals(columnName)) {
                    hasLowStockAlertEnabled = true;
                }
            }
            rs.close();

            // Add missing columns
            if (!hasReorderQuantity) {
                System.out.println("Adding reorder_quantity column to products table...");
                stmt.execute("ALTER TABLE products ADD COLUMN reorder_quantity INTEGER DEFAULT 0");
            }

            if (!hasLowStockAlertEnabled) {
                System.out.println("Adding low_stock_alert_enabled column to products table...");
                stmt.execute("ALTER TABLE products ADD COLUMN low_stock_alert_enabled BOOLEAN DEFAULT 1");
            }

            System.out.println("Low stock management columns updated successfully");
        } catch (SQLException e) {
            System.err.println("Failed to add low stock management columns: " + e.getMessage());
            // Don't throw the exception to prevent application startup failure
        }
    }

    private void addSupplierColumnsIfNotExists(Statement stmt) throws SQLException {
        try {
            // Check existing columns in products table
            ResultSet rs = stmt.executeQuery("PRAGMA table_info(products)");
            boolean hasSupplierId = false;
            boolean hasSupplierName = false;
            boolean hasSupplierCode = false;

            while (rs.next()) {
                String columnName = rs.getString("name");
                if ("supplier_id".equals(columnName)) {
                    hasSupplierId = true;
                } else if ("supplier_name".equals(columnName)) {
                    hasSupplierName = true;
                } else if ("supplier_code".equals(columnName)) {
                    hasSupplierCode = true;
                }
            }
            rs.close();

            // Add missing supplier columns
            if (!hasSupplierId) {
                System.out.println("Adding supplier_id column to products table...");
                stmt.execute("ALTER TABLE products ADD COLUMN supplier_id INTEGER");
            }

            if (!hasSupplierName) {
                System.out.println("Adding supplier_name column to products table...");
                stmt.execute("ALTER TABLE products ADD COLUMN supplier_name TEXT");
            }

            if (!hasSupplierCode) {
                System.out.println("Adding supplier_code column to products table...");
                stmt.execute("ALTER TABLE products ADD COLUMN supplier_code TEXT");
            }

        } catch (SQLException e) {
            System.err.println("Error adding supplier columns: " + e.getMessage());
            // Don't throw exception to avoid breaking existing installations
        }
    }

    private void addMembershipLevelColumnIfNotExists(Statement stmt) throws SQLException {
        try {
            // Check if membership_level column exists in customers table
            ResultSet rs = stmt.executeQuery("PRAGMA table_info(customers)");
            boolean hasMembershipLevel = false;

            while (rs.next()) {
                String columnName = rs.getString("name");
                if ("membership_level".equals(columnName)) {
                    hasMembershipLevel = true;
                    break;
                }
            }
            rs.close();

            // If membership_level column doesn't exist, add it
            if (!hasMembershipLevel) {
                System.out.println("Adding membership_level column to customers table...");
                stmt.execute("ALTER TABLE customers ADD COLUMN membership_level TEXT DEFAULT 'Standard'");
                System.out.println("Membership level column added successfully");
            }
        } catch (SQLException e) {
            System.err.println("Failed to add membership_level column: " + e.getMessage());
            // Don't throw the exception to prevent application startup failure
        }
    }

    private void migrateCategoriesData(Statement stmt) throws SQLException {
        try {
            // Check if categories table has any data
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM categories");
            rs.next();
            int categoryCount = rs.getInt(1);
            rs.close();

            if (categoryCount == 0) {
                System.out.println("Migrating existing categories to categories table...");

                // Get distinct categories from products table
                rs = stmt.executeQuery("SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != ''");
                int order = 1;
                while (rs.next()) {
                    String categoryName = rs.getString("category");
                    if (categoryName != null && !categoryName.trim().isEmpty()) {
                        // Insert category into categories table
                        String insertCategory = "INSERT INTO categories (name, description, display_order, active) VALUES (?, ?, ?, 1)";
                        try (PreparedStatement pstmt = stmt.getConnection().prepareStatement(insertCategory)) {
                            pstmt.setString(1, categoryName.trim());
                            pstmt.setString(2, "Migrated from existing products");
                            pstmt.setInt(3, order++);
                            pstmt.executeUpdate();
                        }
                    }
                }
                rs.close();

                System.out.println("Category migration completed successfully");
            }
        } catch (SQLException e) {
            System.err.println("Failed to migrate categories: " + e.getMessage());
            // Don't throw the exception to prevent application startup failure
        }
    }

    private void initializeDefaultSettings(Statement stmt) throws SQLException {
        // Check if settings table has any data
        ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM settings");
        rs.next();
        int count = rs.getInt(1);
        rs.close();

        if (count == 0) {
            System.out.println("Initializing default settings...");

            // Insert default settings
            String[] defaultSettings = {
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('tax_rate', '8.5', 'Default tax rate percentage', 'POS', 'DECIMAL', '8.5', 1)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('store_name', 'Clothing Store', 'Store name for receipts and reports', 'Store', 'STRING', 'Clothing Store', 1)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('store_address', '123 Main Street', 'Store address for receipts', 'Store', 'STRING', '123 Main Street', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('store_phone', '(*************', 'Store phone number', 'Store', 'STRING', '(*************', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('currency_symbol', '$', 'Currency symbol for display', 'Display', 'STRING', '$', 1)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('receipt_footer', 'Thank you for your business!', 'Footer text on receipts', 'POS', 'STRING', 'Thank you for your business!', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('low_stock_threshold', '10', 'Alert when stock falls below this level', 'Inventory', 'INTEGER', '10', 1)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('enable_loyalty_points', 'true', 'Enable customer loyalty points system', 'Customer', 'BOOLEAN', 'true', 0)",
                // WhatsApp Integration Settings with provided Twilio credentials
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_enabled', 'true', 'Enable WhatsApp receipt delivery', 'WhatsApp', 'BOOLEAN', 'true', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_provider', 'TWILIO', 'WhatsApp service provider', 'WhatsApp', 'STRING', 'TWILIO', 1)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_twilio_account_sid', '**********************************', 'Twilio Account SID', 'WhatsApp', 'STRING', '**********************************', 1)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_twilio_auth_token', '5e621d97587bbdfa3d7dcd6dfe3b6229', 'Twilio Auth Token', 'WhatsApp', 'STRING', '5e621d97587bbdfa3d7dcd6dfe3b6229', 1)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_twilio_from_number', 'whatsapp:+***********', 'Twilio WhatsApp number', 'WhatsApp', 'STRING', 'whatsapp:+***********', 1)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_message_template', '🧾 *Receipt from {STORE_NAME}*\\n\\nTransaction: {TRANSACTION_NUMBER}\\nDate: {DATE}\\nTotal: {TOTAL}\\n\\nThank you for shopping with us!\\n\\n_This is an automated message._', 'WhatsApp message template', 'WhatsApp', 'STRING', '🧾 *Receipt from {STORE_NAME}*\\n\\nTransaction: {TRANSACTION_NUMBER}\\nDate: {DATE}\\nTotal: {TOTAL}\\n\\nThank you for shopping with us!\\n\\n_This is an automated message._', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_send_to_walkin', 'true', 'Send to walk-in customers', 'WhatsApp', 'BOOLEAN', 'true', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_send_to_registered', 'true', 'Send to registered customers', 'WhatsApp', 'BOOLEAN', 'true', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_require_confirmation', 'true', 'Require confirmation before sending', 'WhatsApp', 'BOOLEAN', 'true', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_max_retry_attempts', '3', 'Maximum retry attempts', 'WhatsApp', 'INTEGER', '3', 0)",
                "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required) VALUES "
                + "('whatsapp_retry_delay_seconds', '30', 'Retry delay in seconds', 'WhatsApp', 'INTEGER', '30', 0)"
            };

            for (String setting : defaultSettings) {
                stmt.execute(setting);
            }

            System.out.println("Default settings initialized");
        }
    }

    private void insertSampleData(Connection conn) throws SQLException {
        // Check if sample data already exists
        String checkQuery = "SELECT COUNT(*) FROM products";
        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(checkQuery)) {
            if (rs.next() && rs.getInt(1) > 0) {
                return; // Sample data already exists
            }
        }

        // Insert sample products
        String insertProduct = "INSERT INTO products (sku, name, description, category, brand, color, size, price, cost_price, stock_quantity, min_stock_level) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement pstmt = conn.prepareStatement(insertProduct)) {
            // Sample clothing items

        }

        // Insert sample customers (without membership_level)
    }

    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            System.err.println("Error closing database connection: " + e.getMessage());
        }
    }

    public void backupDatabase(String backupPath) throws SQLException, IOException {
        if (!Files.exists(Paths.get(DATABASE_NAME))) {
            throw new IOException("Database file does not exist");
        }
        Files.copy(Paths.get(DATABASE_NAME), Paths.get(backupPath));
    }

    public void restoreDatabase(String backupPath) throws SQLException, IOException {
        closeConnection();
        if (!Files.exists(Paths.get(backupPath))) {
            throw new IOException("Backup file does not exist");
        }
        Files.copy(Paths.get(backupPath), Paths.get(DATABASE_NAME));
        // Reinitialize connection
        connection = null;
        getConnection();
    }

    /**
     * Reset all data in the database while preserving schema This method
     * removes all records from all tables and resets auto-increment counters
     */
    public void resetAllData() throws SQLException {
        try (Connection conn = getConnection()) {
            conn.setAutoCommit(false);

            try (Statement stmt = conn.createStatement()) {
                // Delete data in correct order to respect foreign key constraints
                stmt.executeUpdate("DELETE FROM transaction_items");
                stmt.executeUpdate("DELETE FROM transactions");
                stmt.executeUpdate("DELETE FROM customers");
                stmt.executeUpdate("DELETE FROM products");
                stmt.executeUpdate("DELETE FROM categories");
                stmt.executeUpdate("DELETE FROM settings");

                // Reset auto-increment counters
                stmt.executeUpdate("UPDATE sqlite_sequence SET seq = 0 WHERE name = 'products'");
                stmt.executeUpdate("UPDATE sqlite_sequence SET seq = 0 WHERE name = 'customers'");
                stmt.executeUpdate("UPDATE sqlite_sequence SET seq = 0 WHERE name = 'transactions'");
                stmt.executeUpdate("UPDATE sqlite_sequence SET seq = 0 WHERE name = 'transaction_items'");
                stmt.executeUpdate("UPDATE sqlite_sequence SET seq = 0 WHERE name = 'categories'");
                stmt.executeUpdate("UPDATE sqlite_sequence SET seq = 0 WHERE name = 'settings'");

                conn.commit();
                System.out.println("Database reset completed - all data removed");

            } catch (SQLException e) {
                conn.rollback();
                throw e;
            } finally {
                conn.setAutoCommit(true);
            }
        }
    }

    /**
     * Reset data and populate with sample data
     */
    public void resetWithSampleData() throws SQLException {
        resetAllData();
        try (Connection conn = getConnection()) {
            insertSampleData(conn);
        }
        System.out.println("Database reset with sample data completed");
    }

    /**
     * Public method to insert sample data
     */
    public void insertSampleData() throws SQLException {
        try (Connection conn = getConnection()) {
            insertSampleData(conn);
        }
    }
}
