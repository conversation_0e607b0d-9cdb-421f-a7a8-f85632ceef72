package com.clothingstore.model;

/**
 * Enumeration for different payment methods supported by the POS system
 */
public enum PaymentMethod {
    CASH("Cash", "Physical cash payment"),
    CREDIT_CARD("Credit Card", "Credit card payment"),
    DEBIT_CARD("Debit Card", "Debit card payment"),
    MOBILE_PAYMENT("Mobile Payment", "Mobile payment (Apple Pay, Google Pay, etc.)"),
    STORE_CREDIT("Store Credit", "Store credit or gift card"),
    CHECK("Check", "Personal or business check"),
    BANK_TRANSFER("Bank Transfer", "Direct bank transfer"),
    LAYAWAY("Layaway", "Layaway payment plan");

    private final String displayName;
    private final String description;

    PaymentMethod(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Check if this payment method requires additional processing
     */
    public boolean requiresAdditionalProcessing() {
        return this == CREDIT_CARD || this == DEBIT_CARD || this == MOBILE_PAYMENT || this == BANK_TRANSFER;
    }

    /**
     * Check if this payment method supports partial payments
     */
    public boolean supportsPartialPayments() {
        return this == LAYAWAY || this == STORE_CREDIT;
    }

    /**
     * Check if this payment method requires change calculation
     */
    public boolean requiresChangeCalculation() {
        return this == CASH;
    }
}
