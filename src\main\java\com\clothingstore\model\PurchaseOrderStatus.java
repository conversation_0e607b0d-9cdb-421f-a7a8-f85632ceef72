package com.clothingstore.model;

/**
 * Enumeration for purchase order status
 */
public enum PurchaseOrderStatus {
    DRAFT("Draft", "Order is being created"),
    PENDING_APPROVAL("Pending Approval", "Order is waiting for approval"),
    APPROVED("Approved", "Order has been approved"),
    SENT("Sent", "Order has been sent to supplier"),
    PARTIALLY_RECEIVED("Partially Received", "Order has been partially received"),
    RECEIVED("Received", "Order has been fully received"),
    CANCELLED("Cancelled", "Order has been cancelled"),
    REJECTED("Rejected", "Order has been rejected");

    private final String displayName;
    private final String description;

    PurchaseOrderStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Check if status indicates order is active
     */
    public boolean isActive() {
        return this != CANCELLED && this != REJECTED && this != RECEIVED;
    }

    /**
     * Check if status indicates order is pending
     */
    public boolean isPending() {
        return this == DRAFT || this == PENDING_APPROVAL || this == APPROVED || this == SENT;
    }

    /**
     * Check if status indicates order is completed
     */
    public boolean isCompleted() {
        return this == RECEIVED || this == CANCELLED || this == REJECTED;
    }

    /**
     * Check if status allows modifications
     */
    public boolean allowsModification() {
        return this == DRAFT || this == PENDING_APPROVAL;
    }

    /**
     * Get next possible statuses
     */
    public PurchaseOrderStatus[] getNextPossibleStatuses() {
        switch (this) {
            case DRAFT:
                return new PurchaseOrderStatus[]{PENDING_APPROVAL, CANCELLED};
            case PENDING_APPROVAL:
                return new PurchaseOrderStatus[]{APPROVED, REJECTED, CANCELLED};
            case APPROVED:
                return new PurchaseOrderStatus[]{SENT, CANCELLED};
            case SENT:
                return new PurchaseOrderStatus[]{PARTIALLY_RECEIVED, RECEIVED, CANCELLED};
            case PARTIALLY_RECEIVED:
                return new PurchaseOrderStatus[]{RECEIVED, CANCELLED};
            default:
                return new PurchaseOrderStatus[0];
        }
    }
}
