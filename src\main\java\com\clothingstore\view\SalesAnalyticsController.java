package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;
import java.util.stream.Collectors;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Transaction;
import com.clothingstore.dao.SalesAnalyticsDAO;
import com.clothingstore.util.AlertUtil;

import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.chart.AreaChart;
import javafx.scene.chart.BarChart;
import javafx.scene.chart.LineChart;
import javafx.scene.chart.PieChart;
import javafx.scene.chart.XYChart;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;

/**
 * Controller for Sales Analytics Dashboard
 */
public class SalesAnalyticsController implements Initializable {

    // FXML Components - Date Range Selection
    @FXML
    private DatePicker startDatePicker;
    @FXML
    private DatePicker endDatePicker;
    @FXML
    private ComboBox<String> periodComboBox;
    @FXML
    private Button refreshButton;
    @FXML
    private Button exportButton;

    // FXML Components - KPI Cards
    @FXML
    private Label totalSalesLabel;
    @FXML
    private Label totalTransactionsLabel;
    @FXML
    private Label averageOrderValueLabel;
    @FXML
    private Label totalCustomersLabel;
    @FXML
    private Label salesGrowthLabel;
    @FXML
    private Label transactionGrowthLabel;

    // FXML Components - Charts
    @FXML
    private LineChart<String, Number> salesTrendChart;
    @FXML
    private BarChart<String, Number> topProductsChart;
    @FXML
    private PieChart salesByCategoryChart;
    @FXML
    private AreaChart<String, Number> dailySalesChart;

    // FXML Components - Tables
    @FXML
    private TableView<ProductSalesData> topProductsTable;
    @FXML
    private TableColumn<ProductSalesData, String> productNameColumn;
    @FXML
    private TableColumn<ProductSalesData, String> quantitySoldColumn;
    @FXML
    private TableColumn<ProductSalesData, String> revenueColumn;

    @FXML
    private TableView<CustomerSalesData> topCustomersTable;
    @FXML
    private TableColumn<CustomerSalesData, String> customerNameColumn;
    @FXML
    private TableColumn<CustomerSalesData, String> totalSpentColumn;
    @FXML
    private TableColumn<CustomerSalesData, String> transactionCountColumn;

    // FXML Components - Filters
    @FXML
    private ComboBox<String> categoryFilter;
    @FXML
    private ComboBox<String> customerGroupFilter;
    @FXML
    private CheckBox includeRefundsCheckBox;

    // Data and Services
    private TransactionDAO transactionDAO;
    private ProductDAO productDAO;
    private CustomerDAO customerDAO;
    private SalesAnalyticsDAO salesAnalyticsDAO;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateFormatter;
    private Timer refreshTimer;

    // Analytics Data
    private List<Transaction> currentTransactions;
    private Map<String, BigDecimal> salesByCategory;
    private Map<String, Integer> salesByDay;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeServices();
        initializeFormatters();
        initializeComponents();
        initializeCharts();
        initializeTables();
        setupAutoRefresh();
        loadInitialData();
    }

    private void initializeServices() {
        try {
            transactionDAO = TransactionDAO.getInstance();
            productDAO = ProductDAO.getInstance();
            customerDAO = CustomerDAO.getInstance();
            salesAnalyticsDAO = SalesAnalyticsDAO.getInstance();
            currentTransactions = new ArrayList<>();
            salesByCategory = new HashMap<>();
            salesByDay = new HashMap<>();

            // Test database connectivity
            transactionDAO.findAll();
        } catch (Exception e) {
            AlertUtil.showWarning("Database Connection",
                "Unable to connect to database. Some features may not work properly.\n\n" +
                "Error: " + e.getMessage());

            // Initialize with empty data
            currentTransactions = new ArrayList<>();
            salesByCategory = new HashMap<>();
            salesByDay = new HashMap<>();
        }
    }

    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateFormatter = DateTimeFormatter.ofPattern("MMM dd");
    }

    private void initializeComponents() {
        // Period ComboBox
        periodComboBox.setItems(FXCollections.observableArrayList(
                "Today", "Yesterday", "This Week", "Last Week",
                "This Month", "Last Month", "This Quarter", "This Year", "Custom Range"));
        periodComboBox.setValue("This Month");

        // Date pickers - default to current month
        LocalDate now = LocalDate.now();
        startDatePicker.setValue(now.withDayOfMonth(1));
        endDatePicker.setValue(now);

        // Category filter
        loadCategoryFilter();

        // Customer group filter
        customerGroupFilter.setItems(FXCollections.observableArrayList(
                "All Groups", "VIP", "Regular", "Wholesale", "Employee"));
        customerGroupFilter.setValue("All Groups");

        // Event handlers
        periodComboBox.setOnAction(e -> handlePeriodChange());
        startDatePicker.setOnAction(e -> refreshData());
        endDatePicker.setOnAction(e -> refreshData());
        categoryFilter.setOnAction(e -> refreshData());
        customerGroupFilter.setOnAction(e -> refreshData());
        includeRefundsCheckBox.setOnAction(e -> refreshData());
    }

    private void initializeCharts() {
        // Sales Trend Chart
        salesTrendChart.setTitle("Sales Trend");
        salesTrendChart.setCreateSymbols(false);

        // Top Products Chart
        topProductsChart.setTitle("Top Products by Revenue");
        topProductsChart.setLegendVisible(false);

        // Sales by Category Chart
        salesByCategoryChart.setTitle("Sales by Category");

        // Daily Sales Chart
        dailySalesChart.setTitle("Daily Sales Overview");
        dailySalesChart.setCreateSymbols(false);
    }

    private void initializeTables() {
        // Top Products Table
        productNameColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getProductName()));
        quantitySoldColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(String.valueOf(cellData.getValue().getQuantitySold())));
        revenueColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getRevenue())));

        // Top Customers Table
        customerNameColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getCustomerName()));
        totalSpentColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getTotalSpent())));
        transactionCountColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(String.valueOf(cellData.getValue().getTransactionCount())));
    }

    private void loadCategoryFilter() {
        try {
            List<String> categories = productDAO.getAllCategories();
            ObservableList<String> categoryItems = FXCollections.observableArrayList("All Categories");
            categoryItems.addAll(categories);
            categoryFilter.setItems(categoryItems);
            categoryFilter.setValue("All Categories");
        } catch (SQLException e) {
            AlertUtil.showError("Load Error", "Failed to load categories: " + e.getMessage());
        }
    }

    private void setupAutoRefresh() {
        // Auto-refresh every 5 minutes
        refreshTimer = new Timer(true);
        refreshTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                Platform.runLater(() -> refreshData());
            }
        }, 300000, 300000); // 5 minutes
    }

    private void loadInitialData() {
        refreshData();
    }

    @FXML
    private void handlePeriodChange() {
        String selectedPeriod = periodComboBox.getValue();
        LocalDate now = LocalDate.now();

        switch (selectedPeriod) {
            case "Today":
                startDatePicker.setValue(now);
                endDatePicker.setValue(now);
                break;
            case "Yesterday":
                startDatePicker.setValue(now.minusDays(1));
                endDatePicker.setValue(now.minusDays(1));
                break;
            case "This Week":
                startDatePicker.setValue(now.minusDays(now.getDayOfWeek().getValue() - 1));
                endDatePicker.setValue(now);
                break;
            case "Last Week":
                LocalDate lastWeekStart = now.minusDays(now.getDayOfWeek().getValue() + 6);
                startDatePicker.setValue(lastWeekStart);
                endDatePicker.setValue(lastWeekStart.plusDays(6));
                break;
            case "This Month":
                startDatePicker.setValue(now.withDayOfMonth(1));
                endDatePicker.setValue(now);
                break;
            case "Last Month":
                LocalDate lastMonth = now.minusMonths(1);
                startDatePicker.setValue(lastMonth.withDayOfMonth(1));
                endDatePicker.setValue(lastMonth.withDayOfMonth(lastMonth.lengthOfMonth()));
                break;
            case "This Quarter":
                int currentQuarter = (now.getMonthValue() - 1) / 3;
                LocalDate quarterStart = now.withMonth(currentQuarter * 3 + 1).withDayOfMonth(1);
                startDatePicker.setValue(quarterStart);
                endDatePicker.setValue(now);
                break;
            case "This Year":
                startDatePicker.setValue(now.withDayOfYear(1));
                endDatePicker.setValue(now);
                break;
        }

        if (!"Custom Range".equals(selectedPeriod)) {
            refreshData();
        }
    }

    @FXML
    private void refreshData() {
        try {
            loadTransactionData();
            updateKPIs();
            updateCharts();
            updateTables();
        } catch (Exception e) {
            AlertUtil.showError("Refresh Error", "Failed to refresh analytics data: " + e.getMessage());
        }
    }

    private void loadTransactionData() throws SQLException {
        LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
        LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

        currentTransactions = transactionDAO.findByDateRange(startDateTime, endDateTime);

        // Apply filters
        String selectedCategory = categoryFilter.getValue();
        String selectedGroup = customerGroupFilter.getValue();
        boolean includeRefunds = includeRefundsCheckBox.isSelected();

        currentTransactions = currentTransactions.stream()
                .filter(t -> includeRefunds || !"REFUND".equals(t.getStatus()))
                .filter(t -> "All Categories".equals(selectedCategory)
                || t.getItems().stream().anyMatch(item -> selectedCategory.equals(item.getProduct().getCategory())))
                .filter(t -> "All Groups".equals(selectedGroup)
                || (t.getCustomer() != null && selectedGroup.equals(t.getCustomer().getMembershipLevel())))
                .collect(Collectors.toList());
    }

    private void updateKPIs() {
        try {
            LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
            LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

            // Get sales summary from database
            SalesAnalyticsDAO.SalesSummary summary = salesAnalyticsDAO.getSalesSummary(startDateTime, endDateTime);

            long uniqueCustomers = currentTransactions.stream()
                    .filter(t -> t.getCustomer() != null)
                    .map(t -> t.getCustomer().getId())
                    .distinct()
                    .count();

            // Calculate growth rates (comparing to previous period)
            BigDecimal salesGrowth = calculateSalesGrowth(startDateTime, endDateTime);
            BigDecimal transactionGrowth = calculateTransactionGrowth(startDateTime, endDateTime);

            // Update UI
            totalSalesLabel.setText(currencyFormat.format(summary.getTotalRevenue()));
            totalTransactionsLabel.setText(String.valueOf(summary.getTransactionCount()));
            averageOrderValueLabel.setText(currencyFormat.format(summary.getAvgTransactionValue()));
            totalCustomersLabel.setText(String.valueOf(uniqueCustomers));
            salesGrowthLabel.setText(String.format("%.1f%%", salesGrowth));
            transactionGrowthLabel.setText(String.format("%.1f%%", transactionGrowth));

            // Color coding for growth
            salesGrowthLabel.setStyle(salesGrowth.compareTo(BigDecimal.ZERO) >= 0
                    ? "-fx-text-fill: green;" : "-fx-text-fill: red;");
            transactionGrowthLabel.setStyle(transactionGrowth.compareTo(BigDecimal.ZERO) >= 0
                    ? "-fx-text-fill: green;" : "-fx-text-fill: red;");
        } catch (Exception e) {
            AlertUtil.showError("KPI Error", "Failed to update KPIs: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private BigDecimal calculateSalesGrowth(LocalDateTime startDate, LocalDateTime endDate) {
        try {
            // Calculate previous period
            long daysBetween = java.time.Duration.between(startDate, endDate).toDays();
            LocalDateTime prevStartDate = startDate.minusDays(daysBetween + 1);
            LocalDateTime prevEndDate = startDate.minusDays(1);

            SalesAnalyticsDAO.SalesSummary currentSummary = salesAnalyticsDAO.getSalesSummary(startDate, endDate);
            SalesAnalyticsDAO.SalesSummary previousSummary = salesAnalyticsDAO.getSalesSummary(prevStartDate, prevEndDate);

            if (previousSummary.getTotalRevenue().compareTo(BigDecimal.ZERO) == 0) {
                return BigDecimal.ZERO;
            }

            BigDecimal growth = currentSummary.getTotalRevenue()
                .subtract(previousSummary.getTotalRevenue())
                .divide(previousSummary.getTotalRevenue(), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));

            return growth;
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    private BigDecimal calculateTransactionGrowth(LocalDateTime startDate, LocalDateTime endDate) {
        try {
            // Calculate previous period
            long daysBetween = java.time.Duration.between(startDate, endDate).toDays();
            LocalDateTime prevStartDate = startDate.minusDays(daysBetween + 1);
            LocalDateTime prevEndDate = startDate.minusDays(1);

            SalesAnalyticsDAO.SalesSummary currentSummary = salesAnalyticsDAO.getSalesSummary(startDate, endDate);
            SalesAnalyticsDAO.SalesSummary previousSummary = salesAnalyticsDAO.getSalesSummary(prevStartDate, prevEndDate);

            if (previousSummary.getTransactionCount() == 0) {
                return BigDecimal.ZERO;
            }

            BigDecimal growth = BigDecimal.valueOf(currentSummary.getTransactionCount() - previousSummary.getTransactionCount())
                .divide(BigDecimal.valueOf(previousSummary.getTransactionCount()), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));

            return growth;
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    private void updateCharts() {
        updateSalesTrendChart();
        updateTopProductsChart();
        updateSalesByCategoryChart();
        updateDailySalesChart();
    }

    private void updateSalesTrendChart() {
        try {
            salesTrendChart.getData().clear();

            LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
            LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

            List<SalesAnalyticsDAO.DailySalesData> dailySalesData =
                salesAnalyticsDAO.getDailySalesData(startDateTime, endDateTime);

            XYChart.Series<String, Number> series = new XYChart.Series<>();
            series.setName("Sales");

            dailySalesData.forEach(data -> {
                series.getData().add(new XYChart.Data<>(
                        data.getDate().format(dateFormatter),
                        data.getTotalRevenue()
                ));
            });

            salesTrendChart.getData().add(series);
        } catch (Exception e) {
            AlertUtil.showError("Chart Error", "Failed to update sales trend chart: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateTopProductsChart() {
        try {
            topProductsChart.getData().clear();

            LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
            LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

            List<SalesAnalyticsDAO.TopProductData> topProductsData =
                salesAnalyticsDAO.getTopSellingProducts(startDateTime, endDateTime, 10);

            XYChart.Series<String, Number> series = new XYChart.Series<>();

            topProductsData.forEach(data -> {
                String productName = data.getProductName().length() > 15
                    ? data.getProductName().substring(0, 15) + "..."
                    : data.getProductName();

                series.getData().add(new XYChart.Data<>(productName, data.getTotalRevenue()));
            });

            topProductsChart.getData().add(series);
        } catch (Exception e) {
            AlertUtil.showError("Chart Error", "Failed to update top products chart: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateSalesByCategoryChart() {
        try {
            salesByCategoryChart.getData().clear();

            LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
            LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

            List<SalesAnalyticsDAO.CategorySalesData> categoryData =
                salesAnalyticsDAO.getSalesByCategory(startDateTime, endDateTime);

            categoryData.forEach(data -> {
                PieChart.Data slice = new PieChart.Data(data.getCategory(), data.getTotalRevenue().doubleValue());
                salesByCategoryChart.getData().add(slice);
            });
        } catch (Exception e) {
            AlertUtil.showError("Chart Error", "Failed to update sales by category chart: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateDailySalesChart() {
        dailySalesChart.getData().clear();

        XYChart.Series<String, Number> series = new XYChart.Series<>();
        series.setName("Daily Sales");

        // Group by day and calculate cumulative sales
        Map<LocalDate, BigDecimal> dailySales = currentTransactions.stream()
                .collect(Collectors.groupingBy(
                        t -> t.getTransactionDate().toLocalDate(),
                        Collectors.reducing(BigDecimal.ZERO, Transaction::getTotal, BigDecimal::add)
                ));

        BigDecimal cumulative = BigDecimal.ZERO;
        for (Map.Entry<LocalDate, BigDecimal> entry : dailySales.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toList())) {
            cumulative = cumulative.add(entry.getValue());
            series.getData().add(new XYChart.Data<>(
                    entry.getKey().format(dateFormatter),
                    cumulative
            ));
        }

        dailySalesChart.getData().add(series);
    }

    private void updateTables() {
        updateTopProductsTable();
        updateTopCustomersTable();
    }

    private void updateTopProductsTable() {
        try {
            LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
            LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

            List<SalesAnalyticsDAO.TopProductData> topProductsData =
                salesAnalyticsDAO.getTopSellingProducts(startDateTime, endDateTime, 10);

            List<ProductSalesData> topProducts = topProductsData.stream()
                .map(data -> new ProductSalesData(data.getProductName(),
                    data.getTotalQuantity(), data.getTotalRevenue()))
                .collect(Collectors.toList());

            topProductsTable.setItems(FXCollections.observableArrayList(topProducts));
        } catch (Exception e) {
            AlertUtil.showError("Table Error", "Failed to update top products table: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateTopCustomersTable() {
        Map<String, CustomerSalesData> customerData = new HashMap<>();

        currentTransactions.stream()
                .filter(t -> t.getCustomer() != null)
                .forEach(transaction -> {
                    String customerName = transaction.getCustomer().getFullName();
                    CustomerSalesData data = customerData.computeIfAbsent(customerName,
                            k -> new CustomerSalesData(customerName, BigDecimal.ZERO, 0));
                    data.addTransaction(transaction.getTotal());
                });

        List<CustomerSalesData> topCustomers = customerData.values().stream()
                .sorted((a, b) -> b.getTotalSpent().compareTo(a.getTotalSpent()))
                .limit(10)
                .collect(Collectors.toList());

        topCustomersTable.setItems(FXCollections.observableArrayList(topCustomers));
    }

    @FXML
    private void handleExport() {
        // TODO: Implement export functionality
        AlertUtil.showInfo("Export", "Export functionality will be implemented in a future update.");
    }

    public void cleanup() {
        if (refreshTimer != null) {
            refreshTimer.cancel();
        }
    }

    // Data classes for tables
    public static class ProductSalesData {

        private String productName;
        private int quantitySold;
        private BigDecimal revenue;

        public ProductSalesData(String productName, int quantitySold, BigDecimal revenue) {
            this.productName = productName;
            this.quantitySold = quantitySold;
            this.revenue = revenue;
        }

        public void addSale(int quantity, BigDecimal amount) {
            this.quantitySold += quantity;
            this.revenue = this.revenue.add(amount);
        }

        // Getters
        public String getProductName() {
            return productName;
        }

        public int getQuantitySold() {
            return quantitySold;
        }

        public BigDecimal getRevenue() {
            return revenue;
        }
    }

    public static class CustomerSalesData {

        private String customerName;
        private BigDecimal totalSpent;
        private int transactionCount;

        public CustomerSalesData(String customerName, BigDecimal totalSpent, int transactionCount) {
            this.customerName = customerName;
            this.totalSpent = totalSpent;
            this.transactionCount = transactionCount;
        }

        public void addTransaction(BigDecimal amount) {
            this.totalSpent = this.totalSpent.add(amount);
            this.transactionCount++;
        }

        // Getters
        public String getCustomerName() {
            return customerName;
        }

        public BigDecimal getTotalSpent() {
            return totalSpent;
        }

        public int getTransactionCount() {
            return transactionCount;
        }
    }
}
