package com.clothingstore.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Result class for outstanding balance update operations during refund processing
 * Provides detailed information about balance changes and operation status
 */
public class OutstandingBalanceUpdateResult {
    
    private boolean success;
    private String message;
    private List<String> warnings;
    
    // Transaction and refund details
    private Long transactionId;
    private BigDecimal refundAmount;
    private Long paymentHistoryId;
    
    // Balance information
    private BigDecimal previousRemainingBalance;
    private BigDecimal newRemainingBalance;
    private boolean hadOutstandingBalance;
    private boolean hasOutstandingBalance;
    
    // Additional tracking
    private String refundType; // "FULL" or "PARTIAL"
    private String transactionStatusBefore;
    private String transactionStatusAfter;
    
    public OutstandingBalanceUpdateResult() {
        this.warnings = new ArrayList<>();
    }
    
    public OutstandingBalanceUpdateResult(boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }
    
    // Basic result properties
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public void setWarnings(List<String> warnings) {
        this.warnings = warnings != null ? warnings : new ArrayList<>();
    }
    
    public void addWarning(String warning) {
        if (warning != null && !warning.trim().isEmpty()) {
            this.warnings.add(warning);
        }
    }
    
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
    
    // Transaction and refund details
    public Long getTransactionId() {
        return transactionId;
    }
    
    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }
    
    public BigDecimal getRefundAmount() {
        return refundAmount;
    }
    
    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }
    
    public Long getPaymentHistoryId() {
        return paymentHistoryId;
    }
    
    public void setPaymentHistoryId(Long paymentHistoryId) {
        this.paymentHistoryId = paymentHistoryId;
    }
    
    // Balance information
    public BigDecimal getPreviousRemainingBalance() {
        return previousRemainingBalance;
    }
    
    public void setPreviousRemainingBalance(BigDecimal previousRemainingBalance) {
        this.previousRemainingBalance = previousRemainingBalance;
    }
    
    public BigDecimal getNewRemainingBalance() {
        return newRemainingBalance;
    }
    
    public void setNewRemainingBalance(BigDecimal newRemainingBalance) {
        this.newRemainingBalance = newRemainingBalance;
    }
    
    public boolean hadOutstandingBalance() {
        return hadOutstandingBalance;
    }
    
    public void setHadOutstandingBalance(boolean hadOutstandingBalance) {
        this.hadOutstandingBalance = hadOutstandingBalance;
    }
    
    public boolean hasOutstandingBalance() {
        return hasOutstandingBalance;
    }
    
    public void setHasOutstandingBalance(boolean hasOutstandingBalance) {
        this.hasOutstandingBalance = hasOutstandingBalance;
    }
    
    // Additional tracking
    public String getRefundType() {
        return refundType;
    }
    
    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }
    
    public String getTransactionStatusBefore() {
        return transactionStatusBefore;
    }
    
    public void setTransactionStatusBefore(String transactionStatusBefore) {
        this.transactionStatusBefore = transactionStatusBefore;
    }
    
    public String getTransactionStatusAfter() {
        return transactionStatusAfter;
    }
    
    public void setTransactionStatusAfter(String transactionStatusAfter) {
        this.transactionStatusAfter = transactionStatusAfter;
    }
    
    // Utility methods
    public BigDecimal getBalanceChange() {
        if (previousRemainingBalance != null && newRemainingBalance != null) {
            return newRemainingBalance.subtract(previousRemainingBalance);
        }
        return BigDecimal.ZERO;
    }
    
    public boolean balanceStatusChanged() {
        return hadOutstandingBalance != hasOutstandingBalance;
    }
    
    public boolean removedFromOutstandingBalances() {
        return hadOutstandingBalance && !hasOutstandingBalance;
    }
    
    public boolean addedToOutstandingBalances() {
        return !hadOutstandingBalance && hasOutstandingBalance;
    }
    
    /**
     * Get a summary of the balance update operation
     */
    public String getBalanceUpdateSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (success) {
            summary.append("Balance update successful. ");
            
            if (refundAmount != null) {
                summary.append("Refund amount: ").append(refundAmount).append(". ");
            }
            
            if (previousRemainingBalance != null && newRemainingBalance != null) {
                summary.append("Balance changed from ").append(previousRemainingBalance)
                       .append(" to ").append(newRemainingBalance).append(". ");
            }
            
            if (removedFromOutstandingBalances()) {
                summary.append("Transaction removed from Outstanding Balances.");
            } else if (addedToOutstandingBalances()) {
                summary.append("Transaction added to Outstanding Balances.");
            } else if (hasOutstandingBalance) {
                summary.append("Transaction remains in Outstanding Balances.");
            }
        } else {
            summary.append("Balance update failed: ").append(message);
        }
        
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return "OutstandingBalanceUpdateResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", transactionId=" + transactionId +
                ", refundAmount=" + refundAmount +
                ", previousBalance=" + previousRemainingBalance +
                ", newBalance=" + newRemainingBalance +
                ", hadOutstanding=" + hadOutstandingBalance +
                ", hasOutstanding=" + hasOutstandingBalance +
                ", warnings=" + warnings.size() +
                '}';
    }
}
