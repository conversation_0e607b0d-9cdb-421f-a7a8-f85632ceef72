package com.clothingstore.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Supplier;
import com.clothingstore.model.SupplierStatus;
import com.clothingstore.model.PaymentTerms;

/**
 * Data Access Object for Supplier operations
 */
public class SupplierDAO {

    private static SupplierDAO instance;

    private SupplierDAO() {
    }

    public static synchronized SupplierDAO getInstance() {
        if (instance == null) {
            instance = new SupplierDAO();
        }
        return instance;
    }

    public List<Supplier> findAll() throws SQLException {
        String sql = "SELECT * FROM suppliers WHERE status = 'ACTIVE' ORDER BY company_name";
        List<Supplier> suppliers = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); 
             PreparedStatement pstmt = conn.prepareStatement(sql); 
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                suppliers.add(mapResultSetToSupplier(rs));
            }
        }

        return suppliers;
    }

    public Optional<Supplier> findById(Long id) throws SQLException {
        String sql = "SELECT * FROM suppliers WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); 
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToSupplier(rs));
                }
            }
        }

        return Optional.empty();
    }

    public Optional<Supplier> findByCode(String supplierCode) throws SQLException {
        String sql = "SELECT * FROM suppliers WHERE supplier_code = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); 
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, supplierCode);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToSupplier(rs));
                }
            }
        }

        return Optional.empty();
    }

    public Supplier save(Supplier supplier) throws SQLException {
        if (supplier.getId() == null) {
            return insert(supplier);
        } else {
            return update(supplier);
        }
    }

    private Supplier insert(Supplier supplier) throws SQLException {
        String sql = "INSERT INTO suppliers (supplier_code, company_name, contact_person, email, phone, " +
                    "mobile, website, address, city, state, zip_code, country, tax_id, status, " +
                    "payment_terms, lead_time_days, minimum_order_amount, currency, notes, " +
                    "credit_limit, current_balance, created_at, updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); 
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            setSupplierParameters(pstmt, supplier);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating supplier failed, no rows affected.");
            }

            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    supplier.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating supplier failed, no ID obtained.");
                }
            }
        }

        return supplier;
    }

    private Supplier update(Supplier supplier) throws SQLException {
        String sql = "UPDATE suppliers SET supplier_code = ?, company_name = ?, contact_person = ?, " +
                    "email = ?, phone = ?, mobile = ?, website = ?, address = ?, city = ?, state = ?, " +
                    "zip_code = ?, country = ?, tax_id = ?, status = ?, payment_terms = ?, " +
                    "lead_time_days = ?, minimum_order_amount = ?, currency = ?, notes = ?, " +
                    "credit_limit = ?, current_balance = ?, updated_at = ? WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); 
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            setSupplierParameters(pstmt, supplier);
            pstmt.setLong(23, supplier.getId());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating supplier failed, no rows affected.");
            }
        }

        return supplier;
    }

    public void delete(Long id) throws SQLException {
        String sql = "UPDATE suppliers SET status = 'INACTIVE', updated_at = ? WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); 
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(java.time.LocalDateTime.now()));
            pstmt.setLong(2, id);

            pstmt.executeUpdate();
        }
    }

    private void setSupplierParameters(PreparedStatement pstmt, Supplier supplier) throws SQLException {
        pstmt.setString(1, supplier.getSupplierCode());
        pstmt.setString(2, supplier.getCompanyName());
        pstmt.setString(3, supplier.getContactPerson());
        pstmt.setString(4, supplier.getEmail());
        pstmt.setString(5, supplier.getPhone());
        pstmt.setString(6, supplier.getMobile());
        pstmt.setString(7, supplier.getWebsite());
        pstmt.setString(8, supplier.getAddress());
        pstmt.setString(9, supplier.getCity());
        pstmt.setString(10, supplier.getState());
        pstmt.setString(11, supplier.getZipCode());
        pstmt.setString(12, supplier.getCountry());
        pstmt.setString(13, supplier.getTaxId());
        pstmt.setString(14, supplier.getStatus().toString());
        pstmt.setString(15, supplier.getPaymentTerms().toString());
        pstmt.setInt(16, supplier.getLeadTimeDays());
        pstmt.setBigDecimal(17, supplier.getMinimumOrderAmount());
        pstmt.setString(18, supplier.getCurrency());
        pstmt.setString(19, supplier.getNotes());
        pstmt.setBigDecimal(20, supplier.getCreditLimit());
        pstmt.setBigDecimal(21, supplier.getCurrentBalance());
        pstmt.setTimestamp(22, Timestamp.valueOf(supplier.getCreatedAt()));
        pstmt.setTimestamp(23, Timestamp.valueOf(supplier.getUpdatedAt()));
    }

    private Supplier mapResultSetToSupplier(ResultSet rs) throws SQLException {
        Supplier supplier = new Supplier();
        supplier.setId(rs.getLong("id"));
        supplier.setSupplierCode(rs.getString("supplier_code"));
        supplier.setCompanyName(rs.getString("company_name"));
        supplier.setContactPerson(rs.getString("contact_person"));
        supplier.setEmail(rs.getString("email"));
        supplier.setPhone(rs.getString("phone"));
        supplier.setMobile(rs.getString("mobile"));
        supplier.setWebsite(rs.getString("website"));
        supplier.setAddress(rs.getString("address"));
        supplier.setCity(rs.getString("city"));
        supplier.setState(rs.getString("state"));
        supplier.setZipCode(rs.getString("zip_code"));
        supplier.setCountry(rs.getString("country"));
        supplier.setTaxId(rs.getString("tax_id"));
        
        String statusStr = rs.getString("status");
        if (statusStr != null) {
            supplier.setStatus(SupplierStatus.valueOf(statusStr));
        }
        
        String paymentTermsStr = rs.getString("payment_terms");
        if (paymentTermsStr != null) {
            supplier.setPaymentTerms(PaymentTerms.valueOf(paymentTermsStr));
        }
        
        supplier.setLeadTimeDays(rs.getInt("lead_time_days"));
        supplier.setMinimumOrderAmount(rs.getBigDecimal("minimum_order_amount"));
        supplier.setCurrency(rs.getString("currency"));
        supplier.setNotes(rs.getString("notes"));
        supplier.setCreditLimit(rs.getBigDecimal("credit_limit"));
        supplier.setCurrentBalance(rs.getBigDecimal("current_balance"));
        
        Timestamp lastOrderDate = rs.getTimestamp("last_order_date");
        if (lastOrderDate != null) {
            supplier.setLastOrderDate(lastOrderDate.toLocalDateTime());
        }
        
        supplier.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        supplier.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
        
        return supplier;
    }
}
