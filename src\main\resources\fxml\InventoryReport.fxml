<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.InventoryReportController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="📦 Inventory Report">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <ComboBox fx:id="cmbReportType" onAction="#handleReportTypeChange" promptText="Report Type" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
            <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </HBox>

      <!-- Summary Metrics -->
      <GridPane hgap="15.0" vgap="15.0" styleClass="form-container">
         <columnConstraints>
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         <children>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="0">
               <children>
                  <Label styleClass="metric-title" text="📊 Total Products" />
                  <Label fx:id="lblTotalProducts" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="1">
               <children>
                  <Label styleClass="metric-title" text="💰 Total Value" />
                  <Label fx:id="lblTotalValue" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="2">
               <children>
                  <Label styleClass="metric-title" text="📈 Categories" />
                  <Label fx:id="lblTotalCategories" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="3">
               <children>
                  <Label styleClass="metric-title" text="⚠️ Low Stock" />
                  <Label fx:id="lblLowStockItems" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </GridPane>

      <!-- Inventory Table -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="Inventory Details">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <TextField fx:id="txtSearch" onKeyReleased="#handleSearch" promptText="Search products..." />
                  <ComboBox fx:id="cmbCategoryFilter" onAction="#handleCategoryFilter" promptText="All Categories" />
               </children>
            </HBox>

            <TableView fx:id="tblInventory" prefHeight="350.0">
               <columns>
                  <TableColumn fx:id="colSku" prefWidth="100.0" text="SKU" />
                  <TableColumn fx:id="colName" prefWidth="200.0" text="Product Name" />
                  <TableColumn fx:id="colCategory" prefWidth="120.0" text="Category" />
                  <TableColumn fx:id="colBrand" prefWidth="100.0" text="Brand" />
                  <TableColumn fx:id="colStock" prefWidth="80.0" text="Stock" />
                  <TableColumn fx:id="colMinStock" prefWidth="80.0" text="Min Stock" />
                  <TableColumn fx:id="colPrice" prefWidth="80.0" text="Price" />
                  <TableColumn fx:id="colTotalValue" prefWidth="100.0" text="Total Value" />
                  <TableColumn fx:id="colStatus" prefWidth="100.0" text="Status" />
               </columns>
            </TableView>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Category Analysis -->
      <HBox spacing="15.0" styleClass="form-container">
         <children>
            <VBox spacing="10.0" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="section-title" text="📊 Category Analysis">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <TableView fx:id="tblCategoryAnalysis" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="colCategoryName" prefWidth="150.0" text="Category" />
                        <TableColumn fx:id="colCategoryCount" prefWidth="80.0" text="Products" />
                        <TableColumn fx:id="colCategoryValue" prefWidth="100.0" text="Total Value" />
                        <TableColumn fx:id="colCategoryAvgPrice" prefWidth="100.0" text="Avg Price" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
