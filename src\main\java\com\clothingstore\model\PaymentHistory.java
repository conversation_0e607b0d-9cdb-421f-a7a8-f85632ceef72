package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing a payment history record
 * Tracks individual payments made for transactions with detailed audit trail
 */
public class PaymentHistory {
    
    private Long id;
    private Long transactionId;
    private Integer paymentSequence;
    private BigDecimal paymentAmount;
    private String paymentMethod;
    private LocalDateTime paymentDate;
    private String paymentReference;
    private BigDecimal runningBalance;
    private BigDecimal remainingBalance;
    private PaymentType paymentType;
    private String notes;
    private String cashierName;
    private PaymentStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Enums
    public enum PaymentType {
        PAYMENT, REFUND
    }
    
    public enum PaymentStatus {
        PENDING, COMPLETED, CANCELLED, FAILED
    }
    
    // Constructors
    public PaymentHistory() {
        this.paymentType = PaymentType.PAYMENT;
        this.status = PaymentStatus.COMPLETED;
        this.paymentDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public PaymentHistory(Long transactionId, Integer paymentSequence, BigDecimal paymentAmount, 
                         String paymentMethod, BigDecimal runningBalance, BigDecimal remainingBalance) {
        this();
        this.transactionId = transactionId;
        this.paymentSequence = paymentSequence;
        this.paymentAmount = paymentAmount;
        this.paymentMethod = paymentMethod;
        this.runningBalance = runningBalance;
        this.remainingBalance = remainingBalance;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getTransactionId() {
        return transactionId;
    }
    
    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }
    
    public Integer getPaymentSequence() {
        return paymentSequence;
    }
    
    public void setPaymentSequence(Integer paymentSequence) {
        this.paymentSequence = paymentSequence;
    }
    
    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }
    
    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }
    
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public LocalDateTime getPaymentDate() {
        return paymentDate;
    }
    
    public void setPaymentDate(LocalDateTime paymentDate) {
        this.paymentDate = paymentDate;
    }
    
    public String getPaymentReference() {
        return paymentReference;
    }
    
    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }
    
    public BigDecimal getRunningBalance() {
        return runningBalance;
    }
    
    public void setRunningBalance(BigDecimal runningBalance) {
        this.runningBalance = runningBalance;
    }
    
    public BigDecimal getRemainingBalance() {
        return remainingBalance;
    }
    
    public void setRemainingBalance(BigDecimal remainingBalance) {
        this.remainingBalance = remainingBalance;
    }
    
    public PaymentType getPaymentType() {
        return paymentType;
    }
    
    public void setPaymentType(PaymentType paymentType) {
        this.paymentType = paymentType;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public String getCashierName() {
        return cashierName;
    }
    
    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }
    
    public PaymentStatus getStatus() {
        return status;
    }
    
    public void setStatus(PaymentStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Utility methods
    public boolean isRefund() {
        return paymentType == PaymentType.REFUND;
    }
    
    public boolean isPayment() {
        return paymentType == PaymentType.PAYMENT;
    }
    
    public boolean isCompleted() {
        return status == PaymentStatus.COMPLETED;
    }
    
    public String getFormattedPaymentAmount() {
        if (paymentAmount == null) return "$0.00";
        return String.format("$%.2f", paymentAmount.doubleValue());
    }
    
    public String getFormattedRunningBalance() {
        if (runningBalance == null) return "$0.00";
        return String.format("$%.2f", runningBalance.doubleValue());
    }
    
    public String getFormattedRemainingBalance() {
        if (remainingBalance == null) return "$0.00";
        return String.format("$%.2f", remainingBalance.doubleValue());
    }
    
    @Override
    public String toString() {
        return "PaymentHistory{" +
                "id=" + id +
                ", transactionId=" + transactionId +
                ", paymentSequence=" + paymentSequence +
                ", paymentAmount=" + paymentAmount +
                ", paymentMethod='" + paymentMethod + '\'' +
                ", paymentDate=" + paymentDate +
                ", paymentType=" + paymentType +
                ", status=" + status +
                '}';
    }
}
