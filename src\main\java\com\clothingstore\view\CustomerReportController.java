package com.clothingstore.view;

import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.util.List;
import java.util.ResourceBundle;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.util.AlertUtil;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;

/**
 * Controller for Customer Report interface
 */
public class CustomerReportController implements Initializable {

    @FXML
    private DatePicker dateFrom;
    @FXML
    private DatePicker dateTo;
    @FXML
    private Button btnRefresh;
    @FXML
    private Button btnExport;
    @FXML
    private TextField txtSearch;
    @FXML
    private ComboBox<String> cmbStatusFilter;

    @FXML
    private Label lblTotalCustomers;
    @FXML
    private Label lblActiveCustomers;
    @FXML
    private Label lblAvgPurchases;
    @FXML
    private Label lblTotalSpent;

    @FXML
    private TableView<Customer> tblCustomers;
    @FXML
    private TableColumn<Customer, String> colCustomerId;
    @FXML
    private TableColumn<Customer, String> colName;
    @FXML
    private TableColumn<Customer, String> colEmail;
    @FXML
    private TableColumn<Customer, String> colPhone;
    @FXML
    private TableColumn<Customer, String> colStatus;
    @FXML
    private TableColumn<Customer, String> colTotalPurchases;
    @FXML
    private TableColumn<Customer, String> colTotalSpent;
    @FXML
    private TableColumn<Customer, String> colLastPurchase;
    @FXML
    private TableColumn<Customer, String> colActions;

    @FXML
    private TableView<TopCustomerItem> tblTopCustomers;
    @FXML
    private TableColumn<TopCustomerItem, String> colTopRank;
    @FXML
    private TableColumn<TopCustomerItem, String> colTopName;
    @FXML
    private TableColumn<TopCustomerItem, String> colTopEmail;
    @FXML
    private TableColumn<TopCustomerItem, String> colTopPurchases;
    @FXML
    private TableColumn<TopCustomerItem, String> colTopSpent;
    @FXML
    private TableColumn<TopCustomerItem, String> colTopAvgOrder;

    private ObservableList<Customer> customerData;
    private ObservableList<TopCustomerItem> topCustomerData;
    private CustomerDAO customerDAO;
    private NumberFormat currencyFormat;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        customerDAO = CustomerDAO.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        customerData = FXCollections.observableArrayList();
        topCustomerData = FXCollections.observableArrayList();

        setupTables();
        setupFilters();
        loadCustomerData();
    }

    private void setupTables() {
        // Customer table
        colCustomerId.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(String.valueOf(cellData.getValue().getId())));
        colName.setCellValueFactory(new PropertyValueFactory<>("name"));

        colPhone.setCellValueFactory(new PropertyValueFactory<>("phone"));
        colStatus.setCellValueFactory(new PropertyValueFactory<>("status"));

        // Placeholder values for purchase data (would need transaction integration)
        colTotalPurchases.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty("0"));
        colTotalSpent.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty("$0.00"));
        colLastPurchase.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty("-"));

        // Action buttons
        colActions.setCellFactory(col -> new TableCell<Customer, String>() {
            private final Button viewBtn = new Button("View");

            {
                viewBtn.setOnAction(e -> {
                    Customer customer = getTableView().getItems().get(getIndex());
                    handleViewCustomer(customer);
                });
                viewBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(viewBtn);
                }
            }
        });

        tblCustomers.setItems(customerData);

        // Top customers table
        colTopRank.setCellValueFactory(new PropertyValueFactory<>("rank"));
        colTopName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colTopEmail.setCellValueFactory(new PropertyValueFactory<>("email"));
        colTopPurchases.setCellValueFactory(new PropertyValueFactory<>("purchases"));
        colTopSpent.setCellValueFactory(new PropertyValueFactory<>("totalSpent"));
        colTopAvgOrder.setCellValueFactory(new PropertyValueFactory<>("avgOrder"));

        tblTopCustomers.setItems(topCustomerData);
    }

    private void setupFilters() {
        cmbStatusFilter.setItems(FXCollections.observableArrayList("All Status", "Active", "Inactive"));
        cmbStatusFilter.setValue("All Status");

        // Set default date range (last 30 days)
        dateTo.setValue(LocalDate.now());
        dateFrom.setValue(LocalDate.now().minusDays(30));
    }

    private void loadCustomerData() {
        try {
            List<Customer> allCustomers = customerDAO.findAll();
            customerData.setAll(allCustomers);

            // Update summary metrics
            int totalCustomers = allCustomers.size();
            long activeCustomers = allCustomers.stream()
                    .mapToLong(c -> "Active".equals(c.getStatus()) ? 1 : 0)
                    .sum();

            lblTotalCustomers.setText(String.valueOf(totalCustomers));
            lblActiveCustomers.setText(String.valueOf(activeCustomers));
            lblAvgPurchases.setText("0"); // Placeholder
            lblTotalSpent.setText("$0.00"); // Placeholder

            // Load top customers (placeholder data)
            loadTopCustomers(allCustomers);

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load customer data: " + e.getMessage());
        }
    }

    private void loadTopCustomers(List<Customer> customers) {
        topCustomerData.clear();

        // Create placeholder top customer data
        for (int i = 0; i < Math.min(5, customers.size()); i++) {
            Customer customer = customers.get(i);

            TopCustomerItem item = new TopCustomerItem();
            item.setRank(String.valueOf(i + 1));
            item.setName(customer.getName());

            item.setPurchases("0"); // Placeholder
            item.setTotalSpent("$0.00"); // Placeholder
            item.setAvgOrder("$0.00"); // Placeholder

            topCustomerData.add(item);
        }
    }

    @FXML
    private void handleRefresh() {
        loadCustomerData();
        AlertUtil.showInfo("Refreshed", "Customer report has been refreshed.");
    }

    @FXML
    private void handleExport() {
        AlertUtil.showInfo("Export", "Customer report export functionality will be implemented in future version.");
    }

    @FXML
    private void handleSearch() {
        String searchTerm = txtSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            loadCustomerData();
            return;
        }

        try {
            List<Customer> allCustomers = customerDAO.findAll();
            List<Customer> filteredCustomers = allCustomers.stream()
                    .filter(c -> c.getName().toLowerCase().contains(searchTerm.toLowerCase())
                    || c.getPhone().toLowerCase().contains(searchTerm.toLowerCase()))
                    .collect(java.util.stream.Collectors.toList());

            customerData.setAll(filteredCustomers);

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to search customers: " + e.getMessage());
        }
    }

    @FXML
    private void handleStatusFilter() {
        String selectedStatus = cmbStatusFilter.getValue();
        if (selectedStatus == null || "All Status".equals(selectedStatus)) {
            loadCustomerData();
            return;
        }

        try {
            List<Customer> allCustomers = customerDAO.findAll();
            List<Customer> filteredCustomers = allCustomers.stream()
                    .filter(c -> selectedStatus.equals(c.getStatus()))
                    .collect(java.util.stream.Collectors.toList());

            customerData.setAll(filteredCustomers);

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to filter customers: " + e.getMessage());
        }
    }

    private void handleViewCustomer(Customer customer) {
        AlertUtil.showInfo("Customer Details",
                String.format("Customer Information:\n\nName: %s\nPhone: %s\nStatus: %s\nAddress: %s",
                        customer.getName(), customer.getPhone(),
                        customer.getStatus(), customer.getAddress()));
    }

    // Inner class for top customers
    public static class TopCustomerItem {

        private String rank;
        private String name;
        private String email;
        private String purchases;
        private String totalSpent;
        private String avgOrder;

        // Getters and setters
        public String getRank() {
            return rank;
        }

        public void setRank(String rank) {
            this.rank = rank;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPurchases() {
            return purchases;
        }

        public void setPurchases(String purchases) {
            this.purchases = purchases;
        }

        public String getTotalSpent() {
            return totalSpent;
        }

        public void setTotalSpent(String totalSpent) {
            this.totalSpent = totalSpent;
        }

        public String getAvgOrder() {
            return avgOrder;
        }

        public void setAvgOrder(String avgOrder) {
            this.avgOrder = avgOrder;
        }
    }
}
