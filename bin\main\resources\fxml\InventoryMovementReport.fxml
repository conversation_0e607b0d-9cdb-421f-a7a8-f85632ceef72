<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.InventoryMovementReportController">
   <children>
      <!-- Enhanced Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <VBox spacing="5.0">
               <children>
                  <Label text="📊 Inventory Movement Report" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <Label fx:id="lblReportSubtitle" text="Track items sold, returned, and inventory changes" textFill="#7f8c8d">
                     <font>
                        <Font size="12.0" />
                     </font>
                  </Label>
               </children>
            </VBox>
            <Region HBox.hgrow="ALWAYS" />
            <VBox spacing="5.0" alignment="CENTER_RIGHT">
               <children>
                  <HBox spacing="5.0" alignment="CENTER_RIGHT">
                     <children>
                        <Button fx:id="btnRefresh" mnemonicParsing="false" onAction="#handleRefresh"
                                style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;" text="🔄 Refresh" />
                        <MenuButton fx:id="mbExport" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold;" text="📤 Export">
                           <items>
                              <MenuItem fx:id="miExportCSV" mnemonicParsing="false" onAction="#handleExportCSV" text="Export to CSV" />
                              <MenuItem fx:id="miExportPDF" mnemonicParsing="false" onAction="#handleExportPDF" text="Export to PDF" />
                              <MenuItem fx:id="miExportExcel" mnemonicParsing="false" onAction="#handleExportExcel" text="Export to Excel" />
                              <SeparatorMenuItem />
                              <MenuItem fx:id="miPrintReport" mnemonicParsing="false" onAction="#handlePrintReport" text="Print Report" />
                           </items>
                        </MenuButton>
                     </children>
                  </HBox>
                  <Label fx:id="lblRecordCount" text="0 records found" textFill="#7f8c8d" style="-fx-font-size: 11px;" />
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <Separator />

      <!-- Enhanced Filters Section -->
      <VBox spacing="15.0">
         <children>
            <!-- Date Range and Quick Filters -->
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <Label text="Date Range:" style="-fx-font-weight: bold;" />
                  <DatePicker fx:id="dpStartDate" promptText="Start Date" />
                  <Label text="to" />
                  <DatePicker fx:id="dpEndDate" promptText="End Date" />
                  <Separator orientation="VERTICAL" />
                  <Label text="Quick:" style="-fx-font-weight: bold;" />
                  <Button fx:id="btnToday" mnemonicParsing="false" onAction="#handleTodayFilter" text="Today" />
                  <Button fx:id="btnThisWeek" mnemonicParsing="false" onAction="#handleThisWeekFilter" text="This Week" />
                  <Button fx:id="btnThisMonth" mnemonicParsing="false" onAction="#handleThisMonthFilter" text="This Month" />
                  <Button fx:id="btnLast30Days" mnemonicParsing="false" onAction="#handleLast30DaysFilter" text="Last 30 Days" />
               </children>
            </HBox>

            <!-- Advanced Filters -->
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <Label text="Filters:" style="-fx-font-weight: bold;" />
                  <ComboBox fx:id="cbCategory" promptText="All Categories" prefWidth="120.0" />
                  <ComboBox fx:id="cbBrand" promptText="All Brands" prefWidth="120.0" />
                  <ComboBox fx:id="cbMovementType" promptText="All Types" prefWidth="120.0" />
                  <TextField fx:id="txtProductSearch" promptText="Search Product..." prefWidth="150.0" />
                  <Separator orientation="VERTICAL" />
                  <Label text="Min Value:" />
                  <TextField fx:id="txtMinValue" promptText="0.00" prefWidth="80.0" />
                  <Label text="Max Value:" />
                  <TextField fx:id="txtMaxValue" promptText="999999" prefWidth="80.0" />
               </children>
            </HBox>

            <!-- Action Buttons -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Button fx:id="btnGenerateReport" mnemonicParsing="false" onAction="#handleGenerateReport"
                          style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold;" text="🔍 Generate Report" />
                  <Button fx:id="btnClearFilters" mnemonicParsing="false" onAction="#handleClearFilters"
                          style="-fx-background-color: #95a5a6; -fx-text-fill: white;" text="Clear Filters" />
                  <Separator orientation="VERTICAL" />
                  <Button fx:id="btnAutoRefresh" mnemonicParsing="false" onAction="#handleToggleAutoRefresh"
                          style="-fx-background-color: #f39c12; -fx-text-fill: white;" text="Auto Refresh: OFF" />
                  <Label fx:id="lblLastUpdated" text="Last Updated: Never" style="-fx-text-fill: #7f8c8d;" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </VBox>

      <Separator />

      <!-- Enhanced Summary Statistics Section -->
      <VBox spacing="15.0">
         <children>
            <Label text="📈 Summary Statistics" textFill="#2c3e50" style="-fx-font-weight: bold; -fx-font-size: 16px;" />

            <GridPane hgap="15.0" vgap="15.0">
               <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" />
               </columnConstraints>
               <rowConstraints>
                  <RowConstraints minHeight="10.0" vgrow="SOMETIMES" />
                  <RowConstraints minHeight="10.0" vgrow="SOMETIMES" />
               </rowConstraints>
               <children>
                  <!-- Items Sold/Processed Statistics -->
                  <VBox alignment="CENTER" style="-fx-background-color: linear-gradient(to bottom, #2ecc71, #27ae60); -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                     <children>
                        <Label text="✅ Items Sold" textFill="white">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblSoldItemCount" text="0" textFill="white">
                           <font>
                              <Font name="System Bold" size="24.0" />
                           </font>
                        </Label>
                        <Label text="items processed" textFill="white" style="-fx-font-size: 11px;" />
                        <Separator style="-fx-background-color: rgba(255,255,255,0.3);" />
                        <HBox spacing="10.0" alignment="CENTER">
                           <children>
                              <VBox alignment="CENTER" spacing="2.0">
                                 <children>
                                    <Label text="Qty" textFill="white" style="-fx-font-size: 10px;" />
                                    <Label fx:id="lblSoldQuantity" text="0" textFill="white" style="-fx-font-weight: bold;" />
                                 </children>
                              </VBox>
                              <VBox alignment="CENTER" spacing="2.0">
                                 <children>
                                    <Label text="Value" textFill="white" style="-fx-font-size: 10px;" />
                                    <Label fx:id="lblSoldValue" text="$0.00" textFill="white" style="-fx-font-weight: bold;" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Items Returned/Refunded Statistics -->
                  <VBox alignment="CENTER" style="-fx-background-color: linear-gradient(to bottom, #e74c3c, #c0392b); -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="1">
                     <children>
                        <Label text="❌ Items Returned" textFill="white">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblReturnedItemCount" text="0" textFill="white">
                           <font>
                              <Font name="System Bold" size="24.0" />
                           </font>
                        </Label>
                        <Label text="items refunded" textFill="white" style="-fx-font-size: 11px;" />
                        <Separator style="-fx-background-color: rgba(255,255,255,0.3);" />
                        <HBox spacing="10.0" alignment="CENTER">
                           <children>
                              <VBox alignment="CENTER" spacing="2.0">
                                 <children>
                                    <Label text="Qty" textFill="white" style="-fx-font-size: 10px;" />
                                    <Label fx:id="lblReturnedQuantity" text="0" textFill="white" style="-fx-font-weight: bold;" />
                                 </children>
                              </VBox>
                              <VBox alignment="CENTER" spacing="2.0">
                                 <children>
                                    <Label text="Value" textFill="white" style="-fx-font-size: 10px;" />
                                    <Label fx:id="lblReturnedValue" text="$0.00" textFill="white" style="-fx-font-weight: bold;" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Net Movement Statistics -->
                  <VBox alignment="CENTER" style="-fx-background-color: linear-gradient(to bottom, #3498db, #2980b9); -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="2">
                     <children>
                        <Label text="📊 Net Movement" textFill="white">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblNetQuantity" text="0" textFill="white">
                           <font>
                              <Font name="System Bold" size="24.0" />
                           </font>
                        </Label>
                        <Label text="net quantity" textFill="white" style="-fx-font-size: 11px;" />
                        <Separator style="-fx-background-color: rgba(255,255,255,0.3);" />
                        <VBox alignment="CENTER" spacing="2.0">
                           <children>
                              <Label text="Net Value" textFill="white" style="-fx-font-size: 10px;" />
                              <Label fx:id="lblNetValue" text="$0.00" textFill="white" style="-fx-font-weight: bold;" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>

                  <!-- Return Rate Statistics -->
                  <VBox alignment="CENTER" style="-fx-background-color: linear-gradient(to bottom, #f39c12, #e67e22); -fx-padding: 20; -fx-background-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="3">
                     <children>
                        <Label text="📈 Return Rate" textFill="white">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblReturnRate" text="0.00%" textFill="white">
                           <font>
                              <Font name="System Bold" size="24.0" />
                           </font>
                        </Label>
                        <Label text="return percentage" textFill="white" style="-fx-font-size: 11px;" />
                        <Separator style="-fx-background-color: rgba(255,255,255,0.3);" />
                        <VBox alignment="CENTER" spacing="2.0">
                           <children>
                              <Label fx:id="lblReturnTrend" text="📊 Trend: Stable" textFill="white" style="-fx-font-size: 10px;" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>
               </children>
            </GridPane>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </VBox>

      <Separator />

      <!-- Enhanced Main Content - Tabbed Interface -->
      <TabPane tabClosingPolicy="UNAVAILABLE" VBox.vgrow="ALWAYS" style="-fx-tab-min-height: 40px;">
         <tabs>
            <!-- Items Sold/Processed Tab -->
            <Tab text="✅ Items Sold/Processed">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <!-- Tab Header with Controls -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #ecf0f1; -fx-padding: 10;">
                           <children>
                              <Label text="📦 All items sold through completed transactions" textFill="#27ae60" style="-fx-font-weight: bold;" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="lblSoldItemsCount" text="0 items" textFill="#7f8c8d" />
                              <Separator orientation="VERTICAL" />
                              <Button fx:id="btnSortSoldByDate" mnemonicParsing="false" onAction="#handleSortSoldByDate" text="📅 Sort by Date" style="-fx-font-size: 11px;" />
                              <Button fx:id="btnSortSoldByValue" mnemonicParsing="false" onAction="#handleSortSoldByValue" text="💰 Sort by Value" style="-fx-font-size: 11px;" />
                              <Button fx:id="btnExportSoldItems" mnemonicParsing="false" onAction="#handleExportSoldItems" text="📤 Export" style="-fx-font-size: 11px;" />
                           </children>
                        </HBox>

                        <!-- Enhanced Table with Better Columns -->
                        <TableView fx:id="tblSoldItems" VBox.vgrow="ALWAYS" style="-fx-selection-bar: #3498db;">
                           <columns>
                              <TableColumn fx:id="colSoldDate" prefWidth="90.0" text="📅 Date" />
                              <TableColumn fx:id="colSoldTime" prefWidth="70.0" text="🕐 Time" />
                              <TableColumn fx:id="colSoldTransaction" prefWidth="120.0" text="🧾 Transaction" />
                              <TableColumn fx:id="colSoldProduct" prefWidth="180.0" text="📦 Product" />
                              <TableColumn fx:id="colSoldSku" prefWidth="80.0" text="🏷️ SKU" />
                              <TableColumn fx:id="colSoldCategory" prefWidth="100.0" text="📂 Category" />
                              <TableColumn fx:id="colSoldBrand" prefWidth="90.0" text="🏢 Brand" />
                              <TableColumn fx:id="colSoldQuantity" prefWidth="60.0" text="📊 Qty" />
                              <TableColumn fx:id="colSoldUnitPrice" prefWidth="90.0" text="💵 Unit Price" />
                              <TableColumn fx:id="colSoldLineTotal" prefWidth="100.0" text="💰 Line Total" />
                              <TableColumn fx:id="colSoldCustomer" prefWidth="130.0" text="👤 Customer" />
                              <TableColumn fx:id="colSoldPaymentMethod" prefWidth="100.0" text="💳 Payment" />
                           </columns>
                           <placeholder>
                              <Label text="No sold items found for the selected period" textFill="#7f8c8d" />
                           </placeholder>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>

            <!-- Items Returned/Refunded Tab -->
            <Tab text="❌ Items Returned/Refunded">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <!-- Tab Header with Controls -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #fdf2f2; -fx-padding: 10;">
                           <children>
                              <Label text="🔄 All items returned to inventory through refunds and cancellations" textFill="#e74c3c" style="-fx-font-weight: bold;" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="lblReturnedItemsCount" text="0 items" textFill="#7f8c8d" />
                              <Separator orientation="VERTICAL" />
                              <Button fx:id="btnSortReturnedByDate" mnemonicParsing="false" onAction="#handleSortReturnedByDate" text="📅 Sort by Date" style="-fx-font-size: 11px;" />
                              <Button fx:id="btnSortReturnedByReason" mnemonicParsing="false" onAction="#handleSortReturnedByReason" text="📝 Sort by Reason" style="-fx-font-size: 11px;" />
                              <Button fx:id="btnExportReturnedItems" mnemonicParsing="false" onAction="#handleExportReturnedItems" text="📤 Export" style="-fx-font-size: 11px;" />
                           </children>
                        </HBox>

                        <!-- Enhanced Table with Better Columns -->
                        <TableView fx:id="tblReturnedItems" VBox.vgrow="ALWAYS" style="-fx-selection-bar: #e74c3c;">
                           <columns>
                              <TableColumn fx:id="colReturnedDate" prefWidth="90.0" text="📅 Date" />
                              <TableColumn fx:id="colReturnedTime" prefWidth="70.0" text="🕐 Time" />
                              <TableColumn fx:id="colReturnedTransaction" prefWidth="120.0" text="🧾 Transaction" />
                              <TableColumn fx:id="colReturnedProduct" prefWidth="180.0" text="📦 Product" />
                              <TableColumn fx:id="colReturnedSku" prefWidth="80.0" text="🏷️ SKU" />
                              <TableColumn fx:id="colReturnedCategory" prefWidth="100.0" text="📂 Category" />
                              <TableColumn fx:id="colReturnedBrand" prefWidth="90.0" text="🏢 Brand" />
                              <TableColumn fx:id="colReturnedQuantity" prefWidth="60.0" text="📊 Qty" />
                              <TableColumn fx:id="colReturnedUnitPrice" prefWidth="90.0" text="💵 Unit Price" />
                              <TableColumn fx:id="colReturnedLineTotal" prefWidth="100.0" text="💰 Line Total" />
                              <TableColumn fx:id="colReturnedType" prefWidth="90.0" text="🔄 Type" />
                              <TableColumn fx:id="colReturnedReason" prefWidth="150.0" text="📝 Reason" />
                              <TableColumn fx:id="colReturnedStatus" prefWidth="80.0" text="📊 Status" />
                           </columns>
                           <placeholder>
                              <Label text="No returned items found for the selected period" textFill="#7f8c8d" />
                           </placeholder>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>

            <!-- Analytics Tab -->
            <Tab text="📊 Analytics">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <!-- Analytics Header -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15;">
                           <children>
                              <Label text="📈 Inventory Movement Analytics" textFill="#2c3e50" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="btnRefreshAnalytics" mnemonicParsing="false" onAction="#handleRefreshAnalytics" text="🔄 Refresh Analytics" />
                           </children>
                        </HBox>

                        <!-- Analytics Content -->
                        <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
                           <content>
                              <VBox spacing="20.0" style="-fx-padding: 20;">
                                 <children>
                                    <!-- Top Products Section -->
                                    <VBox spacing="10.0">
                                       <children>
                                          <Label text="🏆 Top Selling Products" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                          <TableView fx:id="tblTopProducts" prefHeight="200.0">
                                             <columns>
                                                <TableColumn fx:id="colTopProductName" prefWidth="200.0" text="Product" />
                                                <TableColumn fx:id="colTopProductSold" prefWidth="100.0" text="Units Sold" />
                                                <TableColumn fx:id="colTopProductRevenue" prefWidth="120.0" text="Revenue" />
                                                <TableColumn fx:id="colTopProductReturns" prefWidth="100.0" text="Returns" />
                                                <TableColumn fx:id="colTopProductReturnRate" prefWidth="100.0" text="Return Rate" />
                                             </columns>
                                          </TableView>
                                       </children>
                                    </VBox>

                                    <!-- Category Analysis -->
                                    <VBox spacing="10.0">
                                       <children>
                                          <Label text="📂 Category Performance" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                          <TableView fx:id="tblCategoryAnalysis" prefHeight="200.0">
                                             <columns>
                                                <TableColumn fx:id="colCategoryName" prefWidth="150.0" text="Category" />
                                                <TableColumn fx:id="colCategorySold" prefWidth="100.0" text="Units Sold" />
                                                <TableColumn fx:id="colCategoryRevenue" prefWidth="120.0" text="Revenue" />
                                                <TableColumn fx:id="colCategoryReturns" prefWidth="100.0" text="Returns" />
                                                <TableColumn fx:id="colCategoryReturnRate" prefWidth="100.0" text="Return Rate" />
                                                <TableColumn fx:id="colCategoryProfit" prefWidth="120.0" text="Net Profit" />
                                             </columns>
                                          </TableView>
                                       </children>
                                    </VBox>

                                    <!-- Return Reasons Analysis -->
                                    <VBox spacing="10.0">
                                       <children>
                                          <Label text="📝 Return Reasons Analysis" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                                          <TableView fx:id="tblReturnReasons" prefHeight="150.0">
                                             <columns>
                                                <TableColumn fx:id="colReturnReason" prefWidth="200.0" text="Return Reason" />
                                                <TableColumn fx:id="colReturnReasonCount" prefWidth="100.0" text="Count" />
                                                <TableColumn fx:id="colReturnReasonPercentage" prefWidth="100.0" text="Percentage" />
                                                <TableColumn fx:id="colReturnReasonValue" prefWidth="120.0" text="Total Value" />
                                             </columns>
                                          </TableView>
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>
                           </content>
                        </ScrollPane>
                     </children>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </children>
</VBox>
