package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing inventory movement data for reporting
 * Tracks both items sold/processed and items returned/refunded
 */
public class InventoryMovement {
    
    // Movement identification
    private Long id;
    private String transactionNumber;
    private LocalDateTime movementDate;
    private String movementType; // "SOLD", "RETURNED", "REFUNDED", "CANCELLED"
    
    // Product information
    private Long productId;
    private String productName;
    private String productSku;
    private String category;
    private String brand;
    
    // Movement details
    private Integer quantity;
    private BigDecimal unitPrice;
    private BigDecimal lineTotal;
    private String reason; // For returns/refunds
    
    // Customer information
    private Long customerId;
    private String customerName;
    
    // Transaction context
    private String status;
    private String paymentMethod;
    private String cashierName;
    private String notes;
    
    // Timestamps
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructors
    public InventoryMovement() {
    }
    
    public InventoryMovement(String transactionNumber, LocalDateTime movementDate, 
                           String movementType, String productName, String productSku, 
                           Integer quantity, BigDecimal unitPrice, BigDecimal lineTotal) {
        this.transactionNumber = transactionNumber;
        this.movementDate = movementDate;
        this.movementType = movementType;
        this.productName = productName;
        this.productSku = productSku;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.lineTotal = lineTotal;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTransactionNumber() {
        return transactionNumber;
    }
    
    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber;
    }
    
    public LocalDateTime getMovementDate() {
        return movementDate;
    }
    
    public void setMovementDate(LocalDateTime movementDate) {
        this.movementDate = movementDate;
    }
    
    public String getMovementType() {
        return movementType;
    }
    
    public void setMovementType(String movementType) {
        this.movementType = movementType;
    }
    
    public Long getProductId() {
        return productId;
    }
    
    public void setProductId(Long productId) {
        this.productId = productId;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public String getProductSku() {
        return productSku;
    }
    
    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getBrand() {
        return brand;
    }
    
    public void setBrand(String brand) {
        this.brand = brand;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getUnitPrice() {
        return unitPrice != null ? unitPrice : BigDecimal.ZERO;
    }
    
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
    
    public BigDecimal getLineTotal() {
        return lineTotal != null ? lineTotal : BigDecimal.ZERO;
    }
    
    public void setLineTotal(BigDecimal lineTotal) {
        this.lineTotal = lineTotal;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public Long getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public String getCashierName() {
        return cashierName;
    }
    
    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Utility methods
    public boolean isSoldMovement() {
        return "SOLD".equals(movementType);
    }
    
    public boolean isReturnedMovement() {
        return "RETURNED".equals(movementType) || "REFUNDED".equals(movementType);
    }
    
    public String getFormattedMovementDate() {
        return movementDate != null ? movementDate.toLocalDate().toString() : "";
    }
    
    public String getFormattedMovementTime() {
        return movementDate != null ? movementDate.toLocalTime().toString() : "";
    }
    
    @Override
    public String toString() {
        return "InventoryMovement{" +
                "transactionNumber='" + transactionNumber + '\'' +
                ", movementType='" + movementType + '\'' +
                ", productName='" + productName + '\'' +
                ", quantity=" + quantity +
                ", lineTotal=" + lineTotal +
                '}';
    }
}
