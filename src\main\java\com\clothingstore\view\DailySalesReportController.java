package com.clothingstore.view;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.util.List;
import java.util.ResourceBundle;

import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Transaction;
import com.clothingstore.util.AlertUtil;

/**
 * Controller for Daily Sales Report interface
 */
public class DailySalesReportController implements Initializable {

    @FXML private DatePicker dateSelected;
    @FXML private Button btnToday;
    @FXML private Button btnRefresh;
    @FXML private Button btnExport;
    @FXML private TextField txtSearch;

    @FXML private Label lblTotalSales;
    @FXML private Label lblTotalTransactions;
    @FXML private Label lblAvgTransaction;
    @FXML private Label lblUniqueCustomers;

    @FXML private TableView<HourlySalesItem> tblHourlySales;
    @FXML private TableColumn<HourlySalesItem, String> colHour;
    @FXML private TableColumn<HourlySalesItem, String> colHourTransactions;
    @FXML private TableColumn<HourlySalesItem, String> colHourSales;
    @FXML private TableColumn<HourlySalesItem, String> colHourAvg;
    @FXML private TableColumn<HourlySalesItem, String> colHourPercentage;

    @FXML private TableView<Transaction> tblDailyTransactions;
    @FXML private TableColumn<Transaction, String> colTransactionId;
    @FXML private TableColumn<Transaction, String> colTime;
    @FXML private TableColumn<Transaction, String> colCustomer;
    @FXML private TableColumn<Transaction, String> colItems;
    @FXML private TableColumn<Transaction, String> colSubtotal;
    @FXML private TableColumn<Transaction, String> colTax;
    @FXML private TableColumn<Transaction, String> colTotal;
    @FXML private TableColumn<Transaction, String> colPaymentMethod;
    @FXML private TableColumn<Transaction, String> colActions;

    @FXML private TableView<TopProductItem> tblTopProducts;
    @FXML private TableColumn<TopProductItem, String> colProductRank;
    @FXML private TableColumn<TopProductItem, String> colProductName;
    @FXML private TableColumn<TopProductItem, String> colQuantitySold;
    @FXML private TableColumn<TopProductItem, String> colProductRevenue;

    private ObservableList<HourlySalesItem> hourlySalesData;
    private ObservableList<Transaction> transactionData;
    private ObservableList<TopProductItem> topProductData;
    private TransactionDAO transactionDAO;
    private NumberFormat currencyFormat;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        transactionDAO = TransactionDAO.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        hourlySalesData = FXCollections.observableArrayList();
        transactionData = FXCollections.observableArrayList();
        topProductData = FXCollections.observableArrayList();
        
        setupTables();
        setupDatePicker();
        loadDailySalesData();
    }

    private void setupTables() {
        // Hourly sales table
        colHour.setCellValueFactory(new PropertyValueFactory<>("hour"));
        colHourTransactions.setCellValueFactory(new PropertyValueFactory<>("transactions"));
        colHourSales.setCellValueFactory(new PropertyValueFactory<>("sales"));
        colHourAvg.setCellValueFactory(new PropertyValueFactory<>("avgTransaction"));
        colHourPercentage.setCellValueFactory(new PropertyValueFactory<>("percentage"));
        tblHourlySales.setItems(hourlySalesData);

        // Daily transactions table
        colTransactionId.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(String.valueOf(cellData.getValue().getId())));
        colTime.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getTransactionDate().toString()));
        colCustomer.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty("Customer " + cellData.getValue().getCustomerId()));
        colItems.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty("1")); // Placeholder
        colSubtotal.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(currencyFormat.format(cellData.getValue().getSubtotal())));
        colTax.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(currencyFormat.format(cellData.getValue().getTax())));
        colTotal.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(currencyFormat.format(cellData.getValue().getTotal())));
        colPaymentMethod.setCellValueFactory(new PropertyValueFactory<>("paymentMethod"));
        
        colActions.setCellFactory(col -> new TableCell<Transaction, String>() {
            private final Button viewBtn = new Button("View");
            
            {
                viewBtn.setOnAction(e -> {
                    Transaction transaction = getTableView().getItems().get(getIndex());
                    handleViewTransaction(transaction);
                });
                viewBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
            }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(viewBtn);
                }
            }
        });
        
        tblDailyTransactions.setItems(transactionData);

        // Top products table
        colProductRank.setCellValueFactory(new PropertyValueFactory<>("rank"));
        colProductName.setCellValueFactory(new PropertyValueFactory<>("productName"));
        colQuantitySold.setCellValueFactory(new PropertyValueFactory<>("quantitySold"));
        colProductRevenue.setCellValueFactory(new PropertyValueFactory<>("revenue"));
        tblTopProducts.setItems(topProductData);
    }

    private void setupDatePicker() {
        dateSelected.setValue(LocalDate.now());
    }

    private void loadDailySalesData() {
        try {
            LocalDate selectedDate = dateSelected.getValue();
            if (selectedDate == null) {
                selectedDate = LocalDate.now();
            }
            
            List<Transaction> dailyTransactions = transactionDAO.findByDate(selectedDate);
            transactionData.setAll(dailyTransactions);
            
            // Calculate summary metrics
            BigDecimal totalSales = dailyTransactions.stream()
                .map(Transaction::getTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            int totalTransactions = dailyTransactions.size();
            BigDecimal avgTransaction = totalTransactions > 0 ? 
                totalSales.divide(BigDecimal.valueOf(totalTransactions), 2, BigDecimal.ROUND_HALF_UP) : 
                BigDecimal.ZERO;
            
            long uniqueCustomers = dailyTransactions.stream()
                .mapToLong(Transaction::getCustomerId)
                .distinct()
                .count();
            
            // Update summary labels
            lblTotalSales.setText(currencyFormat.format(totalSales));
            lblTotalTransactions.setText(String.valueOf(totalTransactions));
            lblAvgTransaction.setText(currencyFormat.format(avgTransaction));
            lblUniqueCustomers.setText(String.valueOf(uniqueCustomers));
            
            // Load hourly breakdown (placeholder data)
            loadHourlySalesData(dailyTransactions);
            
            // Load top products (placeholder data)
            loadTopProductsData();
            
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load daily sales data: " + e.getMessage());
        }
    }

    private void loadHourlySalesData(List<Transaction> transactions) {
        hourlySalesData.clear();
        
        // Create placeholder hourly data
        for (int hour = 9; hour <= 21; hour++) {
            HourlySalesItem item = new HourlySalesItem();
            item.setHour(String.format("%02d:00 - %02d:59", hour, hour));
            item.setTransactions("0");
            item.setSales("$0.00");
            item.setAvgTransaction("$0.00");
            item.setPercentage("0%");
            
            hourlySalesData.add(item);
        }
    }

    private void loadTopProductsData() {
        topProductData.clear();
        
        // Create placeholder top products data
        for (int i = 1; i <= 5; i++) {
            TopProductItem item = new TopProductItem();
            item.setRank(String.valueOf(i));
            item.setProductName("Product " + i);
            item.setQuantitySold("0");
            item.setRevenue("$0.00");
            
            topProductData.add(item);
        }
    }

    @FXML
    private void handleDateChange() {
        loadDailySalesData();
    }

    @FXML
    private void handleToday() {
        dateSelected.setValue(LocalDate.now());
        loadDailySalesData();
    }

    @FXML
    private void handleRefresh() {
        loadDailySalesData();
        AlertUtil.showInfo("Refreshed", "Daily sales report has been refreshed.");
    }

    @FXML
    private void handleExport() {
        AlertUtil.showInfo("Export", "Daily sales report export functionality will be implemented in future version.");
    }

    @FXML
    private void handleSearch() {
        // Implement search functionality for transactions
        AlertUtil.showInfo("Search", "Transaction search functionality will be implemented in future version.");
    }

    private void handleViewTransaction(Transaction transaction) {
        AlertUtil.showInfo("Transaction Details", 
            String.format("Transaction Information:\n\nID: %d\nDate: %s\nCustomer ID: %d\nSubtotal: %s\nTax: %s\nTotal: %s\nPayment: %s", 
            transaction.getId(), transaction.getTransactionDate(), transaction.getCustomerId(),
            currencyFormat.format(transaction.getSubtotal()), currencyFormat.format(transaction.getTax()),
            currencyFormat.format(transaction.getTotal()), transaction.getPaymentMethod()));
    }

    // Inner classes for table data
    public static class HourlySalesItem {
        private String hour;
        private String transactions;
        private String sales;
        private String avgTransaction;
        private String percentage;

        // Getters and setters
        public String getHour() { return hour; }
        public void setHour(String hour) { this.hour = hour; }
        
        public String getTransactions() { return transactions; }
        public void setTransactions(String transactions) { this.transactions = transactions; }
        
        public String getSales() { return sales; }
        public void setSales(String sales) { this.sales = sales; }
        
        public String getAvgTransaction() { return avgTransaction; }
        public void setAvgTransaction(String avgTransaction) { this.avgTransaction = avgTransaction; }
        
        public String getPercentage() { return percentage; }
        public void setPercentage(String percentage) { this.percentage = percentage; }
    }

    public static class TopProductItem {
        private String rank;
        private String productName;
        private String quantitySold;
        private String revenue;

        // Getters and setters
        public String getRank() { return rank; }
        public void setRank(String rank) { this.rank = rank; }
        
        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }
        
        public String getQuantitySold() { return quantitySold; }
        public void setQuantitySold(String quantitySold) { this.quantitySold = quantitySold; }
        
        public String getRevenue() { return revenue; }
        public void setRevenue(String revenue) { this.revenue = revenue; }
    }
}
