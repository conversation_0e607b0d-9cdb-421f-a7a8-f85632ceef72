/* Main Application Styles */
.root {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 12px;
    -fx-background-color: #f5f5f5;
}

/* Menu Bar Styles */
.main-menu-bar {
    -fx-background-color: #2c3e50;
}

.main-menu-bar .menu-button {
    -fx-text-fill: white;
    -fx-background-color: transparent;
}

.main-menu-bar .menu-button:hover {
    -fx-background-color: #34495e;
}

.main-menu-bar .menu-item {
    -fx-background-color: white;
}

.main-menu-bar .menu-item:focused {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

/* Toolbar Styles */
.main-toolbar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 5;
}

.toolbar-button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.toolbar-button:hover {
    -fx-background-color: #2980b9;
}

.toolbar-button:pressed {
    -fx-background-color: #21618c;
}

.user-label, .time-label {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

/* Navigation Panel Styles */
.navigation-panel {
    -fx-background-color: #34495e;
    -fx-border-color: #2c3e50;
    -fx-border-width: 0 1 0 0;
}

.nav-header {
    -fx-text-fill: #ecf0f1;
    -fx-font-weight: bold;
    -fx-padding: 0 0 10 0;
}

.nav-buttons {
    -fx-spacing: 5;
}

.nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: #ecf0f1;
    -fx-alignment: CENTER_LEFT;
    -fx-padding: 12 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-size: 13px;
}

.nav-button:hover {
    -fx-background-color: #2c3e50;
}

.nav-button:pressed {
    -fx-background-color: #1a252f;
}

.nav-button.selected {
    -fx-background-color: #3498db;
}

/* Content Area Styles */
.content-area {
    -fx-background-color: white;
    -fx-padding: 20;
}

/* Welcome Panel Styles */
.welcome-panel {
    -fx-background-color: white;
    -fx-padding: 40;
}

.welcome-title {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 24px;
    -fx-font-weight: bold;
}

/* Quick Action Cards - Responsive Design */
.quick-action-card {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 25;
    -fx-min-width: 180;
    -fx-max-width: 300;
    -fx-min-height: 140;
    -fx-pref-height: 160;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.quick-action-card:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 4);
    -fx-cursor: hand;
}

.card-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.card-description {
    -fx-font-size: 12px;
    -fx-text-fill: #7f8c8d;
}

.card-button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-padding: 8 20;
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.card-button:hover {
    -fx-background-color: #2980b9;
}

/* Status Bar Styles */
.status-bar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1 0 0 0;
    -fx-font-size: 11px;
}

/* Table Styles - Enhanced for better space utilization */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-table-cell-border-color: #e8e8e8;
    -fx-font-size: 12px;
}

.table-view .column-header {
    -fx-background-color: #34495e;
    -fx-text-fill: white;
    -fx-font-weight: bold;
}

.table-view .column-header:hover {
    -fx-background-color: #2c3e50;
}

.table-row-cell {
    -fx-background-color: white;
}

.table-row-cell:odd {
    -fx-background-color: #f8f9fa;
}

.table-row-cell:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.table-row-cell:hover {
    -fx-background-color: #e8f4fd;
}

/* Form Styles */
.form-container {
    -fx-background-color: white;
    -fx-padding: 20;
    -fx-spacing: 15;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 4;
}

.form-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.form-label {
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.text-field, .text-area, .combo-box, .date-picker {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-padding: 8;
}

.text-field:focused, .text-area:focused, .combo-box:focused, .date-picker:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
}

/* Button Styles */
.button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.button:hover {
    -fx-background-color: #2980b9;
}

.button:pressed {
    -fx-background-color: #21618c;
}

.button.success {
    -fx-background-color: #27ae60;
}

.button.success:hover {
    -fx-background-color: #229954;
}

.button.warning {
    -fx-background-color: #f39c12;
}

.button.warning:hover {
    -fx-background-color: #e67e22;
}

.button.danger {
    -fx-background-color: #e74c3c;
}

.button.danger:hover {
    -fx-background-color: #c0392b;
}

.button.secondary {
    -fx-background-color: #95a5a6;
}

.button.secondary:hover {
    -fx-background-color: #7f8c8d;
}

/* Alert and Dialog Styles */
.alert {
    -fx-background-color: white;
}

.alert .header-panel {
    -fx-background-color: #34495e;
}

.alert .header-panel .label {
    -fx-text-fill: white;
    -fx-font-weight: bold;
}

/* Progress Indicator */
.progress-indicator {
    -fx-progress-color: #3498db;
}

.progress-bar {
    -fx-accent: #3498db;
}

/* Scroll Pane */
.scroll-pane {
    -fx-background-color: transparent;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

/* Tab Pane */
.tab-pane {
    -fx-tab-min-width: 100;
}

.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: #ecf0f1;
}

.tab-pane .tab {
    -fx-background-color: #bdc3c7;
    -fx-text-fill: #2c3e50;
}

.tab-pane .tab:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.tab-pane .tab:hover {
    -fx-background-color: #95a5a6;
}

/* Separator */
.separator {
    -fx-background-color: #bdc3c7;
}

/* Tooltip */
.tooltip {
    -fx-background-color: #2c3e50;
    -fx-text-fill: white;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
}

/* Stock Status Styles */
.stock-ok {
    -fx-text-fill: #27ae60;
    -fx-font-weight: bold;
}

.stock-low {
    -fx-text-fill: #f39c12;
    -fx-font-weight: bold;
}

.stock-out {
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

/* Validation Styles */
.field-error {
    -fx-border-color: #e74c3c;
    -fx-border-width: 2;
    -fx-background-color: #ffebee;
}

.field-success {
    -fx-border-color: #27ae60;
    -fx-border-width: 2;
    -fx-background-color: #e8f5e8;
}

.error-label {
    -fx-text-fill: #e74c3c;
    -fx-font-size: 11px;
    -fx-font-style: italic;
}

/* Loading Indicator */
.loading-overlay {
    -fx-background-color: rgba(0, 0, 0, 0.5);
}

.loading-spinner {
    -fx-progress-color: #3498db;
}

/* Modal Dialog */
.modal-dialog {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 10, 0, 0, 5);
}

.modal-header {
    -fx-background-color: #34495e;
    -fx-text-fill: white;
    -fx-padding: 15;
    -fx-background-radius: 8 8 0 0;
}

.modal-content {
    -fx-padding: 20;
}

.modal-footer {
    -fx-background-color: #ecf0f1;
    -fx-padding: 15;
    -fx-background-radius: 0 0 8 8;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1 0 0 0;
}

/* Data Grid Enhancements */
.data-grid {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 4;
}

.data-grid .column-header {
    -fx-background-color: linear-gradient(to bottom, #ecf0f1, #d5dbdb);
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 0 1 1 0;
}

.data-grid .table-row-cell:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.data-grid .table-row-cell:hover {
    -fx-background-color: #e8f4fd;
}

/* Dashboard Cards */
.dashboard-card {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 20;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 3, 0, 0, 1);
}

.dashboard-card:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 6, 0, 0, 3);
}

.dashboard-card .title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.dashboard-card .value {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #3498db;
}

.dashboard-card .subtitle {
    -fx-font-size: 12px;
    -fx-text-fill: #7f8c8d;
}

/* Notification Styles */
.notification {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-padding: 10;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 5, 0, 0, 2);
}

.notification.success {
    -fx-border-color: #27ae60;
    -fx-background-color: #d5f4e6;
}

.notification.warning {
    -fx-border-color: #f39c12;
    -fx-background-color: #fef9e7;
}

.notification.error {
    -fx-border-color: #e74c3c;
    -fx-background-color: #fadbd8;
}

.notification.info {
    -fx-border-color: #3498db;
    -fx-background-color: #ebf3fd;
}

/* Enhanced Search Box with Modern Design */
.search-box {
    -fx-background-color: white;
    -fx-border-color: #e1e5e9;
    -fx-border-width: 1.5;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-padding: 12 18;
    -fx-font-size: 14px;
    -fx-prompt-text-fill: #6c757d;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 4, 0, 0, 2);
    -fx-transition: all 0.2s ease;
}

.search-box:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
    -fx-effect: dropshadow(gaussian, rgba(0,123,255,0.25), 8, 0, 0, 2);
    -fx-background-color: #ffffff;
}

.search-box:hover {
    -fx-border-color: #007bff;
    -fx-border-width: 1.5;
}

/* Badge Styles */
.badge {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-background-radius: 10;
    -fx-padding: 2 8;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
}

.badge.success {
    -fx-background-color: #27ae60;
}

.badge.warning {
    -fx-background-color: #f39c12;
}

.badge.danger {
    -fx-background-color: #e74c3c;
}

.badge.secondary {
    -fx-background-color: #95a5a6;
}

/* Responsive Design Enhancements */
.split-pane {
    -fx-divider-positions: 0.55;
}

.split-pane .split-pane-divider {
    -fx-background-color: #bdc3c7;
    -fx-padding: 0 2 0 2;
}

/* Form Section Styles */
.form-section {
    -fx-background-color: white;
    -fx-border-color: #e8e8e8;
    -fx-border-width: 1;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.section-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

/* Header Section Styles */
.header-section {
    -fx-background-color: white;
    -fx-border-color: #e8e8e8;
    -fx-border-width: 0 0 1 0;
}

.page-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

/* Enhanced Filter Section with Modern Card Design */
.filter-section {
    -fx-background-color: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-padding: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2);
    -fx-spacing: 15;
}

/* Filter Labels with Better Typography */
.filter-label {
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-text-fill: #495057;
    -fx-padding: 0 8 0 0;
}

/* Enhanced ComboBox Styling */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #e1e5e9;
    -fx-border-width: 1.5;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 8 12;
    -fx-font-size: 13px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.04), 2, 0, 0, 1);
}

.combo-box:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
    -fx-effect: dropshadow(gaussian, rgba(0,123,255,0.15), 4, 0, 0, 1);
}

.combo-box:hover {
    -fx-border-color: #007bff;
    -fx-background-color: #f8f9fa;
}

.combo-box .arrow-button {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.combo-box .arrow {
    -fx-background-color: #6c757d;
    -fx-shape: "M 0 0 L 4 4 L 8 0 Z";
    -fx-scale-shape: true;
    -fx-padding: 4;
}

/* Filter Results Counter */
.filter-results {
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-text-fill: #28a745;
    -fx-background-color: rgba(40, 167, 69, 0.1);
    -fx-background-radius: 15;
    -fx-padding: 4 12;
    -fx-border-color: rgba(40, 167, 69, 0.2);
    -fx-border-width: 1;
    -fx-border-radius: 15;
}

/* Enhanced Clear Filters Button */
.clear-filters-button {
    -fx-background-color: linear-gradient(to bottom, #6c757d 0%, #5a6268 100%);
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-padding: 8 16;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 3, 0, 0, 1);
    -fx-cursor: hand;
}

.clear-filters-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5a6268 0%, #495057 100%);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.25), 4, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.clear-filters-button:pressed {
    -fx-background-color: #495057;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 2, 0, 0, 1);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* Filter Section Animation */
.filter-section:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 3);
}

/* Search Icon Enhancement */
.search-icon {
    -fx-text-fill: #6c757d;
    -fx-font-size: 14px;
    -fx-padding: 0 8 0 0;
}

/* Filter Row Spacing */
.filter-row {
    -fx-spacing: 15;
    -fx-alignment: CENTER_LEFT;
    -fx-padding: 5 0;
}

/* Status Indicators */
.status-active {
    -fx-text-fill: #28a745;
    -fx-font-weight: 600;
}

.status-inactive {
    -fx-text-fill: #dc3545;
    -fx-font-weight: 600;
}

/* Responsive Filter Layout */
.filter-container {
    -fx-spacing: 12;
    -fx-padding: 15;
    -fx-background-color: #ffffff;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
}

/* Sales Analytics Dashboard Styles */
.kpi-section {
    -fx-background-color: #f8f9fa;
    -fx-padding: 15;
}

.kpi-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-padding: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
    -fx-min-width: 180;
    -fx-pref-width: 200;
}

.kpi-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 8, 0, 0, 3);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.kpi-title {
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-text-fill: #6c757d;
    -fx-text-alignment: center;
}

.kpi-value {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-text-alignment: center;
}

.kpi-growth {
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-text-alignment: center;
}

.kpi-subtitle {
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
    -fx-text-alignment: center;
}

.chart-container {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-padding: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 4, 0, 0, 2);
}

.chart-title {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #2c3e50;
    -fx-padding: 0 0 10 0;
}

.table-container {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-padding: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 4, 0, 0, 2);
}

/* Chart Styling */
.chart {
    -fx-background-color: transparent;
    -fx-padding: 10;
}

.chart-plot-background {
    -fx-background-color: #fafafa;
}

.chart-legend {
    -fx-background-color: transparent;
    -fx-padding: 5;
}

.default-color0.chart-series-line { -fx-stroke: #007bff; -fx-stroke-width: 3px; }
.default-color1.chart-series-line { -fx-stroke: #28a745; -fx-stroke-width: 3px; }
.default-color2.chart-series-line { -fx-stroke: #ffc107; -fx-stroke-width: 3px; }

.default-color0.chart-area-symbol { -fx-background-color: #007bff; }
.default-color1.chart-area-symbol { -fx-background-color: #28a745; }
.default-color2.chart-area-symbol { -fx-background-color: #ffc107; }

.default-color0.chart-bar { -fx-bar-fill: linear-gradient(to bottom, #007bff 0%, #0056b3 100%); }
.default-color1.chart-bar { -fx-bar-fill: linear-gradient(to bottom, #28a745 0%, #1e7e34 100%); }
.default-color2.chart-bar { -fx-bar-fill: linear-gradient(to bottom, #ffc107 0%, #e0a800 100%); }

/* Responsive Design for Analytics */
@media screen and (max-width: 1200px) {
    .kpi-card {
        -fx-min-width: 150;
        -fx-pref-width: 170;
    }

    .kpi-value {
        -fx-font-size: 24px;
    }
}

/* Advanced Inventory Management Styles */
.summary-section {
    -fx-background-color: #f8f9fa;
    -fx-padding: 15;
}

.summary-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 4, 0, 0, 2);
    -fx-min-width: 140;
    -fx-pref-width: 160;
}

.summary-card.warning {
    -fx-border-color: #ffc107;
    -fx-background-color: #fff8e1;
}

.summary-card.danger {
    -fx-border-color: #dc3545;
    -fx-background-color: #ffebee;
}

.summary-card.success {
    -fx-border-color: #28a745;
    -fx-background-color: #e8f5e8;
}

.summary-card.info {
    -fx-border-color: #17a2b8;
    -fx-background-color: #e1f5fe;
}

.summary-title {
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-text-fill: #6c757d;
    -fx-text-alignment: center;
}

.summary-value {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-text-alignment: center;
}

/* Stock Level Row Styling */
.table-row-cell:low-stock {
    -fx-background-color: #fff3e0;
    -fx-border-color: #ff9800;
}

.table-row-cell:out-of-stock {
    -fx-background-color: #ffebee;
    -fx-border-color: #f44336;
}

.table-row-cell:in-stock {
    -fx-background-color: #e8f5e8;
    -fx-border-color: #4caf50;
}

/* Form Section Styling */
.form-section {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.04), 3, 0, 0, 1);
}

/* Tab Styling */
.tab-pane .tab {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1 1 0 1;
    -fx-border-radius: 8 8 0 0;
    -fx-background-radius: 8 8 0 0;
    -fx-padding: 8 16;
}

.tab-pane .tab:selected {
    -fx-background-color: white;
    -fx-border-color: #007bff;
    -fx-border-width: 2 2 0 2;
}

.tab-pane .tab-content-area {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 0 0 8 8;
    -fx-background-radius: 0 0 8 8;
}

/* Action Button Styling in Tables */
.table-cell .button {
    -fx-font-size: 11px;
    -fx-padding: 4 8;
    -fx-min-width: 60;
}

/* Inventory Status Indicators */
.status-indicator {
    -fx-padding: 2 8;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-font-size: 11px;
    -fx-font-weight: 600;
}

.status-indicator.in-stock {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
}

.status-indicator.low-stock {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
}

.status-indicator.out-of-stock {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-border-color: #f5c6cb;
}

/* Cash Drawer Management Styles */
.current-drawer-section {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
}

.status-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-padding: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
}

.card-title {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #2c3e50;
    -fx-padding: 0 0 10 0;
}

.info-label {
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-text-fill: #6c757d;
}

.info-value {
    -fx-font-size: 13px;
    -fx-font-weight: 500;
    -fx-text-fill: #2c3e50;
}

.info-value.money {
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
}

.info-value.expected {
    -fx-font-size: 14px;
    -fx-text-fill: #007bff;
}

.status-badge {
    -fx-padding: 2 8;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-font-weight: 600;
    -fx-font-size: 11px;
}

.metric-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
}

.metric-value {
    -fx-font-size: 32px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

/* Action Buttons */
.action-button {
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-padding: 10 20;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);
}

.action-button.open {
    -fx-background-color: linear-gradient(to bottom, #28a745 0%, #1e7e34 100%);
    -fx-text-fill: white;
}

.action-button.close {
    -fx-background-color: linear-gradient(to bottom, #dc3545 0%, #c82333 100%);
    -fx-text-fill: white;
}

.action-button.drop {
    -fx-background-color: linear-gradient(to bottom, #ffc107 0%, #e0a800 100%);
    -fx-text-fill: #212529;
}

.action-button.payout {
    -fx-background-color: linear-gradient(to bottom, #17a2b8 0%, #138496 100%);
    -fx-text-fill: white;
}

.action-button.reconcile {
    -fx-background-color: linear-gradient(to bottom, #6f42c1 0%, #5a32a3 100%);
    -fx-text-fill: white;
}

.action-button:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.action-button:pressed {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 2, 0, 0, 1);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

.action-button:disabled {
    -fx-opacity: 0.6;
    -fx-effect: none;
}

/* Cash Drawer Table Row Styling */
.table-row-cell.drawer-shortage {
    -fx-background-color: #ffebee;
    -fx-border-color: #f44336;
}

.table-row-cell.drawer-overage {
    -fx-background-color: #fff3e0;
    -fx-border-color: #ff9800;
}

.table-row-cell.drawer-reconciled {
    -fx-background-color: #e8f5e8;
    -fx-border-color: #4caf50;
}

/* Return/Exchange Management Styles */
.details-section {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.04), 3, 0, 0, 1);
}

.detail-label {
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-text-fill: #6c757d;
}

.detail-value {
    -fx-font-size: 13px;
    -fx-font-weight: 500;
    -fx-text-fill: #2c3e50;
}

.detail-value.money {
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
    -fx-font-size: 14px;
}

.notes-area {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-font-size: 12px;
}

/* Return/Exchange Action Buttons */
.action-button.approve {
    -fx-background-color: linear-gradient(to bottom, #28a745 0%, #1e7e34 100%);
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-padding: 8 16;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
}

.action-button.reject {
    -fx-background-color: linear-gradient(to bottom, #dc3545 0%, #c82333 100%);
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-padding: 8 16;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
}

.action-button.complete {
    -fx-background-color: linear-gradient(to bottom, #007bff 0%, #0056b3 100%);
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-padding: 8 16;
    -fx-border-radius: 6;
    -fx-background-radius: 6;
}

.action-button:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.action-button:pressed {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 2, 0, 0, 1);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

.action-button:disabled {
    -fx-opacity: 0.6;
    -fx-effect: none;
}

/* Return/Exchange Status Row Styling */
.table-row-cell.return-pending {
    -fx-background-color: #fff3cd;
    -fx-border-color: #ffc107;
}

.table-row-cell.return-approved {
    -fx-background-color: #d1ecf1;
    -fx-border-color: #17a2b8;
}

.table-row-cell.return-rejected {
    -fx-background-color: #f8d7da;
    -fx-border-color: #dc3545;
}

.table-row-cell.return-completed {
    -fx-background-color: #d4edda;
    -fx-border-color: #28a745;
}

/* Email Receipt Management Styles */
.action-button.retry {
    -fx-background-color: linear-gradient(to bottom, #17a2b8 0%, #138496 100%);
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-padding: 6 12;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
}

.action-button.resend {
    -fx-background-color: linear-gradient(to bottom, #6f42c1 0%, #5a32a3 100%);
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-padding: 6 12;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
}

.action-button.delete {
    -fx-background-color: linear-gradient(to bottom, #dc3545 0%, #c82333 100%);
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-padding: 6 12;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
}

/* Email Status Row Styling */
.table-row-cell.email-pending {
    -fx-background-color: #fff3cd;
    -fx-border-color: #ffc107;
}

.table-row-cell.email-sent {
    -fx-background-color: #d4edda;
    -fx-border-color: #28a745;
}

.table-row-cell.email-failed {
    -fx-background-color: #f8d7da;
    -fx-border-color: #dc3545;
}

.table-row-cell.email-retry {
    -fx-background-color: #d1ecf1;
    -fx-border-color: #17a2b8;
}

/* WebView Styling */
.web-view {
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

/* Statistics Section Styles */
.stats-section {
    -fx-background-color: #e8f4fd;
    -fx-border-color: #3498db;
    -fx-border-width: 0 0 1 0;
}

/* Action Button Styles */
.primary-button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.primary-button:hover {
    -fx-background-color: #2980b9;
}

.secondary-button {
    -fx-background-color: #95a5a6;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.secondary-button:hover {
    -fx-background-color: #7f8c8d;
}

.action-button {
    -fx-background-color: #f39c12;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.action-button:hover {
    -fx-background-color: #e67e22;
}

.danger-button {
    -fx-background-color: #e74c3c;
    -fx-text-fill: white;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.danger-button:hover {
    -fx-background-color: #c0392b;
}
