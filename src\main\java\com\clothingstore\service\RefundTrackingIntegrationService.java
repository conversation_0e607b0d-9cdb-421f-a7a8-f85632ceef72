package com.clothingstore.service;

import com.clothingstore.dao.PaymentHistoryDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.*;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;

/**
 * Integration service for comprehensive refund tracking across payment history and outstanding balances
 * Provides unified access to refund-related data and operations
 */
public class RefundTrackingIntegrationService {
    
    private static final Logger LOGGER = Logger.getLogger(RefundTrackingIntegrationService.class.getName());
    private static RefundTrackingIntegrationService instance;
    
    private final PaymentHistoryService paymentHistoryService;
    private final OutstandingBalanceRefundService outstandingBalanceService;
    private final TransactionDAO transactionDAO;
    private final PaymentHistoryDAO paymentHistoryDAO;
    
    private RefundTrackingIntegrationService() {
        this.paymentHistoryService = PaymentHistoryService.getInstance();
        this.outstandingBalanceService = OutstandingBalanceRefundService.getInstance();
        this.transactionDAO = TransactionDAO.getInstance();
        this.paymentHistoryDAO = PaymentHistoryDAO.getInstance();
    }
    
    public static synchronized RefundTrackingIntegrationService getInstance() {
        if (instance == null) {
            instance = new RefundTrackingIntegrationService();
        }
        return instance;
    }
    
    /**
     * Get comprehensive refund summary for a transaction
     */
    public RefundSummary getRefundSummary(Long transactionId) throws SQLException {
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            return null;
        }
        
        Transaction transaction = transactionOpt.get();
        List<PaymentHistory> refundHistory = paymentHistoryService.getRefundHistory(transactionId);
        BigDecimal totalRefunded = paymentHistoryService.getTotalAmountRefunded(transactionId);
        BigDecimal totalPaid = paymentHistoryService.getTotalAmountPaid(transactionId);
        
        return new RefundSummary(
            transactionId,
            transaction.getTotalAmount(),
            totalPaid,
            totalRefunded,
            transaction.getRemainingBalance(),
            refundHistory,
            transaction.getStatus(),
            transaction.hasOutstandingBalance()
        );
    }
    
    /**
     * Get detailed refund audit trail for a transaction
     */
    public RefundAuditTrail getRefundAuditTrail(Long transactionId) throws SQLException {
        List<PaymentHistory> allHistory = paymentHistoryService.getPaymentHistory(transactionId);
        List<PaymentHistory> refundHistory = paymentHistoryService.getRefundHistory(transactionId);
        List<PaymentHistory> paymentHistory = paymentHistoryService.getPaymentHistoryOnly(transactionId);
        
        RefundAuditTrail auditTrail = new RefundAuditTrail(transactionId);
        auditTrail.setAllPaymentHistory(allHistory);
        auditTrail.setRefundHistory(refundHistory);
        auditTrail.setPaymentHistory(paymentHistory);
        
        // Calculate running totals and balances
        BigDecimal runningPaid = BigDecimal.ZERO;
        BigDecimal runningRefunded = BigDecimal.ZERO;
        
        for (PaymentHistory history : allHistory) {
            if (history.getPaymentType() == PaymentHistory.PaymentType.PAYMENT) {
                runningPaid = runningPaid.add(history.getPaymentAmount());
            } else if (history.getPaymentType() == PaymentHistory.PaymentType.REFUND) {
                runningRefunded = runningRefunded.add(history.getPaymentAmount());
            }
        }
        
        auditTrail.setTotalPaid(runningPaid);
        auditTrail.setTotalRefunded(runningRefunded);
        auditTrail.setNetAmount(runningPaid.subtract(runningRefunded));
        
        return auditTrail;
    }
    
    /**
     * Check if transaction has any refunds
     */
    public boolean hasRefunds(Long transactionId) throws SQLException {
        BigDecimal totalRefunded = paymentHistoryService.getTotalAmountRefunded(transactionId);
        return totalRefunded.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * Get refund impact on outstanding balance
     */
    public RefundBalanceImpact getRefundBalanceImpact(Long transactionId, BigDecimal refundAmount) throws SQLException {
        OutstandingBalanceRefundService.OutstandingBalanceInfo currentInfo = 
            outstandingBalanceService.getOutstandingBalanceInfo(transactionId);
        
        if (currentInfo == null) {
            return null;
        }
        
        boolean willHaveOutstandingBalance = outstandingBalanceService.willHaveOutstandingBalanceAfterRefund(transactionId, refundAmount);
        BigDecimal newRemainingBalance = currentInfo.getRemainingBalance().add(refundAmount);
        
        return new RefundBalanceImpact(
            transactionId,
            refundAmount,
            currentInfo.getRemainingBalance(),
            newRemainingBalance,
            currentInfo.hasOutstandingBalance(),
            willHaveOutstandingBalance,
            currentInfo.getTotalPaid(),
            currentInfo.getTotalRefunded().add(refundAmount)
        );
    }
    
    /**
     * Validate refund request with comprehensive checks
     */
    public RefundValidationResult validateRefundRequest(Long transactionId, BigDecimal refundAmount) throws SQLException {
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            return new RefundValidationResult(false, "Transaction not found");
        }
        
        Transaction transaction = transactionOpt.get();
        
        // Check transaction status
        if (!"COMPLETED".equals(transaction.getStatus()) && 
            !"PARTIALLY_REFUNDED".equals(transaction.getStatus())) {
            return new RefundValidationResult(false, "Transaction must be completed or partially refunded to process refunds");
        }
        
        // Check refund amount
        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return new RefundValidationResult(false, "Refund amount must be greater than zero");
        }
        
        // Check available amount for refund
        BigDecimal totalPaid = paymentHistoryService.getTotalAmountPaid(transactionId);
        BigDecimal totalRefunded = paymentHistoryService.getTotalAmountRefunded(transactionId);
        BigDecimal availableForRefund = totalPaid.subtract(totalRefunded);
        
        if (refundAmount.compareTo(availableForRefund) > 0) {
            return new RefundValidationResult(false, 
                "Refund amount (" + refundAmount + ") exceeds available amount for refund (" + availableForRefund + ")");
        }
        
        return new RefundValidationResult(true, "Refund request is valid");
    }
    
    /**
     * Inner class for refund summary information
     */
    public static class RefundSummary {
        private final Long transactionId;
        private final BigDecimal transactionTotal;
        private final BigDecimal totalPaid;
        private final BigDecimal totalRefunded;
        private final BigDecimal remainingBalance;
        private final List<PaymentHistory> refundHistory;
        private final String transactionStatus;
        private final boolean hasOutstandingBalance;
        
        public RefundSummary(Long transactionId, BigDecimal transactionTotal, BigDecimal totalPaid, 
                           BigDecimal totalRefunded, BigDecimal remainingBalance, 
                           List<PaymentHistory> refundHistory, String transactionStatus, 
                           boolean hasOutstandingBalance) {
            this.transactionId = transactionId;
            this.transactionTotal = transactionTotal;
            this.totalPaid = totalPaid;
            this.totalRefunded = totalRefunded;
            this.remainingBalance = remainingBalance;
            this.refundHistory = refundHistory;
            this.transactionStatus = transactionStatus;
            this.hasOutstandingBalance = hasOutstandingBalance;
        }
        
        // Getters
        public Long getTransactionId() { return transactionId; }
        public BigDecimal getTransactionTotal() { return transactionTotal; }
        public BigDecimal getTotalPaid() { return totalPaid; }
        public BigDecimal getTotalRefunded() { return totalRefunded; }
        public BigDecimal getRemainingBalance() { return remainingBalance; }
        public List<PaymentHistory> getRefundHistory() { return refundHistory; }
        public String getTransactionStatus() { return transactionStatus; }
        public boolean hasOutstandingBalance() { return hasOutstandingBalance; }
        
        public BigDecimal getNetAmountPaid() {
            return totalPaid.subtract(totalRefunded);
        }
        
        public int getRefundCount() {
            return refundHistory != null ? refundHistory.size() : 0;
        }
        
        public boolean hasRefunds() {
            return totalRefunded.compareTo(BigDecimal.ZERO) > 0;
        }
    }
    
    /**
     * Inner class for refund audit trail
     */
    public static class RefundAuditTrail {
        private final Long transactionId;
        private List<PaymentHistory> allPaymentHistory;
        private List<PaymentHistory> refundHistory;
        private List<PaymentHistory> paymentHistory;
        private BigDecimal totalPaid;
        private BigDecimal totalRefunded;
        private BigDecimal netAmount;
        
        public RefundAuditTrail(Long transactionId) {
            this.transactionId = transactionId;
            this.allPaymentHistory = new ArrayList<>();
            this.refundHistory = new ArrayList<>();
            this.paymentHistory = new ArrayList<>();
        }
        
        // Getters and Setters
        public Long getTransactionId() { return transactionId; }
        public List<PaymentHistory> getAllPaymentHistory() { return allPaymentHistory; }
        public void setAllPaymentHistory(List<PaymentHistory> allPaymentHistory) { this.allPaymentHistory = allPaymentHistory; }
        public List<PaymentHistory> getRefundHistory() { return refundHistory; }
        public void setRefundHistory(List<PaymentHistory> refundHistory) { this.refundHistory = refundHistory; }
        public List<PaymentHistory> getPaymentHistory() { return paymentHistory; }
        public void setPaymentHistory(List<PaymentHistory> paymentHistory) { this.paymentHistory = paymentHistory; }
        public BigDecimal getTotalPaid() { return totalPaid; }
        public void setTotalPaid(BigDecimal totalPaid) { this.totalPaid = totalPaid; }
        public BigDecimal getTotalRefunded() { return totalRefunded; }
        public void setTotalRefunded(BigDecimal totalRefunded) { this.totalRefunded = totalRefunded; }
        public BigDecimal getNetAmount() { return netAmount; }
        public void setNetAmount(BigDecimal netAmount) { this.netAmount = netAmount; }
    }
    
    /**
     * Inner class for refund balance impact analysis
     */
    public static class RefundBalanceImpact {
        private final Long transactionId;
        private final BigDecimal refundAmount;
        private final BigDecimal currentRemainingBalance;
        private final BigDecimal newRemainingBalance;
        private final boolean currentlyHasOutstandingBalance;
        private final boolean willHaveOutstandingBalance;
        private final BigDecimal totalPaid;
        private final BigDecimal newTotalRefunded;
        
        public RefundBalanceImpact(Long transactionId, BigDecimal refundAmount, 
                                 BigDecimal currentRemainingBalance, BigDecimal newRemainingBalance,
                                 boolean currentlyHasOutstandingBalance, boolean willHaveOutstandingBalance,
                                 BigDecimal totalPaid, BigDecimal newTotalRefunded) {
            this.transactionId = transactionId;
            this.refundAmount = refundAmount;
            this.currentRemainingBalance = currentRemainingBalance;
            this.newRemainingBalance = newRemainingBalance;
            this.currentlyHasOutstandingBalance = currentlyHasOutstandingBalance;
            this.willHaveOutstandingBalance = willHaveOutstandingBalance;
            this.totalPaid = totalPaid;
            this.newTotalRefunded = newTotalRefunded;
        }
        
        // Getters
        public Long getTransactionId() { return transactionId; }
        public BigDecimal getRefundAmount() { return refundAmount; }
        public BigDecimal getCurrentRemainingBalance() { return currentRemainingBalance; }
        public BigDecimal getNewRemainingBalance() { return newRemainingBalance; }
        public boolean currentlyHasOutstandingBalance() { return currentlyHasOutstandingBalance; }
        public boolean willHaveOutstandingBalance() { return willHaveOutstandingBalance; }
        public BigDecimal getTotalPaid() { return totalPaid; }
        public BigDecimal getNewTotalRefunded() { return newTotalRefunded; }
        
        public boolean balanceStatusWillChange() {
            return currentlyHasOutstandingBalance != willHaveOutstandingBalance;
        }
        
        public BigDecimal getBalanceChange() {
            return newRemainingBalance.subtract(currentRemainingBalance);
        }
    }
}
