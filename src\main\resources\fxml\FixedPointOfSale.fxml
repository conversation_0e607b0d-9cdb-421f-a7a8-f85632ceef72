<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.SimplePOSController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #2c3e50; -fx-padding: 15;">
         <children>
            <Label style="-fx-text-fill: white; -fx-font-size: 18px; -fx-font-weight: bold;" text="Point of Sale">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </Region>
            <Label fx:id="lblTransactionNumber" style="-fx-text-fill: white;" text="Transaction: TXN000000" />
            <Label fx:id="lblCashier" style="-fx-text-fill: white;" text="Cashier: Admin" />
            <Button fx:id="btnNewTransaction" onAction="#handleNewTransaction" style="-fx-background-color: #3498db; -fx-text-fill: white;" text="🔄 New Transaction" />
            <Button fx:id="btnRefundTransaction" onAction="#handleRefundTransaction" style="-fx-background-color: #e67e22; -fx-text-fill: white;" text="💵 Refund Transaction" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Main Content - Responsive layout -->
      <HBox spacing="18.0">
         <VBox.vgrow>ALWAYS</VBox.vgrow>
         <children>
            <!-- Product Search - Flexible width -->
            <VBox minWidth="350.0" prefWidth="450.0" spacing="12.0">
               <HBox.hgrow>SOMETIMES</HBox.hgrow>
               <children>
                  <Label style="-fx-font-size: 14px; -fx-font-weight: bold;" text="Product Search">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>

                  <HBox spacing="12.0">
                     <children>
                        <TextField fx:id="txtProductSearch" promptText="Search products, SKU, or barcode...">
                           <HBox.hgrow>ALWAYS</HBox.hgrow>
                        </TextField>
                        <Button fx:id="btnScanBarcode" onAction="#handleScanBarcode" style="-fx-background-color: #9b59b6; -fx-text-fill: white;" text="📷 Scan" />
                     </children>
                  </HBox>

                  <!-- Product Filters -->
                  <HBox spacing="10.0">
                     <children>
                        <Label style="-fx-font-weight: bold;" text="Category:" />
                        <ComboBox fx:id="cmbProductCategory" onAction="#handleCategoryFilter" prefWidth="120.0" promptText="All Categories" />
                        <Label style="-fx-font-weight: bold;" text="Supplier:" />
                        <ComboBox fx:id="cmbProductSupplier" onAction="#handleSupplierFilter" prefWidth="130.0" promptText="All Suppliers" />
                        <Button fx:id="btnClearSearch" onAction="#handleClearSearch" style="-fx-background-color: #95a5a6; -fx-text-fill: white;" text="🗑 Clear" />
                     </children>
                  </HBox>

                  <!-- Office Features Toolbar -->
                  <HBox spacing="8.0">
                     <children>
                        <Button fx:id="btnQuickAddProduct" onAction="#handleQuickAddProduct" style="-fx-background-color: #27ae60; -fx-text-fill: white;" text="+ Add Product" />
                        <Button fx:id="btnEditProduct" onAction="#handleEditProduct" style="-fx-background-color: #f39c12; -fx-text-fill: white;" text="✏ Edit" />
                        <Button fx:id="btnAdjustStock" onAction="#handleAdjustStock" style="-fx-background-color: #8e44ad; -fx-text-fill: white;" text="📦 Stock" />
                        <Button fx:id="btnGenerateBarcode" onAction="#handleGenerateBarcode" style="-fx-background-color: #34495e; -fx-text-fill: white;" text="🏷 Barcode" />
                     </children>
                  </HBox>

                  <TableView fx:id="tblProducts" prefHeight="320.0">
                     <VBox.vgrow>ALWAYS</VBox.vgrow>
                     <columns>
                        <TableColumn fx:id="colProductSku" prefWidth="80.0" text="SKU" />
                        <TableColumn fx:id="colProductName" prefWidth="160.0" text="Product" />
                        <TableColumn fx:id="colProductSupplier" prefWidth="120.0" text="Supplier" />
                        <TableColumn fx:id="colProductPrice" prefWidth="70.0" text="Price" />
                        <TableColumn fx:id="colProductStock" prefWidth="60.0" text="Stock" />
                     </columns>
                  </TableView>
               </children>
               <padding>
                  <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
               </padding>
            </VBox>

            <!-- Enhanced Shopping Cart -->
            <VBox spacing="12.0" minWidth="400.0" prefWidth="500.0" style="-fx-background-color: #ecf0f1; -fx-border-color: #bdc3c7; -fx-border-width: 1;">
               <HBox.hgrow>ALWAYS</HBox.hgrow>
               <children>
                  <Label style="-fx-font-size: 14px; -fx-font-weight: bold;" text="🛒 Shopping Cart">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>

                  <TableView fx:id="tblCartItems" prefHeight="280.0">
                     <VBox.vgrow>ALWAYS</VBox.vgrow>
                     <columns>
                        <TableColumn fx:id="colCartProduct" prefWidth="220.0" text="Product" />
                        <TableColumn fx:id="colCartQuantity" prefWidth="90.0" text="Qty" />
                        <TableColumn fx:id="colCartUnitPrice" prefWidth="90.0" text="Unit Price" />
                        <TableColumn fx:id="colCartTotal" prefWidth="90.0" text="Total" />
                     </columns>
                  </TableView>

                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #bdc3c7; -fx-border-width: 1;">
                     <children>
                        <HBox spacing="12.0">
                           <children>
                              <Label text="Subtotal:" />
                              <Region>
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Region>
                              <Label fx:id="lblSubtotal" style="-fx-font-weight: bold; -fx-text-fill: #27ae60;" text="$0.00" />
                           </children>
                        </HBox>
                        <Separator />
                        <HBox spacing="12.0">
                           <children>
                              <Label style="-fx-font-weight: bold; -fx-font-size: 16px;" text="Total:" />
                              <Region>
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Region>
                              <Label fx:id="lblTotal" style="-fx-font-weight: bold; -fx-font-size: 18px; -fx-text-fill: #27ae60;" text="$0.00" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
               </padding>
            </VBox>
         </children>
      </HBox>

      <!-- Payment Section -->
      <HBox spacing="20.0" style="-fx-background-color: #f8f9fa; -fx-padding: 15;">
         <children>
            <!-- Enhanced Customer Section -->
            <VBox spacing="12.0" minWidth="400.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1;">
               <HBox.hgrow>ALWAYS</HBox.hgrow>
               <children>
                  <Label style="-fx-font-size: 14px; -fx-font-weight: bold;" text="Customer">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>

                  <!-- Customer Search Row -->
                  <HBox spacing="8.0" alignment="CENTER_LEFT">
                     <children>
                        <TextField fx:id="txtCustomerSearch" onKeyReleased="#handleCustomerSearch" promptText="Search by name, phone, email...">
                           <HBox.hgrow>ALWAYS</HBox.hgrow>
                        </TextField>
                        <ComboBox fx:id="cmbCustomerGroup" onAction="#handleCustomerGroupFilter" promptText="Group" prefWidth="80.0" />
                        <Button fx:id="btnClearCustomer" onAction="#handleClearCustomerSearch" style="-fx-background-color: #e74c3c; -fx-text-fill: white;" text="✕" />
                     </children>
                  </HBox>

                  <!-- Selected Customer Info -->
                  <VBox fx:id="customerInfo" spacing="6.0" visible="false" managed="false" style="-fx-background-color: #e8f5e8; -fx-padding: 10; -fx-border-color: #27ae60; -fx-border-width: 1;">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label fx:id="lblCustomerName" style="-fx-font-weight: bold;" text="Customer Name" />
                              <Region>
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Region>
                              <Button fx:id="btnRemoveCustomer" onAction="#handleRemoveCustomer" style="-fx-background-color: #e74c3c; -fx-text-fill: white;" text="Remove" />
                           </children>
                        </HBox>
                        <HBox spacing="15.0">
                           <children>
                              <Label fx:id="lblCustomerPhone" text="Phone: " />
                              <Label fx:id="lblCustomerEmail" text="Email: " />
                           </children>
                        </HBox>
                        <HBox spacing="15.0">
                           <children>
                              <Label fx:id="lblCustomerGroup" text="Group: " />
                              <Label fx:id="lblCustomerPoints" style="-fx-text-fill: #f39c12; -fx-font-weight: bold;" text="Points: 0" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Default Customer Display -->
                  <Label fx:id="lblSelectedCustomer" style="-fx-text-fill: #6c757d;" text="Walk-in Customer" />

                  <!-- Action Buttons -->
                  <HBox spacing="8.0">
                     <children>
                        <Button fx:id="btnNewCustomer" onAction="#handleNewCustomer" style="-fx-background-color: #17a2b8; -fx-text-fill: white;" text="👤 New Customer" />
                        <Button fx:id="btnFindTransaction" onAction="#handleFindTransaction" style="-fx-background-color: #6f42c1; -fx-text-fill: white;" text="🔍 Find Transaction" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
               </padding>
            </VBox>

            <!-- Payment Section -->
            <VBox spacing="12.0" minWidth="350.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1;">
               <HBox.hgrow>ALWAYS</HBox.hgrow>
               <children>
                  <Label style="-fx-font-size: 14px; -fx-font-weight: bold;" text="💳 Payment Processing">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>

                  <VBox spacing="10.0">
                     <children>
                        <Label text="Payment Method:" />
                        <ComboBox fx:id="cmbPaymentMethod" prefWidth="220.0" promptText="Select Payment Method" />
                     </children>
                  </VBox>

                  <VBox spacing="10.0">
                     <children>
                        <Label text="Amount Received:" />
                        <TextField fx:id="txtAmountReceived" prefWidth="170.0" promptText="0.00" />
                     </children>
                  </VBox>

                  <HBox spacing="12.0" style="-fx-background-color: #f8f9fa; -fx-padding: 10;">
                     <children>
                        <Label text="Change:" />
                        <Region>
                           <HBox.hgrow>ALWAYS</HBox.hgrow>
                        </Region>
                        <Label fx:id="lblChange" style="-fx-font-weight: bold; -fx-text-fill: #27ae60;" text="$0.00" />
                     </children>
                  </HBox>

                  <!-- Enhanced Action Buttons -->
                  <VBox spacing="12.0">
                     <children>
                        <Button fx:id="btnProcessPayment" onAction="#handleProcessPayment" prefHeight="50.0" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;" text="💳 Process Payment" />
                        <Button fx:id="btnMultiplePayments" onAction="#handleMultiplePayments" prefHeight="45.0" style="-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;" text="💰 Multiple Payment Methods" />
                        <HBox spacing="12.0">
                           <children>
                              <Button fx:id="btnHoldTransaction" onAction="#handleHoldTransaction" style="-fx-background-color: #ffc107; -fx-text-fill: black; -fx-font-weight: bold;" text="⏸ Hold">
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Button>
                              <Button fx:id="btnVoidTransaction" onAction="#handleVoidTransaction" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold;" text="❌ Void">
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Button>
                           </children>
                        </HBox>
                        <Button fx:id="btnPrintReceipt" onAction="#handlePrintReceipt" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold;" text="🖨 Print Receipt" />
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
               </padding>
            </VBox>
         </children>
      </HBox>

      <!-- Status Bar -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #343a40; -fx-padding: 10;">
         <children>
            <Label fx:id="lblStatus" style="-fx-text-fill: white;" text="Ready for new transaction" />
            <Region>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </Region>
            <Label fx:id="lblItemCount" style="-fx-text-fill: white;" text="Items: 0" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>
   </children>
</VBox>
