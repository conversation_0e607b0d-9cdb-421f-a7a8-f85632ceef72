import com.clothingstore.database.DatabaseManager;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class CheckAvailableTransactions {
    public static void main(String[] args) {
        try {
            System.out.println("=== CHECKING AVAILABLE TRANSACTIONS ===");
            
            try (Connection conn = DatabaseManager.getInstance().getConnection()) {
                
                // Check all transactions with their status and item counts
                String sql = "SELECT t.id, t.transaction_number, t.total_amount, t.status, " +
                           "COUNT(ti.id) as item_count " +
                           "FROM transactions t " +
                           "LEFT JOIN transaction_items ti ON t.id = ti.transaction_id " +
                           "GROUP BY t.id, t.transaction_number, t.total_amount, t.status " +
                           "ORDER BY t.id DESC LIMIT 20";
                
                try (PreparedStatement stmt = conn.prepareStatement(sql); ResultSet rs = stmt.executeQuery()) {
                    System.out.println("Recent Transactions:");
                    System.out.println("ID | Transaction Number | Amount | Status | Items");
                    System.out.println("------------------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%d | %s | $%.2f | %s | %d%n",
                            rs.getLong("id"),
                            rs.getString("transaction_number"),
                            rs.getDouble("total_amount"),
                            rs.getString("status"),
                            rs.getInt("item_count")
                        );
                    }
                }
                
                // Check specifically for COMPLETED transactions with positive amounts
                System.out.println("\n=== COMPLETED TRANSACTIONS WITH POSITIVE AMOUNTS ===");
                String completedSql = "SELECT t.id, t.transaction_number, t.total_amount, " +
                                    "COUNT(ti.id) as item_count " +
                                    "FROM transactions t " +
                                    "LEFT JOIN transaction_items ti ON t.id = ti.transaction_id " +
                                    "WHERE t.status = 'COMPLETED' AND t.total_amount > 0 " +
                                    "AND t.transaction_number NOT LIKE 'PREF-%' " +
                                    "GROUP BY t.id, t.transaction_number, t.total_amount " +
                                    "ORDER BY t.id DESC";
                
                try (PreparedStatement stmt = conn.prepareStatement(completedSql); ResultSet rs = stmt.executeQuery()) {
                    System.out.println("ID | Transaction Number | Amount | Items");
                    System.out.println("--------------------------------------");
                    
                    boolean found = false;
                    while (rs.next()) {
                        found = true;
                        System.out.printf("%d | %s | $%.2f | %d%n",
                            rs.getLong("id"),
                            rs.getString("transaction_number"),
                            rs.getDouble("total_amount"),
                            rs.getInt("item_count")
                        );
                    }
                    
                    if (!found) {
                        System.out.println("No suitable completed transactions found!");
                    }
                }
                
            }
            
        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
