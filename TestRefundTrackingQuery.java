
import java.sql.SQLException;
import java.util.List;

import com.clothingstore.dao.RefundItemTrackingDAO;
import com.clothingstore.model.RefundItemTracking;
import com.clothingstore.model.TransactionItemRefundSummary;
import com.clothingstore.service.RefundItemTrackingService;

public class TestRefundTrackingQuery {

    public static void main(String[] args) {
        try {
            System.out.println("=== TESTING REFUND TRACKING QUERY ===");

            RefundItemTrackingDAO dao = new RefundItemTrackingDAO();
            RefundItemTrackingService service = new RefundItemTrackingService();

            // Test transaction item ID 171 (current item in transaction 17)
            Long transactionItemId = 171L;

            System.out.println("Testing transaction item ID: " + transactionItemId);

            // 1. Direct DAO query
            System.out.println("\n1. Direct DAO Query:");
            List<RefundItemTracking> refundHistory = dao.findByTransactionItemId(transactionItemId);
            System.out.println("Found " + refundHistory.size() + " refund records:");

            for (RefundItemTracking record : refundHistory) {
                System.out.printf("  - Refund ID: %d, Qty: %d, Amount: %.2f, Product: %s%n",
                        record.getId(),
                        record.getRefundedQuantity(),
                        record.getRefundAmount().doubleValue(),
                        record.getProductName()
                );
            }

            // 2. Service query
            System.out.println("\n2. Service Query:");
            try {
                TransactionItemRefundSummary summary = service.getTransactionItemRefundSummary(transactionItemId);
                if (summary != null) {
                    System.out.println("Summary found:");
                    System.out.println("  - Original Quantity: " + summary.getOriginalQuantity());
                    System.out.println("  - Total Refunded: " + summary.getTotalRefundedQuantity());
                    System.out.println("  - Remaining: " + summary.getRemainingQuantity());
                    System.out.println("  - Has Refunds: " + summary.hasRefunds());
                    System.out.println("  - Refund History Size: " + summary.getRefundHistory().size());
                    System.out.println("  - Status: " + summary.getRefundStatus());
                    System.out.println("  - Display: " + summary.getQuantityDisplay());
                } else {
                    System.out.println("No summary found!");
                }
            } catch (SQLException e) {
                System.out.println("Service query failed: " + e.getMessage());
                e.printStackTrace();
            }

            // 3. Test with transaction item ID 172 (other current item in transaction 17)
            System.out.println("\n3. Testing transaction item ID 172:");
            transactionItemId = 172L;

            List<RefundItemTracking> refundHistory172 = dao.findByTransactionItemId(transactionItemId);
            System.out.println("Found " + refundHistory172.size() + " refund records for item 172:");

            for (RefundItemTracking record : refundHistory172) {
                System.out.printf("  - Refund ID: %d, Qty: %d, Amount: %.2f, Product: %s%n",
                        record.getId(),
                        record.getRefundedQuantity(),
                        record.getRefundAmount().doubleValue(),
                        record.getProductName()
                );
            }

            try {
                TransactionItemRefundSummary summary172 = service.getTransactionItemRefundSummary(transactionItemId);
                if (summary172 != null) {
                    System.out.println("Summary for item 172:");
                    System.out.println("  - Original Quantity: " + summary172.getOriginalQuantity());
                    System.out.println("  - Total Refunded: " + summary172.getTotalRefundedQuantity());
                    System.out.println("  - Remaining: " + summary172.getRemainingQuantity());
                    System.out.println("  - Has Refunds: " + summary172.hasRefunds());
                    System.out.println("  - Display: " + summary172.getQuantityDisplay());
                } else {
                    System.out.println("No summary found for item 172!");
                }
            } catch (SQLException e) {
                System.out.println("Service query failed for item 172: " + e.getMessage());
                e.printStackTrace();
            }

        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
