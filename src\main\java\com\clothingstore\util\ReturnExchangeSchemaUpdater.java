package com.clothingstore.util;

import com.clothingstore.database.DatabaseManager;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Logger;

/**
 * Database schema updater for Return/Exchange functionality
 */
public class ReturnExchangeSchemaUpdater {

    private static final Logger LOGGER = Logger.getLogger(ReturnExchangeSchemaUpdater.class.getName());

    /**
     * Update database schema to support return/exchange functionality
     */
    public static void updateSchemaForReturnExchange() throws SQLException {
        LOGGER.info("Starting return/exchange database schema updates...");

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            createReturnExchangeTable(conn);
            createReturnExchangeItemsTable(conn);
            createIndexes(conn);

            LOGGER.info("Return/exchange database schema updates completed successfully");
        }
    }

    private static void createReturnExchangeTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS return_exchanges (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "return_number TEXT UNIQUE NOT NULL, " +
                "original_transaction_id INTEGER NOT NULL, " +
                "original_transaction_number TEXT NOT NULL, " +
                "customer_id INTEGER, " +
                "customer_name TEXT, " +
                "type TEXT NOT NULL CHECK (type IN ('RETURN', 'EXCHANGE')), " +
                "reason TEXT, " +
                "status TEXT NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED', 'COMPLETED')), " +
                "total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00, " +
                "refund_amount DECIMAL(10,2) DEFAULT 0.00, " +
                "exchange_amount DECIMAL(10,2) DEFAULT 0.00, " +
                "payment_method TEXT, " +
                "notes TEXT, " +
                "processed_by TEXT, " +
                "request_date DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "processed_date DATETIME, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (original_transaction_id) REFERENCES transactions (id), " +
                "FOREIGN KEY (customer_id) REFERENCES customers (id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            LOGGER.info("Created return_exchanges table");
        }
    }

    private static void createReturnExchangeItemsTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS return_exchange_items (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "return_exchange_id INTEGER NOT NULL, " +
                "original_transaction_item_id INTEGER, " +
                "product_id INTEGER NOT NULL, " +
                "product_name TEXT NOT NULL, " +
                "product_sku TEXT, " +
                "original_quantity INTEGER NOT NULL, " +
                "return_quantity INTEGER NOT NULL, " +
                "unit_price DECIMAL(10,2) NOT NULL, " +
                "line_total DECIMAL(10,2) NOT NULL, " +
                "condition_type TEXT NOT NULL DEFAULT 'NEW' CHECK (condition_type IN ('NEW', 'USED', 'DAMAGED')), " +
                "reason TEXT, " +
                "action_type TEXT NOT NULL DEFAULT 'REFUND' CHECK (action_type IN ('REFUND', 'EXCHANGE', 'STORE_CREDIT')), " +
                "exchange_product_id INTEGER, " +
                "exchange_product_name TEXT, " +
                "exchange_quantity INTEGER DEFAULT 0, " +
                "exchange_unit_price DECIMAL(10,2) DEFAULT 0.00, " +
                "exchange_line_total DECIMAL(10,2) DEFAULT 0.00, " +
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (return_exchange_id) REFERENCES return_exchanges (id) ON DELETE CASCADE, " +
                "FOREIGN KEY (product_id) REFERENCES products (id), " +
                "FOREIGN KEY (exchange_product_id) REFERENCES products (id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            LOGGER.info("Created return_exchange_items table");
        }
    }

    private static void createIndexes(Connection conn) throws SQLException {
        String[] indexes = {
                "CREATE INDEX IF NOT EXISTS idx_return_exchanges_transaction ON return_exchanges(original_transaction_id)",
                "CREATE INDEX IF NOT EXISTS idx_return_exchanges_customer ON return_exchanges(customer_id)",
                "CREATE INDEX IF NOT EXISTS idx_return_exchanges_status ON return_exchanges(status)",
                "CREATE INDEX IF NOT EXISTS idx_return_exchanges_type ON return_exchanges(type)",
                "CREATE INDEX IF NOT EXISTS idx_return_exchanges_date ON return_exchanges(request_date)",
                "CREATE INDEX IF NOT EXISTS idx_return_exchange_items_return ON return_exchange_items(return_exchange_id)",
                "CREATE INDEX IF NOT EXISTS idx_return_exchange_items_product ON return_exchange_items(product_id)"
        };

        try (Statement stmt = conn.createStatement()) {
            for (String index : indexes) {
                stmt.execute(index);
            }
            LOGGER.info("Created return/exchange indexes");
        }
    }

    /**
     * Run all return/exchange schema updates
     */
    public static void runAllUpdates() {
        try {
            updateSchemaForReturnExchange();
        } catch (SQLException e) {
            LOGGER.severe("Failed to update return/exchange schema: " + e.getMessage());
            throw new RuntimeException("Return/exchange database schema update failed", e);
        }
    }

    /**
     * Main method for running schema updates
     */
    public static void main(String[] args) {
        System.out.println("Running return/exchange schema updates...");
        runAllUpdates();
        System.out.println("Return/exchange schema updates completed successfully!");
    }
}
