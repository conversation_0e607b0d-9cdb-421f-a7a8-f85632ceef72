package com.clothingstore.test;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Diagnostic tool to check transaction date formats
 */
public class DateFormatDiagnostic {

    private static final String DB_URL = "*****************************";

    public static void main(String[] args) {
        System.out.println("=== DATE FORMAT DIAGNOSTIC ===");
        System.out.println("Checking transaction date formats");
        System.out.println();

        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // Check transaction date formats
            String query = "SELECT id, transaction_number, transaction_date, status, total_amount FROM transactions ORDER BY id LIMIT 10";
            
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(query)) {
                System.out.println("Sample transaction dates:");
                System.out.println("ID | Number | Date | Status | Amount");
                System.out.println("---|--------|------|--------|--------");
                
                while (rs.next()) {
                    System.out.println(rs.getInt("id") + " | " + 
                            rs.getString("transaction_number") + " | " + 
                            rs.getString("transaction_date") + " | " + 
                            rs.getString("status") + " | $" + 
                            rs.getDouble("total_amount"));
                }
            }
            
            // Test different date range queries
            System.out.println("\nTesting different date range approaches:");
            
            // Test 1: All transactions
            String allQuery = "SELECT COUNT(*) as count FROM transactions WHERE status = 'COMPLETED'";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(allQuery)) {
                if (rs.next()) {
                    System.out.println("Total COMPLETED transactions: " + rs.getInt("count"));
                }
            }
            
            // Test 2: Using DATETIME() function
            String datetimeQuery = "SELECT COUNT(*) as count FROM transactions WHERE status = 'COMPLETED' AND DATETIME(transaction_date) >= DATETIME('now', '-30 days')";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(datetimeQuery)) {
                if (rs.next()) {
                    System.out.println("COMPLETED transactions (last 30 days using DATETIME): " + rs.getInt("count"));
                }
            }
            
            // Test 3: Using epoch time if dates are stored as numbers
            String epochQuery = "SELECT COUNT(*) as count FROM transactions WHERE status = 'COMPLETED' AND CAST(transaction_date AS INTEGER) > ?";
            long thirtyDaysAgo = System.currentTimeMillis() - (30L * 24 * 60 * 60 * 1000);
            try (PreparedStatement stmt = conn.prepareStatement(epochQuery)) {
                stmt.setLong(1, thirtyDaysAgo);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        System.out.println("COMPLETED transactions (last 30 days using epoch): " + rs.getInt("count"));
                    }
                }
            }
            
            // Test 4: Check actual profit query with all data
            String profitQuery = "SELECT " +
                    "COUNT(DISTINCT t.id) as transaction_count, " +
                    "SUM(ti.quantity) as total_items_sold, " +
                    "SUM(ti.line_total) as total_revenue, " +
                    "SUM(p.cost_price * ti.quantity) as total_cost " +
                    "FROM transactions t " +
                    "INNER JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "INNER JOIN products p ON ti.product_id = p.id " +
                    "WHERE t.status = 'COMPLETED' " +
                    "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) " +
                    "AND p.cost_price IS NOT NULL";
            
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(profitQuery)) {
                if (rs.next()) {
                    System.out.println("\nProfit query (all COMPLETED transactions):");
                    System.out.println("  Transactions: " + rs.getInt("transaction_count"));
                    System.out.println("  Items Sold: " + rs.getInt("total_items_sold"));
                    System.out.println("  Revenue: $" + String.format("%.2f", rs.getDouble("total_revenue")));
                    System.out.println("  Cost: $" + String.format("%.2f", rs.getDouble("total_cost")));
                    double profit = rs.getDouble("total_revenue") - rs.getDouble("total_cost");
                    System.out.println("  Profit: $" + String.format("%.2f", profit));
                }
            }
            
            // Test 5: Category breakdown without date filter
            String categoryQuery = "SELECT " +
                    "COALESCE(p.category, 'Uncategorized') as category, " +
                    "COUNT(DISTINCT t.id) as transaction_count, " +
                    "SUM(ti.quantity) as items_sold, " +
                    "SUM(ti.line_total) as revenue, " +
                    "SUM(p.cost_price * ti.quantity) as cost " +
                    "FROM transactions t " +
                    "INNER JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "INNER JOIN products p ON ti.product_id = p.id " +
                    "WHERE t.status = 'COMPLETED' " +
                    "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) " +
                    "AND p.cost_price IS NOT NULL " +
                    "GROUP BY p.category " +
                    "ORDER BY (SUM(ti.line_total) - SUM(p.cost_price * ti.quantity)) DESC";
            
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(categoryQuery)) {
                System.out.println("\nCategory breakdown (all COMPLETED transactions):");
                while (rs.next()) {
                    double profit = rs.getDouble("revenue") - rs.getDouble("cost");
                    System.out.println("  " + rs.getString("category") + ":");
                    System.out.println("    Revenue: $" + String.format("%.2f", rs.getDouble("revenue")));
                    System.out.println("    Cost: $" + String.format("%.2f", rs.getDouble("cost")));
                    System.out.println("    Profit: $" + String.format("%.2f", profit));
                    System.out.println("    Items: " + rs.getInt("items_sold"));
                    System.out.println("    Transactions: " + rs.getInt("transaction_count"));
                }
            }
            
        } catch (SQLException e) {
            System.err.println("Diagnostic failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
