package com.clothingstore.service;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Product;
import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Service for monitoring and alerting on low stock levels
 */
public class LowStockAlertService {
    
    private static final Logger LOGGER = Logger.getLogger(LowStockAlertService.class.getName());
    private static LowStockAlertService instance;
    
    private final ProductDAO productDAO;
    private final ScheduledExecutorService scheduler;
    private boolean alertsEnabled = true;
    private int checkIntervalMinutes = 30; // Check every 30 minutes
    
    private LowStockAlertService() {
        this.productDAO = ProductDAO.getInstance();
        this.scheduler = Executors.newScheduledThreadPool(1);
        startPeriodicChecks();
    }
    
    public static synchronized LowStockAlertService getInstance() {
        if (instance == null) {
            instance = new LowStockAlertService();
        }
        return instance;
    }
    
    /**
     * Start periodic low stock checks
     */
    private void startPeriodicChecks() {
        scheduler.scheduleAtFixedRate(this::checkLowStock, 
                checkIntervalMinutes, checkIntervalMinutes, TimeUnit.MINUTES);
        LOGGER.info("Low stock alert service started with " + checkIntervalMinutes + " minute intervals");
    }
    
    /**
     * Check for low stock products and show alerts
     */
    public void checkLowStock() {
        if (!alertsEnabled) {
            return;
        }
        
        try {
            List<Product> lowStockProducts = getLowStockProducts();
            
            if (!lowStockProducts.isEmpty()) {
                Platform.runLater(() -> showLowStockAlert(lowStockProducts));
            }
            
        } catch (Exception e) {
            LOGGER.severe("Error checking low stock: " + e.getMessage());
        }
    }
    
    /**
     * Get list of products with low stock
     */
    public List<Product> getLowStockProducts() {
        try {
            List<Product> allProducts = productDAO.findAll();
            return allProducts.stream()
                    .filter(product -> product.isActive() && 
                            product.isLowStockAlertEnabled() && 
                            product.isLowStock())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.severe("Error getting low stock products: " + e.getMessage());
            return List.of();
        }
    }
    
    /**
     * Show low stock alert dialog
     */
    private void showLowStockAlert(List<Product> lowStockProducts) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("Low Stock Alert");
        alert.setHeaderText("Products with Low Stock");
        
        StringBuilder content = new StringBuilder();
        content.append("The following products are running low on stock:\n\n");
        
        for (Product product : lowStockProducts) {
            content.append(String.format("• %s (%s) - Current: %d, Minimum: %d\n",
                    product.getName(),
                    product.getSku(),
                    product.getStockQuantity(),
                    product.getMinStockLevel()));
        }
        
        content.append("\nWould you like to view the inventory management page?");
        alert.setContentText(content.toString());
        
        alert.getButtonTypes().setAll(ButtonType.YES, ButtonType.NO);
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.YES) {
                // TODO: Navigate to inventory management page
                LOGGER.info("User chose to view inventory management");
            }
        });
    }
    
    /**
     * Manually trigger low stock check
     */
    public void checkLowStockNow() {
        checkLowStock();
    }
    
    /**
     * Get products that need reordering
     */
    public List<Product> getProductsNeedingReorder() {
        try {
            List<Product> allProducts = productDAO.findAll();
            return allProducts.stream()
                    .filter(product -> product.isActive() && 
                            product.isLowStock() && 
                            product.getReorderQuantity() > 0)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.severe("Error getting products needing reorder: " + e.getMessage());
            return List.of();
        }
    }
    
    /**
     * Generate reorder report
     */
    public String generateReorderReport() {
        List<Product> reorderProducts = getProductsNeedingReorder();
        
        if (reorderProducts.isEmpty()) {
            return "No products currently need reordering.";
        }
        
        StringBuilder report = new StringBuilder();
        report.append("REORDER REPORT\n");
        report.append("Generated: ").append(java.time.LocalDateTime.now()).append("\n\n");
        
        for (Product product : reorderProducts) {
            report.append(String.format("Product: %s (%s)\n", product.getName(), product.getSku()));
            report.append(String.format("Current Stock: %d\n", product.getStockQuantity()));
            report.append(String.format("Minimum Level: %d\n", product.getMinStockLevel()));
            report.append(String.format("Suggested Reorder: %d units\n", product.getReorderQuantity()));
            report.append("---\n");
        }
        
        return report.toString();
    }
    
    // Configuration methods
    public boolean isAlertsEnabled() {
        return alertsEnabled;
    }
    
    public void setAlertsEnabled(boolean alertsEnabled) {
        this.alertsEnabled = alertsEnabled;
        LOGGER.info("Low stock alerts " + (alertsEnabled ? "enabled" : "disabled"));
    }
    
    public int getCheckIntervalMinutes() {
        return checkIntervalMinutes;
    }
    
    public void setCheckIntervalMinutes(int checkIntervalMinutes) {
        this.checkIntervalMinutes = checkIntervalMinutes;
        // Restart scheduler with new interval
        scheduler.shutdown();
        startPeriodicChecks();
        LOGGER.info("Check interval updated to " + checkIntervalMinutes + " minutes");
    }
    
    /**
     * Shutdown the service
     */
    public void shutdown() {
        scheduler.shutdown();
        LOGGER.info("Low stock alert service shutdown");
    }
}
