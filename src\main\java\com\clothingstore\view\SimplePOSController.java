package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Timer;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.PaymentHistoryService;
import com.clothingstore.service.ReceiptPrintingService;
import com.clothingstore.service.TransactionService;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.TextInputDialog;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.layout.VBox;

public class SimplePOSController implements Initializable {

    // Header controls
    @FXML
    private Label lblTransactionNumber;
    @FXML
    private Label lblCashier;
    @FXML
    private Button btnNewTransaction;
    @FXML
    private Button btnRefundTransaction;

    // Product search controls
    @FXML
    private TextField txtProductSearch;
    @FXML
    private Button btnScanBarcode;
    @FXML
    private TableView<Product> tblProducts;
    @FXML
    private TableColumn<Product, String> colProductSku;
    @FXML
    private TableColumn<Product, String> colProductName;
    @FXML
    private TableColumn<Product, String> colProductPrice;
    @FXML
    private TableColumn<Product, String> colProductStock;
    @FXML
    private TableColumn<Product, String> colProductSupplier;
    @FXML
    private ComboBox<String> cmbProductCategory;
    @FXML
    private ComboBox<String> cmbProductSupplier;
    @FXML
    private Button btnClearSearch;
    @FXML
    private Button btnEditProduct;
    @FXML
    private Button btnAdjustStock;
    @FXML
    private Button btnGenerateBarcode;

    // Shopping cart controls
    @FXML
    private TableView<TransactionItem> tblCartItems;
    @FXML
    private TableColumn<TransactionItem, String> colCartProduct;
    @FXML
    private TableColumn<TransactionItem, String> colCartQuantity;
    @FXML
    private TableColumn<TransactionItem, String> colCartUnitPrice;
    @FXML
    private TableColumn<TransactionItem, String> colCartTotal;

    // Payment controls
    @FXML
    private ComboBox<String> cmbPaymentMethod;
    @FXML
    private TextField txtAmountReceived;
    @FXML
    private TextField txtCustomerSearch;
    @FXML
    private ComboBox<String> cmbCustomerGroup;
    @FXML
    private Button btnClearCustomer;
    @FXML
    private Button btnRemoveCustomer;
    @FXML
    private Button btnNewCustomer;
    @FXML
    private Button btnFindTransaction;
    @FXML
    private Label lblCustomerName;
    @FXML
    private Label lblCustomerPhone;
    @FXML
    private Label lblCustomerEmail;
    @FXML
    private Label lblCustomerGroup;
    @FXML
    private Label lblCustomerPoints;
    @FXML
    private Label lblSelectedCustomer;
    @FXML
    private Button btnProcessPayment;
    @FXML
    private Button btnMultiplePayments;
    @FXML
    private Button btnInstallmentPayment;

    // Installment payment tracking components
    @FXML
    private VBox installmentTrackingPanel;
    @FXML
    private Label lblInstallmentStatus;
    @FXML
    private Label lblInstallmentProgress;
    @FXML
    private Label lblRemainingBalance;
    @FXML
    private Button btnHoldTransaction;
    @FXML
    private Button btnVoidTransaction;
    @FXML
    private Button btnPrintReceipt;

    // Status controls
    @FXML
    private Label lblStatus;
    @FXML
    private Label lblItemCount;
    @FXML
    private Label lblWhatsAppStatus;
    @FXML
    private Button btnRetryWhatsApp;

    // Data and services
    private ObservableList<Product> allProducts;
    private ObservableList<Product> filteredProducts;
    private ObservableList<TransactionItem> cartItems;
    private ProductDAO productDAO;
    private CustomerDAO customerDAO;
    private TransactionService transactionService;
    // private BarcodeScannerService scannerService; // Removed - not available
    private ReceiptPrintingService receiptService;
    // private POSOfficeService officeService; // Removed - not available
    // private BarcodeService barcodeService; // Removed - not available
    private NumberFormat currencyFormat;
    private Timer clockTimer;

    // Current transaction data
    private Transaction currentTransaction;
    private Customer selectedCustomer;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Initialize services
        productDAO = ProductDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        transactionService = TransactionService.getInstance();
        // scannerService = BarcodeScannerService.getInstance(); // Removed - not available
        receiptService = ReceiptPrintingService.getInstance();
        // officeService = POSOfficeService.getInstance(); // Removed - not available
        // barcodeService = BarcodeService.getInstance(); // Removed - not available
        currencyFormat = NumberFormat.getCurrencyInstance();

        // Initialize collections
        allProducts = FXCollections.observableArrayList();
        filteredProducts = FXCollections.observableArrayList();
        cartItems = FXCollections.observableArrayList();

        setupTables();
        setupControls();
        // setupBarcodeScanner(); // Removed - not available
        loadProducts();
        startNewTransaction();

        setStatus("POS System initialized successfully");
    }

    private void setupTables() {
        // Setup product table
        colProductSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colProductName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colProductPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getPrice())));
        colProductStock.setCellValueFactory(cellData
                -> new SimpleStringProperty(String.valueOf(cellData.getValue().getStockQuantity())));

        tblProducts.setItems(filteredProducts);

        // Add double-click to add product to cart
        tblProducts.setRowFactory(tv -> {
            TableRow<Product> row = new TableRow<>();
            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    addProductToCart(row.getItem(), 1);
                }
            });
            return row;
        });

        // Setup cart table
        colCartProduct.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getProductName()));
        colCartQuantity.setCellValueFactory(cellData
                -> new SimpleStringProperty(String.valueOf(cellData.getValue().getQuantity())));
        colCartUnitPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice())));
        colCartTotal.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getLineTotal())));

        tblCartItems.setItems(cartItems);

        // Add context menu for cart items
        ContextMenu cartContextMenu = new ContextMenu();
        MenuItem removeItem = new MenuItem("Remove Item");
        MenuItem editQuantity = new MenuItem("Edit Quantity");

        removeItem.setOnAction(e -> {
            TransactionItem selected = tblCartItems.getSelectionModel().getSelectedItem();
            if (selected != null) {
                cartItems.remove(selected);
                updateCartSummary();
            }
        });

        editQuantity.setOnAction(e -> {
            TransactionItem selected = tblCartItems.getSelectionModel().getSelectedItem();
            if (selected != null) {
                editCartItemQuantity(selected);
            }
        });

        cartContextMenu.getItems().addAll(removeItem, editQuantity);
        tblCartItems.setContextMenu(cartContextMenu);
    }

    private void setupControls() {
        // Setup payment methods
        cmbPaymentMethod.setItems(FXCollections.observableArrayList(
                "CASH", "CREDIT_CARD", "DEBIT_CARD", "GIFT_CARD"));
        cmbPaymentMethod.setValue("CASH");

        // Add listeners for real-time updates
        cartItems.addListener((javafx.collections.ListChangeListener<TransactionItem>) change -> {
            updateCartSummary();
            updateItemCount();
        });

        txtAmountReceived.textProperty().addListener((obs, oldVal, newVal) -> calculateChange());

        // Add search functionality
        txtProductSearch.textProperty().addListener((obs, oldVal, newVal) -> filterProducts());

        // Add keyboard shortcuts
        txtProductSearch.setOnKeyPressed(this::handleKeyPressed);
    }

    /*
    private void setupBarcodeScanner() {
        // Setup barcode scanner callbacks - removed
        // scannerService.setOnProductScanned(this::handleScannedProduct);
        // scannerService.setOnScanError(this::handleScanError);
    }
     */
    private void loadProducts() {
        try {
            List<Product> products = productDAO.findAll();
            allProducts.setAll(products);
            filteredProducts.setAll(products);
            setStatus("Loaded " + products.size() + " products");
        } catch (SQLException e) {
            setStatus("Error loading products: " + e.getMessage());
            AlertUtil.showError("Database Error", "Failed to load products: " + e.getMessage());
        }
    }

    private void startNewTransaction() {
        currentTransaction = new Transaction();
        currentTransaction.setTransactionNumber("TXN" + System.currentTimeMillis());
        currentTransaction.setCashierName("Admin"); // TODO: Get from logged-in user

        cartItems.clear();
        selectedCustomer = null;
        txtCustomerSearch.clear();
        txtAmountReceived.clear();

        if (lblTransactionNumber != null) {
            lblTransactionNumber.setText("Transaction: " + currentTransaction.getTransactionNumber());
        }
        if (lblCashier != null) {
            lblCashier.setText("Cashier: Admin");
        }

        updateCartSummary();
        updateItemCount();
        updateInstallmentTrackingDisplay(); // Update installment tracking display
        setStatus("New transaction started");
    }

    // Event Handlers
    @FXML
    private void handleNewTransaction() {
        if (!cartItems.isEmpty()) {
            if (AlertUtil.showConfirmation("New Transaction",
                    "Current transaction will be lost. Continue?")) {
                startNewTransaction();
            }
        } else {
            startNewTransaction();
        }
    }

    @FXML
    private void handleAddToCart() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            addProductToCart(selected, 1);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to add to cart.");
        }
    }

    @FXML
    private void handleScanBarcode() {
        AlertUtil.showInfo("Feature Not Available", "Barcode scanning is not available in this version.");
    }

    @FXML
    private void handleProcessPayment() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Add items to cart before processing payment.");
            return;
        }

        try {
            // Prepare transaction for payment
            currentTransaction.getItems().clear();
            for (TransactionItem item : cartItems) {
                currentTransaction.addItem(item);
            }
            currentTransaction.setCustomer(selectedCustomer);
            currentTransaction.recalculateAmounts();

            // Show enhanced payment dialog
            showPaymentDialog();

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process payment: " + e.getMessage());
        }
    }

    @FXML
    private void handleMultiplePayments() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Add items to cart before processing payment.");
            return;
        }

        try {
            // Prepare transaction for payment
            currentTransaction.getItems().clear();
            for (TransactionItem item : cartItems) {
                currentTransaction.addItem(item);
            }
            currentTransaction.setCustomer(selectedCustomer);
            currentTransaction.recalculateAmounts();

            // Show multiple payment methods dialog
            showMultiplePaymentDialog();

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process multiple payments: " + e.getMessage());
        }
    }

    @FXML
    private void handleInstallmentPayment() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Add items to cart before processing installment payment.");
            return;
        }

        try {
            // Prepare transaction for payment
            currentTransaction.getItems().clear();
            for (TransactionItem item : cartItems) {
                currentTransaction.addItem(item);
            }
            currentTransaction.setCustomer(selectedCustomer);
            currentTransaction.recalculateAmounts();

            // Show installment payment dialog with partial payment pre-selected
            showInstallmentPaymentDialog();

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process installment payment: " + e.getMessage());
        }
    }

    private void showPaymentDialog() {
        try {
            System.out.println("DEBUG: Starting payment dialog for transaction: " + currentTransaction.getTransactionNumber());
            System.out.println("DEBUG: Transaction total: " + currentTransaction.getTotalAmount());
            System.out.println("DEBUG: Cart items count: " + cartItems.size());

            // Validate transaction before showing dialog
            if (currentTransaction.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
                AlertUtil.showError("Invalid Transaction", "Transaction total must be greater than zero.");
                return;
            }

            // Load the payment dialog
            java.net.URL fxmlUrl = getClass().getResource("/fxml/PaymentDialog.fxml");
            if (fxmlUrl == null) {
                AlertUtil.showError("Resource Error", "PaymentDialog.fxml not found. Please check the FXML file exists.");
                return;
            }

            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(fxmlUrl);
            javafx.scene.Parent root = loader.load();
            System.out.println("DEBUG: PaymentDialog FXML loaded successfully");

            PaymentDialogController controller = loader.getController();
            if (controller == null) {
                AlertUtil.showError("Controller Error", "PaymentDialogController not found or not properly initialized.");
                return;
            }

            controller.setTransaction(currentTransaction);
            System.out.println("DEBUG: Transaction set on payment dialog controller");

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Process Payment - " + currentTransaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(tblProducts.getScene().getWindow());
            stage.setResizable(false);

            System.out.println("DEBUG: Showing payment dialog...");
            stage.showAndWait();
            System.out.println("DEBUG: Payment dialog closed");

            // Check if payment was processed
            if (controller.isPaymentProcessed()) {
                System.out.println("DEBUG: Payment was processed successfully");
                System.out.println("DEBUG: Payment amount: " + controller.getPaymentAmount());
                System.out.println("DEBUG: Is partial payment: " + controller.isPartialPayment());

                try {
                    // Validate payment details before saving
                    if (controller.getPaymentAmount() == null || controller.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        AlertUtil.showError("Payment Error", "Invalid payment amount received from dialog.");
                        return;
                    }

                    // Save the transaction to database
                    System.out.println("DEBUG: Saving transaction to database...");
                    Transaction savedTransaction;
                    if (controller.isPartialPayment()) {
                        // Save as partial payment
                        System.out.println("DEBUG: Processing as partial payment");
                        savedTransaction = transactionService.processTransaction(currentTransaction);
                    } else {
                        // Complete the transaction normally
                        System.out.println("DEBUG: Processing as full payment");
                        savedTransaction = transactionService.processTransaction(currentTransaction);
                    }
                    System.out.println("DEBUG: Transaction saved with ID: " + savedTransaction.getId());

                    // Record payment history
                    try {
                        PaymentHistoryService paymentHistoryService = PaymentHistoryService.getInstance();
                        PaymentDialogController.PaymentDetails paymentDetails = controller.getPaymentDetails();
                        if (paymentDetails != null) {
                            String cashierName = "System"; // In a real system, this would be the logged-in user
                            String notes = paymentDetails.getNotes();
                            if (notes == null || notes.trim().isEmpty()) {
                                notes = "Payment processed via Simple POS";
                            }
                            paymentHistoryService.recordPayment(
                                    savedTransaction.getId(),
                                    paymentDetails.getAmount(),
                                    paymentDetails.getPaymentMethod(),
                                    cashierName,
                                    notes
                            );
                            System.out.println("DEBUG: Payment history recorded for transaction " + savedTransaction.getId());
                        }
                    } catch (Exception historyError) {
                        System.err.println("WARNING: Failed to record payment history: " + historyError.getMessage());
                        // Don't fail the entire process if history recording fails
                    }

                    // For completed transactions, show enhanced payment completion dialog
                    BigDecimal amountReceived = controller.getPaymentAmount();
                    BigDecimal change = controller.getChangeAmount();

                    if (!controller.isPartialPayment()) {
                        // Show enhanced payment completion dialog with WhatsApp option
                        showPaymentCompletionDialog(savedTransaction, selectedCustomer, amountReceived, change);
                    } else {
                        // For partial payments, show traditional receipt preview
                        receiptService.showReceiptPreview(savedTransaction, amountReceived, change, selectedCustomer);

                        // Show partial payment message
                        String message = "Partial payment processed successfully!\n";
                        message += "Amount Paid: " + NumberFormat.getCurrencyInstance().format(controller.getPaymentAmount()) + "\n";
                        message += "Remaining Balance: " + NumberFormat.getCurrencyInstance().format(savedTransaction.getRemainingBalance());
                        message += "\nTransaction saved with PARTIAL_PAYMENT status.";

                        AlertUtil.showSuccess("Partial Payment Processed", message);
                    }

                    // Start new transaction
                    startNewTransaction();

                } catch (TransactionService.InsufficientStockException e) {
                    AlertUtil.showError("Insufficient Stock", e.getMessage());
                } catch (SQLException e) {
                    AlertUtil.showError("Database Error", "Failed to save transaction: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            System.err.println("ERROR: Failed to show payment dialog:");
            e.printStackTrace();

            // Try to show a simplified payment dialog as fallback
            boolean tryFallback = AlertUtil.showConfirmation("Payment Dialog Error",
                    "Failed to show payment dialog: " + e.getMessage()
                    + "\n\nWould you like to try a simplified payment dialog?");

            if (tryFallback) {
                showSimplifiedPaymentDialog();
            }
        }
    }

    /**
     * Update installment payment tracking display
     */
    private void updateInstallmentTrackingDisplay() {
        if (installmentTrackingPanel == null) {
            return; // Panel not available in this FXML version
        }

        try {
            // Check if current transaction has installment payments
            if (currentTransaction != null && currentTransaction.getId() != null) {
                // Use DAO to find the transaction since TransactionService doesn't have findById
                com.clothingstore.dao.TransactionDAO transactionDAO = com.clothingstore.dao.TransactionDAO.getInstance();
                Optional<Transaction> transactionOpt = transactionDAO.findById(currentTransaction.getId());
                if (transactionOpt.isPresent()) {
                    Transaction savedTransaction = transactionOpt.get();
                    if (savedTransaction.isInstallmentTransaction()) {
                        showInstallmentTrackingInfo(savedTransaction);
                        installmentTrackingPanel.setVisible(true);
                        installmentTrackingPanel.setManaged(true);
                        return;
                    }
                }
            }

            // Hide tracking panel if no installment transaction
            installmentTrackingPanel.setVisible(false);
            installmentTrackingPanel.setManaged(false);

        } catch (Exception e) {
            System.err.println("Error updating installment tracking display: " + e.getMessage());
            // Hide panel on error
            if (installmentTrackingPanel != null) {
                installmentTrackingPanel.setVisible(false);
                installmentTrackingPanel.setManaged(false);
            }
        }
    }

    /**
     * Show installment tracking information for a transaction
     */
    private void showInstallmentTrackingInfo(Transaction transaction) {
        if (lblInstallmentStatus == null || lblInstallmentProgress == null || lblRemainingBalance == null) {
            return; // Labels not available in this FXML version
        }

        try {
            // Update status label
            String status = transaction.getInstallmentStatus();
            int completedPayments = transaction.getCompletedInstallments();

            if ("PENDING".equals(status)) {
                lblInstallmentStatus.setText("Status: Installment - Pending First Payment");
                lblInstallmentStatus.setStyle("-fx-text-fill: #ffc107; -fx-font-weight: bold;");
            } else if ("IN_PROGRESS".equals(status)) {
                lblInstallmentStatus.setText("Status: Installment - " + completedPayments + " payment(s) made");
                lblInstallmentStatus.setStyle("-fx-text-fill: #17a2b8; -fx-font-weight: bold;");
            } else if ("COMPLETED".equals(status)) {
                lblInstallmentStatus.setText("Status: Installment - Completed");
                lblInstallmentStatus.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");
            } else {
                lblInstallmentStatus.setText("Status: " + status);
                lblInstallmentStatus.setStyle("-fx-text-fill: #6c757d; -fx-font-weight: bold;");
            }

            // Update progress label
            BigDecimal totalAmount = transaction.getTotalAmount();
            BigDecimal amountPaid = transaction.getAmountPaid() != null ? transaction.getAmountPaid() : BigDecimal.ZERO;
            double progressPercent = 0.0;

            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                progressPercent = amountPaid.divide(totalAmount, 4, java.math.RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).doubleValue();
            }

            lblInstallmentProgress.setText(String.format("Progress: %.1f%% (%s of %s paid)",
                    progressPercent,
                    NumberFormat.getCurrencyInstance().format(amountPaid),
                    NumberFormat.getCurrencyInstance().format(totalAmount)));
            lblInstallmentProgress.setStyle("-fx-text-fill: #495057;");

            // Update remaining balance label
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            lblRemainingBalance.setText("Remaining Balance: "
                    + NumberFormat.getCurrencyInstance().format(remainingBalance));

            if (remainingBalance.compareTo(BigDecimal.ZERO) == 0) {
                lblRemainingBalance.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");
            } else {
                lblRemainingBalance.setStyle("-fx-text-fill: #dc3545; -fx-font-weight: bold;");
            }

        } catch (Exception e) {
            System.err.println("Error showing installment tracking info: " + e.getMessage());
        }
    }

    /**
     * Simplified payment dialog as fallback when main dialog fails
     */
    private void showSimplifiedPaymentDialog() {
        try {
            // Create a simple input dialog for payment amount
            javafx.scene.control.TextInputDialog dialog = new javafx.scene.control.TextInputDialog(
                    currentTransaction.getTotalAmount().toString());
            dialog.setTitle("Process Payment");
            dialog.setHeaderText("Payment for Transaction: " + currentTransaction.getTransactionNumber());
            dialog.setContentText("Payment Amount ($" + currentTransaction.getTotalAmount() + "):");

            java.util.Optional<String> result = dialog.showAndWait();
            if (result.isPresent()) {
                try {
                    BigDecimal paymentAmount = new BigDecimal(result.get());

                    if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        AlertUtil.showWarning("Invalid Amount", "Payment amount must be greater than zero.");
                        return;
                    }

                    // Process the payment
                    if (paymentAmount.compareTo(currentTransaction.getTotalAmount()) >= 0) {
                        currentTransaction.processFullPayment(paymentAmount);
                    } else {
                        currentTransaction.processPartialPayment(paymentAmount);
                    }

                    currentTransaction.setPaymentMethod("CASH"); // Default to cash for simplified dialog

                    // Save transaction
                    Transaction savedTransaction = transactionService.processTransaction(currentTransaction);

                    // Show success message
                    String message = "Payment processed successfully!\n";
                    message += "Transaction: " + savedTransaction.getTransactionNumber() + "\n";
                    message += "Amount Paid: " + NumberFormat.getCurrencyInstance().format(paymentAmount);

                    if (paymentAmount.compareTo(currentTransaction.getTotalAmount()) < 0) {
                        message += "\nRemaining Balance: " + NumberFormat.getCurrencyInstance().format(savedTransaction.getRemainingBalance());
                    }

                    AlertUtil.showSuccess("Payment Completed", message);

                    // Start new transaction
                    startNewTransaction();

                } catch (NumberFormatException e) {
                    AlertUtil.showWarning("Invalid Input", "Please enter a valid payment amount.");
                } catch (Exception e) {
                    AlertUtil.showError("Payment Error", "Failed to process payment: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            AlertUtil.showError("Simplified Payment Error", "Failed to show simplified payment dialog: " + e.getMessage());
        }
    }

    private void showMultiplePaymentDialog() {
        try {
            // Load the multiple payment dialog
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/MultiplePaymentDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            MultiplePaymentDialogController controller = loader.getController();
            controller.setTransaction(currentTransaction);

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Multiple Payment Methods - " + currentTransaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(tblProducts.getScene().getWindow());
            stage.setResizable(false);

            stage.showAndWait();

            // Check if transaction was completed
            if (controller.isTransactionCompleted()) {
                try {
                    // Save the transaction to database
                    Transaction savedTransaction = transactionService.processTransaction(currentTransaction);

                    // Show completion message with payment details
                    StringBuilder paymentDetails = new StringBuilder();
                    paymentDetails.append("Transaction completed successfully!\n\n");
                    paymentDetails.append("Payment Methods Used:\n");

                    for (MultiplePaymentDialogController.PaymentEntry payment : controller.getPayments()) {
                        paymentDetails.append("• ").append(payment.getPaymentMethod())
                                .append(": ").append(NumberFormat.getCurrencyInstance().format(payment.getAmount()))
                                .append("\n");
                    }

                    BigDecimal change = controller.getChangeAmount();
                    if (change.compareTo(BigDecimal.ZERO) > 0) {
                        paymentDetails.append("\nChange Due: ").append(NumberFormat.getCurrencyInstance().format(change));
                    }

                    // Show enhanced payment completion dialog
                    showPaymentCompletionDialog(savedTransaction, selectedCustomer,
                            controller.getTotalPaid(), change);

                    // Start new transaction
                    startNewTransaction();

                } catch (Exception e) {
                    AlertUtil.showError("Database Error", "Failed to save transaction: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            AlertUtil.showError("Multiple Payment Dialog Error", "Failed to show multiple payment dialog: " + e.getMessage());
        }
    }

    private void showInstallmentPaymentDialog() {
        try {
            // Load the enhanced payment dialog with partial payment support
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/PaymentDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            PaymentDialogController controller = loader.getController();
            controller.setTransaction(currentTransaction);

            // Pre-select partial payment mode for installment payments
            controller.setPartialPaymentMode(true);

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Installment Payment - " + currentTransaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(tblProducts.getScene().getWindow());
            stage.setResizable(false);

            stage.showAndWait();

            // Check if payment was processed
            if (controller.isPaymentProcessed()) {
                try {
                    // Save the transaction with payment information
                    Transaction savedTransaction;
                    if (controller.isPartialPayment()) {
                        // Save as partial payment
                        savedTransaction = transactionService.processTransaction(currentTransaction);
                    } else {
                        // Complete the transaction normally
                        savedTransaction = transactionService.processTransaction(currentTransaction);
                    }

                    // Show installment payment confirmation
                    BigDecimal amountPaid = controller.getPaymentAmount();
                    BigDecimal remainingBalance = savedTransaction.getRemainingBalance();

                    String message = String.format("Installment payment of %s processed successfully.\n\nRemaining balance: %s\n\nCustomer can make additional payments through Outstanding Balances.",
                            NumberFormat.getCurrencyInstance().format(amountPaid),
                            NumberFormat.getCurrencyInstance().format(remainingBalance));

                    AlertUtil.showSuccess("Installment Payment Processed", message);

                    // Start new transaction
                    startNewTransaction();

                } catch (Exception e) {
                    AlertUtil.showError("Database Error", "Failed to save installment transaction: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            AlertUtil.showError("Installment Payment Dialog Error", "Failed to show installment payment dialog: " + e.getMessage());
        }
    }

    @FXML
    private void handleNewCustomer() {
        AlertUtil.showInfo("Feature Coming Soon", "Customer registration will be implemented next.");
    }

    // Helper Methods
    private void addProductToCart(Product product, int quantity) {
        if (product == null) {
            AlertUtil.showError("Invalid Product", "Product cannot be null.");
            return;
        }

        if (quantity <= 0) {
            AlertUtil.showWarning("Invalid Quantity", "Quantity must be greater than 0.");
            return;
        }

        if (quantity > 9999) {
            AlertUtil.showWarning("Invalid Quantity", "Quantity cannot exceed 9,999.");
            return;
        }

        if (product.getId() == null) {
            AlertUtil.showError("Invalid Product", "Product ID is missing.");
            return;
        }

        if (product.getStockQuantity() < quantity) {
            AlertUtil.showWarning("Insufficient Stock",
                    String.format("Cannot add %d items. Only %d available in stock.",
                            quantity, product.getStockQuantity()));
            return;
        }

        // Check if product already in cart
        Optional<TransactionItem> existingItem = cartItems.stream()
                .filter(item -> item.getProductId() != null && item.getProductId().equals(product.getId()))
                .findFirst();

        if (existingItem.isPresent()) {
            TransactionItem item = existingItem.get();
            int newQuantity = item.getQuantity() + quantity;

            if (product.getStockQuantity() < newQuantity) {
                AlertUtil.showWarning("Insufficient Stock",
                        String.format("Cannot add %d more items. Only %d available (current cart quantity: %d).",
                                quantity, product.getStockQuantity(), item.getQuantity()));
                return;
            }

            item.setQuantity(newQuantity);
            setStatus(String.format("Updated %s quantity to %d", product.getName(), newQuantity));
        } else {
            TransactionItem newItem = new TransactionItem(product, quantity);
            cartItems.add(newItem);
            setStatus(String.format("Added %s (qty: %d) to cart", product.getName(), quantity));
        }

        // Always update cart summary after changes
        updateCartSummary();
    }

    private void updateCartSummary() {
        if (currentTransaction == null) {
            return;
        }

        currentTransaction.getItems().clear();
        for (TransactionItem item : cartItems) {
            currentTransaction.addItem(item);
        }

        currentTransaction.recalculateAmounts();
    }

    private void updateItemCount() {
        int totalItems = cartItems.stream().mapToInt(TransactionItem::getQuantity).sum();
        if (lblItemCount != null) {
            lblItemCount.setText("Items: " + totalItems);
        }
    }

    private void calculateChange() {
        if (currentTransaction == null || txtAmountReceived == null) {
            return;
        }

        try {
            String amountText = txtAmountReceived.getText().trim();
            if (!amountText.isEmpty()) {
                BigDecimal amountReceived = new BigDecimal(amountText);
                BigDecimal change = amountReceived.subtract(currentTransaction.getTotalAmount());
                // Could display change in a label if available
            }
        } catch (NumberFormatException e) {
            // Invalid input, ignore
        }
    }

    private void filterProducts() {
        String searchText = txtProductSearch.getText().toLowerCase().trim();

        if (searchText.isEmpty()) {
            filteredProducts.setAll(allProducts);
        } else {
            filteredProducts.setAll(allProducts.stream()
                    .filter(product
                            -> product.getName().toLowerCase().contains(searchText)
                    || product.getSku().toLowerCase().contains(searchText)
                    || (product.getBarcode() != null && product.getBarcode().contains(searchText))
                    || (product.getCategory() != null && product.getCategory().toLowerCase().contains(searchText)))
                    .toList());
        }
    }

    private void editCartItemQuantity(TransactionItem item) {
        TextInputDialog dialog = new TextInputDialog(String.valueOf(item.getQuantity()));
        dialog.setTitle("Edit Quantity");
        dialog.setHeaderText("Edit quantity for: " + item.getProductName());
        dialog.setContentText("Quantity (Max: " + item.getProduct().getStockQuantity() + ", Current: " + item.getQuantity() + "):");

        // Add input validation to the dialog
        dialog.getEditor().textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*")) {
                dialog.getEditor().setText(oldVal);
            }
        });

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            String input = result.get().trim();

            // Enhanced validation
            if (input.isEmpty()) {
                AlertUtil.showWarning("Invalid Input", "Please enter a quantity.");
                return;
            }

            try {
                // Handle decimal inputs by converting to integer
                double doubleValue = Double.parseDouble(input);
                int newQuantity = (int) Math.round(doubleValue);

                // Validate quantity range
                if (newQuantity < 0) {
                    AlertUtil.showWarning("Invalid Quantity", "Quantity cannot be negative.");
                    return;
                }

                if (newQuantity > 9999) {
                    AlertUtil.showWarning("Invalid Quantity", "Quantity cannot exceed 9,999.");
                    return;
                }

                if (newQuantity == 0) {
                    // Confirm removal
                    if (AlertUtil.showConfirmation("Remove Item",
                            "Setting quantity to 0 will remove this item from cart. Continue?")) {
                        cartItems.remove(item);
                        updateCartSummary();
                        setStatus("Removed " + item.getProductName() + " from cart");
                    }
                    return;
                }

                if (newQuantity > item.getProduct().getStockQuantity()) {
                    AlertUtil.showWarning("Insufficient Stock",
                            String.format("Only %d items available in stock. Cannot set quantity to %d.",
                                    item.getProduct().getStockQuantity(), newQuantity));
                    return;
                }

                // Update quantity and refresh display
                int oldQuantity = item.getQuantity();
                item.setQuantity(newQuantity);
                updateCartSummary();

                setStatus(String.format("Updated %s quantity from %d to %d",
                        item.getProductName(), oldQuantity, newQuantity));

            } catch (NumberFormatException e) {
                AlertUtil.showWarning("Invalid Input",
                        "Please enter a valid number. Decimals will be rounded to the nearest whole number.");
            }
        }
    }

    private void handleScannedProduct(Product product) {
        addProductToCart(product, 1);
        setStatus("Scanned: " + product.getName());
    }

    private void handleScanError(String error) {
        setStatus("Scan error: " + error);
        AlertUtil.showWarning("Scan Error", error);
    }

    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.F1) {
            handleScanBarcode();
        } else if (event.getCode() == KeyCode.F2) {
            handleNewTransaction();
        } else if (event.getCode() == KeyCode.F3) {
            handleProcessPayment();
        }
    }

    private void setStatus(String message) {
        if (lblStatus != null) {
            lblStatus.setText(message);
        }
        System.out.println("POS Status: " + message);
    }

    // Office Features Integration
    @FXML
    private void handleQuickAddProduct() {
        AlertUtil.showInfo("Feature Not Available", "Quick add product is not available in this version.");
    }

    @FXML
    private void handleEditProduct() {
        AlertUtil.showInfo("Feature Not Available", "Quick edit product is not available in this version.");
    }

    @FXML
    private void handleAdjustStock() {
        AlertUtil.showInfo("Feature Not Available", "Stock adjustment is not available in this version.");
    }

    @FXML
    private void handleGenerateBarcode() {
        AlertUtil.showInfo("Feature Not Available", "Barcode generation is not available in this version.");
    }

    @FXML
    private void handlePrintReceipt() {
        if (currentTransaction == null || cartItems.isEmpty()) {
            AlertUtil.showWarning("No Transaction", "Complete a transaction first to print receipt.");
            return;
        }

        // Get payment details
        BigDecimal amountReceived = null;
        BigDecimal change = BigDecimal.ZERO;

        if ("CASH".equals(currentTransaction.getPaymentMethod())) {
            try {
                String amountText = txtAmountReceived.getText().trim();
                if (!amountText.isEmpty()) {
                    amountReceived = new BigDecimal(amountText);
                    change = amountReceived.subtract(currentTransaction.getTotalAmount());
                }
            } catch (NumberFormatException e) {
                // Use null for amount received if invalid
            }
        }

        receiptService.showReceiptPreview(currentTransaction, amountReceived, change, selectedCustomer);
    }

    @FXML
    private void handleHoldTransaction() {
        AlertUtil.showInfo("Feature Coming Soon", "Hold transaction functionality will be implemented next.");
    }

    @FXML
    private void handleVoidTransaction() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "No transaction to void.");
            return;
        }

        if (AlertUtil.showConfirmation("Void Transaction", "Are you sure you want to void this transaction?")) {
            startNewTransaction();
            setStatus("Transaction voided");
        }
    }

    /**
     * Show enhanced payment completion dialog with WhatsApp receipt option
     */
    private void showPaymentCompletionDialog(Transaction transaction, Customer customer, BigDecimal amountPaid, BigDecimal changeAmount) {
        try {
            // Load the payment completion dialog
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/PaymentCompletionDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            PaymentCompletionController controller = loader.getController();
            controller.setTransactionData(transaction, customer, amountPaid, changeAmount);

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Payment Completed - " + transaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(btnProcessPayment.getScene().getWindow());
            stage.setResizable(false);

            stage.showAndWait();

            // Check if new transaction was requested
            if (controller.isNewTransactionRequested()) {
                startNewTransaction();
            }

        } catch (Exception e) {
            // Fallback to traditional receipt preview if dialog fails
            receiptService.showReceiptPreview(transaction, amountPaid, changeAmount, customer);
            AlertUtil.showError("Dialog Error", "Could not load payment completion dialog: " + e.getMessage());
        }
    }

    /**
     * Show info notification to user
     */
    private void showInfoNotification(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing notification: " + e.getMessage());
        }
    }

    /**
     * Show success notification to user
     */
    private void showSuccessNotification(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText("Success");
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing success notification: " + e.getMessage());
        }
    }

    /**
     * Show warning notification to user
     */
    private void showWarningNotification(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.WARNING);
            alert.setTitle(title);
            alert.setHeaderText("Warning");
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing warning notification: " + e.getMessage());
        }
    }

    // Additional FXML methods for PointOfSaleNew.fxml
    @FXML
    private void handleRefundTransaction() {
        AlertUtil.showInfo("Feature Not Available", "Refund transaction is not available in this version.");
    }

    @FXML
    private void handleCategoryFilter() {
        // Filter products by category
        filterProducts();
    }

    @FXML
    private void handleSupplierFilter() {
        // Filter products by supplier
        filterProducts();
    }

    @FXML
    private void handleClearSearch() {
        txtProductSearch.clear();
        if (cmbProductCategory != null) {
            cmbProductCategory.setValue(null);
        }
        if (cmbProductSupplier != null) {
            cmbProductSupplier.setValue(null);
        }
        filterProducts();
    }

    @FXML
    private void handleCustomerSearch() {
        // Handle customer search as user types
        String searchText = txtCustomerSearch.getText();
        if (searchText != null && searchText.length() > 2) {
            // Could implement customer search here
            setStatus("Searching customers: " + searchText);
        }
    }

    @FXML
    private void handleCustomerGroupFilter() {
        // Filter customers by group
        setStatus("Customer group filter applied");
    }

    @FXML
    private void handleClearCustomerSearch() {
        txtCustomerSearch.clear();
        if (cmbCustomerGroup != null) {
            cmbCustomerGroup.setValue(null);
        }
        selectedCustomer = null;
        setStatus("Customer search cleared");
    }

    @FXML
    private void handleRemoveCustomer() {
        selectedCustomer = null;
        txtCustomerSearch.clear();
        setStatus("Customer removed from transaction");
    }

    @FXML
    private void handleFindTransaction() {
        AlertUtil.showInfo("Feature Not Available", "Transaction search is not available in this version.");
    }
}
