@echo off
title Create Desktop Shortcuts - Clothing Store Management System
echo ========================================
echo    CLOTHING STORE MANAGEMENT SYSTEM
echo       Desktop Shortcuts Creator
echo ========================================
echo.

REM Get current directory
set "CURRENT_DIR=%CD%"

REM Get desktop path
for /f "tokens=3*" %%i in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop 2^>nul') do set "DESKTOP_PATH=%%j"

if "%DESKTOP_PATH%"=="" (
    set "DESKTOP_PATH=%USERPROFILE%\Desktop"
)

echo Current project directory: %CURRENT_DIR%
echo Desktop path: %DESKTOP_PATH%
echo.

REM Check if batch files exist
if not exist "%CURRENT_DIR%\run-javafx-gui.bat" (
    echo ERROR: run-javafx-gui.bat not found in current directory
    pause
    exit /b 1
)

echo Creating desktop shortcuts...
echo.

REM Create PowerShell script to generate shortcuts
echo Creating shortcut generation script...

(
echo $WshShell = New-Object -comObject WScript.Shell
echo.
echo # JavaFX GUI Shortcut
echo $Shortcut = $WshShell.CreateShortcut^("%DESKTOP_PATH%\Clothing Store - JavaFX GUI.lnk"^)
echo $Shortcut.TargetPath = "%CURRENT_DIR%\run-javafx-gui.bat"
echo $Shortcut.WorkingDirectory = "%CURRENT_DIR%"
echo $Shortcut.Description = "Clothing Store Management System - JavaFX GUI"
echo $Shortcut.IconLocation = "shell32.dll,21"
echo $Shortcut.Save^(^)
echo.
echo # Swing GUI Shortcut
echo $Shortcut = $WshShell.CreateShortcut^("%DESKTOP_PATH%\Clothing Store - Swing GUI.lnk"^)
echo $Shortcut.TargetPath = "%CURRENT_DIR%\run-swing-gui.bat"
echo $Shortcut.WorkingDirectory = "%CURRENT_DIR%"
echo $Shortcut.Description = "Clothing Store Management System - Swing GUI"
echo $Shortcut.IconLocation = "shell32.dll,21"
echo $Shortcut.Save^(^)
echo.
echo # Console Demo Shortcut
echo $Shortcut = $WshShell.CreateShortcut^("%DESKTOP_PATH%\Clothing Store - Console Demo.lnk"^)
echo $Shortcut.TargetPath = "%CURRENT_DIR%\run-console-demo.bat"
echo $Shortcut.WorkingDirectory = "%CURRENT_DIR%"
echo $Shortcut.Description = "Clothing Store Management System - Console Demo"
echo $Shortcut.IconLocation = "shell32.dll,3"
echo $Shortcut.Save^(^)
echo.
echo # Compile Project Shortcut
echo $Shortcut = $WshShell.CreateShortcut^("%DESKTOP_PATH%\Clothing Store - Compile Project.lnk"^)
echo $Shortcut.TargetPath = "%CURRENT_DIR%\compile-project.bat"
echo $Shortcut.WorkingDirectory = "%CURRENT_DIR%"
echo $Shortcut.Description = "Clothing Store Management System - Compile Project"
echo $Shortcut.IconLocation = "shell32.dll,162"
echo $Shortcut.Save^(^)
echo.
echo Write-Host "Desktop shortcuts created successfully!" -ForegroundColor Green
) > "%TEMP%\create_shortcuts.ps1"

REM Execute PowerShell script
powershell -ExecutionPolicy Bypass -File "%TEMP%\create_shortcuts.ps1"

if %errorlevel% equ 0 (
    echo.
    echo ✓ SUCCESS: Desktop shortcuts created!
    echo.
    echo The following shortcuts have been added to your desktop:
    echo   • Clothing Store - JavaFX GUI.lnk
    echo   • Clothing Store - Swing GUI.lnk  
    echo   • Clothing Store - Console Demo.lnk
    echo   • Clothing Store - Compile Project.lnk
    echo.
    echo You can now double-click these shortcuts to launch the applications!
) else (
    echo.
    echo ✗ ERROR: Failed to create shortcuts
    echo Please check if PowerShell is available and try again
)

REM Clean up temporary file
del "%TEMP%\create_shortcuts.ps1" 2>nul

echo.
echo Press any key to exit...
pause >nul
