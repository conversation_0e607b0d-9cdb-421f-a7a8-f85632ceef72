
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.clothingstore.database.DatabaseManager;

public class CheckRefundTracking {

    public static void main(String[] args) {
        try {
            System.out.println("=== CHECKING REFUND TRACKING RECORDS ===");

            try (Connection conn = DatabaseManager.getInstance().getConnection()) {
                // Check refund_item_tracking table
                String sql = "SELECT * FROM refund_item_tracking ORDER BY id DESC LIMIT 10";
                try (PreparedStatement stmt = conn.prepareStatement(sql); ResultSet rs = stmt.executeQuery()) {

                    System.out.println("\nRefund Item Tracking Records:");
                    System.out.println("ID | Orig_TXN | Orig_Item | Refund_TXN | Product | Orig_Qty | Refund_Qty | Refund_Amount");
                    System.out.println("-".repeat(100));

                    while (rs.next()) {
                        System.out.printf("%d | %d | %d | %d | %s | %d | %d | %.2f%n",
                                rs.getLong("id"),
                                rs.getLong("original_transaction_id"),
                                rs.getLong("original_transaction_item_id"),
                                rs.getLong("refund_transaction_id"),
                                rs.getString("product_name"),
                                rs.getInt("original_quantity"),
                                rs.getInt("refunded_quantity"),
                                rs.getBigDecimal("refund_amount").doubleValue()
                        );
                    }
                }

                // Check transaction_items table for recent transactions
                String itemSql = "SELECT ti.id, ti.transaction_id, ti.quantity "
                        + "FROM transaction_items ti "
                        + "JOIN transactions t ON ti.transaction_id = t.id "
                        + "WHERE t.transaction_number LIKE 'TXN%' "
                        + "ORDER BY t.id DESC LIMIT 10";

                try (PreparedStatement stmt = conn.prepareStatement(itemSql); ResultSet rs = stmt.executeQuery()) {

                    System.out.println("\nTransaction Items (Recent):");
                    System.out.println("Item_ID | TXN_ID | Qty");
                    System.out.println("-".repeat(50));

                    while (rs.next()) {
                        System.out.printf("%d | %d | %d%n",
                                rs.getLong("id"),
                                rs.getLong("transaction_id"),
                                rs.getInt("quantity")
                        );
                    }
                }
            }

        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
