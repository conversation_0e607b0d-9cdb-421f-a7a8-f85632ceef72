# Clothing Store Management System - PowerShell Launcher
# Usage: .\Run-ClothingStore.ps1 [-Mode <javafx|swing|console|status>]

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("javafx", "swing", "console", "status", "compile")]
    [string]$Mode = "javafx"
)

# Set console title and colors
$Host.UI.RawUI.WindowTitle = "Clothing Store Management System"
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   CLOTHING STORE MANAGEMENT SYSTEM" -ForegroundColor Yellow
Write-Host "        PowerShell Launcher" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

function Test-JavaInstallation {
    try {
        $javaVersion = java -version 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "Java not found"
        }
        Write-Host "✓ Java is available" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ ERROR: Java is not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install Java 11 or higher" -ForegroundColor Yellow
        return $false
    }
}

function Test-ProjectStructure {
    $errors = @()
    
    if (-not (Test-Path "lib\sqlite-jdbc-********.jar")) {
        $errors += "SQLite JDBC driver not found (lib\sqlite-jdbc-********.jar)"
    }
    
    if (-not (Test-Path "target\classes")) {
        $errors += "Compiled classes not found (target\classes) - Run compilation first"
    }
    
    if ($Mode -eq "javafx" -and -not (Test-Path "javafx-sdk-17.0.2\lib")) {
        $errors += "JavaFX SDK not found (javafx-sdk-17.0.2\lib)"
    }
    
    if ($errors.Count -gt 0) {
        Write-Host "✗ Project structure issues:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  - $error" -ForegroundColor Yellow
        }
        return $false
    }
    
    Write-Host "✓ Project structure is valid" -ForegroundColor Green
    return $true
}

function Start-Application {
    param([string]$AppMode)
    
    $classpath = "target\classes;lib\sqlite-jdbc-********.jar"
    
    switch ($AppMode) {
        "javafx" {
            Write-Host "Starting JavaFX GUI Application..." -ForegroundColor Cyan
            $javaArgs = @(
                "--module-path", "javafx-sdk-17.0.2\lib",
                "--add-modules", "javafx.controls,javafx.fxml",
                "-cp", $classpath,
                "com.clothingstore.ClothingStoreApp"
            )
        }
        "swing" {
            Write-Host "Starting Swing GUI Application..." -ForegroundColor Cyan
            $javaArgs = @("-cp", $classpath, "com.clothingstore.gui.SwingPOSDemo")
        }
        "console" {
            Write-Host "Starting Console Demo..." -ForegroundColor Cyan
            $javaArgs = @("-cp", $classpath, "com.clothingstore.demo.SimplePOSDemo")
        }
        "status" {
            Write-Host "Running System Status Check..." -ForegroundColor Cyan
            $javaArgs = @("-cp", $classpath, "com.clothingstore.demo.ProjectStatusDemo")
        }
        "compile" {
            Write-Host "Compiling project..." -ForegroundColor Cyan
            return Invoke-Compilation
        }
    }
    
    try {
        & java @javaArgs
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "✓ Application completed successfully" -ForegroundColor Green
        } else {
            Write-Host ""
            Write-Host "✗ Application exited with error code: $LASTEXITCODE" -ForegroundColor Red
        }
    }
    catch {
        Write-Host ""
        Write-Host "✗ Failed to start application: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Invoke-Compilation {
    if (-not (Test-Path "target\classes")) {
        New-Item -ItemType Directory -Path "target\classes" -Force | Out-Null
    }
    
    $sourceFiles = @(
        "src\main\java\com\clothingstore\*.java",
        "src\main\java\com\clothingstore\model\*.java",
        "src\main\java\com\clothingstore\dao\*.java",
        "src\main\java\com\clothingstore\service\*.java",
        "src\main\java\com\clothingstore\gui\*.java",
        "src\main\java\com\clothingstore\demo\*.java"
    )
    
    $javacArgs = @(
        "-cp", "lib\sqlite-jdbc-********.jar",
        "-d", "target\classes"
    ) + $sourceFiles
    
    try {
        & javac @javacArgs
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Compilation successful!" -ForegroundColor Green
            Write-Host ""
            Write-Host "Available run modes:" -ForegroundColor Cyan
            Write-Host "  .\Run-ClothingStore.ps1 -Mode javafx   (Recommended)" -ForegroundColor White
            Write-Host "  .\Run-ClothingStore.ps1 -Mode swing    (Alternative GUI)" -ForegroundColor White
            Write-Host "  .\Run-ClothingStore.ps1 -Mode console  (Console Demo)" -ForegroundColor White
            Write-Host "  .\Run-ClothingStore.ps1 -Mode status   (System Status)" -ForegroundColor White
        } else {
            Write-Host "✗ Compilation failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ Compilation error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution
if (-not (Test-JavaInstallation)) {
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-ProjectStructure)) {
    Read-Host "Press Enter to exit"
    exit 1
}

Start-Application -AppMode $Mode

Write-Host ""
Write-Host "Press Enter to exit..." -ForegroundColor Gray
Read-Host
