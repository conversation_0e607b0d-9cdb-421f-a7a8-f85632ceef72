<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.SimplePOSController">
   <!-- Stylesheets removed for compatibility -->
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #f8f9fa; -fx-padding: 10;">
         <children>
            <Label styleClass="form-title" text="Point of Sale">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </Region>
            <Label fx:id="lblTransactionNumber" text="Transaction: TXN000000" />
            <Label fx:id="lblCashier" text="Cashier: Admin" />
            <Button fx:id="btnNewTransaction" onAction="#handleNewTransaction" text="🔄 New Transaction" />
            <Button fx:id="btnRefundTransaction" onAction="#handleRefundTransaction" text="💵 Refund Transaction" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Main Content - Responsive layout -->
      <HBox spacing="18.0">
         <VBox.vgrow>ALWAYS</VBox.vgrow>
         <children>
            <!-- Product Search - Flexible width -->
            <VBox minWidth="350.0" prefWidth="450.0" spacing="12.0">
               <HBox.hgrow>SOMETIMES</HBox.hgrow>
               <children>
                  <Label text="Product Search">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>

                  <HBox spacing="12.0">
                     <children>
                        <TextField fx:id="txtProductSearch" promptText="Search products, SKU, or barcode...">
                           <HBox.hgrow>ALWAYS</HBox.hgrow>
                        </TextField>
                        <Button fx:id="btnScanBarcode" onAction="#handleScanBarcode" text="📷 Scan" />
                     </children>
                  </HBox>

                  <!-- Product Filters -->
                  <HBox spacing="10.0">
                     <children>
                        <Label styleClass="form-label" text="Category:" />
                        <ComboBox fx:id="cmbProductCategory" onAction="#handleCategoryFilter" prefWidth="120.0" promptText="All Categories" />
                        <Label styleClass="form-label" text="Supplier:" />
                        <ComboBox fx:id="cmbProductSupplier" onAction="#handleSupplierFilter" prefWidth="130.0" promptText="All Suppliers" />
                        <Button fx:id="btnClearSearch" onAction="#handleClearSearch" text="🗑 Clear" />
                     </children>
                  </HBox>

                  <!-- Office Features Toolbar - Better spacing -->
                  <HBox spacing="8.0">
                     <children>
                        <Button fx:id="btnQuickAddProduct" onAction="#handleQuickAddProduct" text="+ Add Product" />
                        <Button fx:id="btnEditProduct" onAction="#handleEditProduct" text="✏ Edit" />
                        <Button fx:id="btnAdjustStock" onAction="#handleAdjustStock" text="📦 Stock" />
                        <Button fx:id="btnGenerateBarcode" onAction="#handleGenerateBarcode" text="🏷 Barcode" />
                     </children>
                  </HBox>

                  <TableView fx:id="tblProducts" prefHeight="320.0">
                     <VBox.vgrow>ALWAYS</VBox.vgrow>
                     <columns>
                        <TableColumn fx:id="colProductSku" prefWidth="80.0" text="SKU" />
                        <TableColumn fx:id="colProductName" prefWidth="160.0" text="Product" />
                        <TableColumn fx:id="colProductSupplier" prefWidth="120.0" text="Supplier" />
                        <TableColumn fx:id="colProductPrice" prefWidth="70.0" text="Price" />
                        <TableColumn fx:id="colProductStock" prefWidth="60.0" text="Stock" />
                     </columns>
                  </TableView>
               </children>
               <padding>
                  <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
               </padding>
            </VBox>

            <!-- Enhanced Shopping Cart - Better responsive design -->
            <VBox spacing="12.0" styleClass="card" minWidth="400.0" prefWidth="500.0">
               <HBox.hgrow>ALWAYS</HBox.hgrow>
               <children>
                  <Label styleClass="card-header" text="🛒 Shopping Cart">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>

                  <TableView fx:id="tblCartItems" prefHeight="280.0">
                     <VBox.vgrow>ALWAYS</VBox.vgrow>
                     <columns>
                        <TableColumn fx:id="colCartProduct" prefWidth="220.0" text="Product" />
                        <TableColumn fx:id="colCartQuantity" prefWidth="90.0" text="Qty" />
                        <TableColumn fx:id="colCartUnitPrice" prefWidth="90.0" text="Unit Price" />
                        <TableColumn fx:id="colCartTotal" prefWidth="90.0" text="Total" />
                     </columns>
                  </TableView>

                  <VBox spacing="10.0" styleClass="amount-display">
                     <children>
                        <HBox spacing="12.0">
                           <children>
                              <Label text="Subtotal:" />
                              <Region>
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Region>
                              <Label fx:id="lblSubtotal" style="-fx-font-weight: bold;" text="$0.00" />
                           </children>
                        </HBox>
                        <Separator />
                        <HBox spacing="12.0">
                           <children>
                              <Label style="-fx-font-weight: bold; -fx-font-size: 16px;" text="Total:" />
                              <Region>
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Region>
                              <Label fx:id="lblTotal" style="-fx-font-weight: bold; -fx-font-size: 18px;" styleClass="amount-positive" text="$0.00" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
               </padding>
            </VBox>
         </children>
      </HBox>

      <!-- Payment Section - Better responsive layout -->
      <HBox spacing="20.0" styleClass="form-container">
         <children>
            <!-- Enhanced Customer Section -->
            <VBox spacing="12.0" styleClass="form-container" minWidth="400.0">
               <HBox.hgrow>ALWAYS</HBox.hgrow>
               <children>
                  <Label styleClass="form-title" text="Customer">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>

                  <!-- Customer Search Row -->
                  <HBox spacing="8.0" alignment="CENTER_LEFT">
                     <children>
                        <TextField fx:id="txtCustomerSearch" onKeyReleased="#handleCustomerSearch" promptText="Search by name, phone, email...">
                           <HBox.hgrow>ALWAYS</HBox.hgrow>
                        </TextField>
                        <ComboBox fx:id="cmbCustomerGroup" onAction="#handleCustomerGroupFilter" promptText="Group" prefWidth="80.0" />
                        <Button fx:id="btnClearCustomer" onAction="#handleClearCustomerSearch" text="✕" styleClass="btn-clear" />
                     </children>
                  </HBox>

                  <!-- Customer Selection Results -->
                  <VBox fx:id="customerSearchResults" spacing="4.0" visible="false" managed="false" maxHeight="120.0">
                     <children>
                        <Label text="Search Results:" styleClass="search-results-label" />
                        <ScrollPane fitToWidth="true" maxHeight="100.0" styleClass="search-results-scroll">
                           <content>
                              <VBox fx:id="customerResultsList" spacing="2.0" />
                           </content>
                        </ScrollPane>
                     </children>
                  </VBox>

                  <!-- Selected Customer Info -->
                  <VBox fx:id="customerInfo" spacing="6.0" visible="false" managed="false" styleClass="selected-customer-info">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label fx:id="lblCustomerName" text="Customer Name" styleClass="customer-name" />
                              <Region>
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Region>
                              <Button fx:id="btnRemoveCustomer" onAction="#handleRemoveCustomer" text="Remove" styleClass="btn-remove" />
                           </children>
                        </HBox>
                        <HBox spacing="15.0">
                           <children>
                              <Label fx:id="lblCustomerPhone" text="Phone: " styleClass="customer-detail" />
                           </children>
                        </HBox>
                        <HBox spacing="15.0">
                           <children>
                              <Label fx:id="lblCustomerGroup" text="Group: " styleClass="customer-detail" />
                              <Label fx:id="lblCustomerPoints" text="Points: 0" styleClass="customer-points" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Default Customer Display -->
                  <Label fx:id="lblSelectedCustomer" text="Walk-in Customer" styleClass="walk-in-customer" />

                  <!-- Action Buttons -->
                  <HBox spacing="8.0">
                     <children>
                        <Button fx:id="btnNewCustomer" onAction="#handleNewCustomer" text="👤 New Customer" styleClass="btn-action" />
                        <Button fx:id="btnFindTransaction" onAction="#handleFindTransaction" text="🔍 Find Transaction" styleClass="btn-action" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
               </padding>
            </VBox>

            <!-- Payment Section -->
            <VBox spacing="12.0" styleClass="form-container" minWidth="350.0">
               <HBox.hgrow>ALWAYS</HBox.hgrow>
               <children>
                  <Label styleClass="card-header" text="💳 Payment Processing">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>

                  <VBox spacing="10.0">
                     <children>
                        <Label text="Payment Method:" />
                        <ComboBox fx:id="cmbPaymentMethod" prefWidth="220.0" promptText="Select Payment Method" styleClass="combo-box" />
                     </children>
                  </VBox>

                  <VBox spacing="10.0">
                     <children>
                        <Label text="Amount Received:" />
                        <TextField fx:id="txtAmountReceived" prefWidth="170.0" promptText="0.00" styleClass="text-field" />
                     </children>
                  </VBox>

                  <HBox spacing="12.0" styleClass="amount-display">
                     <children>
                        <Label text="Change:" />
                        <Region>
                           <HBox.hgrow>ALWAYS</HBox.hgrow>
                        </Region>
                        <Label fx:id="lblChange" styleClass="amount-positive" text="$0.00" />
                     </children>
                  </HBox>

                  <!-- Enhanced Action Buttons -->
                  <VBox spacing="12.0">
                     <children>
                        <Button fx:id="btnProcessPayment" onAction="#handleProcessPayment" prefHeight="50.0" styleClass="btn-success" text="💳 Process Payment" />
                        <Button fx:id="btnMultiplePayments" onAction="#handleMultiplePayments" prefHeight="45.0" styleClass="btn-info" text="💰 Multiple Payment Methods" />
                        <HBox spacing="12.0">
                           <children>
                              <Button fx:id="btnHoldTransaction" onAction="#handleHoldTransaction" styleClass="btn-warning" text="⏸ Hold">
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Button>
                              <Button fx:id="btnVoidTransaction" onAction="#handleVoidTransaction" styleClass="btn-danger" text="❌ Void">
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Button>
                           </children>
                        </HBox>
                        <Button fx:id="btnPrintReceipt" onAction="#handlePrintReceipt" styleClass="btn-primary" text="🖨 Print Receipt" />
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
               </padding>
            </VBox>
         </children>
      </HBox>

      <!-- Status Bar -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label fx:id="lblStatus" text="Ready for new transaction" />
            <Region>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </Region>
            <Label fx:id="lblItemCount" text="Items: 0" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>
   </children>
</VBox>
