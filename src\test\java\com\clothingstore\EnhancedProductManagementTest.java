package com.clothingstore;

import com.clothingstore.model.Product;
import com.clothingstore.model.Supplier;
import com.clothingstore.model.SupplierStatus;
import com.clothingstore.service.SupplierService;
import com.clothingstore.service.SupplierOperationResult;

import java.math.BigDecimal;

/**
 * Test class to verify enhanced product management functionality
 * including supplier integration and cost management
 */
public class EnhancedProductManagementTest {

    public static void main(String[] args) {
        System.out.println("Testing Enhanced Product Management with Supplier Integration...\n");

        // Test 1: Create a supplier for testing
        System.out.println("=== Step 1: Creating Test Supplier ===");
        SupplierService supplierService = SupplierService.getInstance();
        
        Supplier testSupplier = new Supplier();
        testSupplier.setCompanyName("Fashion Wholesale Inc.");
        testSupplier.setContactPerson("Sarah Johnson");
        testSupplier.setEmail("<EMAIL>");
        testSupplier.setPhone("******-FASHION");
        testSupplier.setAddress("456 Wholesale Ave");
        testSupplier.setCity("Fashion City");
        testSupplier.setState("FC");
        testSupplier.setZipCode("54321");
        testSupplier.setCountry("USA");
        testSupplier.setStatus(SupplierStatus.ACTIVE);
        testSupplier.setLeadTimeDays(7);
        testSupplier.setMinimumOrderAmount(new BigDecimal("1000.00"));
        testSupplier.setCreditLimit(new BigDecimal("25000.00"));
        testSupplier.setNotes("Premium fashion supplier with fast delivery");

        SupplierOperationResult supplierResult = supplierService.createSupplier(testSupplier);
        if (supplierResult.isSuccess()) {
            System.out.println("SUCCESS: Test supplier created");
            System.out.println("  Company: " + supplierResult.getSupplier().getCompanyName());
            System.out.println("  ID: " + supplierResult.getSupplier().getId());
            System.out.println("  Status: " + supplierResult.getSupplier().getStatus());
        } else {
            System.out.println("ERROR: Failed to create supplier: " + supplierResult.getMessage());
            return;
        }

        Supplier createdSupplier = supplierResult.getSupplier();
        System.out.println();

        // Test 2: Create enhanced products with cost management
        System.out.println("=== Step 2: Creating Enhanced Products ===");
        
        // Product 1: High-profit item
        Product product1 = createTestProduct(
            "Premium T-Shirt", "TSHIRT-001", "Shirts", "Premium Brand",
            new BigDecimal("15.00"), new BigDecimal("45.00"), 50,
            createdSupplier
        );
        testProductFinancials(product1, "Premium T-Shirt");

        // Product 2: Low-profit item
        Product product2 = createTestProduct(
            "Basic Jeans", "JEANS-001", "Pants", "Basic Brand",
            new BigDecimal("25.00"), new BigDecimal("35.00"), 30,
            createdSupplier
        );
        testProductFinancials(product2, "Basic Jeans");

        // Product 3: Loss-making item (for validation testing)
        Product product3 = createTestProduct(
            "Clearance Jacket", "JACKET-001", "Jackets", "Clearance Brand",
            new BigDecimal("40.00"), new BigDecimal("30.00"), 10,
            createdSupplier
        );
        testProductFinancials(product3, "Clearance Jacket");

        System.out.println();

        // Test 3: Financial Analysis
        System.out.println("=== Step 3: Financial Analysis ===");
        testFinancialAnalysis(product1, product2, product3);

        System.out.println();

        // Test 4: Supplier Integration
        System.out.println("=== Step 4: Supplier Integration Testing ===");
        testSupplierIntegration(product1, createdSupplier);

        System.out.println();

        // Test 5: Validation Testing
        System.out.println("=== Step 5: Validation Testing ===");
        testValidation();

        System.out.println();

        // Clean up
        System.out.println("=== Cleanup ===");
        SupplierOperationResult deleteResult = supplierService.deleteSupplier(createdSupplier.getId());
        if (deleteResult.isSuccess()) {
            System.out.println("SUCCESS: Test supplier cleaned up");
        } else {
            System.out.println("INFO: Cleanup note: " + deleteResult.getMessage());
        }

        System.out.println("\n=== Enhanced Product Management Test Completed ===");
        System.out.println("All supplier integration and cost management features are working!");
    }

    private static Product createTestProduct(String name, String sku, String category, String brand,
                                           BigDecimal costPrice, BigDecimal sellingPrice, int stock,
                                           Supplier supplier) {
        Product product = new Product();
        product.setName(name);
        product.setSku(sku);
        product.setCategory(category);
        product.setBrand(brand);
        product.setCostPrice(costPrice);
        product.setPrice(sellingPrice);
        product.setStockQuantity(stock);
        product.setMinStockLevel(10);
        product.setReorderQuantity(25);
        product.setDescription("Test product for enhanced functionality");
        
        // Set supplier information
        product.setSupplierId(supplier.getId());
        product.setSupplierName(supplier.getCompanyName());
        product.setSupplierCode(supplier.getSupplierCode());
        
        return product;
    }

    private static void testProductFinancials(Product product, String productName) {
        System.out.println("--- " + productName + " Financial Analysis ---");
        System.out.println("  Cost Price: $" + product.getCostPrice());
        System.out.println("  Selling Price: $" + product.getPrice());
        System.out.println("  Profit per Unit: $" + product.getProfit());
        System.out.println("  Profit Margin: " + product.getProfitMarginFormatted());
        System.out.println("  Stock Quantity: " + product.getStockQuantity());
        System.out.println("  Total Investment: $" + product.getTotalInvestment());
        System.out.println("  Potential Revenue: $" + product.getPotentialRevenue());
        System.out.println("  Potential Profit: $" + product.getPotentialProfit());
        System.out.println("  Valid Pricing: " + (product.isValidPricing() ? "YES" : "NO - LOSS MAKING"));
        System.out.println("  Supplier: " + product.getSupplierDisplayName());
        System.out.println();
    }

    private static void testFinancialAnalysis(Product... products) {
        BigDecimal totalInvestment = BigDecimal.ZERO;
        BigDecimal totalPotentialRevenue = BigDecimal.ZERO;
        BigDecimal totalPotentialProfit = BigDecimal.ZERO;
        int profitableProducts = 0;
        int lossProducts = 0;

        for (Product product : products) {
            totalInvestment = totalInvestment.add(product.getTotalInvestment());
            totalPotentialRevenue = totalPotentialRevenue.add(product.getPotentialRevenue());
            totalPotentialProfit = totalPotentialProfit.add(product.getPotentialProfit());
            
            if (product.isValidPricing()) {
                profitableProducts++;
            } else {
                lossProducts++;
            }
        }

        System.out.println("Portfolio Analysis:");
        System.out.println("  Total Products: " + products.length);
        System.out.println("  Profitable Products: " + profitableProducts);
        System.out.println("  Loss-making Products: " + lossProducts);
        System.out.println("  Total Investment: $" + totalInvestment);
        System.out.println("  Total Potential Revenue: $" + totalPotentialRevenue);
        System.out.println("  Total Potential Profit: $" + totalPotentialProfit);
        
        if (totalInvestment.compareTo(BigDecimal.ZERO) > 0) {
            double overallMargin = totalPotentialProfit.divide(totalInvestment, 4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100;
            System.out.println("  Overall Profit Margin: " + String.format("%.2f%%", overallMargin));
        }
    }

    private static void testSupplierIntegration(Product product, Supplier supplier) {
        System.out.println("Supplier Integration Test:");
        System.out.println("  Product: " + product.getName());
        System.out.println("  Linked Supplier ID: " + product.getSupplierId());
        System.out.println("  Supplier Name: " + product.getSupplierName());
        System.out.println("  Supplier Code: " + product.getSupplierCode());
        System.out.println("  Supplier Display: " + product.getSupplierDisplayName());
        System.out.println("  Has Supplier: " + (product.hasSupplier() ? "YES" : "NO"));
        
        // Test supplier information consistency
        boolean consistent = product.getSupplierId().equals(supplier.getId()) &&
                           product.getSupplierName().equals(supplier.getCompanyName()) &&
                           product.getSupplierCode().equals(supplier.getSupplierCode());
        
        System.out.println("  Data Consistency: " + (consistent ? "PASS" : "FAIL"));
        
        // Test supplier lead time impact
        System.out.println("  Supplier Lead Time: " + supplier.getLeadTimeDays() + " days");
        System.out.println("  Supplier Status: " + supplier.getStatus());
        System.out.println("  Can Receive Orders: " + (supplier.getStatus().canReceiveOrders() ? "YES" : "NO"));
    }

    private static void testValidation() {
        System.out.println("Validation Tests:");
        
        // Test 1: Valid pricing
        Product validProduct = new Product();
        validProduct.setCostPrice(new BigDecimal("10.00"));
        validProduct.setPrice(new BigDecimal("20.00"));
        System.out.println("  Valid Pricing (Cost: $10, Price: $20): " + 
                         (validProduct.isValidPricing() ? "PASS" : "FAIL"));
        
        // Test 2: Invalid pricing (loss)
        Product invalidProduct = new Product();
        invalidProduct.setCostPrice(new BigDecimal("20.00"));
        invalidProduct.setPrice(new BigDecimal("15.00"));
        System.out.println("  Invalid Pricing (Cost: $20, Price: $15): " + 
                         (!invalidProduct.isValidPricing() ? "PASS" : "FAIL"));
        
        // Test 3: Null pricing (should be valid)
        Product nullProduct = new Product();
        System.out.println("  Null Pricing Handling: " + 
                         (nullProduct.isValidPricing() ? "PASS" : "FAIL"));
        
        // Test 4: Financial summary
        System.out.println("  Financial Summary Generation: " + 
                         (validProduct.getFinancialSummary().contains("Cost:") ? "PASS" : "FAIL"));
    }
}
