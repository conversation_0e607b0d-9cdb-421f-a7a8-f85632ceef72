<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>


<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.EmailReceiptController">
   <children>
      <!-- Header Section -->
      <VBox spacing="15.0" styleClass="header-section">
         <children>
            <!-- Title and Actions -->
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="page-title" text="Email Receipt Management" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="configureEmailButton" onAction="#handleConfigureEmail" styleClass="secondary-button" text="⚙️ Configure" />
                  <Button onAction="#handleTestConfiguration" styleClass="secondary-button" text="🧪 Test Config" />
                  <Button fx:id="sendEmailButton" onAction="#handleSendEmail" styleClass="secondary-button" text="📧 Send Email" />
                  <Button onAction="#handleRefreshData" styleClass="primary-button" text="🔄 Refresh" />
               </children>
            </HBox>
            
            <!-- Summary Cards -->
            <HBox spacing="15.0" styleClass="summary-section">
               <children>
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card">
                     <children>
                        <Label styleClass="summary-title" text="📧 Total Emails" />
                        <Label fx:id="totalEmailsLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card success">
                     <children>
                        <Label styleClass="summary-title" text="✅ Sent" />
                        <Label fx:id="sentEmailsLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card danger">
                     <children>
                        <Label styleClass="summary-title" text="❌ Failed" />
                        <Label fx:id="failedEmailsLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card warning">
                     <children>
                        <Label styleClass="summary-title" text="⏳ Pending" />
                        <Label fx:id="pendingEmailsLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Filter Controls -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="📧 Email:" />
                  <TextField fx:id="emailSearchField" prefWidth="150.0" promptText="Search email..." styleClass="search-box" />
                  <Label styleClass="filter-label" text="Transaction:" />
                  <TextField fx:id="transactionSearchField" prefWidth="120.0" promptText="Transaction..." styleClass="search-box" />
                  <Label styleClass="filter-label" text="Status:" />
                  <ComboBox fx:id="statusFilter" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="From:" />
                  <DatePicker fx:id="startDatePicker" prefWidth="130.0" styleClass="form-field" />
                  <Label styleClass="filter-label" text="To:" />
                  <DatePicker fx:id="endDatePicker" prefWidth="130.0" styleClass="form-field" />
                  <Button fx:id="searchButton" onAction="#handleSearch" styleClass="primary-button" text="🔍 Search" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </VBox>

      <!-- Main Content -->
      <SplitPane dividerPositions="0.6" VBox.vgrow="ALWAYS">
         <items>
            <!-- Left Panel - Email Receipt List -->
            <VBox spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="📋 Email Receipt History" />
                  
                  <TableView fx:id="emailReceiptTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="transactionNumberColumn" prefWidth="100.0" text="Transaction" />
                        <TableColumn fx:id="customerEmailColumn" prefWidth="150.0" text="Email" />
                        <TableColumn fx:id="customerNameColumn" prefWidth="120.0" text="Customer" />
                        <TableColumn fx:id="statusColumn" prefWidth="80.0" text="Status" />
                        <TableColumn fx:id="sentAtColumn" prefWidth="120.0" text="Sent At" />
                        <TableColumn fx:id="retryCountColumn" prefWidth="60.0" text="Retries" />
                        <TableColumn fx:id="errorColumn" prefWidth="150.0" text="Error" />
                     </columns>
                  </TableView>
               </children>
               <padding>
                  <Insets bottom="10.0" left="15.0" right="5.0" top="10.0" />
               </padding>
            </VBox>
            
            <!-- Right Panel - Details and Preview -->
            <VBox spacing="15.0">
               <children>
                  <!-- Details Section -->
                  <VBox spacing="10.0" styleClass="details-section">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="15.0">
                           <children>
                              <Label styleClass="section-title" text="📄 Email Details" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="retryButton" onAction="#handleRetry" styleClass="action-button retry" text="🔄 Retry" />
                              <Button fx:id="resendButton" onAction="#handleResend" styleClass="action-button resend" text="📧 Resend" />
                              <Button fx:id="deleteButton" onAction="#handleDelete" styleClass="action-button delete" text="🗑️ Delete" />
                           </children>
                        </HBox>
                        
                        <GridPane hgap="10.0" vgap="8.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           
                           <Label styleClass="detail-label" text="Transaction:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                           <Label fx:id="detailsTransactionLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                           
                           <Label styleClass="detail-label" text="Email:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                           <Label fx:id="detailsEmailLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                           
                           <Label styleClass="detail-label" text="Status:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                           <Label fx:id="detailsStatusLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                           
                           <Label styleClass="detail-label" text="Sent At:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                           <Label fx:id="detailsSentAtLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           
                           <Label styleClass="detail-label" text="Retry Count:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                           <Label fx:id="detailsRetryCountLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                           
                           <Label styleClass="detail-label" text="Provider:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                           <Label fx:id="detailsProviderLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                           
                           <Label styleClass="detail-label" text="Message ID:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                           <Label fx:id="detailsMessageIdLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                        </GridPane>
                        
                        <VBox spacing="5.0">
                           <children>
                              <Label styleClass="detail-label" text="Error Message:" />
                              <TextArea fx:id="detailsErrorArea" editable="false" prefRowCount="3" styleClass="notes-area" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Receipt Preview Section -->
                  <VBox spacing="10.0" VBox.vgrow="ALWAYS">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="section-title" text="📄 Receipt Preview" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="previewButton" onAction="#handlePreview" styleClass="secondary-button" text="👁️ Preview" />
                           </children>
                        </HBox>
                        
                        <TextArea fx:id="receiptPreview" editable="false" prefHeight="400.0" styleClass="receipt-preview" VBox.vgrow="ALWAYS" wrapText="true" />
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="5.0" right="15.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </children>
</VBox>
