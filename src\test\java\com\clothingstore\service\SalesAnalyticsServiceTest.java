package com.clothingstore.service;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.SalesMetrics;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test suite for SalesAnalyticsService
 * Tests all major functionality including validation, metrics calculation, and error handling
 */
public class SalesAnalyticsServiceTest {

    private SalesAnalyticsService analyticsService;
    private LocalDateTime testStartDate;
    private LocalDateTime testEndDate;

    @BeforeEach
    void setUp() {
        analyticsService = SalesAnalyticsService.getInstance();
        testStartDate = LocalDateTime.now().minusDays(30);
        testEndDate = LocalDateTime.now();
    }

    @Test
    @DisplayName("Test SalesAnalyticsService singleton instance")
    void testSingletonInstance() {
        SalesAnalyticsService instance1 = SalesAnalyticsService.getInstance();
        SalesAnalyticsService instance2 = SalesAnalyticsService.getInstance();
        
        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2, "Should return the same singleton instance");
    }

    @Test
    @DisplayName("Test date range validation - valid dates")
    void testValidDateRangeValidation() {
        LocalDateTime start = LocalDateTime.now().minusDays(7);
        LocalDateTime end = LocalDateTime.now();
        
        // This should not throw an exception and should return metrics
        SalesMetrics metrics = analyticsService.generateSalesMetrics(start, end);
        assertNotNull(metrics);
        assertEquals(start, metrics.getPeriodStart());
        assertEquals(end, metrics.getPeriodEnd());
    }

    @Test
    @DisplayName("Test date range validation - null start date")
    void testNullStartDateValidation() {
        LocalDateTime end = LocalDateTime.now();
        
        // Should return empty metrics due to validation failure
        SalesMetrics metrics = analyticsService.generateSalesMetrics(null, end);
        assertNotNull(metrics);
        // Should have default zero values due to validation failure
        assertEquals(BigDecimal.ZERO, metrics.getTotalRevenue());
    }

    @Test
    @DisplayName("Test date range validation - null end date")
    void testNullEndDateValidation() {
        LocalDateTime start = LocalDateTime.now().minusDays(7);
        
        // Should return empty metrics due to validation failure
        SalesMetrics metrics = analyticsService.generateSalesMetrics(start, null);
        assertNotNull(metrics);
        // Should have default zero values due to validation failure
        assertEquals(BigDecimal.ZERO, metrics.getTotalRevenue());
    }

    @Test
    @DisplayName("Test date range validation - start after end")
    void testInvalidDateOrderValidation() {
        LocalDateTime start = LocalDateTime.now();
        LocalDateTime end = LocalDateTime.now().minusDays(7);
        
        // Should return empty metrics due to validation failure
        SalesMetrics metrics = analyticsService.generateSalesMetrics(start, end);
        assertNotNull(metrics);
        // Should have default zero values due to validation failure
        assertEquals(BigDecimal.ZERO, metrics.getTotalRevenue());
    }

    @Test
    @DisplayName("Test phone number validation")
    void testPhoneNumberValidation() {
        // Test valid 11-digit phone number
        String validPhone = "12345678901";
        String result = analyticsService.testPhoneValidation(validPhone);
        assertNotNull(result);
        assertTrue(result.contains("VALID"), "Valid 11-digit phone should pass validation");

        // Test invalid phone number (too short)
        String invalidPhone = "123456789";
        result = analyticsService.testPhoneValidation(invalidPhone);
        assertNotNull(result);
        assertTrue(result.contains("INVALID"), "Short phone number should fail validation");

        // Test invalid phone number (too long)
        String longPhone = "123456789012";
        result = analyticsService.testPhoneValidation(longPhone);
        assertNotNull(result);
        assertTrue(result.contains("INVALID"), "Long phone number should fail validation");

        // Test phone with formatting
        String formattedPhone = "+****************";
        result = analyticsService.testPhoneValidation(formattedPhone);
        assertNotNull(result);
        assertTrue(result.contains("VALID"), "Formatted 11-digit phone should pass validation");
    }

    @Test
    @DisplayName("Test customer filtering functionality")
    void testCustomerFiltering() {
        LocalDateTime periodStart = LocalDateTime.now().minusDays(30);
        
        // Create test customers
        List<Customer> testCustomers = createTestCustomers(periodStart);
        
        // Test bulk filtering
        List<Customer> newCustomers = analyticsService.filterNewCustomers(testCustomers, periodStart);
        assertNotNull(newCustomers);
        
        // Should include customers registered at or after period start
        assertTrue(newCustomers.size() >= 2, "Should find at least 2 new customers");
    }

    @Test
    @DisplayName("Test customer filtering validation")
    void testCustomerFilteringValidation() {
        LocalDateTime periodStart = LocalDateTime.now().minusDays(30);
        List<Customer> testCustomers = createTestCustomers(periodStart);
        
        SalesAnalyticsService.ValidationResult result = 
            analyticsService.validateCustomerFiltering(testCustomers, periodStart);
        
        assertNotNull(result);
        assertTrue(result.isValid(), "Customer filtering validation should pass");
        assertTrue(result.isConsistent(), "Individual and bulk filtering should be consistent");
        assertEquals(0, result.getErrorCount(), "Should have no errors");
    }

    @Test
    @DisplayName("Test daily sales data generation")
    void testDailySalesDataGeneration() {
        int days = 7;
        List<SalesAnalyticsService.DailySalesData> dailyData = 
            analyticsService.getDailySalesData(days);
        
        assertNotNull(dailyData);
        assertEquals(days, dailyData.size(), "Should return data for requested number of days");
        
        // Verify each day has valid data structure
        for (SalesAnalyticsService.DailySalesData data : dailyData) {
            assertNotNull(data.getDate());
            assertNotNull(data.getRevenue());
            assertTrue(data.getTransactionCount() >= 0);
            assertNotNull(data.getFormattedDate());
        }
    }

    @Test
    @DisplayName("Test metrics comparison functionality")
    void testMetricsComparison() {
        LocalDateTime period1Start = LocalDateTime.now().minusDays(60);
        LocalDateTime period1End = LocalDateTime.now().minusDays(30);
        LocalDateTime period2Start = LocalDateTime.now().minusDays(30);
        LocalDateTime period2End = LocalDateTime.now();
        
        SalesAnalyticsService.MetricsComparison comparison = 
            analyticsService.compareMetrics(period1Start, period1End, period2Start, period2End);
        
        assertNotNull(comparison);
        assertNotNull(comparison.getPeriod1());
        assertNotNull(comparison.getPeriod2());
        assertNotNull(comparison.getRevenueGrowth());
    }

    /**
     * Helper method to create test customers for filtering tests
     */
    private List<Customer> createTestCustomers(LocalDateTime periodStart) {
        List<Customer> customers = new ArrayList<>();
        
        // Customer registered before period (should not be new)
        Customer oldCustomer = new Customer();
        oldCustomer.setId(1L);
        oldCustomer.setFirstName("Old");
        oldCustomer.setLastName("Customer");
        oldCustomer.setRegistrationDate(periodStart.minusDays(1));
        customers.add(oldCustomer);
        
        // Customer registered exactly at period start (should be new)
        Customer exactCustomer = new Customer();
        exactCustomer.setId(2L);
        exactCustomer.setFirstName("Exact");
        exactCustomer.setLastName("Customer");
        exactCustomer.setRegistrationDate(periodStart);
        customers.add(exactCustomer);
        
        // Customer registered after period start (should be new)
        Customer newCustomer = new Customer();
        newCustomer.setId(3L);
        newCustomer.setFirstName("New");
        newCustomer.setLastName("Customer");
        newCustomer.setRegistrationDate(periodStart.plusDays(1));
        customers.add(newCustomer);
        
        // Customer with null registration date (should not be new)
        Customer nullCustomer = new Customer();
        nullCustomer.setId(4L);
        nullCustomer.setFirstName("Null");
        nullCustomer.setLastName("Customer");
        nullCustomer.setRegistrationDate(null);
        customers.add(nullCustomer);
        
        return customers;
    }

    @Test
    @DisplayName("Test error handling with invalid parameters")
    void testErrorHandling() {
        // Test with negative days parameter
        List<SalesAnalyticsService.DailySalesData> result = analyticsService.getDailySalesData(-1);
        assertNotNull(result);
        assertTrue(result.isEmpty(), "Should return empty list for invalid days parameter");
        
        // Test with extremely large days parameter
        result = analyticsService.getDailySalesData(1000001);
        assertNotNull(result);
        assertTrue(result.isEmpty(), "Should return empty list for excessive days parameter");
    }

    @Test
    @DisplayName("Test null safety in customer filtering")
    void testNullSafetyInCustomerFiltering() {
        // Test with null customer list
        assertThrows(IllegalArgumentException.class, () -> {
            analyticsService.filterNewCustomers(null, LocalDateTime.now());
        }, "Should throw exception for null customer list");
        
        // Test with null period start
        List<Customer> customers = new ArrayList<>();
        List<Customer> result = analyticsService.filterNewCustomers(customers, null);
        assertNotNull(result);
        assertTrue(result.isEmpty(), "Should return empty list for null period start");
    }
}
