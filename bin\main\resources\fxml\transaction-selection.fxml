<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.TransactionSelectionController" styleClass="transaction-selection-window">
   <children>
      <!-- Header Section -->
      <VBox styleClass="header-section">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="📋" styleClass="header-icon">
                     <font>
                        <Font size="20.0" />
                     </font>
                  </Label>
                  <Label text="Transaction History" styleClass="header-title">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
               </children>
            </HBox>

            <VBox styleClass="customer-info-container">
               <children>
                  <Label fx:id="lblCustomerInfo" styleClass="customer-info-text" wrapText="true">
                     <font>
                        <Font name="System Bold" size="13.0" />
                     </font>
                     <VBox.margin>
                        <Insets top="8.0" />
                     </VBox.margin>
                  </Label>
                  <Label fx:id="lblTransactionCount" styleClass="transaction-count-text">
                     <font>
                        <Font size="11.0" />
                     </font>
                     <VBox.margin>
                        <Insets top="4.0" />
                     </VBox.margin>
                  </Label>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>

      <!-- Instructions -->
      <HBox styleClass="instructions-section">
         <children>
            <Label text="💡" styleClass="instruction-icon">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
            <Label text="Double-click a transaction to view details, or use the buttons below" styleClass="instruction-text">
               <font>
                  <Font size="11.0" />
               </font>
               <HBox.margin>
                  <Insets left="8.0" />
               </HBox.margin>
            </Label>
         </children>
         <padding>
            <Insets bottom="12.0" left="20.0" right="20.0" top="12.0" />
         </padding>
      </HBox>

      <!-- Transaction Table Container -->
      <VBox VBox.vgrow="ALWAYS" styleClass="table-container">
         <children>
            <TableView fx:id="tblTransactions" VBox.vgrow="ALWAYS" styleClass="enhanced-transaction-table">
               <columns>
                  <TableColumn fx:id="colDate" prefWidth="160.0" text="Date &amp; Time" styleClass="table-column-header" />
                  <TableColumn fx:id="colTransactionNumber" prefWidth="120.0" text="Transaction #" styleClass="table-column-header" />
                  <TableColumn fx:id="colAmount" prefWidth="100.0" text="Amount" styleClass="table-column-header" />
                  <TableColumn fx:id="colStatus" prefWidth="95.0" text="Status" styleClass="table-column-header" />
                  <TableColumn fx:id="colPaymentMethod" prefWidth="110.0" text="Payment" styleClass="table-column-header" />
                  <TableColumn fx:id="colDescription" prefWidth="175.0" text="Description" styleClass="table-column-header" />
               </columns>
            </TableView>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="5.0" />
         </padding>
      </VBox>

      <!-- Footer Section -->
      <Separator styleClass="footer-separator" />
      <HBox styleClass="footer-section">
         <children>
            <!-- Primary Action Buttons -->
            <HBox spacing="12.0" alignment="CENTER_LEFT">
               <children>
                  <Button fx:id="btnViewSelected" mnemonicParsing="false" onAction="#handleViewSelected"
                          text="📄 View Selected" styleClass="primary-button" disable="true">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Button>

                  <Button fx:id="btnViewAll" mnemonicParsing="false" onAction="#handleViewAll"
                          text="📋 View All" styleClass="secondary-button">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Button>
               </children>
            </HBox>

            <!-- Spacer -->
            <Region HBox.hgrow="ALWAYS" />

            <!-- Close Button -->
            <Button fx:id="btnClose" mnemonicParsing="false" onAction="#handleClose"
                    text="✖ Close" styleClass="close-button">
               <font>
                  <Font size="12.0" />
               </font>
            </Button>
         </children>
         <padding>
            <Insets bottom="18.0" left="20.0" right="20.0" top="18.0" />
         </padding>
      </HBox>
   </children>
</VBox>
