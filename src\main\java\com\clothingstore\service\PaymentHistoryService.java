package com.clothingstore.service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;

import com.clothingstore.dao.PaymentHistoryDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.PaymentHistory;
import com.clothingstore.model.PaymentHistory.PaymentStatus;
import com.clothingstore.model.PaymentHistory.PaymentType;
import com.clothingstore.model.Transaction;

/**
 * Service class for PaymentHistory business logic Handles payment history
 * operations and calculations
 */
public class PaymentHistoryService {

    private static final Logger LOGGER = Logger.getLogger(PaymentHistoryService.class.getName());
    private static PaymentHistoryService instance;

    private final PaymentHistoryDAO paymentHistoryDAO;
    private final TransactionDAO transactionDAO;

    private PaymentHistoryService() {
        this.paymentHistoryDAO = PaymentHistoryDAO.getInstance();
        this.transactionDAO = TransactionDAO.getInstance();
    }

    public static synchronized PaymentHistoryService getInstance() {
        if (instance == null) {
            instance = new PaymentHistoryService();
        }
        return instance;
    }

    /**
     * Record a payment for a transaction
     */
    public PaymentHistory recordPayment(Long transactionId, BigDecimal paymentAmount,
            String paymentMethod, String cashierName, String notes) throws SQLException {

        // Get transaction details
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            throw new IllegalArgumentException("Transaction not found with ID: " + transactionId);
        }
        Transaction transaction = transactionOpt.get();

        // Calculate balances
        BigDecimal totalAmountPaid = paymentHistoryDAO.getTotalAmountPaid(transactionId);
        BigDecimal newRunningBalance = totalAmountPaid.add(paymentAmount);
        BigDecimal remainingBalance = transaction.getTotalAmount().subtract(newRunningBalance);

        // Validate payment amount
        if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Payment amount must be greater than zero");
        }

        if (paymentAmount.compareTo(remainingBalance.add(BigDecimal.valueOf(0.01))) > 0) {
            throw new IllegalArgumentException("Payment amount cannot exceed remaining balance");
        }

        // Get next payment sequence
        int paymentSequence = paymentHistoryDAO.getNextPaymentSequence(transactionId);

        // Create payment history record
        PaymentHistory paymentHistory = new PaymentHistory(
                transactionId, paymentSequence, paymentAmount, paymentMethod,
                newRunningBalance, remainingBalance
        );

        paymentHistory.setPaymentType(PaymentType.PAYMENT);
        paymentHistory.setStatus(PaymentStatus.COMPLETED);
        paymentHistory.setCashierName(cashierName);
        paymentHistory.setNotes(notes);
        paymentHistory.setPaymentDate(LocalDateTime.now());

        // Save payment history
        PaymentHistory savedPayment = paymentHistoryDAO.create(paymentHistory);

        // Update transaction status and amount paid
        updateTransactionAfterPayment(transaction, newRunningBalance, remainingBalance);

        LOGGER.info("Recorded payment of " + paymentAmount + " for transaction " + transactionId);
        return savedPayment;
    }

    /**
     * Record a refund for a transaction
     */
    public PaymentHistory recordRefund(Long transactionId, BigDecimal refundAmount,
            String paymentMethod, String cashierName, String notes) throws SQLException {

        // Get transaction details
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            throw new IllegalArgumentException("Transaction not found with ID: " + transactionId);
        }
        Transaction transaction = transactionOpt.get();

        // Calculate balances
        BigDecimal totalAmountPaid = paymentHistoryDAO.getTotalAmountPaid(transactionId);
        BigDecimal totalAmountRefunded = paymentHistoryDAO.getTotalAmountRefunded(transactionId);
        BigDecimal newTotalRefunded = totalAmountRefunded.add(refundAmount);

        // Special balance calculation for COMPLETED transactions without payment history
        BigDecimal newRunningBalance;
        BigDecimal remainingBalance;
        if ("COMPLETED".equals(transaction.getStatus()) && totalAmountPaid.compareTo(BigDecimal.ZERO) == 0) {
            // For COMPLETED transactions without payment history:
            // - Running balance represents net amount (total paid - total refunded)
            // - Since no payments were recorded, running balance = -totalRefunded
            // - Remaining balance = total amount - total refunded
            newRunningBalance = newTotalRefunded.negate(); // Negative because it's all refunds
            remainingBalance = transaction.getTotalAmount().subtract(newTotalRefunded);
        } else {
            // Standard calculation for transactions with payment history
            newRunningBalance = totalAmountPaid.subtract(newTotalRefunded);
            remainingBalance = transaction.getTotalAmount().subtract(newRunningBalance);
        }

        // Validate refund amount
        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Refund amount must be greater than zero");
        }

        // Special handling for COMPLETED transactions without payment history
        BigDecimal maxRefundableAmount;
        if ("COMPLETED".equals(transaction.getStatus()) && totalAmountPaid.compareTo(BigDecimal.ZERO) == 0) {
            // For COMPLETED transactions without payment history, allow refunds up to transaction total
            maxRefundableAmount = transaction.getTotalAmount();
            LOGGER.info("COMPLETED transaction " + transactionId + " has no payment history. "
                    + "Using transaction total (" + transaction.getTotalAmount() + ") as maximum refundable amount.");
        } else {
            // Standard calculation for transactions with payment history
            maxRefundableAmount = totalAmountPaid;
        }

        if (newTotalRefunded.compareTo(maxRefundableAmount.add(BigDecimal.valueOf(0.01))) > 0) {
            throw new IllegalArgumentException("Total refund amount cannot exceed total amount paid");
        }

        // Get next payment sequence
        int paymentSequence = paymentHistoryDAO.getNextPaymentSequence(transactionId);

        // Create refund history record
        PaymentHistory refundHistory = new PaymentHistory(
                transactionId, paymentSequence, refundAmount, paymentMethod,
                newRunningBalance, remainingBalance
        );

        refundHistory.setPaymentType(PaymentType.REFUND);
        refundHistory.setStatus(PaymentStatus.COMPLETED);
        refundHistory.setCashierName(cashierName);
        refundHistory.setNotes(notes);
        refundHistory.setPaymentDate(LocalDateTime.now());

        // Save refund history
        PaymentHistory savedRefund = paymentHistoryDAO.create(refundHistory);

        // Update transaction status and refunded amount
        updateTransactionAfterRefund(transaction, newTotalRefunded, newRunningBalance, remainingBalance);

        LOGGER.info("Recorded refund of " + refundAmount + " for transaction " + transactionId);
        return savedRefund;
    }

    /**
     * Get payment history for a transaction
     */
    public List<PaymentHistory> getPaymentHistory(Long transactionId) throws SQLException {
        return paymentHistoryDAO.findByTransactionId(transactionId);
    }

    /**
     * Get refund history for a transaction
     */
    public List<PaymentHistory> getRefundHistory(Long transactionId) throws SQLException {
        return paymentHistoryDAO.findByTransactionIdAndType(transactionId, PaymentType.REFUND);
    }

    /**
     * Get payment history (excluding refunds) for a transaction
     */
    public List<PaymentHistory> getPaymentHistoryOnly(Long transactionId) throws SQLException {
        return paymentHistoryDAO.findByTransactionIdAndType(transactionId, PaymentType.PAYMENT);
    }

    /**
     * Get payment history for a transaction by type
     */
    public List<PaymentHistory> getPaymentHistoryByType(Long transactionId, PaymentType paymentType) throws SQLException {
        return paymentHistoryDAO.findByTransactionIdAndType(transactionId, paymentType);
    }

    /**
     * Get current remaining balance for a transaction
     */
    public BigDecimal getCurrentRemainingBalance(Long transactionId) throws SQLException {
        return paymentHistoryDAO.getCurrentRemainingBalance(transactionId);
    }

    /**
     * Get total amount paid for a transaction
     */
    public BigDecimal getTotalAmountPaid(Long transactionId) throws SQLException {
        return paymentHistoryDAO.getTotalAmountPaid(transactionId);
    }

    /**
     * Get total amount refunded for a transaction
     */
    public BigDecimal getTotalAmountRefunded(Long transactionId) throws SQLException {
        return paymentHistoryDAO.getTotalAmountRefunded(transactionId);
    }

    /**
     * Check if transaction is fully paid
     */
    public boolean isTransactionFullyPaid(Long transactionId) throws SQLException {
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            return false;
        }
        Transaction transaction = transactionOpt.get();

        BigDecimal totalAmountPaid = getTotalAmountPaid(transactionId);
        BigDecimal totalAmountRefunded = getTotalAmountRefunded(transactionId);
        BigDecimal netAmountPaid = totalAmountPaid.subtract(totalAmountRefunded);

        return netAmountPaid.compareTo(transaction.getTotalAmount()) >= 0;
    }

    /**
     * Get payment history within date range
     */
    public List<PaymentHistory> getPaymentHistoryByDateRange(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        return paymentHistoryDAO.findByDateRange(startDate, endDate);
    }

    /**
     * Update transaction after payment
     */
    private void updateTransactionAfterPayment(Transaction transaction, BigDecimal newRunningBalance,
            BigDecimal remainingBalance) throws SQLException {

        transaction.setAmountPaid(newRunningBalance);

        // Update transaction status based on remaining balance
        if (remainingBalance.compareTo(BigDecimal.valueOf(0.01)) <= 0) {
            transaction.setStatus("COMPLETED");
        } else {
            transaction.setStatus("PARTIAL_PAYMENT");
        }

        transactionDAO.save(transaction);
    }

    /**
     * Update transaction after refund
     */
    private void updateTransactionAfterRefund(Transaction transaction, BigDecimal totalRefunded,
            BigDecimal newRunningBalance, BigDecimal remainingBalance) throws SQLException {

        transaction.setRefundedAmount(totalRefunded);
        transaction.setAmountPaid(newRunningBalance);

        // Update transaction status based on refund and payment status
        if (totalRefunded.compareTo(transaction.getTotalAmount()) >= 0) {
            transaction.setStatus("REFUNDED");
        } else if (totalRefunded.compareTo(BigDecimal.ZERO) > 0) {
            if (remainingBalance.compareTo(BigDecimal.valueOf(0.01)) <= 0) {
                transaction.setStatus("PARTIALLY_REFUNDED");
            } else {
                transaction.setStatus("PARTIAL_PAYMENT");
            }
        }

        transactionDAO.save(transaction);
    }
}
