package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Product;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;

/**
 * Controller for Product Management interface
 */
public class ProductManagementController implements Initializable {

    @FXML
    private TextField txtSearch;
    @FXML
    private ComboBox<String> cmbCategory;
    @FXML
    private ComboBox<String> cmbBrand;
    @FXML
    private ComboBox<String> cmbSize;
    @FXML
    private ComboBox<String> cmbColor;
    @FXML
    private ComboBox<String> cmbSupplier;
    @FXML
    private TextField txtMinPrice;
    @FXML
    private TextField txtMaxPrice;
    @FXML
    private ComboBox<String> cmbStockStatus;
    @FXML
    private Button btnManageCategories;
    @FXML
    private Button btnManageSuppliers;
    @FXML
    private Button btnAddProduct;
    @FXML
    private Button btnRefresh;
    @FXML
    private Button btnClearFilters;
    @FXML
    private Button btnAdvancedSearch;
    @FXML
    private Button btnExport;
    @FXML
    private Button btnLowStockReport;
    @FXML
    private Button btnTopProducts;
    @FXML
    private Button btnOutOfStockReport;

    @FXML
    private TableView<Product> tblProducts;
    @FXML
    private TableColumn<Product, String> colSku;
    @FXML
    private TableColumn<Product, String> colName;
    @FXML
    private TableColumn<Product, String> colCategory;
    @FXML
    private TableColumn<Product, String> colBrand;
    @FXML
    private TableColumn<Product, String> colSupplier;
    @FXML
    private TableColumn<Product, String> colSize;
    @FXML
    private TableColumn<Product, String> colColor;
    @FXML
    private TableColumn<Product, String> colPrice;
    @FXML
    private TableColumn<Product, Integer> colStock;
    @FXML
    private TableColumn<Product, Integer> colMinStock;
    @FXML
    private TableColumn<Product, String> colStatus;
    @FXML
    private TableColumn<Product, String> colActions;

    @FXML
    private MenuItem menuEdit;
    @FXML
    private MenuItem menuDuplicate;
    @FXML
    private MenuItem menuAdjustStock;
    @FXML
    private MenuItem menuViewHistory;
    @FXML
    private MenuItem menuDelete;

    @FXML
    private Label lblTotalProducts;
    @FXML
    private Label lblFilteredProducts;
    @FXML
    private Label lblLowStockCount;
    @FXML
    private Label lblOutOfStockCount;
    @FXML
    private Label lblTotalValue;
    @FXML
    private Label lblAveragePrice;
    @FXML
    private Label lblTopCategory;

    private ObservableList<Product> allProducts;
    private ObservableList<Product> filteredProducts;
    private ProductDAO productDAO;
    private NumberFormat currencyFormat;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        productDAO = ProductDAO.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();

        allProducts = FXCollections.observableArrayList();
        filteredProducts = FXCollections.observableArrayList();

        setupTableColumns();
        setupFilters();
        loadProducts();
        updateSummary();
    }

    private void setupTableColumns() {
        colSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colBrand.setCellValueFactory(new PropertyValueFactory<>("brand"));
        colSupplier.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            return new SimpleStringProperty(product.getSupplierDisplayName());
        });
        colSize.setCellValueFactory(new PropertyValueFactory<>("size"));
        colColor.setCellValueFactory(new PropertyValueFactory<>("color"));
        colStock.setCellValueFactory(new PropertyValueFactory<>("stockQuantity"));
        colMinStock.setCellValueFactory(new PropertyValueFactory<>("minStockLevel"));

        // Custom cell factories for formatted display
        colPrice.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            if (product.getPrice() != null && product.getCostPrice() != null) {
                return new SimpleStringProperty(String.format("%s (Cost: %s, Profit: %s)",
                        currencyFormat.format(product.getPrice()),
                        currencyFormat.format(product.getCostPrice()),
                        currencyFormat.format(product.getProfit())
                ));
            } else if (product.getPrice() != null) {
                return new SimpleStringProperty(currencyFormat.format(product.getPrice()));
            }
            return new SimpleStringProperty("N/A");
        });

        colStatus.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            String stockStatus = product.isLowStock() ? "LOW STOCK" : "OK";
            String profitStatus = "";

            if (product.getCostPrice() != null && product.getPrice() != null) {
                if (!product.isValidPricing()) {
                    profitStatus = " | LOSS";
                } else if (product.getProfitMargin() > 50) {
                    profitStatus = " | HIGH PROFIT";
                } else if (product.getProfitMargin() > 20) {
                    profitStatus = " | GOOD PROFIT";
                }
            }

            return new SimpleStringProperty(stockStatus + profitStatus);
        });

        // Style low stock rows
        tblProducts.setRowFactory(tv -> {
            TableRow<Product> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldProduct, newProduct) -> {
                if (newProduct != null && newProduct.isLowStock()) {
                    row.setStyle("-fx-background-color: #ffebee;");
                } else {
                    row.setStyle("");
                }
            });
            return row;
        });

        // Action buttons column
        colActions.setCellFactory(col -> new TableCell<Product, String>() {
            private final Button editBtn = new Button("Edit");
            private final Button stockBtn = new Button("Stock");

            {
                editBtn.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    handleEditProduct(product);
                });

                stockBtn.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    handleAdjustStock(product);
                });

                editBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
                stockBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(new javafx.scene.layout.HBox(2, editBtn, stockBtn));
                }
            }
        });

        tblProducts.setItems(filteredProducts);
    }

    private void setupFilters() {
        // Stock status filter
        cmbStockStatus.setItems(FXCollections.observableArrayList("All", "In Stock", "Low Stock", "Out of Stock"));
        cmbStockStatus.setValue("All");

        loadCategories();
        loadBrands();
        loadSizes();
        loadColors();
        loadSuppliers();
    }

    private void loadCategories() {
        try {
            List<String> categories = productDAO.getAllCategories();
            ObservableList<String> categoryItems = FXCollections.observableArrayList("All Categories");
            categoryItems.addAll(categories);
            cmbCategory.setItems(categoryItems);
            cmbCategory.setValue("All Categories");
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load categories: " + e.getMessage());
        }
    }

    private void loadBrands() {
        try {
            List<String> brands = productDAO.getAllBrands();
            ObservableList<String> brandItems = FXCollections.observableArrayList("All Brands");
            brandItems.addAll(brands);
            cmbBrand.setItems(brandItems);
            cmbBrand.setValue("All Brands");
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load brands: " + e.getMessage());
        }
    }

    private void loadSizes() {
        try {
            List<String> sizes = productDAO.getAllSizes();
            ObservableList<String> sizeItems = FXCollections.observableArrayList("All Sizes");
            sizeItems.addAll(sizes);
            cmbSize.setItems(sizeItems);
            cmbSize.setValue("All Sizes");
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load sizes: " + e.getMessage());
        }
    }

    private void loadColors() {
        try {
            List<String> colors = productDAO.getAllColors();
            ObservableList<String> colorItems = FXCollections.observableArrayList("All Colors");
            colorItems.addAll(colors);
            cmbColor.setItems(colorItems);
            cmbColor.setValue("All Colors");
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load colors: " + e.getMessage());
        }
    }

    private void loadProducts() {
        try {
            List<Product> products = productDAO.findAll();
            allProducts.setAll(products);
            applyFilters();
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load products: " + e.getMessage());
        }
    }

    private void loadSuppliers() {
        try {
            // Load from SupplierService for better integration
            com.clothingstore.service.SupplierService supplierService
                    = com.clothingstore.service.SupplierService.getInstance();
            List<com.clothingstore.model.Supplier> allSuppliers = supplierService.getAllSuppliers();

            ObservableList<String> supplierItems = FXCollections.observableArrayList();
            supplierItems.add("All Suppliers");

            // Add active suppliers to the filter
            allSuppliers.stream()
                    .filter(supplier -> supplier.getStatus().canReceiveOrders())
                    .forEach(supplier -> supplierItems.add(supplier.getCompanyName()));

            cmbSupplier.setItems(supplierItems);
            cmbSupplier.setValue("All Suppliers");

        } catch (Exception e) {
            // Fallback to ProductDAO method
            try {
                List<String> suppliers = productDAO.getAllSupplierNames();
                ObservableList<String> supplierItems = FXCollections.observableArrayList();
                supplierItems.add("All Suppliers");
                supplierItems.addAll(suppliers);
                cmbSupplier.setItems(supplierItems);
                cmbSupplier.setValue("All Suppliers");
            } catch (SQLException sqlE) {
                AlertUtil.showError("Database Error", "Failed to load suppliers: " + sqlE.getMessage());
            }
        }
    }

    private void applyFilters() {
        List<Product> filtered = allProducts.stream()
                .filter(this::matchesSearchFilter)
                .filter(this::matchesCategoryFilter)
                .filter(this::matchesBrandFilter)
                .filter(this::matchesSizeFilter)
                .filter(this::matchesColorFilter)
                .filter(this::matchesSupplierFilter)
                .filter(this::matchesPriceFilter)
                .filter(this::matchesStockFilter)
                .collect(Collectors.toList());

        filteredProducts.setAll(filtered);
        updateSummary();
    }

    private boolean matchesSearchFilter(Product product) {
        String searchTerm = txtSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return true;
        }

        String term = searchTerm.toLowerCase();
        return (product.getName() != null && product.getName().toLowerCase().contains(term))
                || (product.getSku() != null && product.getSku().toLowerCase().contains(term))
                || (product.getDescription() != null && product.getDescription().toLowerCase().contains(term))
                || (product.getBrand() != null && product.getBrand().toLowerCase().contains(term));
    }

    private boolean matchesCategoryFilter(Product product) {
        String selectedCategory = cmbCategory.getValue();
        return selectedCategory == null || "All Categories".equals(selectedCategory)
                || selectedCategory.equals(product.getCategory());
    }

    private boolean matchesBrandFilter(Product product) {
        String selectedBrand = cmbBrand.getValue();
        return selectedBrand == null || "All Brands".equals(selectedBrand)
                || selectedBrand.equals(product.getBrand());
    }

    private boolean matchesSizeFilter(Product product) {
        String selectedSize = cmbSize.getValue();
        return selectedSize == null || "All Sizes".equals(selectedSize)
                || selectedSize.equals(product.getSize());
    }

    private boolean matchesColorFilter(Product product) {
        String selectedColor = cmbColor.getValue();
        return selectedColor == null || "All Colors".equals(selectedColor)
                || selectedColor.equals(product.getColor());
    }

    private boolean matchesSupplierFilter(Product product) {
        String selectedSupplier = cmbSupplier.getValue();
        if (selectedSupplier == null || "All Suppliers".equals(selectedSupplier)) {
            return true;
        }
        return selectedSupplier.equals(product.getSupplierName());
    }

    private boolean matchesPriceFilter(Product product) {
        try {
            String minPriceText = txtMinPrice.getText();
            String maxPriceText = txtMaxPrice.getText();

            if ((minPriceText == null || minPriceText.trim().isEmpty())
                    && (maxPriceText == null || maxPriceText.trim().isEmpty())) {
                return true;
            }

            BigDecimal productPrice = product.getPrice();
            if (productPrice == null) {
                return true;
            }

            if (minPriceText != null && !minPriceText.trim().isEmpty()) {
                BigDecimal minPrice = new BigDecimal(minPriceText);
                if (productPrice.compareTo(minPrice) < 0) {
                    return false;
                }
            }

            if (maxPriceText != null && !maxPriceText.trim().isEmpty()) {
                BigDecimal maxPrice = new BigDecimal(maxPriceText);
                if (productPrice.compareTo(maxPrice) > 0) {
                    return false;
                }
            }

            return true;
        } catch (NumberFormatException e) {
            return true; // If price format is invalid, don't filter
        }
    }

    private boolean matchesStockFilter(Product product) {
        String stockStatus = cmbStockStatus.getValue();
        if (stockStatus == null || "All".equals(stockStatus)) {
            return true;
        }

        switch (stockStatus) {
            case "In Stock":
                return product.getStockQuantity() > product.getMinStockLevel();
            case "Low Stock":
                return product.isLowStock() && product.getStockQuantity() > 0;
            case "Out of Stock":
                return product.getStockQuantity() == 0;
            default:
                return true;
        }
    }

    private void updateSummary() {
        try {
            // Get total product count from database
            int totalProductsInDB = productDAO.getTotalProductCount();
            int filteredCount = filteredProducts.size();

            long lowStockCount = filteredProducts.stream()
                    .mapToLong(p -> p.isLowStock() ? 1 : 0)
                    .sum();

            long outOfStockCount = filteredProducts.stream()
                    .mapToLong(p -> p.getStockQuantity() == 0 ? 1 : 0)
                    .sum();

            BigDecimal totalValue = filteredProducts.stream()
                    .map(p -> p.getPrice().multiply(BigDecimal.valueOf(p.getStockQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Calculate average price
            BigDecimal averagePrice = filteredProducts.isEmpty() ? BigDecimal.ZERO
                    : filteredProducts.stream()
                            .map(Product::getPrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(BigDecimal.valueOf(filteredProducts.size()), 2, BigDecimal.ROUND_HALF_UP);

            // Find top category
            String topCategory = filteredProducts.stream()
                    .collect(Collectors.groupingBy(Product::getCategory, Collectors.counting()))
                    .entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse("-");

            // Update labels
            lblTotalProducts.setText("Total Products: " + totalProductsInDB);
            lblFilteredProducts.setText("Filtered: " + filteredCount);
            lblLowStockCount.setText("Low Stock: " + lowStockCount);
            lblOutOfStockCount.setText("Out of Stock: " + outOfStockCount);
            lblTotalValue.setText("Total Value: " + currencyFormat.format(totalValue));
            lblAveragePrice.setText("Avg Price: " + currencyFormat.format(averagePrice));
            lblTopCategory.setText("Top Category: " + topCategory);

            // Update button styles
            if (lowStockCount > 0) {
                btnLowStockReport.getStyleClass().removeAll("button", "warning");
                btnLowStockReport.getStyleClass().addAll("button", "warning");
            }

            if (outOfStockCount > 0) {
                btnOutOfStockReport.getStyleClass().removeAll("button", "danger");
                btnOutOfStockReport.getStyleClass().addAll("button", "danger");
            }

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to update summary: " + e.getMessage());
        }
    }

    // Event Handlers
    @FXML
    private void handleManageCategories() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/CategoryManagement.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Category Management");
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(btnManageCategories.getScene().getWindow());
            stage.setResizable(true);

            stage.showAndWait();

            // Refresh categories after dialog closes
            loadCategories();
            loadProducts();

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open Category Management: " + e.getMessage());
        }
    }

    @FXML
    private void handleManageSuppliers() {
        try {
            // Use NavigationUtil to navigate to Supplier Management
            com.clothingstore.util.NavigationUtil.navigateToSupplierManagement(btnManageSuppliers);
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Supplier Management: " + e.getMessage());
        }
    }

    @FXML
    private void handleAddProduct() {
        showProductDialog(null);
    }

    @FXML
    private void handleRefresh() {
        loadProducts();
        loadCategories();
    }

    @FXML
    private void handleSearch() {
        applyFilters();
    }

    @FXML
    private void handleCategoryFilter() {
        applyFilters();
    }

    @FXML
    private void handleStockFilter() {
        applyFilters();
    }

    @FXML
    private void handleBrandFilter() {
        applyFilters();
    }

    @FXML
    private void handleSizeFilter() {
        applyFilters();
    }

    @FXML
    private void handleColorFilter() {
        applyFilters();
    }

    @FXML
    private void handleSupplierFilter() {
        applyFilters();
    }

    @FXML
    private void handlePriceFilter() {
        applyFilters();
    }

    @FXML
    private void handleAdvancedSearch() {
        showAdvancedSearchDialog();
    }

    @FXML
    private void handleClearFilters() {
        txtSearch.clear();
        cmbCategory.setValue("All Categories");
        cmbBrand.setValue("All Brands");
        cmbSize.setValue("All Sizes");
        cmbColor.setValue("All Colors");
        cmbSupplier.setValue("All Suppliers");
        txtMinPrice.clear();
        txtMaxPrice.clear();
        cmbStockStatus.setValue("All");
        applyFilters();
    }

    @FXML
    private void handleEditProduct() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleEditProduct(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to edit.");
        }
    }

    private void handleEditProduct(Product product) {
        showProductDialog(product);
    }

    @FXML
    private void handleDuplicateProduct() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            AlertUtil.showInfo("Feature Coming Soon", "Duplicate Product feature for: " + selected.getName());
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to duplicate.");
        }
    }

    @FXML
    private void handleAdjustStock() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleAdjustStock(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to adjust stock.");
        }
    }

    private void handleAdjustStock(Product product) {
        String input = AlertUtil.showTextInput("Adjust Stock",
                "Current stock: " + product.getStockQuantity(),
                "Enter new stock quantity:",
                String.valueOf(product.getStockQuantity())).orElse(null);

        if (input != null) {
            try {
                int newQuantity = Integer.parseInt(input);
                if (newQuantity < 0) {
                    AlertUtil.showError("Invalid Input", "Stock quantity cannot be negative.");
                    return;
                }

                productDAO.updateStock(product.getId(), newQuantity);
                product.setStockQuantity(newQuantity);
                tblProducts.refresh();
                updateSummary();

                AlertUtil.showSuccess("Stock Updated",
                        "Stock for " + product.getName() + " updated to " + newQuantity);

            } catch (NumberFormatException e) {
                AlertUtil.showError("Invalid Input", "Please enter a valid number.");
            } catch (SQLException e) {
                AlertUtil.showError("Database Error", "Failed to update stock: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleViewHistory() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            showProductHistory(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to view history.");
        }
    }

    @FXML
    private void handleDeleteProduct() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            if (AlertUtil.showConfirmation("Delete Product",
                    "Are you sure you want to delete: " + selected.getName() + "?")) {
                try {
                    productDAO.delete(selected.getId());
                    allProducts.remove(selected);
                    applyFilters();
                    AlertUtil.showSuccess("Product Deleted", selected.getName() + " has been deleted.");
                } catch (SQLException e) {
                    AlertUtil.showError("Database Error", "Failed to delete product: " + e.getMessage());
                }
            }
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to delete.");
        }
    }

    @FXML
    private void handleExport() {
        exportProductsToCSV();
    }

    @FXML
    private void handleLowStockReport() {
        try {
            List<Product> lowStockProducts = productDAO.findLowStockProducts();
            if (lowStockProducts.isEmpty()) {
                AlertUtil.showInfo("Low Stock Report", "No products are currently low on stock.");
            } else {
                StringBuilder report = new StringBuilder("Low Stock Products:\n\n");
                for (Product product : lowStockProducts) {
                    report.append(String.format("• %s (%s) - Stock: %d, Min: %d\n",
                            product.getName(), product.getSku(),
                            product.getStockQuantity(), product.getMinStockLevel()));
                }
                AlertUtil.showWarning("Low Stock Report", report.toString());
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate low stock report: " + e.getMessage());
        }
    }

    // Product Dialog Method - Using Legacy Dialog for Reliability
    private void showProductDialog(Product product) {
        System.out.println("Opening product dialog for: " + (product == null ? "new product" : product.getName()));

        // Use legacy dialog for now to ensure functionality works
        showLegacyProductDialog(product);
    }

    // Enhanced Legacy Dialog with Supplier Integration and Cost Management
    private void showLegacyProductDialog(Product product) {
        try {
            // Create enhanced dialog for product entry/editing
            javafx.scene.control.Dialog<Product> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle(product == null ? "Add New Product" : "Edit Product");
            dialog.setHeaderText(product == null ? "Enter product details with supplier and cost information" : "Edit product: " + product.getName());

            // Create form fields
            GridPane grid = new GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));

            // Basic product fields
            javafx.scene.control.TextField nameField = new javafx.scene.control.TextField();
            javafx.scene.control.TextField skuField = new javafx.scene.control.TextField();
            javafx.scene.control.TextField stockField = new javafx.scene.control.TextField();
            javafx.scene.control.ComboBox<String> categoryComboBox = new javafx.scene.control.ComboBox<>();
            javafx.scene.control.TextField brandField = new javafx.scene.control.TextField();
            javafx.scene.control.TextArea descriptionField = new javafx.scene.control.TextArea();
            descriptionField.setPrefRowCount(3);

            // Enhanced fields for supplier and cost management
            javafx.scene.control.ComboBox<String> supplierComboBox = new javafx.scene.control.ComboBox<>();
            javafx.scene.control.TextField costPriceField = new javafx.scene.control.TextField();
            javafx.scene.control.TextField sellingPriceField = new javafx.scene.control.TextField();
            javafx.scene.control.Label profitLabel = new javafx.scene.control.Label("$0.00 (0.00%)");
            javafx.scene.control.Label investmentLabel = new javafx.scene.control.Label("$0.00");
            javafx.scene.control.Label revenueLabel = new javafx.scene.control.Label("$0.00");

            // Setup category ComboBox
            categoryComboBox.setEditable(true);
            try {
                java.util.List<String> categories = productDAO.getAllCategories();
                categoryComboBox.getItems().addAll(categories);
            } catch (Exception e) {
                AlertUtil.showError("Error", "Failed to load categories: " + e.getMessage());
            }

            // Setup supplier ComboBox
            try {
                com.clothingstore.service.SupplierService supplierService
                        = com.clothingstore.service.SupplierService.getInstance();
                java.util.List<com.clothingstore.model.Supplier> suppliers = supplierService.getActiveSuppliers();

                supplierComboBox.getItems().add("No Supplier");
                for (com.clothingstore.model.Supplier supplier : suppliers) {
                    supplierComboBox.getItems().add(supplier.getCompanyName());
                }
                supplierComboBox.setValue("No Supplier");
            } catch (Exception e) {
                AlertUtil.showError("Error", "Failed to load suppliers: " + e.getMessage());
            }

            // Setup real-time profit calculations
            Runnable updateCalculations = () -> {
                try {
                    String costText = costPriceField.getText().trim();
                    String priceText = sellingPriceField.getText().trim();
                    String stockText = stockField.getText().trim();

                    if (!costText.isEmpty() && !priceText.isEmpty()) {
                        BigDecimal cost = new BigDecimal(costText);
                        BigDecimal price = new BigDecimal(priceText);
                        BigDecimal profit = price.subtract(cost);
                        double profitPercent = cost.compareTo(BigDecimal.ZERO) > 0
                                ? profit.divide(cost, 4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100 : 0;

                        profitLabel.setText(String.format("$%.2f (%.2f%%)", profit, profitPercent));
                        profitLabel.setStyle(profit.compareTo(BigDecimal.ZERO) >= 0
                                ? "-fx-text-fill: green;" : "-fx-text-fill: red;");

                        if (!stockText.isEmpty()) {
                            int stock = Integer.parseInt(stockText);
                            BigDecimal investment = cost.multiply(new BigDecimal(stock));
                            BigDecimal revenue = price.multiply(new BigDecimal(stock));

                            investmentLabel.setText(String.format("$%.2f", investment));
                            revenueLabel.setText(String.format("$%.2f", revenue));
                        }
                    } else {
                        profitLabel.setText("$0.00 (0.00%)");
                        investmentLabel.setText("$0.00");
                        revenueLabel.setText("$0.00");
                    }
                } catch (NumberFormatException e) {
                    profitLabel.setText("Invalid");
                    profitLabel.setStyle("-fx-text-fill: red;");
                }
            };

            costPriceField.textProperty().addListener((obs, oldVal, newVal) -> updateCalculations.run());
            sellingPriceField.textProperty().addListener((obs, oldVal, newVal) -> updateCalculations.run());
            stockField.textProperty().addListener((obs, oldVal, newVal) -> updateCalculations.run());

            // Pre-populate fields if editing
            if (product != null) {
                nameField.setText(product.getName());
                skuField.setText(product.getSku());
                stockField.setText(String.valueOf(product.getStockQuantity()));
                categoryComboBox.setValue(product.getCategory());
                brandField.setText(product.getBrand());
                descriptionField.setText(product.getDescription());

                // Populate enhanced fields
                if (product.getCostPrice() != null) {
                    costPriceField.setText(product.getCostPrice().toString());
                }
                if (product.getPrice() != null) {
                    sellingPriceField.setText(product.getPrice().toString());
                }
                if (product.getSupplierName() != null && !product.getSupplierName().isEmpty()) {
                    supplierComboBox.setValue(product.getSupplierName());
                } else {
                    supplierComboBox.setValue("No Supplier");
                }

                // Trigger calculations
                updateCalculations.run();
            } else {
                // Generate SKU for new products
                skuField.setText("PRD" + System.currentTimeMillis());
            }

            // Basic Information Section
            grid.add(new javafx.scene.control.Label("Name:"), 0, 0);
            grid.add(nameField, 1, 0);
            grid.add(new javafx.scene.control.Label("SKU:"), 0, 1);
            grid.add(skuField, 1, 1);
            grid.add(new javafx.scene.control.Label("Stock:"), 0, 2);
            grid.add(stockField, 1, 2);
            grid.add(new javafx.scene.control.Label("Category:"), 0, 3);
            javafx.scene.layout.HBox categoryBox = new javafx.scene.layout.HBox(5);
            javafx.scene.control.Button addCategoryBtn = new javafx.scene.control.Button("+ New");
            addCategoryBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 10px;");
            addCategoryBtn.setOnAction(e -> {
                String newCategory = showAddCategoryDialog();
                if (newCategory != null) {
                    categoryComboBox.getItems().add(newCategory);
                    categoryComboBox.setValue(newCategory);
                }
            });
            categoryBox.getChildren().addAll(categoryComboBox, addCategoryBtn);
            grid.add(categoryBox, 1, 3);
            grid.add(new javafx.scene.control.Label("Brand:"), 0, 4);
            grid.add(brandField, 1, 4);

            // Supplier Section
            grid.add(new javafx.scene.control.Label("Supplier:"), 0, 5);
            grid.add(supplierComboBox, 1, 5);

            // Cost Management Section
            grid.add(new javafx.scene.control.Label("Cost Price:"), 0, 6);
            grid.add(costPriceField, 1, 6);
            grid.add(new javafx.scene.control.Label("Selling Price:"), 0, 7);
            grid.add(sellingPriceField, 1, 7);
            grid.add(new javafx.scene.control.Label("Profit:"), 0, 8);
            grid.add(profitLabel, 1, 8);

            // Financial Tracking Section
            grid.add(new javafx.scene.control.Label("Investment:"), 0, 9);
            grid.add(investmentLabel, 1, 9);
            grid.add(new javafx.scene.control.Label("Revenue:"), 0, 10);
            grid.add(revenueLabel, 1, 10);

            grid.add(new javafx.scene.control.Label("Description:"), 0, 11);
            grid.add(descriptionField, 1, 11);

            dialog.getDialogPane().setContent(grid);

            // Add buttons
            javafx.scene.control.ButtonType saveButtonType = new javafx.scene.control.ButtonType("Save", javafx.scene.control.ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, javafx.scene.control.ButtonType.CANCEL);

            // Convert result when save is clicked
            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == saveButtonType) {
                    try {
                        // Validate required fields
                        if (nameField.getText().trim().isEmpty()) {
                            AlertUtil.showError("Validation Error", "Product name is required.");
                            return null;
                        }

                        String sku = skuField.getText().trim();
                        if (sku.isEmpty()) {
                            AlertUtil.showError("Validation Error", "SKU is required.");
                            return null;
                        }

                        if (!validateSku(sku, product)) {
                            return null; // Validation failed
                        }

                        // Validate pricing if both cost and selling price are provided
                        BigDecimal costPrice = null;
                        BigDecimal sellingPrice = null;

                        if (!costPriceField.getText().trim().isEmpty()) {
                            costPrice = new BigDecimal(costPriceField.getText().trim());
                        }
                        if (!sellingPriceField.getText().trim().isEmpty()) {
                            sellingPrice = new BigDecimal(sellingPriceField.getText().trim());
                        }

                        if (costPrice != null && sellingPrice != null && sellingPrice.compareTo(costPrice) <= 0) {
                            AlertUtil.showError("Pricing Error", "Selling price must be higher than cost price.");
                            return null;
                        }

                        Product newProduct = product == null ? new Product() : product;
                        newProduct.setName(nameField.getText().trim());
                        newProduct.setSku(sku);
                        newProduct.setStockQuantity(Integer.parseInt(stockField.getText().trim()));
                        newProduct.setCategory(categoryComboBox.getValue());
                        newProduct.setBrand(brandField.getText().trim());
                        newProduct.setDescription(descriptionField.getText().trim());

                        // Set enhanced fields
                        newProduct.setCostPrice(costPrice);
                        newProduct.setPrice(sellingPrice);

                        // Set supplier information
                        String selectedSupplier = supplierComboBox.getValue();
                        if (selectedSupplier != null && !selectedSupplier.equals("No Supplier")) {
                            newProduct.setSupplierName(selectedSupplier);
                            // Find supplier ID by name
                            try {
                                com.clothingstore.service.SupplierService supplierService
                                        = com.clothingstore.service.SupplierService.getInstance();
                                java.util.List<com.clothingstore.model.Supplier> suppliers = supplierService.getAllSuppliers();
                                for (com.clothingstore.model.Supplier supplier : suppliers) {
                                    if (supplier.getCompanyName().equals(selectedSupplier)) {
                                        newProduct.setSupplierId(supplier.getId());
                                        newProduct.setSupplierCode(supplier.getSupplierCode());
                                        break;
                                    }
                                }
                            } catch (Exception e) {
                                System.out.println("Warning: Could not set supplier ID: " + e.getMessage());
                            }
                        }

                        return newProduct;
                    } catch (NumberFormatException e) {
                        AlertUtil.showError("Invalid Input", "Please enter valid numbers for prices and stock.");
                        return null;
                    }
                }
                return null;
            });

            java.util.Optional<Product> result = dialog.showAndWait();
            result.ifPresent(newProduct -> {
                try {
                    if (product == null) {
                        // Add new product
                        productDAO.save(newProduct);
                        AlertUtil.showInfo("Success", "Product added successfully!");
                    } else {
                        // Update existing product
                        productDAO.save(newProduct);
                        AlertUtil.showInfo("Success", "Product updated successfully!");
                    }
                    loadProducts();
                } catch (Exception e) {
                    AlertUtil.showError("Database Error", "Failed to save product: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open product dialog: " + e.getMessage());
        }
    }

    private String showAddCategoryDialog() {
        try {
            javafx.scene.control.TextInputDialog dialog = new javafx.scene.control.TextInputDialog();
            dialog.setTitle("Add New Category");
            dialog.setHeaderText("Create a new product category");
            dialog.setContentText("Category name:");

            java.util.Optional<String> result = dialog.showAndWait();
            if (result.isPresent() && !result.get().trim().isEmpty()) {
                String categoryName = result.get().trim();

                // Create and save the new category
                try {
                    com.clothingstore.dao.CategoryDAO categoryDAO = com.clothingstore.dao.CategoryDAO.getInstance();
                    com.clothingstore.model.Category newCategory = new com.clothingstore.model.Category(categoryName);
                    categoryDAO.save(newCategory);

                    AlertUtil.showSuccess("Success", "Category '" + categoryName + "' created successfully!");
                    return categoryName;

                } catch (Exception e) {
                    AlertUtil.showError("Database Error", "Failed to create category: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open add category dialog: " + e.getMessage());
        }
        return null;
    }

    private void showProductHistory(Product product) {
        // Create a simple history dialog
        javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
        alert.setTitle("Product History");
        alert.setHeaderText("History for: " + product.getName());
        alert.setContentText(
                "SKU: " + product.getSku() + "\n"
                + "Current Stock: " + product.getStockQuantity() + "\n"
                + "Price: " + currencyFormat.format(product.getPrice()) + "\n"
                + "Category: " + product.getCategory() + "\n"
                + "Brand: " + product.getBrand() + "\n\n"
                + "Note: Detailed transaction history will be implemented in future version."
        );
        alert.showAndWait();
    }

    private void exportProductsToCSV() {
        try {
            javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
            fileChooser.setTitle("Export Products to CSV");
            fileChooser.getExtensionFilters().add(
                    new javafx.stage.FileChooser.ExtensionFilter("CSV Files", "*.csv"));
            fileChooser.setInitialFileName("products_export.csv");

            java.io.File file = fileChooser.showSaveDialog(tblProducts.getScene().getWindow());
            if (file != null) {
                try (java.io.PrintWriter writer = new java.io.PrintWriter(file)) {
                    // Write CSV header
                    writer.println("SKU,Name,Category,Brand,Price,Stock,Description");

                    // Write product data
                    for (Product product : allProducts) {
                        writer.printf("\"%s\",\"%s\",\"%s\",\"%s\",%.2f,%d,\"%s\"%n",
                                product.getSku(),
                                product.getName(),
                                product.getCategory(),
                                product.getBrand(),
                                product.getPrice(),
                                product.getStockQuantity(),
                                product.getDescription().replace("\"", "\"\"")
                        );
                    }

                    AlertUtil.showInfo("Export Successful",
                            "Products exported to: " + file.getAbsolutePath() + "\n"
                            + "Total products: " + allProducts.size());
                }
            }
        } catch (Exception e) {
            AlertUtil.showError("Export Failed", "Failed to export products: " + e.getMessage());
        }
    }

    // New Enhanced Methods
    @FXML
    private void handleTopProducts() {
        try {
            List<Product> topProducts = productDAO.findTopSellingProducts(10);
            if (topProducts.isEmpty()) {
                AlertUtil.showInfo("Top Products", "No products found.");
            } else {
                StringBuilder report = new StringBuilder("Top 10 Products (by stock quantity):\n\n");
                for (int i = 0; i < topProducts.size(); i++) {
                    Product product = topProducts.get(i);
                    report.append(String.format("%d. %s (%s) - Stock: %d, Price: %s\n",
                            i + 1, product.getName(), product.getSku(),
                            product.getStockQuantity(), currencyFormat.format(product.getPrice())));
                }
                AlertUtil.showInfo("Top Products Report", report.toString());
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate top products report: " + e.getMessage());
        }
    }

    @FXML
    private void handleOutOfStockReport() {
        try {
            List<Product> outOfStockProducts = productDAO.findOutOfStockProducts();
            if (outOfStockProducts.isEmpty()) {
                AlertUtil.showInfo("Out of Stock Report", "No products are currently out of stock.");
            } else {
                StringBuilder report = new StringBuilder("Out of Stock Products:\n\n");
                for (Product product : outOfStockProducts) {
                    report.append(String.format("• %s (%s) - Category: %s, Brand: %s\n",
                            product.getName(), product.getSku(),
                            product.getCategory(), product.getBrand()));
                }
                AlertUtil.showWarning("Out of Stock Report", report.toString());
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate out of stock report: " + e.getMessage());
        }
    }

    private void showAdvancedSearchDialog() {
        try {
            // Create advanced search dialog
            javafx.scene.control.Dialog<Void> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Advanced Product Search");
            dialog.setHeaderText("Search products with multiple criteria");

            // Create form fields
            GridPane grid = new GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));

            javafx.scene.control.TextField searchField = new javafx.scene.control.TextField();
            javafx.scene.control.ComboBox<String> categoryBox = new javafx.scene.control.ComboBox<>();
            javafx.scene.control.ComboBox<String> brandBox = new javafx.scene.control.ComboBox<>();
            javafx.scene.control.TextField minPriceField = new javafx.scene.control.TextField();
            javafx.scene.control.TextField maxPriceField = new javafx.scene.control.TextField();
            javafx.scene.control.TextField minStockField = new javafx.scene.control.TextField();

            // Populate combo boxes
            categoryBox.setItems(cmbCategory.getItems());
            brandBox.setItems(cmbBrand.getItems());

            grid.add(new javafx.scene.control.Label("Search Term:"), 0, 0);
            grid.add(searchField, 1, 0);
            grid.add(new javafx.scene.control.Label("Category:"), 0, 1);
            grid.add(categoryBox, 1, 1);
            grid.add(new javafx.scene.control.Label("Brand:"), 0, 2);
            grid.add(brandBox, 1, 2);
            grid.add(new javafx.scene.control.Label("Min Price:"), 0, 3);
            grid.add(minPriceField, 1, 3);
            grid.add(new javafx.scene.control.Label("Max Price:"), 0, 4);
            grid.add(maxPriceField, 1, 4);
            grid.add(new javafx.scene.control.Label("Min Stock:"), 0, 5);
            grid.add(minStockField, 1, 5);

            dialog.getDialogPane().setContent(grid);

            // Add buttons
            javafx.scene.control.ButtonType searchButtonType = new javafx.scene.control.ButtonType("Search", javafx.scene.control.ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(searchButtonType, javafx.scene.control.ButtonType.CANCEL);

            // Handle search
            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == searchButtonType) {
                    // Apply advanced search filters
                    txtSearch.setText(searchField.getText());
                    if (categoryBox.getValue() != null) {
                        cmbCategory.setValue(categoryBox.getValue());
                    }
                    if (brandBox.getValue() != null) {
                        cmbBrand.setValue(brandBox.getValue());
                    }
                    txtMinPrice.setText(minPriceField.getText());
                    txtMaxPrice.setText(maxPriceField.getText());
                    applyFilters();
                }
                return null;
            });

            dialog.showAndWait();

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open advanced search dialog: " + e.getMessage());
        }
    }

    // Enhanced SKU validation in product dialog
    private boolean validateSku(String sku, Product currentProduct) {
        try {
            if (sku == null || sku.trim().isEmpty()) {
                AlertUtil.showError("Validation Error", "SKU cannot be empty.");
                return false;
            }

            // Check if SKU exists (but allow current product's SKU)
            if (productDAO.isSkuExists(sku)) {
                if (currentProduct == null || !sku.equals(currentProduct.getSku())) {
                    AlertUtil.showError("Validation Error", "SKU '" + sku + "' already exists. Please choose a different SKU.");
                    return false;
                }
            }

            return true;
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to validate SKU: " + e.getMessage());
            return false;
        }
    }
}
