package com.clothingstore.test;

import com.clothingstore.service.ProfitAnalysisService;
import com.clothingstore.service.ProfitAnalysisService.ProfitMetrics;
import com.clothingstore.service.ProfitAnalysisService.CategoryProfitData;
import com.clothingstore.service.ProfitAnalysisService.ComparisonMetrics;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Test class to verify profit analysis functionality
 */
public class ProfitAnalysisTest {

    public static void main(String[] args) {
        System.out.println("=== PROFIT ANALYSIS TEST ===");
        System.out.println("Testing profit calculation functionality...\n");

        try {
            ProfitAnalysisService profitService = new ProfitAnalysisService();

            // Test with last 30 days
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(30);

            System.out.println("Date Range: " + startDate.toLocalDate() + " to " + endDate.toLocalDate());
            System.out.println("Calculating profit metrics...\n");

            ProfitMetrics metrics = profitService.calculateProfitMetrics(startDate, endDate);

            System.out.println("=== PROFIT ANALYSIS RESULTS ===");
            System.out.println("Total Revenue: " + metrics.getFormattedRevenue());
            System.out.println("Total Cost: " + metrics.getFormattedCost());
            System.out.println("Gross Profit: " + metrics.getFormattedProfit());
            System.out.println("Profit Percentage: " + metrics.getFormattedProfitPercentage());
            System.out.println("Profit Margin: " + metrics.getFormattedProfitMargin());
            System.out.println("Items Sold: " + metrics.getTotalItemsSold());
            System.out.println("Transactions: " + metrics.getTransactionCount());

            System.out.println("\n=== DETAILED METRICS ===");
            System.out.println(metrics.toString());

            // Validation
            System.out.println("\n=== VALIDATION ===");
            boolean hasData = metrics.getTransactionCount() > 0;
            boolean profitCalculationValid = metrics.getTotalRevenue() >= metrics.getTotalCost();
            boolean percentageValid = metrics.getProfitPercentage() >= 0;

            System.out.println("Has Transaction Data: " + (hasData ? "YES" : "NO"));
            System.out.println("Profit Calculation Valid: " + (profitCalculationValid ? "YES" : "NO"));
            System.out.println("Percentage Calculation Valid: " + (percentageValid ? "YES" : "NO"));

            if (hasData && profitCalculationValid && percentageValid) {
                System.out.println("\n✓ PROFIT ANALYSIS TEST PASSED");
                System.out.println("✓ All calculations appear correct");
                System.out.println("✓ Ready for UI integration testing");
            } else {
                System.out.println("\n⚠ PROFIT ANALYSIS TEST NEEDS ATTENTION");
                if (!hasData) {
                    System.out.println("⚠ No transaction data found");
                }
                if (!profitCalculationValid) {
                    System.out.println("⚠ Profit calculation issue detected");
                }
                if (!percentageValid) {
                    System.out.println("⚠ Percentage calculation issue detected");
                }
            }

            // Test with different date ranges
            System.out.println("\n=== TESTING DIFFERENT DATE RANGES ===");

            // Last 7 days
            LocalDateTime weekStart = endDate.minusDays(7);
            ProfitMetrics weekMetrics = profitService.calculateProfitMetrics(weekStart, endDate);
            System.out.println("Last 7 days: " + weekMetrics.getTransactionCount() + " transactions, "
                    + weekMetrics.getFormattedProfit() + " profit");

            // Last 90 days
            LocalDateTime quarterStart = endDate.minusDays(90);
            ProfitMetrics quarterMetrics = profitService.calculateProfitMetrics(quarterStart, endDate);
            System.out.println("Last 90 days: " + quarterMetrics.getTransactionCount() + " transactions, "
                    + quarterMetrics.getFormattedProfit() + " profit");

            // Test enhanced features
            System.out.println("\n=== TESTING ENHANCED FEATURES ===");

            // Category Analysis
            System.out.println("\n--- Category Profit Breakdown ---");
            List<CategoryProfitData> categories = profitService.calculateCategoryProfitBreakdown(startDate, endDate);
            System.out.println("Found " + categories.size() + " categories:");
            for (CategoryProfitData category : categories) {
                System.out.println("  " + category.getCategoryName() + ": "
                        + category.getFormattedProfit() + " profit ("
                        + category.getFormattedProfitMargin() + " margin)");
            }

            // Comparison Analysis
            System.out.println("\n--- Period Comparison Analysis ---");
            LocalDateTime previousStart = startDate.minusDays(30);
            LocalDateTime previousEnd = endDate.minusDays(30);
            ComparisonMetrics comparison = profitService.calculateComparisonMetrics(startDate, endDate, previousStart, previousEnd);

            System.out.println("Current Period: " + comparison.getCurrentPeriod().getFormattedProfit());
            System.out.println("Previous Period: " + comparison.getPreviousPeriod().getFormattedProfit());
            System.out.println("Revenue Growth: " + comparison.getFormattedRevenueGrowth());
            System.out.println("Profit Growth: " + comparison.getFormattedProfitGrowth());
            System.out.println("Margin Change: " + comparison.getFormattedMarginChange());

            // Cache Testing
            System.out.println("\n--- Cache Performance Test ---");
            long startTime = System.currentTimeMillis();
            profitService.calculateProfitMetrics(startDate, endDate); // Should use cache
            long cachedTime = System.currentTimeMillis() - startTime;
            System.out.println("Cached calculation time: " + cachedTime + "ms");

            System.out.println("\n✓ ENHANCED FEATURES TEST PASSED");
            System.out.println("✓ Category analysis working");
            System.out.println("✓ Comparison analysis working");
            System.out.println("✓ Caching system functional");

        } catch (Exception e) {
            System.err.println("Test failed with exception: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("\n=== ENHANCED PROFIT ANALYSIS TEST COMPLETE ===");
    }
}
