package com.clothingstore.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Model class representing a generated report
 */
public class Report {

    private String title;
    private String description;
    private ReportType type;
    private LocalDateTime generatedAt;
    private LocalDateTime periodStart;
    private LocalDateTime periodEnd;
    private String generatedBy;
    private ReportFormat format;
    private List<ReportSection> sections;
    private Map<String, Object> parameters;
    private String summary;
    private boolean hasData;

    // Constructors
    public Report() {
        this.generatedAt = LocalDateTime.now();
        this.hasData = false;
    }

    public Report(String title, ReportType type) {
        this();
        this.title = title;
        this.type = type;
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ReportType getType() {
        return type;
    }

    public void setType(ReportType type) {
        this.type = type;
    }

    public LocalDateTime getGeneratedAt() {
        return generatedAt;
    }

    public void setGeneratedAt(LocalDateTime generatedAt) {
        this.generatedAt = generatedAt;
    }

    public LocalDateTime getPeriodStart() {
        return periodStart;
    }

    public void setPeriodStart(LocalDateTime periodStart) {
        this.periodStart = periodStart;
    }

    public LocalDateTime getPeriodEnd() {
        return periodEnd;
    }

    public void setPeriodEnd(LocalDateTime periodEnd) {
        this.periodEnd = periodEnd;
    }

    public String getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(String generatedBy) {
        this.generatedBy = generatedBy;
    }

    public ReportFormat getFormat() {
        return format;
    }

    public void setFormat(ReportFormat format) {
        this.format = format;
    }

    public List<ReportSection> getSections() {
        return sections;
    }

    public void setSections(List<ReportSection> sections) {
        this.sections = sections;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public boolean isHasData() {
        return hasData;
    }

    public void setHasData(boolean hasData) {
        this.hasData = hasData;
    }

    // Utility methods
    public String getFormattedGeneratedAt() {
        return generatedAt.format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm"));
    }

    public String getFormattedPeriod() {
        if (periodStart == null || periodEnd == null) {
            return "All Time";
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
        return String.format("%s - %s",
                periodStart.format(formatter),
                periodEnd.format(formatter));
    }

    public String getFileName() {
        StringBuilder fileName = new StringBuilder();

        // Add report type
        fileName.append(type.name().toLowerCase().replace("_", "-"));

        // Add date range if applicable
        if (periodStart != null && periodEnd != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            fileName.append("-").append(periodStart.format(formatter));
            fileName.append("-to-").append(periodEnd.format(formatter));
        }

        // Add timestamp
        fileName.append("-").append(generatedAt.format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmm")));

        // Add extension based on format
        if (format != null) {
            fileName.append(".").append(format.getExtension());
        }

        return fileName.toString();
    }

    @Override
    public String toString() {
        return String.format("Report{title='%s', type=%s, period=%s}",
                title, type, getFormattedPeriod());
    }
}
