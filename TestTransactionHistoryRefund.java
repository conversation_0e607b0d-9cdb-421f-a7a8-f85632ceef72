import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.RefundItem;
import com.clothingstore.service.RefundService;
import com.clothingstore.model.RefundResult;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;

public class TestTransactionHistoryRefund {
    public static void main(String[] args) {
        try {
            System.out.println("=== Testing Transaction History Partial Refund ===");
            
            // Initialize services
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            RefundService refundService = RefundService.getInstance();
            
            // Find a completed transaction
            List<Transaction> completedTransactions = transactionDAO.findByStatus("COMPLETED");
            if (completedTransactions.isEmpty()) {
                System.out.println("No COMPLETED transactions found");
                return;
            }
            
            Transaction testTransaction = completedTransactions.get(0);
            System.out.println("Testing transaction: " + testTransaction.getTransactionNumber());
            System.out.println("Transaction ID: " + testTransaction.getId());
            System.out.println("Transaction status: " + testTransaction.getStatus());
            System.out.println("Transaction total: $" + testTransaction.getTotalAmount());
            System.out.println("Number of items: " + testTransaction.getItems().size());
            
            // Create refund items (similar to what TransactionHistoryController does)
            List<RefundItem> refundItems = new ArrayList<>();
            if (!testTransaction.getItems().isEmpty()) {
                var firstItem = testTransaction.getItems().get(0);
                RefundItem refundItem = new RefundItem(firstItem);
                refundItem.setRefundQuantity(1); // Refund 1 unit
                refundItems.add(refundItem);
                
                System.out.println("Refund item: " + firstItem.getProductName());
                System.out.println("Original quantity: " + firstItem.getQuantity());
                System.out.println("Refund quantity: " + refundItem.getRefundQuantity());
                System.out.println("Refund amount: $" + refundItem.getRefundAmount());
            }
            
            // Process partial refund (same as TransactionHistoryController)
            System.out.println("\n=== Processing Partial Refund (UI Simulation) ===");
            String refundReason = "Customer return - UI test";
            RefundResult result = refundService.processPartialRefund(testTransaction, refundItems, refundReason, "System User");
            
            if (result.isSuccess()) {
                // Calculate total refund amount
                BigDecimal totalRefundAmount = refundItems.stream()
                    .map(RefundItem::getRefundAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                // Display success message (same format as TransactionHistoryController)
                String successMessage = String.format(
                    "Refund processed successfully!\n\n" +
                    "Refund amount: $%.2f\n" +
                    "Items refunded: %d\n" +
                    "Inventory restored: Yes\n" +
                    "Transaction status updated: Yes",
                    totalRefundAmount.doubleValue(),
                    refundItems.size()
                );
                
                System.out.println("✓ SUCCESS!");
                System.out.println("Success Message:");
                System.out.println(successMessage);
                
                // Check updated transaction status
                Transaction updatedTransaction = transactionDAO.findById(testTransaction.getId()).orElse(null);
                if (updatedTransaction != null) {
                    System.out.println("\n=== Updated Transaction Status ===");
                    System.out.println("Status: " + updatedTransaction.getStatus());
                    System.out.println("Amount paid: $" + updatedTransaction.getAmountPaid());
                    System.out.println("Refunded amount: $" + updatedTransaction.getRefundedAmount());
                    System.out.println("Remaining balance: $" + updatedTransaction.getRemainingBalance());
                    System.out.println("Notes: " + updatedTransaction.getNotes());
                }
                
            } else {
                System.out.println("✗ FAILED!");
                System.out.println("Error: " + result.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("Error testing transaction history refund: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
