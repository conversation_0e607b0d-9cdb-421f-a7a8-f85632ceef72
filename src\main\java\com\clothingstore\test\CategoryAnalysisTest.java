package com.clothingstore.test;

import com.clothingstore.database.OptimizedProfitQueries;
import com.clothingstore.database.OptimizedProfitQueries.CategoryProfitResult;
import com.clothingstore.database.OptimizedProfitQueries.ProductProfitResult;
import com.clothingstore.service.ProfitAnalysisService;
import com.clothingstore.service.ProfitAnalysisService.CategoryProfitData;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Comprehensive Category Analysis using real database data
 */
public class CategoryAnalysisTest {

    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("COMPREHENSIVE CATEGORY ANALYSIS - REAL DATABASE DATA");
        System.out.println("=".repeat(80));
        System.out.println("Analysis Date: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();

        try {
            // Initialize services
            OptimizedProfitQueries queries = new OptimizedProfitQueries();
            ProfitAnalysisService profitService = new ProfitAnalysisService();

            // Test different time periods
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime[] startDates = {
                now.minusDays(7), // Last 7 days
                now.minusDays(30), // Last 30 days
                now.minusDays(90), // Last 90 days
                now.minusYears(1) // Last year
            };
            String[] periodNames = {"Last 7 Days", "Last 30 Days", "Last 90 Days", "Last Year"};

            // 1. CATEGORY ANALYSIS BY TIME PERIODS
            System.out.println("[ANALYSIS] CATEGORY PERFORMANCE BY TIME PERIODS");
            System.out.println("-".repeat(60));

            for (int i = 0; i < startDates.length; i++) {
                System.out.println("\n[PERIOD] " + periodNames[i] + ":");
                System.out.println("-".repeat(40));

                // Using OptimizedProfitQueries directly
                List<CategoryProfitResult> rawResults = queries.calculateCategoryProfitBreakdown(startDates[i], now);

                if (rawResults.isEmpty()) {
                    System.out.println("   No data available for this period");
                    continue;
                }

                // Display raw database results
                for (CategoryProfitResult result : rawResults) {
                    double profit = result.revenue - result.cost;
                    double margin = result.revenue > 0 ? (profit / result.revenue) * 100 : 0;

                    System.out.println("   [CATEGORY] " + result.category);
                    System.out.println("      Revenue: $" + String.format("%.2f", result.revenue));
                    System.out.println("      Cost: $" + String.format("%.2f", result.cost));
                    System.out.println("      Profit: $" + String.format("%.2f", profit));
                    System.out.println("      Margin: " + String.format("%.2f%%", margin));
                    System.out.println("      Items Sold: " + result.itemsSold);
                    System.out.println("      Transactions: " + result.transactionCount);
                    System.out.println();
                }

                // Summary for this period
                double totalRevenue = rawResults.stream().mapToDouble(r -> r.revenue).sum();
                double totalCost = rawResults.stream().mapToDouble(r -> r.cost).sum();
                double totalProfit = totalRevenue - totalCost;
                int totalItems = rawResults.stream().mapToInt(r -> r.itemsSold).sum();
                int totalTransactions = rawResults.stream().mapToInt(r -> r.transactionCount).sum();

                System.out.println("   [SUMMARY] PERIOD TOTALS:");
                System.out.println("      Total Revenue: $" + String.format("%.2f", totalRevenue));
                System.out.println("      Total Profit: $" + String.format("%.2f", totalProfit));
                System.out.println("      Overall Margin: " + String.format("%.2f%%",
                        totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0));
                System.out.println("      Total Items: " + totalItems);
                System.out.println("      Total Transactions: " + totalTransactions);
                System.out.println("      Categories: " + rawResults.size());
            }

            // 2. DETAILED CATEGORY COMPARISON
            System.out.println("\n\n[COMPARISON] DETAILED CATEGORY COMPARISON (Last 90 Days)");
            System.out.println("-".repeat(60));

            LocalDateTime last90Days = now.minusDays(90);
            List<CategoryProfitResult> detailedResults = queries.calculateCategoryProfitBreakdown(last90Days, now);

            if (!detailedResults.isEmpty()) {
                // Sort by profit descending
                detailedResults.sort((a, b) -> Double.compare(
                        (b.revenue - b.cost), (a.revenue - a.cost)));

                System.out.println("\nRanking by Profit Performance:");
                for (int i = 0; i < detailedResults.size(); i++) {
                    CategoryProfitResult result = detailedResults.get(i);
                    double profit = result.revenue - result.cost;
                    double margin = result.revenue > 0 ? (profit / result.revenue) * 100 : 0;
                    double avgTransactionValue = result.transactionCount > 0
                            ? result.revenue / result.transactionCount : 0;
                    double revenuePerItem = result.itemsSold > 0
                            ? result.revenue / result.itemsSold : 0;

                    String rank = (i == 0) ? "🥇" : (i == 1) ? "🥈" : (i == 2) ? "🥉" : String.valueOf(i + 1);

                    System.out.println("\n" + rank + " " + result.category.toUpperCase());
                    System.out.println("   💰 Financial Metrics:");
                    System.out.println("      Revenue: $" + String.format("%.2f", result.revenue));
                    System.out.println("      Cost: $" + String.format("%.2f", result.cost));
                    System.out.println("      Profit: $" + String.format("%.2f", profit));
                    System.out.println("      Margin: " + String.format("%.2f%%", margin));

                    System.out.println("   📊 Performance Metrics:");
                    System.out.println("      Items Sold: " + result.itemsSold);
                    System.out.println("      Transactions: " + result.transactionCount);
                    System.out.println("      Avg Transaction Value: $" + String.format("%.2f", avgTransactionValue));
                    System.out.println("      Revenue per Item: $" + String.format("%.2f", revenuePerItem));

                    // Performance indicators
                    System.out.println("   🎯 Performance Indicators:");
                    if (margin > 40) {
                        System.out.println("      ✅ EXCELLENT margin - Premium category");
                    } else if (margin > 25) {
                        System.out.println("      ✅ GOOD margin - Healthy performance");
                    } else if (margin > 15) {
                        System.out.println("      ⚠️ MODERATE margin - Room for improvement");
                    } else {
                        System.out.println("      🔴 LOW margin - Needs attention");
                    }

                    if (avgTransactionValue > 100) {
                        System.out.println("      ✅ HIGH transaction value - Premium positioning");
                    } else if (avgTransactionValue > 50) {
                        System.out.println("      ✅ GOOD transaction value - Solid performance");
                    } else {
                        System.out.println("      ⚠️ LOW transaction value - Consider bundling");
                    }
                }
            }

            // 3. PRODUCT-LEVEL ANALYSIS BY CATEGORY
            System.out.println("\n\n🛍️ PRODUCT-LEVEL ANALYSIS BY CATEGORY");
            System.out.println("-".repeat(60));

            List<ProductProfitResult> productResults = queries.calculateProductProfitBreakdown(last90Days, now, 50, 0);

            if (!productResults.isEmpty()) {
                // Group products by category
                Map<String, List<ProductProfitResult>> productsByCategory = productResults.stream()
                        .collect(Collectors.groupingBy(p -> p.category));

                for (Map.Entry<String, List<ProductProfitResult>> entry : productsByCategory.entrySet()) {
                    String category = entry.getKey();
                    List<ProductProfitResult> products = entry.getValue();

                    System.out.println("\n📂 " + category.toUpperCase() + " PRODUCTS:");
                    System.out.println("-".repeat(30));

                    // Sort products by profit
                    products.sort((a, b) -> Double.compare(
                            (b.revenue - b.cost), (a.revenue - a.cost)));

                    for (ProductProfitResult product : products) {
                        double profit = product.revenue - product.cost;
                        double margin = product.revenue > 0 ? (profit / product.revenue) * 100 : 0;

                        System.out.println("   🏷️ " + product.productName + " (SKU: " + product.sku + ")");
                        System.out.println("      Revenue: $" + String.format("%.2f", product.revenue));
                        System.out.println("      Profit: $" + String.format("%.2f", profit));
                        System.out.println("      Margin: " + String.format("%.2f%%", margin));
                        System.out.println("      Qty Sold: " + product.quantitySold);
                        System.out.println("      Transactions: " + product.transactionCount);
                        System.out.println();
                    }
                }
            }

            // 4. BUSINESS INSIGHTS AND RECOMMENDATIONS
            System.out.println("\n💡 CATEGORY ANALYSIS INSIGHTS & RECOMMENDATIONS");
            System.out.println("-".repeat(60));

            if (!detailedResults.isEmpty()) {
                CategoryProfitResult topCategory = detailedResults.get(0);
                CategoryProfitResult bottomCategory = detailedResults.get(detailedResults.size() - 1);

                double topProfit = topCategory.revenue - topCategory.cost;
                double bottomProfit = bottomCategory.revenue - bottomCategory.cost;
                double topMargin = topCategory.revenue > 0 ? (topProfit / topCategory.revenue) * 100 : 0;
                double bottomMargin = bottomCategory.revenue > 0 ? (bottomProfit / bottomCategory.revenue) * 100 : 0;

                System.out.println("\n🎯 KEY INSIGHTS:");
                System.out.println("• TOP PERFORMER: " + topCategory.category
                        + " ($" + String.format("%.2f", topProfit) + " profit, "
                        + String.format("%.1f%%", topMargin) + " margin)");
                System.out.println("• NEEDS ATTENTION: " + bottomCategory.category
                        + " ($" + String.format("%.2f", bottomProfit) + " profit, "
                        + String.format("%.1f%%", bottomMargin) + " margin)");

                // Calculate category diversity
                double totalRevenue = detailedResults.stream().mapToDouble(r -> r.revenue).sum();
                System.out.println("• CATEGORY DIVERSITY: " + detailedResults.size() + " active categories");

                // Revenue concentration
                double topCategoryShare = totalRevenue > 0 ? (topCategory.revenue / totalRevenue) * 100 : 0;
                System.out.println("• REVENUE CONCENTRATION: " + String.format("%.1f%%", topCategoryShare)
                        + " from top category");

                System.out.println("\n🚀 STRATEGIC RECOMMENDATIONS:");
                System.out.println("• EXPAND: Focus marketing budget on " + topCategory.category + " category");
                System.out.println("• OPTIMIZE: Review pricing strategy for " + bottomCategory.category + " products");
                System.out.println("• DIVERSIFY: Consider expanding product range in high-margin categories");
                System.out.println("• ANALYZE: Investigate why " + bottomCategory.category + " has lower margins");

                if (detailedResults.size() < 5) {
                    System.out.println("• GROWTH: Consider adding new product categories for diversification");
                }
            }

            System.out.println("\n" + "=".repeat(80));
            System.out.println("✅ COMPREHENSIVE CATEGORY ANALYSIS COMPLETE");
            System.out.println("=".repeat(80));

        } catch (Exception e) {
            System.err.println("❌ Category analysis failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
