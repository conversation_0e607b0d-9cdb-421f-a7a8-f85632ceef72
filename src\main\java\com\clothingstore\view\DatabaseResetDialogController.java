package com.clothingstore.view;

import com.clothingstore.service.DatabaseResetService;
import com.clothingstore.util.AlertUtil;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controller for Database Reset Dialog
 */
public class DatabaseResetDialogController implements Initializable {

    @FXML
    private Label lblCurrentStats;
    @FXML
    private RadioButton rbCompleteReset;
    @FXML
    private RadioButton rbResetWithDemo;
    @FXML
    private CheckBox chkConfirmReset;
    @FXML
    private TextField txtConfirmationText;
    @FXML
    private ProgressBar progressBar;
    @FXML
    private Label lblProgress;
    @FXML
    private Button btnReset;
    @FXML
    private Button btnCancel;

    private DatabaseResetService resetService;
    private ToggleGroup resetTypeGroup;
    private boolean resetCompleted = false;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        resetService = DatabaseResetService.getInstance();

        setupResetTypeToggle();
        setupValidation();
        loadCurrentStats();

        // Initially hide progress elements
        progressBar.setVisible(false);
        lblProgress.setVisible(false);
    }

    private void setupResetTypeToggle() {
        resetTypeGroup = new ToggleGroup();
        rbCompleteReset.setToggleGroup(resetTypeGroup);
        rbResetWithDemo.setToggleGroup(resetTypeGroup);

        // Default to reset with demo data
        rbResetWithDemo.setSelected(true);
    }

    private void setupValidation() {
        // Enable reset button only when all confirmations are checked
        btnReset.setDisable(true);

        chkConfirmReset.selectedProperty().addListener((obs, oldVal, newVal) -> updateResetButtonState());
        txtConfirmationText.textProperty().addListener((obs, oldVal, newVal) -> updateResetButtonState());
    }

    private void updateResetButtonState() {
        boolean confirmChecked = chkConfirmReset.isSelected();
        boolean textMatches = "RESET DATABASE".equals(txtConfirmationText.getText().trim().toUpperCase());

        btnReset.setDisable(!(confirmChecked && textMatches));
    }

    private void loadCurrentStats() {
        try {
            DatabaseResetService.DatabaseStats stats = resetService.getDatabaseStats();

            StringBuilder statsText = new StringBuilder();
            statsText.append("Current Database Contents:\n");
            statsText.append("• Products: ").append(stats.getProductCount()).append("\n");
            statsText.append("• Customers: ").append(stats.getCustomerCount()).append("\n");
            statsText.append("• Transactions: ").append(stats.getTransactionCount()).append("\n\n");

            if (stats.isEmpty()) {
                statsText.append("Database is currently empty.");
            } else {
                statsText.append("WARNING: All this data will be permanently deleted!");
            }

            lblCurrentStats.setText(statsText.toString());

        } catch (Exception e) {
            lblCurrentStats.setText("Error loading database statistics: " + e.getMessage());
        }
    }

    @FXML
    private void handleReset() {
        if (!validateReset()) {
            return;
        }

        // Show final confirmation
        if (!showFinalConfirmation()) {
            return;
        }

        // Disable UI during reset
        setUIEnabled(false);
        progressBar.setVisible(true);
        lblProgress.setVisible(true);
        lblProgress.setText("Preparing database reset...");

        // Perform reset in background thread
        Task<Void> resetTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                updateMessage("Resetting database...");
                updateProgress(0.2, 1.0);

                if (rbCompleteReset.isSelected()) {
                    resetService.resetDatabase();
                } else {
                    updateMessage("Resetting database and loading demo data...");
                    updateProgress(0.5, 1.0);
                    resetService.resetWithDemoData();
                }

                updateProgress(1.0, 1.0);
                updateMessage("Database reset completed successfully!");
                return null;
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    resetCompleted = true;
                    lblProgress.setText("Database reset completed successfully!");

                    AlertUtil.showSuccess("Reset Complete",
                            "Database has been reset successfully!\n\n"
                            + (rbResetWithDemo.isSelected()
                            ? "Demo data has been loaded."
                            : "Database is now empty."));

                    closeDialog();
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    lblProgress.setText("Database reset failed!");
                    setUIEnabled(true);
                    progressBar.setVisible(false);
                    lblProgress.setVisible(false);

                    AlertUtil.showError("Reset Failed",
                            "Database reset failed: " + exception.getMessage());
                });
            }
        };

        // Bind progress
        progressBar.progressProperty().bind(resetTask.progressProperty());
        lblProgress.textProperty().bind(resetTask.messageProperty());

        // Run task
        Thread resetThread = new Thread(resetTask);
        resetThread.setDaemon(true);
        resetThread.start();
    }

    private boolean validateReset() {
        if (!chkConfirmReset.isSelected()) {
            AlertUtil.showWarning("Confirmation Required", "Please check the confirmation checkbox.");
            return false;
        }

        if (!"RESET DATABASE".equals(txtConfirmationText.getText().trim().toUpperCase())) {
            AlertUtil.showWarning("Confirmation Required",
                    "Please type 'RESET DATABASE' in the confirmation field.");
            return false;
        }

        return true;
    }

    private boolean showFinalConfirmation() {
        String resetType = rbCompleteReset.isSelected() ? "complete reset" : "reset with demo data";

        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("Final Confirmation");
        alert.setHeaderText("Are you absolutely sure?");
        alert.setContentText(
                "This will perform a " + resetType + " of the database.\n\n"
                + "WARNING: ALL DATA WILL BE PERMANENTLY DELETED!\n\n"
                + "This action cannot be undone.\n\n"
                + "Do you want to continue?"
        );

        ButtonType yesButton = new ButtonType("Yes, Reset Database", ButtonBar.ButtonData.YES);
        ButtonType noButton = new ButtonType("No, Cancel", ButtonBar.ButtonData.NO);
        alert.getButtonTypes().setAll(yesButton, noButton);

        return alert.showAndWait().orElse(noButton) == yesButton;
    }

    private void setUIEnabled(boolean enabled) {
        rbCompleteReset.setDisable(!enabled);
        rbResetWithDemo.setDisable(!enabled);
        chkConfirmReset.setDisable(!enabled);
        txtConfirmationText.setDisable(!enabled);
        btnReset.setDisable(!enabled);
    }

    @FXML
    private void handleCancel() {
        closeDialog();
    }

    private void closeDialog() {
        Stage stage = (Stage) btnCancel.getScene().getWindow();
        stage.close();
    }

    public boolean isResetCompleted() {
        return resetCompleted;
    }
}
