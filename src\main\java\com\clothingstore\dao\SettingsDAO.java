package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Setting;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Data Access Object for Settings operations
 */
public class SettingsDAO {
    
    private static SettingsDAO instance;
    private final Map<String, Setting> settingsCache = new HashMap<>();
    private boolean cacheInitialized = false;
    
    private SettingsDAO() {}
    
    public static synchronized SettingsDAO getInstance() {
        if (instance == null) {
            instance = new SettingsDAO();
        }
        return instance;
    }
    
    public List<Setting> findAll() throws SQLException {
        String sql = "SELECT * FROM settings ORDER BY category, key";
        List<Setting> settings = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                settings.add(mapResultSetToSetting(rs));
            }
        }
        
        return settings;
    }
    
    public Optional<Setting> findByKey(String key) throws SQLException {
        // Check cache first
        if (cacheInitialized && settingsCache.containsKey(key)) {
            return Optional.of(settingsCache.get(key));
        }
        
        String sql = "SELECT * FROM settings WHERE key = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, key);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Setting setting = mapResultSetToSetting(rs);
                    settingsCache.put(key, setting);
                    return Optional.of(setting);
                }
            }
        }
        
        return Optional.empty();
    }
    
    public List<Setting> findByCategory(String category) throws SQLException {
        String sql = "SELECT * FROM settings WHERE category = ? ORDER BY key";
        List<Setting> settings = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, category);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    settings.add(mapResultSetToSetting(rs));
                }
            }
        }
        
        return settings;
    }
    
    public String getValue(String key, String defaultValue) {
        try {
            Optional<Setting> setting = findByKey(key);
            return setting.map(Setting::getStringValue).orElse(defaultValue);
        } catch (SQLException e) {
            return defaultValue;
        }
    }
    
    public int getIntValue(String key, int defaultValue) {
        try {
            Optional<Setting> setting = findByKey(key);
            return setting.map(Setting::getIntValue).orElse(defaultValue);
        } catch (SQLException e) {
            return defaultValue;
        }
    }
    
    public double getDoubleValue(String key, double defaultValue) {
        try {
            Optional<Setting> setting = findByKey(key);
            return setting.map(Setting::getDoubleValue).orElse(defaultValue);
        } catch (SQLException e) {
            return defaultValue;
        }
    }
    
    public boolean getBooleanValue(String key, boolean defaultValue) {
        try {
            Optional<Setting> setting = findByKey(key);
            return setting.map(Setting::getBooleanValue).orElse(defaultValue);
        } catch (SQLException e) {
            return defaultValue;
        }
    }
    
    public java.math.BigDecimal getDecimalValue(String key, java.math.BigDecimal defaultValue) {
        try {
            Optional<Setting> setting = findByKey(key);
            return setting.map(Setting::getDecimalValue).orElse(defaultValue);
        } catch (SQLException e) {
            return defaultValue;
        }
    }
    
    public Setting save(Setting setting) throws SQLException {
        if (setting.getId() == null) {
            return insert(setting);
        } else {
            return update(setting);
        }
    }
    
    private Setting insert(Setting setting) throws SQLException {
        String sql = "INSERT INTO settings (key, value, description, category, data_type, default_value, is_required, created_at, updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, setting.getKey());
            pstmt.setString(2, setting.getValue());
            pstmt.setString(3, setting.getDescription());
            pstmt.setString(4, setting.getCategory());
            pstmt.setString(5, setting.getDataType());
            pstmt.setString(6, setting.getDefaultValue());
            pstmt.setBoolean(7, setting.isRequired());
            pstmt.setTimestamp(8, Timestamp.valueOf(setting.getCreatedAt()));
            pstmt.setTimestamp(9, Timestamp.valueOf(setting.getUpdatedAt()));
            
            pstmt.executeUpdate();
            
            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    setting.setId(generatedKeys.getLong(1));
                }
            }
        }
        
        // Update cache
        settingsCache.put(setting.getKey(), setting);
        
        return setting;
    }
    
    private Setting update(Setting setting) throws SQLException {
        String sql = "UPDATE settings SET value = ?, description = ?, category = ?, data_type = ?, " +
                    "default_value = ?, is_required = ?, updated_at = ? WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            setting.setUpdatedAt(LocalDateTime.now());
            
            pstmt.setString(1, setting.getValue());
            pstmt.setString(2, setting.getDescription());
            pstmt.setString(3, setting.getCategory());
            pstmt.setString(4, setting.getDataType());
            pstmt.setString(5, setting.getDefaultValue());
            pstmt.setBoolean(6, setting.isRequired());
            pstmt.setTimestamp(7, Timestamp.valueOf(setting.getUpdatedAt()));
            pstmt.setLong(8, setting.getId());
            
            pstmt.executeUpdate();
        }
        
        // Update cache
        settingsCache.put(setting.getKey(), setting);
        
        return setting;
    }
    
    public void setValue(String key, String value) throws SQLException {
        Optional<Setting> existingSetting = findByKey(key);
        
        if (existingSetting.isPresent()) {
            Setting setting = existingSetting.get();
            setting.setValue(value);
            update(setting);
        } else {
            Setting newSetting = new Setting(key, value);
            insert(newSetting);
        }
    }
    
    public void delete(Long id) throws SQLException {
        String sql = "DELETE FROM settings WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            pstmt.executeUpdate();
        }
        
        // Remove from cache
        settingsCache.entrySet().removeIf(entry -> entry.getValue().getId().equals(id));
    }
    
    public void initializeCache() throws SQLException {
        settingsCache.clear();
        List<Setting> allSettings = findAll();
        for (Setting setting : allSettings) {
            settingsCache.put(setting.getKey(), setting);
        }
        cacheInitialized = true;
    }
    
    public void clearCache() {
        settingsCache.clear();
        cacheInitialized = false;
    }
    
    private Setting mapResultSetToSetting(ResultSet rs) throws SQLException {
        Setting setting = new Setting();
        setting.setId(rs.getLong("id"));
        setting.setKey(rs.getString("key"));
        setting.setValue(rs.getString("value"));
        setting.setDescription(rs.getString("description"));
        setting.setCategory(rs.getString("category"));
        setting.setDataType(rs.getString("data_type"));
        setting.setDefaultValue(rs.getString("default_value"));
        setting.setRequired(rs.getBoolean("is_required"));
        
        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            setting.setCreatedAt(createdAt.toLocalDateTime());
        }
        
        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            setting.setUpdatedAt(updatedAt.toLocalDateTime());
        }
        
        return setting;
    }
}
