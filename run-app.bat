@echo off
echo Starting Clothing Store Management System...
echo.

REM Check if JavaFX SDK exists
if not exist "javafx-sdk-17.0.2\lib" (
    echo ERROR: JavaFX SDK not found!
    echo Please ensure javafx-sdk-17.0.2 folder exists in the project directory.
    pause
    exit /b 1
)

REM Check if SQLite library exists
if not exist "lib\sqlite-jdbc-3.50.1.0.jar" (
    echo ERROR: SQLite JDBC library not found!
    echo Please ensure lib\sqlite-jdbc-3.50.1.0.jar exists.
    pause
    exit /b 1
)

REM Check if compiled classes exist
if not exist "target\classes" (
    echo ERROR: Compiled classes not found!
    echo Please compile the project first using compile.bat
    pause
    exit /b 1
)

echo All dependencies found. Starting application...
echo.

REM Run the application
java --module-path "javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml -cp "target\classes;lib\sqlite-jdbc-3.50.1.0.jar" com.clothingstore.ClothingStoreApp

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Application failed to start!
    echo Error code: %ERRORLEVEL%
    pause
)
