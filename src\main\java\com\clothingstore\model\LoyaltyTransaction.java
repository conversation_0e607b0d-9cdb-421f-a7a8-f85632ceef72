package com.clothingstore.model;

import java.time.LocalDateTime;

/**
 * Model class representing a loyalty points transaction
 */
public class LoyaltyTransaction {

    private Long id;
    private Long accountId;
    private LoyaltyTransactionType transactionType;
    private int points;
    private String description;
    private String referenceNumber; // Transaction ID, order number, etc.
    private LocalDateTime transactionDate;
    private LocalDateTime createdAt;

    // Constructors
    public LoyaltyTransaction() {
        this.transactionDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
    }

    public LoyaltyTransaction(Long accountId, LoyaltyTransactionType type, int points, String description) {
        this();
        this.accountId = accountId;
        this.transactionType = type;
        this.points = points;
        this.description = description;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public LoyaltyTransactionType getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(LoyaltyTransactionType transactionType) {
        this.transactionType = transactionType;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    // Utility methods
    public boolean isEarned() {
        return transactionType == LoyaltyTransactionType.EARNED;
    }

    public boolean isRedeemed() {
        return transactionType == LoyaltyTransactionType.REDEEMED;
    }

    public String getDisplayText() {
        StringBuilder display = new StringBuilder();

        if (isEarned()) {
            display.append("+").append(points).append(" points earned");
        } else {
            display.append(Math.abs(points)).append(" points redeemed");
        }

        if (description != null && !description.trim().isEmpty()) {
            display.append(" - ").append(description);
        }

        return display.toString();
    }

    @Override
    public String toString() {
        return String.format("LoyaltyTransaction{id=%d, type=%s, points=%d, date=%s}",
                id, transactionType, points, transactionDate);
    }
}
