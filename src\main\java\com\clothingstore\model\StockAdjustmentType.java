package com.clothingstore.model;

/**
 * Enumeration for different types of stock adjustments
 */
public enum StockAdjustmentType {
    MANUAL_ADJUSTMENT("Manual Adjustment", "Manual stock level correction"),
    PHYSICAL_COUNT("Physical Count", "Adjustment based on physical inventory count"),
    DAMAGE("Damage", "Stock reduction due to damaged items"),
    THEFT("Theft", "Stock reduction due to theft or loss"),
    EXPIRED("Expired", "Stock reduction due to expired items"),
    RETURN_TO_VENDOR("Return to Vendor", "Stock reduction due to vendor returns"),
    RECEIVED_SHIPMENT("Received Shipment", "Stock increase from new shipment"),
    TRANSFER_IN("Transfer In", "Stock increase from store transfer"),
    TRANSFER_OUT("Transfer Out", "Stock reduction from store transfer"),
    PROMOTION_SAMPLE("Promotion Sample", "Stock reduction for promotional samples"),
    BULK_IMPORT("Bulk Import", "Stock adjustment from bulk import operation"),
    SYSTEM_CORRECTION("System Correction", "System-generated correction"),
    RECOUNT("Recount", "Adjustment from inventory recount"),
    WRITE_OFF("Write-off", "Stock write-off for accounting purposes");

    private final String displayName;
    private final String description;

    StockAdjustmentType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Check if this adjustment type typically increases stock
     */
    public boolean isStockIncrease() {
        return this == RECEIVED_SHIPMENT || this == TRANSFER_IN || this == MANUAL_ADJUSTMENT || 
               this == PHYSICAL_COUNT || this == SYSTEM_CORRECTION || this == RECOUNT;
    }

    /**
     * Check if this adjustment type typically decreases stock
     */
    public boolean isStockDecrease() {
        return this == DAMAGE || this == THEFT || this == EXPIRED || this == RETURN_TO_VENDOR ||
               this == TRANSFER_OUT || this == PROMOTION_SAMPLE || this == WRITE_OFF;
    }

    /**
     * Check if this adjustment type requires additional documentation
     */
    public boolean requiresDocumentation() {
        return this == THEFT || this == DAMAGE || this == RETURN_TO_VENDOR || 
               this == TRANSFER_IN || this == TRANSFER_OUT || this == WRITE_OFF;
    }

    /**
     * Check if this adjustment type affects inventory valuation
     */
    public boolean affectsValuation() {
        return this == DAMAGE || this == THEFT || this == EXPIRED || this == WRITE_OFF ||
               this == RECEIVED_SHIPMENT || this == RETURN_TO_VENDOR;
    }
}
