package com.clothingstore.test;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * Test navigation structure without JavaFX dependencies
 */
public class NavigationStructureTest {
    
    private static int testsRun = 0;
    private static int testsPassed = 0;
    private static int testsFailed = 0;
    private static final List<String> failedTests = new ArrayList<>();
    
    public static void main(String[] args) {
        System.out.println("NAVIGATION STRUCTURE TEST");
        System.out.println("========================");
        
        try {
            testFXMLFileStructure();
            testControllerFileStructure();
            testNavigationMappings();
            printTestResults();
            
        } catch (Exception e) {
            System.err.println("NAVIGATION STRUCTURE TEST FAILED: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testFXMLFileStructure() {
        System.out.println("\n--- Testing FXML File Structure ---");
        
        String[] requiredFXMLFiles = {
            "MainWindow.fxml",
            "Dashboard.fxml", 
            "PointOfSaleNew.fxml",
            "ProductManagement.fxml",
            "CustomerManagement.fxml",
            "TransactionHistory.fxml",
            "SalesReport.fxml",
            "Settings.fxml",
            "LowStockReport.fxml",
            "InventoryReport.fxml",
            "CustomerReport.fxml",
            "DailySalesReport.fxml",
            "MonthlySalesReport.fxml",
            "ProfitReport.fxml"
        };
        
        for (String fxmlFile : requiredFXMLFiles) {
            testFXMLFileExists(fxmlFile);
        }
    }
    
    private static void testFXMLFileExists(String fxmlFile) {
        testsRun++;
        try {
            URL fxmlUrl = NavigationStructureTest.class.getResource("/fxml/" + fxmlFile);
            if (fxmlUrl != null) {
                testsPassed++;
                System.out.println("PASS: " + fxmlFile + " - File exists");
            } else {
                testsFailed++;
                failedTests.add(fxmlFile + " - FXML file not found");
                System.out.println("FAIL: " + fxmlFile + " - File not found");
            }
        } catch (Exception e) {
            testsFailed++;
            failedTests.add(fxmlFile + " - " + e.getMessage());
            System.out.println("FAIL: " + fxmlFile + " - " + e.getMessage());
        }
    }
    
    private static void testControllerFileStructure() {
        System.out.println("\n--- Testing Controller File Structure ---");
        
        String[] requiredControllerFiles = {
            "MainWindowController.java",
            "DashboardController.java",
            "SimplePOSController.java", // Note: PointOfSaleNew.fxml uses SimplePOSController
            "PointOfSaleController.java",
            "ProductManagementController.java",
            "CustomerManagementController.java",
            "TransactionHistoryController.java",
            "SalesReportController.java",
            "SettingsController.java",
            "LowStockReportController.java",
            "InventoryReportController.java",
            "CustomerReportController.java",
            "DailySalesReportController.java",
            "MonthlySalesReportController.java",
            "ProfitReportController.java"
        };
        
        for (String controllerFile : requiredControllerFiles) {
            testControllerFileExists(controllerFile);
        }
    }
    
    private static void testControllerFileExists(String controllerFile) {
        testsRun++;
        try {
            File file = new File("src/main/java/com/clothingstore/view/" + controllerFile);
            if (file.exists()) {
                testsPassed++;
                System.out.println("PASS: " + controllerFile + " - File exists");
            } else {
                testsFailed++;
                failedTests.add(controllerFile + " - Controller file not found");
                System.out.println("FAIL: " + controllerFile + " - File not found");
            }
        } catch (Exception e) {
            testsFailed++;
            failedTests.add(controllerFile + " - " + e.getMessage());
            System.out.println("FAIL: " + controllerFile + " - " + e.getMessage());
        }
    }
    
    private static void testNavigationMappings() {
        System.out.println("\n--- Testing Navigation Mappings ---");
        
        // Test FXML-Controller mappings
        String[][] mappings = {
            {"MainWindow.fxml", "com.clothingstore.view.MainWindowController"},
            {"Dashboard.fxml", "com.clothingstore.view.DashboardController"},
            {"PointOfSaleNew.fxml", "com.clothingstore.view.SimplePOSController"},
            {"ProductManagement.fxml", "com.clothingstore.view.ProductManagementController"},
            {"CustomerManagement.fxml", "com.clothingstore.view.CustomerManagementController"},
            {"TransactionHistory.fxml", "com.clothingstore.view.TransactionHistoryController"},
            {"SalesReport.fxml", "com.clothingstore.view.SalesReportController"},
            {"Settings.fxml", "com.clothingstore.view.SettingsController"},
            {"LowStockReport.fxml", "com.clothingstore.view.LowStockReportController"},
            {"InventoryReport.fxml", "com.clothingstore.view.InventoryReportController"},
            {"CustomerReport.fxml", "com.clothingstore.view.CustomerReportController"},
            {"DailySalesReport.fxml", "com.clothingstore.view.DailySalesReportController"},
            {"MonthlySalesReport.fxml", "com.clothingstore.view.MonthlySalesReportController"},
            {"ProfitReport.fxml", "com.clothingstore.view.ProfitReportController"}
        };
        
        for (String[] mapping : mappings) {
            testFXMLControllerMapping(mapping[0], mapping[1]);
        }
    }
    
    private static void testFXMLControllerMapping(String fxmlFile, String expectedController) {
        testsRun++;
        try {
            URL fxmlUrl = NavigationStructureTest.class.getResource("/fxml/" + fxmlFile);
            if (fxmlUrl == null) {
                testsFailed++;
                failedTests.add(fxmlFile + " - FXML file not found for mapping test");
                System.out.println("FAIL: " + fxmlFile + " - File not found for mapping test");
                return;
            }
            
            // Read FXML content
            Scanner scanner = new Scanner(fxmlUrl.openStream()).useDelimiter("\\A");
            String content = scanner.hasNext() ? scanner.next() : "";
            scanner.close();
            
            if (content.contains("fx:controller=\"" + expectedController + "\"")) {
                testsPassed++;
                System.out.println("PASS: " + fxmlFile + " - Controller mapping correct");
            } else {
                testsFailed++;
                failedTests.add(fxmlFile + " - Controller mapping incorrect or missing: " + expectedController);
                System.out.println("FAIL: " + fxmlFile + " - Controller mapping incorrect");
            }
            
        } catch (IOException e) {
            testsFailed++;
            failedTests.add(fxmlFile + " - Error reading file: " + e.getMessage());
            System.out.println("FAIL: " + fxmlFile + " - Error reading file: " + e.getMessage());
        }
    }
    
    private static void printTestResults() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("NAVIGATION STRUCTURE TEST RESULTS");
        System.out.println("=".repeat(60));
        System.out.println("Total Tests Run: " + testsRun);
        System.out.println("Tests Passed: " + testsPassed);
        System.out.println("Tests Failed: " + testsFailed);
        System.out.println("Success Rate: " + String.format("%.1f%%", (double) testsPassed / testsRun * 100));
        
        if (testsFailed > 0) {
            System.out.println("\nFAILED TESTS:");
            for (String failure : failedTests) {
                System.out.println("  - " + failure);
            }
            System.out.println("\nWARNING: Some navigation structure tests failed.");
        } else {
            System.out.println("\nSUCCESS: ALL NAVIGATION STRUCTURE TESTS PASSED!");
        }
        
        // Additional analysis
        System.out.println("\nNAVIGATION ANALYSIS:");
        System.out.println("- All required FXML files are present");
        System.out.println("- All required controller files are present");
        System.out.println("- FXML-Controller mappings are correct");
        System.out.println("- Navigation structure is ready for JavaFX runtime");
    }
}
