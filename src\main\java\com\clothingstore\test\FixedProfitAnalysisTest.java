package com.clothingstore.test;

import java.sql.*;
import java.time.LocalDateTime;

/**
 * Test the fixed profit analysis queries with epoch timestamp handling
 */
public class FixedProfitAnalysisTest {

    private static final String DB_URL = "*****************************";

    public static void main(String[] args) {
        System.out.println("=== FIXED PROFIT ANALYSIS TEST ===");
        System.out.println("Testing fixed epoch timestamp handling");
        System.out.println();

        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // Test date range - last 30 days
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(30);
            
            // Convert to epoch milliseconds
            long startEpoch = startDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
            long endEpoch = endDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
            
            System.out.println("Date range: " + startDate + " to " + endDate);
            System.out.println("Epoch range: " + startEpoch + " to " + endEpoch);
            System.out.println();
            
            // Test 1: Fixed profit metrics query
            String profitQuery = "SELECT " +
                    "COUNT(DISTINCT t.id) as transaction_count, " +
                    "SUM(ti.quantity) as total_items_sold, " +
                    "SUM(ti.line_total) as total_revenue, " +
                    "SUM(p.cost_price * ti.quantity) as total_cost " +
                    "FROM transactions t " +
                    "INNER JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "INNER JOIN products p ON ti.product_id = p.id " +
                    "WHERE t.status = 'COMPLETED' " +
                    "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) " +
                    "AND CAST(t.transaction_date AS INTEGER) BETWEEN ? AND ? " +
                    "AND p.cost_price IS NOT NULL";
            
            try (PreparedStatement stmt = conn.prepareStatement(profitQuery)) {
                stmt.setLong(1, startEpoch);
                stmt.setLong(2, endEpoch);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        System.out.println("FIXED Profit Metrics Query Result:");
                        System.out.println("  Transactions: " + rs.getInt("transaction_count"));
                        System.out.println("  Items Sold: " + rs.getInt("total_items_sold"));
                        System.out.println("  Revenue: $" + String.format("%.2f", rs.getDouble("total_revenue")));
                        System.out.println("  Cost: $" + String.format("%.2f", rs.getDouble("total_cost")));
                        double profit = rs.getDouble("total_revenue") - rs.getDouble("total_cost");
                        System.out.println("  Profit: $" + String.format("%.2f", profit));
                        
                        if (rs.getInt("transaction_count") > 0) {
                            System.out.println("  ✓ SUCCESS: Found transactions in date range!");
                        } else {
                            System.out.println("  ✗ No transactions found in date range");
                        }
                    }
                }
            }
            
            System.out.println();
            
            // Test 2: Fixed category breakdown query
            String categoryQuery = "SELECT " +
                    "COALESCE(p.category, 'Uncategorized') as category, " +
                    "COUNT(DISTINCT t.id) as transaction_count, " +
                    "SUM(ti.quantity) as items_sold, " +
                    "SUM(ti.line_total) as revenue, " +
                    "SUM(p.cost_price * ti.quantity) as cost " +
                    "FROM transactions t " +
                    "INNER JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "INNER JOIN products p ON ti.product_id = p.id " +
                    "WHERE t.status = 'COMPLETED' " +
                    "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) " +
                    "AND CAST(t.transaction_date AS INTEGER) BETWEEN ? AND ? " +
                    "AND p.cost_price IS NOT NULL " +
                    "GROUP BY p.category " +
                    "ORDER BY (SUM(ti.line_total) - SUM(p.cost_price * ti.quantity)) DESC";
            
            try (PreparedStatement stmt = conn.prepareStatement(categoryQuery)) {
                stmt.setLong(1, startEpoch);
                stmt.setLong(2, endEpoch);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    System.out.println("FIXED Category Breakdown Query Result:");
                    int categoryCount = 0;
                    while (rs.next()) {
                        categoryCount++;
                        double profit = rs.getDouble("revenue") - rs.getDouble("cost");
                        System.out.println("  " + rs.getString("category") + ":");
                        System.out.println("    Revenue: $" + String.format("%.2f", rs.getDouble("revenue")));
                        System.out.println("    Cost: $" + String.format("%.2f", rs.getDouble("cost")));
                        System.out.println("    Profit: $" + String.format("%.2f", profit));
                        System.out.println("    Items: " + rs.getInt("items_sold"));
                        System.out.println("    Transactions: " + rs.getInt("transaction_count"));
                    }
                    
                    if (categoryCount > 0) {
                        System.out.println("  ✓ SUCCESS: Found " + categoryCount + " categories!");
                    } else {
                        System.out.println("  ✗ No categories found in date range");
                    }
                }
            }
            
            System.out.println();
            
            // Test 3: Compare with all-time data
            System.out.println("COMPARISON - All-time data (no date filter):");
            
            String allTimeQuery = "SELECT " +
                    "COUNT(DISTINCT t.id) as transaction_count, " +
                    "SUM(ti.quantity) as total_items_sold, " +
                    "SUM(ti.line_total) as total_revenue, " +
                    "SUM(p.cost_price * ti.quantity) as total_cost " +
                    "FROM transactions t " +
                    "INNER JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "INNER JOIN products p ON ti.product_id = p.id " +
                    "WHERE t.status = 'COMPLETED' " +
                    "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) " +
                    "AND p.cost_price IS NOT NULL";
            
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(allTimeQuery)) {
                if (rs.next()) {
                    System.out.println("  All-time Transactions: " + rs.getInt("transaction_count"));
                    System.out.println("  All-time Items Sold: " + rs.getInt("total_items_sold"));
                    System.out.println("  All-time Revenue: $" + String.format("%.2f", rs.getDouble("total_revenue")));
                    System.out.println("  All-time Cost: $" + String.format("%.2f", rs.getDouble("total_cost")));
                    double profit = rs.getDouble("total_revenue") - rs.getDouble("total_cost");
                    System.out.println("  All-time Profit: $" + String.format("%.2f", profit));
                }
            }
            
            // Test 4: Check actual transaction dates in epoch range
            System.out.println("\nTransaction dates in database:");
            String dateCheckQuery = "SELECT transaction_date, CAST(transaction_date AS INTEGER) as epoch_date FROM transactions WHERE status = 'COMPLETED' ORDER BY CAST(transaction_date AS INTEGER) DESC LIMIT 5";
            
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(dateCheckQuery)) {
                while (rs.next()) {
                    long epochDate = rs.getLong("epoch_date");
                    System.out.println("  " + rs.getString("transaction_date") + " (epoch: " + epochDate + ")");
                    
                    if (epochDate >= startEpoch && epochDate <= endEpoch) {
                        System.out.println("    ✓ This transaction is in our date range!");
                    } else {
                        System.out.println("    ✗ This transaction is outside our date range");
                    }
                }
            }
            
            System.out.println("\n=== TEST COMPLETE ===");
            
        } catch (SQLException e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
