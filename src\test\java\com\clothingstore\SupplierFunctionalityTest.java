package com.clothingstore;

import java.math.BigDecimal;

import com.clothingstore.model.Supplier;
import com.clothingstore.model.SupplierStatus;
import com.clothingstore.service.SupplierOperationResult;
import com.clothingstore.service.SupplierService;

/**
 * Simple test to verify supplier functionality is working
 */
public class SupplierFunctionalityTest {

    public static void main(String[] args) {
        System.out.println("Testing Supplier Management Functionality...\n");

        SupplierService supplierService = SupplierService.getInstance();

        // Test 1: List existing suppliers
        System.out.println("Current suppliers in system:");
        supplierService.getAllSuppliers().forEach(supplier
                -> System.out.println("  - " + supplier.getCompanyName() + " (" + supplier.getStatus() + ")")
        );
        System.out.println();

        // Test 2: Create a new supplier
        System.out.println("Creating new supplier...");
        Supplier newSupplier = new Supplier();
        newSupplier.setCompanyName("Test Supplier Co.");
        newSupplier.setContactPerson("John Test");
        newSupplier.setEmail("<EMAIL>");
        newSupplier.setPhone("******-TEST");
        newSupplier.setAddress("123 Test Street");
        newSupplier.setCity("Test City");
        newSupplier.setState("TS");
        newSupplier.setZipCode("12345");
        newSupplier.setCountry("USA");
        newSupplier.setStatus(SupplierStatus.ACTIVE);
        newSupplier.setLeadTimeDays(14);
        newSupplier.setMinimumOrderAmount(new BigDecimal("500.00"));
        newSupplier.setCreditLimit(new BigDecimal("10000.00"));
        newSupplier.setNotes("Test supplier for functionality verification");

        SupplierOperationResult createResult = supplierService.createSupplier(newSupplier);
        if (createResult.isSuccess()) {
            System.out.println("SUCCESS: Supplier created successfully!");
            System.out.println("   ID: " + createResult.getSupplier().getId());
            System.out.println("   Company: " + createResult.getSupplier().getCompanyName());
            System.out.println("   Status: " + createResult.getSupplier().getStatus());
        } else {
            System.out.println("ERROR: Failed to create supplier: " + createResult.getMessage());
            return;
        }
        System.out.println();

        Supplier createdSupplier = createResult.getSupplier();

        // Test 3: Update the supplier
        System.out.println("✏️ Updating supplier...");
        createdSupplier.setContactPerson("Jane Updated");
        createdSupplier.setEmail("<EMAIL>");
        createdSupplier.setLeadTimeDays(21);
        createdSupplier.setStatus(SupplierStatus.UNDER_REVIEW);

        SupplierOperationResult updateResult = supplierService.updateSupplier(createdSupplier);
        if (updateResult.isSuccess()) {
            System.out.println("✅ Supplier updated successfully!");
            System.out.println("   Contact Person: " + updateResult.getSupplier().getContactPerson());
            System.out.println("   Email: " + updateResult.getSupplier().getEmail());
            System.out.println("   Lead Time: " + updateResult.getSupplier().getLeadTimeDays() + " days");
            System.out.println("   Status: " + updateResult.getSupplier().getStatus());
        } else {
            System.out.println("❌ Failed to update supplier: " + updateResult.getMessage());
        }
        System.out.println();

        // Test 4: Retrieve the supplier
        System.out.println("🔍 Retrieving supplier by ID...");
        Supplier retrievedSupplier = supplierService.getSupplier(createdSupplier.getId());
        if (retrievedSupplier != null) {
            System.out.println("✅ Supplier retrieved successfully!");
            System.out.println("   Company: " + retrievedSupplier.getCompanyName());
            System.out.println("   Contact: " + retrievedSupplier.getContactPerson());
            System.out.println("   Status: " + retrievedSupplier.getStatus());
        } else {
            System.out.println("❌ Failed to retrieve supplier");
        }
        System.out.println();

        // Test 5: List all suppliers again
        System.out.println("📋 All suppliers after creation:");
        supplierService.getAllSuppliers().forEach(supplier
                -> System.out.println("  - " + supplier.getCompanyName() + " (" + supplier.getStatus() + ")")
        );
        System.out.println();

        // Test 6: Delete the supplier
        System.out.println("🗑️ Deleting test supplier...");
        SupplierOperationResult deleteResult = supplierService.deleteSupplier(createdSupplier.getId());
        if (deleteResult.isSuccess()) {
            System.out.println("✅ Supplier deleted successfully!");
            System.out.println("   Message: " + deleteResult.getMessage());
        } else {
            System.out.println("❌ Failed to delete supplier: " + deleteResult.getMessage());
        }
        System.out.println();

        // Test 7: Verify deletion
        System.out.println("🔍 Verifying deletion...");
        Supplier deletedSupplier = supplierService.getSupplier(createdSupplier.getId());
        if (deletedSupplier == null) {
            System.out.println("✅ Supplier successfully deleted - not found in system");
        } else {
            System.out.println("❌ Supplier still exists after deletion");
        }
        System.out.println();

        // Test 8: Final supplier list
        System.out.println("📋 Final supplier list:");
        supplierService.getAllSuppliers().forEach(supplier
                -> System.out.println("  - " + supplier.getCompanyName() + " (" + supplier.getStatus() + ")")
        );

        System.out.println("\n🎉 All supplier functionality tests completed!");

        // Test validation
        System.out.println("\n🛡️ Testing validation...");
        Supplier invalidSupplier = new Supplier();
        // Missing company name - should fail
        invalidSupplier.setContactPerson("Test Contact");

        SupplierOperationResult validationResult = supplierService.createSupplier(invalidSupplier);
        if (!validationResult.isSuccess()) {
            System.out.println("✅ Validation working: " + validationResult.getMessage());
        } else {
            System.out.println("❌ Validation failed - invalid supplier was accepted");
        }

        System.out.println("\n✨ Supplier Management Backend is FULLY FUNCTIONAL! ✨");
    }
}
