<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.DailySalesReportController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="📈 Daily Sales Report">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <DatePicker fx:id="dateSelected" onAction="#handleDateChange" promptText="Select Date" />
            <Button fx:id="btnToday" onAction="#handleToday" text="📅 Today" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
            <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </HBox>

      <!-- Daily Summary -->
      <GridPane hgap="15.0" vgap="15.0" styleClass="form-container">
         <columnConstraints>
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         <children>
            <VBox spacing="5.0" styleClass="metric-card success" GridPane.columnIndex="0">
               <children>
                  <Label styleClass="metric-title" text="💰 Total Sales" />
                  <Label fx:id="lblTotalSales" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card info" GridPane.columnIndex="1">
               <children>
                  <Label styleClass="metric-title" text="🛒 Transactions" />
                  <Label fx:id="lblTotalTransactions" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card primary" GridPane.columnIndex="2">
               <children>
                  <Label styleClass="metric-title" text="📊 Avg Transaction" />
                  <Label fx:id="lblAvgTransaction" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card warning" GridPane.columnIndex="3">
               <children>
                  <Label styleClass="metric-title" text="👥 Customers" />
                  <Label fx:id="lblUniqueCustomers" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </GridPane>

      <!-- Hourly Sales Chart Area -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="section-title" text="📊 Hourly Sales Distribution">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            <TableView fx:id="tblHourlySales" prefHeight="200.0">
               <columns>
                  <TableColumn fx:id="colHour" prefWidth="100.0" text="Hour" />
                  <TableColumn fx:id="colHourTransactions" prefWidth="120.0" text="Transactions" />
                  <TableColumn fx:id="colHourSales" prefWidth="120.0" text="Sales Amount" />
                  <TableColumn fx:id="colHourAvg" prefWidth="120.0" text="Avg per Transaction" />
                  <TableColumn fx:id="colHourPercentage" prefWidth="100.0" text="% of Total" />
               </columns>
            </TableView>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Daily Transactions -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="💳 Daily Transactions">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <TextField fx:id="txtSearch" onKeyReleased="#handleSearch" promptText="Search transactions..." />
               </children>
            </HBox>

            <TableView fx:id="tblDailyTransactions" prefHeight="250.0">
               <columns>
                  <TableColumn fx:id="colTransactionId" prefWidth="100.0" text="Transaction ID" />
                  <TableColumn fx:id="colTime" prefWidth="100.0" text="Time" />
                  <TableColumn fx:id="colCustomer" prefWidth="150.0" text="Customer" />
                  <TableColumn fx:id="colItems" prefWidth="80.0" text="Items" />
                  <TableColumn fx:id="colSubtotal" prefWidth="100.0" text="Subtotal" />
                  <TableColumn fx:id="colTax" prefWidth="80.0" text="Tax" />
                  <TableColumn fx:id="colTotal" prefWidth="100.0" text="Total" />
                  <TableColumn fx:id="colPaymentMethod" prefWidth="120.0" text="Payment Method" />
                  <TableColumn fx:id="colActions" prefWidth="100.0" text="Actions" />
               </columns>
            </TableView>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Top Products Today -->
      <HBox spacing="15.0" styleClass="form-container">
         <children>
            <VBox spacing="10.0" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="section-title" text="🏆 Top Products Today">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <TableView fx:id="tblTopProducts" prefHeight="150.0">
                     <columns>
                        <TableColumn fx:id="colProductRank" prefWidth="50.0" text="Rank" />
                        <TableColumn fx:id="colProductName" prefWidth="200.0" text="Product" />
                        <TableColumn fx:id="colQuantitySold" prefWidth="100.0" text="Qty Sold" />
                        <TableColumn fx:id="colProductRevenue" prefWidth="120.0" text="Revenue" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
