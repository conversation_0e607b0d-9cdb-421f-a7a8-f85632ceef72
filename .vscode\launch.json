{"version": "0.2.0", "configurations": [{"type": "java", "name": "Launch ClothingStoreApp", "request": "launch", "mainClass": "com.clothingstore.ClothingStoreApp", "projectName": "clothing-store-pos", "classpath": ["${workspaceFolder}/bin", "${workspaceFolder}/lib/sqlite-jdbc-3.50.1.0.jar"], "vmArgs": "--module-path ${workspaceFolder}/javafx-sdk-11.0.2/lib --add-modules javafx.controls,javafx.fxml", "console": "integratedTerminal"}, {"type": "java", "name": "Launch BasicJavaFXApp", "request": "launch", "mainClass": "com.clothingstore.BasicJavaFXApp", "projectName": "clothing-store-pos", "classpath": ["${workspaceFolder}/bin", "${workspaceFolder}/lib/sqlite-jdbc-3.50.1.0.jar"], "vmArgs": "--module-path ${workspaceFolder}/javafx-sdk-11.0.2/lib --add-modules javafx.controls,javafx.fxml", "console": "integratedTerminal"}]}