package com.clothingstore.service;

import com.clothingstore.dao.RefundItemTrackingDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.*;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Service class for managing detailed refund item tracking Provides
 * comprehensive refund history and remaining quantity calculations
 */
public class RefundItemTrackingService {

    private static final Logger LOGGER = Logger.getLogger(RefundItemTrackingService.class.getName());

    private final RefundItemTrackingDAO refundTrackingDAO;
    private final TransactionDAO transactionDAO;

    public RefundItemTrackingService() {
        this.refundTrackingDAO = new RefundItemTrackingDAO();
        this.transactionDAO = TransactionDAO.getInstance();
    }

    /**
     * Record a refund for specific transaction items
     */
    public void recordRefund(Transaction originalTransaction, Transaction refundTransaction,
            List<RefundItem> refundItems, String refundReason, String cashierName) throws SQLException {

        for (RefundItem refundItem : refundItems) {
            // Get the original transaction item from refund item
            TransactionItem originalItem = refundItem.getOriginalItem();
            if (originalItem == null) {
                LOGGER.warning("Could not find original transaction item for refund item");
                continue;
            }

            // Create refund tracking record
            RefundItemTracking tracking = new RefundItemTracking(
                    originalTransaction.getId(),
                    originalItem.getId(),
                    refundTransaction.getId(),
                    refundItem.getProductId(),
                    refundItem.getProductName(),
                    originalItem.getQuantity(),
                    refundItem.getQuantity(),
                    refundItem.getUnitPrice(),
                    refundItem.getLineTotal(),
                    refundReason,
                    cashierName
            );

            // Set product SKU if available
            if (originalItem.getProduct() != null) {
                tracking.setProductSku(originalItem.getProduct().getSku());
            }

            // Save the tracking record
            refundTrackingDAO.save(tracking);

            // Update transaction item refund tracking columns
            refundTrackingDAO.updateTransactionItemRefundTracking(originalItem.getId());
        }
    }

    /**
     * Get refund summary for all items in a transaction
     */
    public List<TransactionItemRefundSummary> getTransactionRefundSummary(Long transactionId) throws SQLException {
        Transaction transaction = transactionDAO.findById(transactionId);
        if (transaction == null) {
            return new ArrayList<>();
        }

        List<TransactionItemRefundSummary> summaries = new ArrayList<>();

        for (TransactionItem item : transaction.getItems()) {
            TransactionItemRefundSummary summary = getTransactionItemRefundSummary(item.getId());
            summaries.add(summary);
        }

        return summaries;
    }

    /**
     * Get detailed refund summary for a specific transaction item
     */
    public TransactionItemRefundSummary getTransactionItemRefundSummary(Long transactionItemId) throws SQLException {
        // Get the transaction item details
        TransactionItem item = getTransactionItemById(transactionItemId);
        if (item == null) {
            throw new SQLException("Transaction item not found: " + transactionItemId);
        }

        // Create summary with basic information
        TransactionItemRefundSummary summary = new TransactionItemRefundSummary(
                item.getId(),
                item.getTransactionId(),
                item.getProductId(),
                item.getProduct() != null ? item.getProduct().getName() : "Unknown Product",
                item.getQuantity(),
                item.getUnitPrice()
        );

        // Set product SKU if available
        if (item.getProduct() != null) {
            summary.setProductSku(item.getProduct().getSku());
        }

        // Get refund history
        List<RefundItemTracking> refundHistory = refundTrackingDAO.findByTransactionItemId(transactionItemId);
        summary.setRefundHistory(refundHistory);

        return summary;
    }

    /**
     * Check if a transaction item can be refunded with the specified quantity
     */
    public boolean canRefundQuantity(Long transactionItemId, int requestedQuantity) throws SQLException {
        TransactionItemRefundSummary summary = getTransactionItemRefundSummary(transactionItemId);
        return summary.canRefund(requestedQuantity);
    }

    /**
     * Get remaining refundable quantity for a transaction item
     */
    public int getRemainingRefundableQuantity(Long transactionItemId) throws SQLException {
        TransactionItemRefundSummary summary = getTransactionItemRefundSummary(transactionItemId);
        return summary.getRemainingQuantity();
    }

    /**
     * Get total refunded amount for a transaction item
     */
    public BigDecimal getTotalRefundedAmount(Long transactionItemId) throws SQLException {
        return refundTrackingDAO.getTotalRefundedAmount(transactionItemId);
    }

    /**
     * Get refund history for a specific transaction
     */
    public List<RefundItemTracking> getTransactionRefundHistory(Long transactionId) throws SQLException {
        return refundTrackingDAO.findByTransactionId(transactionId);
    }

    /**
     * Get refund history for a specific product across all transactions
     */
    public List<RefundItemTracking> getProductRefundHistory(Long productId) throws SQLException {
        return refundTrackingDAO.findByProductId(productId);
    }

    /**
     * Validate refund request against available quantities
     */
    public RefundValidationResult validateRefundRequest(Long transactionId, List<RefundItem> refundItems) throws SQLException {
        List<String> errors = new ArrayList<>();
        Map<Long, Integer> availableQuantities = new HashMap<>();

        // Get transaction details
        Transaction transaction = transactionDAO.findById(transactionId);
        if (transaction == null) {
            errors.add("Transaction not found: " + transactionId);
            return new RefundValidationResult(false, errors);
        }

        // Build map of available quantities
        for (TransactionItem item : transaction.getItems()) {
            TransactionItemRefundSummary summary = getTransactionItemRefundSummary(item.getId());
            availableQuantities.put(item.getProductId(), summary.getRemainingQuantity());
        }

        // Validate each refund item
        for (RefundItem refundItem : refundItems) {
            Integer availableQty = availableQuantities.get(refundItem.getProductId());

            if (availableQty == null) {
                errors.add("Product not found in original transaction: " + refundItem.getProductName());
                continue;
            }

            if (refundItem.getQuantity() > availableQty) {
                errors.add(String.format("Cannot refund %d units of %s. Only %d units available for refund.",
                        refundItem.getQuantity(), refundItem.getProductName(), availableQty));
            }
        }

        return new RefundValidationResult(errors.isEmpty(), errors);
    }

    /**
     * Get comprehensive refund statistics for a transaction
     */
    public TransactionRefundStatistics getTransactionRefundStatistics(Long transactionId) throws SQLException {
        List<TransactionItemRefundSummary> summaries = getTransactionRefundSummary(transactionId);

        int totalItems = summaries.size();
        int itemsWithRefunds = 0;
        int fullyRefundedItems = 0;
        BigDecimal totalRefundedAmount = BigDecimal.ZERO;
        BigDecimal totalRemainingRefundableAmount = BigDecimal.ZERO;

        for (TransactionItemRefundSummary summary : summaries) {
            if (summary.hasRefunds()) {
                itemsWithRefunds++;
            }
            if (summary.isFullyRefunded()) {
                fullyRefundedItems++;
            }
            totalRefundedAmount = totalRefundedAmount.add(summary.getTotalRefundedAmount());
            totalRemainingRefundableAmount = totalRemainingRefundableAmount.add(summary.getRemainingRefundableAmount());
        }

        return new TransactionRefundStatistics(
                transactionId,
                totalItems,
                itemsWithRefunds,
                fullyRefundedItems,
                totalRefundedAmount,
                totalRemainingRefundableAmount
        );
    }

    // Helper methods
    private TransactionItem findTransactionItem(Transaction transaction, Long productId) {
        return transaction.getItems().stream()
                .filter(item -> item.getProductId().equals(productId))
                .findFirst()
                .orElse(null);
    }

    private TransactionItem getTransactionItemById(Long transactionItemId) throws SQLException {
        // This would need to be implemented in TransactionDAO
        // For now, we'll get it through the transaction
        // This is a simplified approach - in a real implementation, 
        // you'd want a direct method in TransactionDAO

        String sql = "SELECT transaction_id FROM transaction_items WHERE id = ?";
        try (var conn = com.clothingstore.database.DatabaseManager.getInstance().getConnection(); var stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionItemId);
            try (var rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Long transactionId = rs.getLong("transaction_id");
                    Transaction transaction = transactionDAO.findById(transactionId);
                    if (transaction != null) {
                        return transaction.getItems().stream()
                                .filter(item -> item.getId().equals(transactionItemId))
                                .findFirst()
                                .orElse(null);
                    }
                }
            }
        }
        return null;
    }

    /**
     * Statistics class for transaction refund information
     */
    public static class TransactionRefundStatistics {

        private final Long transactionId;
        private final int totalItems;
        private final int itemsWithRefunds;
        private final int fullyRefundedItems;
        private final BigDecimal totalRefundedAmount;
        private final BigDecimal totalRemainingRefundableAmount;

        public TransactionRefundStatistics(Long transactionId, int totalItems, int itemsWithRefunds,
                int fullyRefundedItems, BigDecimal totalRefundedAmount,
                BigDecimal totalRemainingRefundableAmount) {
            this.transactionId = transactionId;
            this.totalItems = totalItems;
            this.itemsWithRefunds = itemsWithRefunds;
            this.fullyRefundedItems = fullyRefundedItems;
            this.totalRefundedAmount = totalRefundedAmount;
            this.totalRemainingRefundableAmount = totalRemainingRefundableAmount;
        }

        // Getters
        public Long getTransactionId() {
            return transactionId;
        }

        public int getTotalItems() {
            return totalItems;
        }

        public int getItemsWithRefunds() {
            return itemsWithRefunds;
        }

        public int getFullyRefundedItems() {
            return fullyRefundedItems;
        }

        public BigDecimal getTotalRefundedAmount() {
            return totalRefundedAmount;
        }

        public BigDecimal getTotalRemainingRefundableAmount() {
            return totalRemainingRefundableAmount;
        }

        public boolean hasAnyRefunds() {
            return itemsWithRefunds > 0;
        }

        public boolean isFullyRefunded() {
            return fullyRefundedItems == totalItems;
        }

        public boolean isPartiallyRefunded() {
            return itemsWithRefunds > 0 && fullyRefundedItems < totalItems;
        }
    }
}
