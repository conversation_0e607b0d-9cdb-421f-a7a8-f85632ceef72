package com.clothingstore.service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Logger;

import com.clothingstore.dao.RefundItemTrackingDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.RefundItem;
import com.clothingstore.model.RefundItemTracking;
import com.clothingstore.model.RefundValidationResult;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.model.TransactionItemRefundSummary;

/**
 * Service class for managing detailed refund item tracking Provides
 * comprehensive refund history and remaining quantity calculations
 */
public class RefundItemTrackingService {

    private static final Logger LOGGER = Logger.getLogger(RefundItemTrackingService.class.getName());

    private final RefundItemTrackingDAO refundTrackingDAO;
    private final TransactionDAO transactionDAO;

    public RefundItemTrackingService() {
        this.refundTrackingDAO = new RefundItemTrackingDAO();
        this.transactionDAO = TransactionDAO.getInstance();
    }

    /**
     * Record a refund for specific transaction items
     */
    public void recordRefund(Transaction originalTransaction, Transaction refundTransaction,
            List<RefundItem> refundItems, String refundReason, String cashierName) throws SQLException {

        for (RefundItem refundItem : refundItems) {
            // Get the original transaction item from refund item
            TransactionItem originalItem = refundItem.getOriginalItem();
            if (originalItem == null) {
                LOGGER.warning("Could not find original transaction item for refund item");
                continue;
            }

            // Create refund tracking record
            RefundItemTracking tracking = new RefundItemTracking(
                    originalTransaction.getId(),
                    originalItem.getId(),
                    refundTransaction.getId(),
                    originalItem.getProduct() != null ? originalItem.getProduct().getId() : null,
                    originalItem.getProductName(),
                    originalItem.getQuantity(),
                    refundItem.getRefundQuantity(),
                    originalItem.getUnitPrice(),
                    refundItem.getRefundAmount(),
                    refundReason,
                    cashierName
            );

            // Set product SKU if available
            if (originalItem.getProduct() != null) {
                tracking.setProductSku(originalItem.getProduct().getSku());
            }

            // Save the tracking record
            refundTrackingDAO.save(tracking);

            // Update transaction item refund tracking columns
            refundTrackingDAO.updateTransactionItemRefundTracking(originalItem.getId());
        }
    }

    /**
     * Get refund summary for all items in a transaction
     */
    public List<TransactionItemRefundSummary> getTransactionRefundSummary(Long transactionId) throws SQLException {
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            return new ArrayList<>();
        }
        Transaction transaction = transactionOpt.get();

        List<TransactionItemRefundSummary> summaries = new ArrayList<>();

        for (TransactionItem item : transaction.getItems()) {
            TransactionItemRefundSummary summary = getTransactionItemRefundSummary(item.getId());
            summaries.add(summary);
        }

        return summaries;
    }

    /**
     * Get detailed refund summary for a specific transaction item
     */
    public TransactionItemRefundSummary getTransactionItemRefundSummary(Long transactionItemId) throws SQLException {
        // Get the transaction item details
        TransactionItem item = getTransactionItemById(transactionItemId);
        if (item == null) {
            // Transaction item ID might be stale due to transaction updates
            // Try to find the current item using refund tracking records
            item = findCurrentTransactionItemFromRefundHistory(transactionItemId);

            if (item == null) {
                return null; // Graceful handling
            }
        }

        // Create summary with basic information
        TransactionItemRefundSummary summary = new TransactionItemRefundSummary(
                item.getId(),
                item.getTransactionId(),
                item.getProductId(),
                item.getProduct() != null ? item.getProduct().getName() : "Unknown Product",
                item.getQuantity(),
                item.getUnitPrice()
        );

        // Set product SKU if available
        if (item.getProduct() != null) {
            summary.setProductSku(item.getProduct().getSku());
        }

        // Get refund history
        List<RefundItemTracking> refundHistory = refundTrackingDAO.findByTransactionItemId(transactionItemId);

        // If no direct records found, try to find by transaction and product
        if (refundHistory.isEmpty() && item.getTransactionId() != null && item.getProductId() != null) {
            refundHistory = findRefundHistoryByTransactionAndProduct(item.getTransactionId(), item.getProductId());
        }

        summary.setRefundHistory(refundHistory);

        // Calculate totals
        int totalRefunded = refundHistory.stream()
                .mapToInt(RefundItemTracking::getRefundedQuantity)
                .sum();

        BigDecimal totalRefundAmount = refundHistory.stream()
                .map(RefundItemTracking::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        summary.setTotalRefundedQuantity(totalRefunded);
        summary.setTotalRefundedAmount(totalRefundAmount);
        summary.setRemainingQuantity(item.getQuantity() - totalRefunded);

        return summary;
    }

    /**
     * Get detailed refund summary for a specific transaction item with graceful
     * error handling
     */
    public TransactionItemRefundSummary getTransactionItemRefundSummarySafe(Long transactionItemId) {
        try {
            return getTransactionItemRefundSummary(transactionItemId);
        } catch (SQLException e) {
            System.err.println("Warning: Could not get refund summary for transaction item " + transactionItemId + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Clean up orphaned refund tracking records that reference non-existent
     * transaction items Returns the number of orphaned records found and
     * cleaned up
     */
    public int cleanupOrphanedRefundRecords() throws SQLException {
        String findOrphansSql = "SELECT DISTINCT rit.original_transaction_item_id "
                + "FROM refund_item_tracking rit "
                + "LEFT JOIN transaction_items ti ON rit.original_transaction_item_id = ti.id "
                + "WHERE ti.id IS NULL";

        String deleteOrphansSql = "DELETE FROM refund_item_tracking "
                + "WHERE original_transaction_item_id NOT IN "
                + "(SELECT id FROM transaction_items)";

        int orphanedCount = 0;

        try (var conn = com.clothingstore.database.DatabaseManager.getInstance().getConnection()) {
            // First, count orphaned records
            try (var stmt = conn.prepareStatement(findOrphansSql); var rs = stmt.executeQuery()) {
                while (rs.next()) {
                    orphanedCount++;
                    System.out.println("Found orphaned refund tracking record for transaction item ID: "
                            + rs.getLong("original_transaction_item_id"));
                }
            }

            // Then delete them
            if (orphanedCount > 0) {
                try (var stmt = conn.prepareStatement(deleteOrphansSql)) {
                    int deletedCount = stmt.executeUpdate();
                    System.out.println("Cleaned up " + deletedCount + " orphaned refund tracking records");
                    return deletedCount;
                }
            }
        }

        return 0;
    }

    /**
     * Check if a transaction item can be refunded with the specified quantity
     */
    public boolean canRefundQuantity(Long transactionItemId, int requestedQuantity) throws SQLException {
        TransactionItemRefundSummary summary = getTransactionItemRefundSummary(transactionItemId);
        return summary.canRefund(requestedQuantity);
    }

    /**
     * Get remaining refundable quantity for a transaction item
     */
    public int getRemainingRefundableQuantity(Long transactionItemId) throws SQLException {
        TransactionItemRefundSummary summary = getTransactionItemRefundSummary(transactionItemId);
        return summary.getRemainingQuantity();
    }

    /**
     * Get total refunded amount for a transaction item
     */
    public BigDecimal getTotalRefundedAmount(Long transactionItemId) throws SQLException {
        return refundTrackingDAO.getTotalRefundedAmount(transactionItemId);
    }

    /**
     * Get refund history for a specific transaction
     */
    public List<RefundItemTracking> getTransactionRefundHistory(Long transactionId) throws SQLException {
        return refundTrackingDAO.findByTransactionId(transactionId);
    }

    /**
     * Get refund history for a specific product across all transactions
     */
    public List<RefundItemTracking> getProductRefundHistory(Long productId) throws SQLException {
        return refundTrackingDAO.findByProductId(productId);
    }

    /**
     * Validate refund request against available quantities
     */
    public RefundValidationResult validateRefundRequest(Long transactionId, List<RefundItem> refundItems) throws SQLException {
        List<String> errors = new ArrayList<>();
        Map<Long, Integer> availableQuantities = new HashMap<>();

        // Get transaction details
        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
        if (!transactionOpt.isPresent()) {
            errors.add("Transaction not found: " + transactionId);
            return new RefundValidationResult(false, String.join("; ", errors));
        }
        Transaction transaction = transactionOpt.get();

        // Build map of available quantities
        for (TransactionItem item : transaction.getItems()) {
            TransactionItemRefundSummary summary = getTransactionItemRefundSummary(item.getId());
            availableQuantities.put(item.getProduct() != null ? item.getProduct().getId() : null, summary.getRemainingQuantity());
        }

        // Validate each refund item
        for (RefundItem refundItem : refundItems) {
            Long productId = refundItem.getOriginalItem().getProduct() != null
                    ? refundItem.getOriginalItem().getProduct().getId() : null;
            Integer availableQty = availableQuantities.get(productId);

            if (availableQty == null) {
                errors.add("Product not found in original transaction: " + refundItem.getOriginalItem().getProductName());
                continue;
            }

            if (refundItem.getRefundQuantity() > availableQty) {
                errors.add(String.format("Cannot refund %d units of %s. Only %d units available for refund.",
                        refundItem.getRefundQuantity(), refundItem.getOriginalItem().getProductName(), availableQty));
            }
        }

        return new RefundValidationResult(errors.isEmpty(), String.join("; ", errors));
    }

    /**
     * Get comprehensive refund statistics for a transaction
     */
    public TransactionRefundStatistics getTransactionRefundStatistics(Long transactionId) throws SQLException {
        List<TransactionItemRefundSummary> summaries = getTransactionRefundSummary(transactionId);

        int totalItems = summaries.size();
        int itemsWithRefunds = 0;
        int fullyRefundedItems = 0;
        BigDecimal totalRefundedAmount = BigDecimal.ZERO;
        BigDecimal totalRemainingRefundableAmount = BigDecimal.ZERO;

        for (TransactionItemRefundSummary summary : summaries) {
            if (summary.hasRefunds()) {
                itemsWithRefunds++;
            }
            if (summary.isFullyRefunded()) {
                fullyRefundedItems++;
            }
            totalRefundedAmount = totalRefundedAmount.add(summary.getTotalRefundedAmount());
            totalRemainingRefundableAmount = totalRemainingRefundableAmount.add(summary.getRemainingRefundableAmount());
        }

        return new TransactionRefundStatistics(
                transactionId,
                totalItems,
                itemsWithRefunds,
                fullyRefundedItems,
                totalRefundedAmount,
                totalRemainingRefundableAmount
        );
    }

    // Helper methods
    private TransactionItem findTransactionItem(Transaction transaction, Long productId) {
        return transaction.getItems().stream()
                .filter(item -> item.getProductId().equals(productId))
                .findFirst()
                .orElse(null);
    }

    /**
     * Find refund history by transaction and product when transaction item ID
     * is stale
     */
    private List<RefundItemTracking> findRefundHistoryByTransactionAndProduct(Long transactionId, Long productId) throws SQLException {
        return refundTrackingDAO.findByTransactionAndProduct(transactionId, productId);
    }

    /**
     * Find current transaction item from refund history when original item ID
     * is stale
     */
    private TransactionItem findCurrentTransactionItemFromRefundHistory(Long staleTransactionItemId) throws SQLException {
        // Get refund records for the stale item ID
        List<RefundItemTracking> refundRecords = refundTrackingDAO.findByTransactionItemId(staleTransactionItemId);

        if (refundRecords.isEmpty()) {
            return null;
        }

        // Use the first record to get transaction and product info
        RefundItemTracking firstRecord = refundRecords.get(0);
        Long transactionId = firstRecord.getOriginalTransactionId();
        Long productId = firstRecord.getProductId();

        if (transactionId == null || productId == null) {
            return null;
        }

        // Find the current transaction item by transaction and product
        try {
            TransactionDAO transactionDAO = TransactionDAO.getInstance();
            Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);

            if (transactionOpt.isPresent()) {
                Transaction transaction = transactionOpt.get();
                return findTransactionItem(transaction, productId);
            }
        } catch (SQLException e) {
            LOGGER.warning("Could not find current transaction item: " + e.getMessage());
        }

        return null;
    }

    private TransactionItem getTransactionItemById(Long transactionItemId) throws SQLException {
        // This would need to be implemented in TransactionDAO
        // For now, we'll get it through the transaction
        // This is a simplified approach - in a real implementation, 
        // you'd want a direct method in TransactionDAO

        String sql = "SELECT transaction_id FROM transaction_items WHERE id = ?";
        try (var conn = com.clothingstore.database.DatabaseManager.getInstance().getConnection(); var stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionItemId);
            try (var rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Long transactionId = rs.getLong("transaction_id");
                    Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
                    if (transactionOpt.isPresent()) {
                        Transaction transaction = transactionOpt.get();
                        return transaction.getItems().stream()
                                .filter(item -> item.getId().equals(transactionItemId))
                                .findFirst()
                                .orElse(null);
                    }
                }
            }
        }
        return null;
    }

    /**
     * Statistics class for transaction refund information
     */
    public static class TransactionRefundStatistics {

        private final Long transactionId;
        private final int totalItems;
        private final int itemsWithRefunds;
        private final int fullyRefundedItems;
        private final BigDecimal totalRefundedAmount;
        private final BigDecimal totalRemainingRefundableAmount;

        public TransactionRefundStatistics(Long transactionId, int totalItems, int itemsWithRefunds,
                int fullyRefundedItems, BigDecimal totalRefundedAmount,
                BigDecimal totalRemainingRefundableAmount) {
            this.transactionId = transactionId;
            this.totalItems = totalItems;
            this.itemsWithRefunds = itemsWithRefunds;
            this.fullyRefundedItems = fullyRefundedItems;
            this.totalRefundedAmount = totalRefundedAmount;
            this.totalRemainingRefundableAmount = totalRemainingRefundableAmount;
        }

        // Getters
        public Long getTransactionId() {
            return transactionId;
        }

        public int getTotalItems() {
            return totalItems;
        }

        public int getItemsWithRefunds() {
            return itemsWithRefunds;
        }

        public int getFullyRefundedItems() {
            return fullyRefundedItems;
        }

        public BigDecimal getTotalRefundedAmount() {
            return totalRefundedAmount;
        }

        public BigDecimal getTotalRemainingRefundableAmount() {
            return totalRemainingRefundableAmount;
        }

        public boolean hasAnyRefunds() {
            return itemsWithRefunds > 0;
        }

        public boolean isFullyRefunded() {
            return fullyRefundedItems == totalItems;
        }

        public boolean isPartiallyRefunded() {
            return itemsWithRefunds > 0 && fullyRefundedItems < totalItems;
        }
    }
}
