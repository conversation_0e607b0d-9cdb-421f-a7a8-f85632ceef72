<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox fx:id="mainContainer" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.controller.SupplierManagementController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="header-section">
         <children>
            <Label styleClass="page-title" text="Supplier Management">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="refreshButton" mnemonicParsing="false" onAction="#handleRefresh" styleClass="action-button" text="Refresh" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Search and Filter Section -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="filter-section">
         <children>
            <Label text="Search:" />
            <TextField fx:id="searchField" prefWidth="200.0" promptText="Search suppliers..." />
            <Label text="Status:" />
            <ComboBox fx:id="filterStatusComboBox" prefWidth="150.0" promptText="All Statuses" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="generateReorderSuggestionsButton" mnemonicParsing="false" onAction="#handleGenerateReorderSuggestions" styleClass="secondary-button" text="Reorder Suggestions" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="5.0" />
         </padding>
      </HBox>

      <!-- Main Content Area - Better split ratio and responsive design -->
      <SplitPane dividerPositions="0.55" VBox.vgrow="ALWAYS">
         <items>
            <!-- Left Side - Supplier List -->
            <VBox spacing="8.0" minWidth="400.0">
               <children>
                  <!-- Supplier List Header -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label styleClass="section-title" text="Suppliers">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <Button fx:id="addSupplierButton" mnemonicParsing="false" onAction="#handleAddSupplier" styleClass="primary-button" text="Add Supplier" />
                     </children>
                  </HBox>

                  <!-- Supplier Table - Improved column widths -->
                  <TableView fx:id="supplierTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="companyNameColumn" prefWidth="180.0" text="Company Name" />
                        <TableColumn fx:id="contactPersonColumn" prefWidth="140.0" text="Contact Person" />
                        <TableColumn fx:id="emailColumn" prefWidth="180.0" text="Email" />
                        <TableColumn fx:id="phoneColumn" prefWidth="120.0" text="Phone" />
                        <TableColumn fx:id="statusColumn" prefWidth="90.0" text="Status" />
                        <TableColumn fx:id="leadTimeColumn" prefWidth="90.0" text="Lead Time" />
                     </columns>
                  </TableView>

                  <!-- Supplier Action Buttons -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Button fx:id="editSupplierButton" disable="true" mnemonicParsing="false" onAction="#handleEditSupplier" styleClass="secondary-button" text="Edit" />
                        <Button fx:id="deleteSupplierButton" disable="true" mnemonicParsing="false" onAction="#handleDeleteSupplier" styleClass="danger-button" text="Delete" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Button fx:id="createPurchaseOrderButton" disable="true" mnemonicParsing="false" onAction="#handleCreatePurchaseOrder" styleClass="primary-button" text="Create PO" />
                     </children>
                     <padding>
                        <Insets top="5.0" />
                     </padding>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="5.0" top="10.0" />
               </padding>
            </VBox>

            <!-- Right Side - Supplier Details and Purchase Orders -->
            <VBox spacing="12.0" minWidth="450.0">
               <children>
                  <!-- Supplier Details Form -->
                  <VBox spacing="8.0" styleClass="form-section">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="section-title" text="Supplier Details">
                                 <font>
                                    <Font name="System Bold" size="16.0" />
                                 </font>
                              </Label>
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="saveSupplierButton" mnemonicParsing="false" onAction="#handleSaveSupplier" styleClass="primary-button" text="Save" visible="false" />
                              <Button fx:id="cancelSupplierButton" mnemonicParsing="false" onAction="#handleCancelSupplier" styleClass="secondary-button" text="Cancel" visible="false" />
                           </children>
                        </HBox>

                        <!-- Supplier Form - Improved layout with ScrollPane -->
                        <ScrollPane fitToWidth="true" prefHeight="300.0">
                           <content>
                              <GridPane hgap="12.0" vgap="10.0">
                                 <columnConstraints>
                                    <ColumnConstraints hgrow="NEVER" minWidth="120.0" prefWidth="130.0" />
                                    <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                                 </columnConstraints>
                                 <children>
                                    <Label text="Company Name:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                    <TextField fx:id="companyNameField" disable="true" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                                    <Label text="Contact Person:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                    <TextField fx:id="contactPersonField" disable="true" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                                    <Label text="Email:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                    <TextField fx:id="emailField" disable="true" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                                    <Label text="Phone:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                                    <TextField fx:id="phoneField" disable="true" GridPane.columnIndex="1" GridPane.rowIndex="3" />

                                    <Label text="Address:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                                    <TextField fx:id="addressField" disable="true" GridPane.columnIndex="1" GridPane.rowIndex="4" />

                                    <Label text="City:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                                    <HBox spacing="8.0" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                       <children>
                                          <TextField fx:id="cityField" disable="true" HBox.hgrow="ALWAYS" />
                                          <Label text="State:" />
                                          <TextField fx:id="stateField" disable="true" prefWidth="70.0" />
                                          <Label text="ZIP:" />
                                          <TextField fx:id="zipCodeField" disable="true" prefWidth="90.0" />
                                       </children>
                                    </HBox>

                                    <Label text="Country:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                                    <TextField fx:id="countryField" disable="true" GridPane.columnIndex="1" GridPane.rowIndex="6" />

                                    <Label text="Status:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                                    <ComboBox fx:id="statusComboBox" disable="true" prefWidth="180.0" GridPane.columnIndex="1" GridPane.rowIndex="7" />

                                    <Label text="Lead Time (days):" GridPane.columnIndex="0" GridPane.rowIndex="8" />
                                    <HBox spacing="12.0" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                       <children>
                                          <TextField fx:id="leadTimeDaysField" disable="true" prefWidth="90.0" />
                                          <Label text="Min Order:" />
                                          <TextField fx:id="minimumOrderAmountField" disable="true" prefWidth="110.0" />
                                          <Label text="Credit Limit:" />
                                          <TextField fx:id="creditLimitField" disable="true" prefWidth="110.0" />
                                       </children>
                                    </HBox>

                                    <Label text="Notes:" GridPane.columnIndex="0" GridPane.rowIndex="9" />
                                    <TextArea fx:id="notesArea" disable="true" prefRowCount="3" GridPane.columnIndex="1" GridPane.rowIndex="9" />
                                 </children>
                              </GridPane>
                           </content>
                        </ScrollPane>
                     </children>
                     <padding>
                        <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
                     </padding>
                  </VBox>

                  <!-- Purchase Orders Section -->
                  <VBox spacing="8.0" styleClass="form-section" VBox.vgrow="ALWAYS">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="section-title" text="Purchase Orders">
                                 <font>
                                    <Font name="System Bold" size="16.0" />
                                 </font>
                              </Label>
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="viewPurchaseOrderButton" disable="true" mnemonicParsing="false" onAction="#handleViewPurchaseOrder" styleClass="secondary-button" text="View Details" />
                           </children>
                        </HBox>

                        <!-- Purchase Orders Table - Improved column widths -->
                        <TableView fx:id="purchaseOrderTable" VBox.vgrow="ALWAYS" minHeight="200.0">
                           <columns>
                              <TableColumn fx:id="orderNumberColumn" prefWidth="140.0" text="Order Number" />
                              <TableColumn fx:id="orderStatusColumn" prefWidth="110.0" text="Status" />
                              <TableColumn fx:id="orderDateColumn" prefWidth="120.0" text="Order Date" />
                              <TableColumn fx:id="expectedDeliveryColumn" prefWidth="140.0" text="Expected Delivery" />
                              <TableColumn fx:id="totalAmountColumn" prefWidth="120.0" text="Total Amount" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
                     </padding>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="5.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </children>
</VBox>
