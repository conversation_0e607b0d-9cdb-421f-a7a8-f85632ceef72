package com.clothingstore.util;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Logger;

import com.clothingstore.database.DatabaseManager;

/**
 * Core database setup functionality without JavaFX dependencies Used for
 * testing and command-line operations
 */
public class DatabaseSetupCore {

    private static final Logger LOGGER = Logger.getLogger(DatabaseSetupCore.class.getName());

    /**
     * Verify database connection
     */
    public static boolean verifyDatabaseConnection() {
        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            LOGGER.severe("Database connection verification failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Verify that required tables exist
     */
    public static boolean verifyTablesExist() {
        String[] requiredTables = {
            "return_exchanges",
            "return_exchange_items",
            "cash_drawers",
            "cash_drops",
            "cash_payouts"
        };

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            for (String table : requiredTables) {
                try (Statement stmt = conn.createStatement()) {
                    stmt.executeQuery("SELECT COUNT(*) FROM " + table);
                    LOGGER.info("Table verified: " + table);
                } catch (SQLException e) {
                    LOGGER.warning("Table missing or inaccessible: " + table);
                    return false;
                }
            }
            return true;
        } catch (SQLException e) {
            LOGGER.severe("Table verification failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Verify refund system schema
     */
    public static boolean verifyRefundSystemSchema() {
        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Verify core tables exist
            String[] coreTables = {
                "products", "customers", "suppliers", "categories",
                "transactions", "transaction_items"
            };

            for (String tableName : coreTables) {
                if (!tableExists(conn, tableName)) {
                    LOGGER.severe("Required core table missing: " + tableName);
                    return false;
                }
            }

            // Verify refund tracking columns exist
            if (!columnExists(conn, "transactions", "refunded_amount")) {
                LOGGER.warning("Refunded amount column missing - should be created by RefundTrackingSchemaUpdater");
                return false;
            }

            if (!columnExists(conn, "transactions", "amount_paid")) {
                LOGGER.warning("Amount paid column missing - should be created by PartialPaymentSchemaUpdater");
                return false;
            }

            LOGGER.info("Refund system schema verification completed successfully");
            return true;
        } catch (SQLException e) {
            LOGGER.severe("Refund system schema verification failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Quick setup without UI
     */
    public static boolean quickSetup() {
        try {
            System.out.println("Verifying database connection...");
            if (!verifyDatabaseConnection()) {
                System.err.println("Database connection failed!");
                return false;
            }

            System.out.println("Creating Return/Exchange tables...");
            ReturnExchangeSchemaUpdater.runAllUpdates();

            System.out.println("Creating Cash Drawer tables...");
            CashDrawerSchemaUpdater.runAllUpdates();

            System.out.println("Verifying tables...");
            if (!verifyTablesExist()) {
                System.err.println("Table verification failed!");
                return false;
            }

            System.out.println("Database setup completed successfully!");
            return true;

        } catch (Exception e) {
            System.err.println("Database setup failed: " + e.getMessage());
            LOGGER.severe("Database setup failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Perform complete database setup
     */
    public static boolean performCompleteSetup() {
        try {
            LOGGER.info("Starting complete database setup with refund functionality...");

            // Run all schema updaters
            System.out.println("Updating partial payment schema...");
            PartialPaymentSchemaUpdater.updateSchemaForPartialPayments();

            System.out.println("Updating refund tracking schema...");
            RefundTrackingSchemaUpdater.updateSchemaForRefundTracking();

            System.out.println("Creating return/exchange tables...");
            ReturnExchangeSchemaUpdater.runAllUpdates();

            System.out.println("Creating cash drawer tables...");
            CashDrawerSchemaUpdater.runAllUpdates();

            System.out.println("Creating installment payment tables...");
            InstallmentPaymentSchemaUpdater.runAllUpdates();

            System.out.println("Creating payment history tables...");
            PaymentHistorySchemaUpdater.runAllUpdates();

            System.out.println("Creating refund item tracking tables...");
            RefundItemTrackingSchemaUpdater.updateSchemaForRefundItemTracking();

            // Create performance indexes
            System.out.println("Creating performance indexes...");
            createPerformanceIndexes();

            // Verify the setup
            System.out.println("Verifying database setup...");
            if (!verifyRefundSystemSchema()) {
                LOGGER.severe("Schema verification failed after setup");
                return false;
            }

            LOGGER.info("Complete database setup finished successfully");
            return true;

        } catch (Exception e) {
            LOGGER.severe("Complete database setup failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Create performance indexes
     */
    private static void createPerformanceIndexes() throws SQLException {
        String[] indexes = {
            "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_transaction_items_product ON transaction_items(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction ON transaction_items(transaction_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
            "CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)",
            "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(first_name, last_name)"
        };

        try (Connection conn = DatabaseManager.getInstance().getConnection(); Statement stmt = conn.createStatement()) {

            for (String index : indexes) {
                stmt.execute(index);
            }
            LOGGER.info("Performance indexes created successfully");
        }
    }

    /**
     * Check if table exists
     */
    private static boolean tableExists(Connection conn, String tableName) throws SQLException {
        DatabaseMetaData meta = conn.getMetaData();
        try (ResultSet rs = meta.getTables(null, null, tableName.toUpperCase(), new String[]{"TABLE"})) {
            boolean exists = rs.next();
            if (!exists) {
                // Try lowercase
                try (ResultSet rs2 = meta.getTables(null, null, tableName.toLowerCase(), new String[]{"TABLE"})) {
                    exists = rs2.next();
                }
            }
            return exists;
        }
    }

    /**
     * Check if column exists in table
     */
    private static boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
        DatabaseMetaData meta = conn.getMetaData();
        try (ResultSet rs = meta.getColumns(null, null, tableName, columnName)) {
            return rs.next();
        }
    }

    /**
     * Main method for command line usage
     */
    public static void main(String[] args) {
        System.out.println("=== Clothing Store Database Setup ===");

        if (quickSetup()) {
            System.out.println("✓ Database setup completed successfully!");
            System.exit(0);
        } else {
            System.err.println("✗ Database setup failed!");
            System.exit(1);
        }
    }
}
