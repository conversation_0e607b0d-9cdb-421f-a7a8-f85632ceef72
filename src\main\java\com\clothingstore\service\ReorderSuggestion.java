package com.clothingstore.service;

import com.clothingstore.model.Product;
import com.clothingstore.model.Supplier;

import java.math.BigDecimal;

/**
 * Class for reorder suggestions
 */
public class ReorderSuggestion {

    private final Product product;
    private final Supplier supplier;
    private final int suggestedQuantity;
    private final BigDecimal estimatedCost;
    private final Priority priority;

    public ReorderSuggestion(Product product, Supplier supplier, int suggestedQuantity, BigDecimal estimatedCost) {
        this.product = product;
        this.supplier = supplier;
        this.suggestedQuantity = suggestedQuantity;
        this.estimatedCost = estimatedCost;
        this.priority = calculatePriority(product);
    }

    public Product getProduct() {
        return product;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    public int getSuggestedQuantity() {
        return suggestedQuantity;
    }

    public BigDecimal getEstimatedCost() {
        return estimatedCost;
    }

    public Priority getPriority() {
        return priority;
    }

    public String getDisplayText() {
        return String.format("%s - Qty: %d, Est. Cost: $%.2f (Supplier: %s)",
                product.getName(), suggestedQuantity, estimatedCost, supplier.getCompanyName());
    }

    private Priority calculatePriority(Product product) {
        int currentStock = product.getStockQuantity();
        int minLevel = product.getMinStockLevel();

        if (currentStock == 0) {
            return Priority.CRITICAL;
        } else if (currentStock <= minLevel / 2) {
            return Priority.HIGH;
        } else if (currentStock <= minLevel) {
            return Priority.MEDIUM;
        } else {
            return Priority.LOW;
        }
    }

    public enum Priority {
        CRITICAL(4),
        HIGH(3),
        MEDIUM(2),
        LOW(1);

        private final int value;

        Priority(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        // compareTo is inherited from Enum and cannot be overridden
    }

    @Override
    public String toString() {
        return String.format("ReorderSuggestion{product='%s', supplier='%s', quantity=%d, cost=%s, priority=%s}",
                product.getName(), supplier.getCompanyName(), suggestedQuantity, estimatedCost, priority);
    }
}
