package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.CashDrop;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * Data Access Object for Cash Drop operations
 */
public class CashDropDAO {
    private static final Logger LOGGER = Logger.getLogger(CashDropDAO.class.getName());
    private static CashDropDAO instance;

    private CashDropDAO() {
        // Private constructor for singleton
    }

    public static synchronized CashDropDAO getInstance() {
        if (instance == null) {
            instance = new CashDropDAO();
        }
        return instance;
    }

    /**
     * Create a new cash drop
     */
    public CashDrop create(CashDrop cashDrop) throws SQLException {
        String sql = "INSERT INTO cash_drops (cash_drawer_id, drawer_number, cashier_name, " +
                "amount, reason, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setLong(1, cashDrop.getCashDrawerId());
            pstmt.setString(2, cashDrop.getDrawerNumber());
            pstmt.setString(3, cashDrop.getCashierName());
            pstmt.setBigDecimal(4, cashDrop.getAmount());
            pstmt.setString(5, cashDrop.getReason());
            pstmt.setString(6, cashDrop.getNotes());
            pstmt.setString(7, cashDrop.getStatus());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating cash drop failed, no rows affected.");
            }

            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    cashDrop.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating cash drop failed, no ID obtained.");
                }
            }

            LOGGER.info("Created cash drop: " + cashDrop.getId());
            return cashDrop;
        }
    }

    /**
     * Find cash drop by ID
     */
    public CashDrop findById(Long id) throws SQLException {
        String sql = "SELECT * FROM cash_drops WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToCashDrop(rs);
                }
            }
        }
        return null;
    }

    /**
     * Find cash drops by drawer ID
     */
    public List<CashDrop> findByDrawerId(Long drawerId) throws SQLException {
        String sql = "SELECT * FROM cash_drops WHERE cash_drawer_id = ? ORDER BY drop_time DESC";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, drawerId);
            
            List<CashDrop> cashDrops = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    cashDrops.add(mapResultSetToCashDrop(rs));
                }
            }
            return cashDrops;
        }
    }

    /**
     * Find all cash drops with optional filters
     */
    public List<CashDrop> findAll(String status, LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT * FROM cash_drops WHERE 1=1");
        List<Object> params = new ArrayList<>();

        if (status != null && !status.equals("All")) {
            sql.append(" AND status = ?");
            params.add(status);
        }

        if (startDate != null) {
            sql.append(" AND drop_time >= ?");
            params.add(Timestamp.valueOf(startDate));
        }

        if (endDate != null) {
            sql.append(" AND drop_time <= ?");
            params.add(Timestamp.valueOf(endDate));
        }

        sql.append(" ORDER BY drop_time DESC");

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                pstmt.setObject(i + 1, params.get(i));
            }

            List<CashDrop> cashDrops = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    cashDrops.add(mapResultSetToCashDrop(rs));
                }
            }
            return cashDrops;
        }
    }

    /**
     * Update cash drop status
     */
    public void updateStatus(Long id, String status, String authorizedBy) throws SQLException {
        String sql = "UPDATE cash_drops SET status = ?, authorized_by = ?, " +
                "updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, status);
            pstmt.setString(2, authorizedBy);
            pstmt.setLong(3, id);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating cash drop status failed, no rows affected.");
            }

            LOGGER.info("Updated cash drop status: " + id + " to " + status);
        }
    }

    /**
     * Update cash drop notes
     */
    public void updateNotes(Long id, String notes) throws SQLException {
        String sql = "UPDATE cash_drops SET notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, notes);
            pstmt.setLong(2, id);
            pstmt.executeUpdate();

            LOGGER.info("Updated cash drop notes: " + id);
        }
    }

    /**
     * Delete cash drop
     */
    public void delete(Long id) throws SQLException {
        String sql = "DELETE FROM cash_drops WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows == 0) {
                throw new SQLException("Deleting cash drop failed, no rows affected.");
            }

            LOGGER.info("Deleted cash drop: " + id);
        }
    }

    /**
     * Get total cash drops for a drawer
     */
    public java.math.BigDecimal getTotalDropsForDrawer(Long drawerId) throws SQLException {
        String sql = "SELECT COALESCE(SUM(amount), 0) as total FROM cash_drops " +
                "WHERE cash_drawer_id = ? AND status = 'CONFIRMED'";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, drawerId);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getBigDecimal("total");
                }
            }
        }
        return java.math.BigDecimal.ZERO;
    }

    private CashDrop mapResultSetToCashDrop(ResultSet rs) throws SQLException {
        CashDrop cashDrop = new CashDrop();
        cashDrop.setId(rs.getLong("id"));
        cashDrop.setCashDrawerId(rs.getLong("cash_drawer_id"));
        cashDrop.setDrawerNumber(rs.getString("drawer_number"));
        cashDrop.setCashierName(rs.getString("cashier_name"));
        cashDrop.setAmount(rs.getBigDecimal("amount"));
        cashDrop.setReason(rs.getString("reason"));
        cashDrop.setNotes(rs.getString("notes"));
        cashDrop.setStatus(rs.getString("status"));
        cashDrop.setAuthorizedBy(rs.getString("authorized_by"));

        Timestamp dropTime = rs.getTimestamp("drop_time");
        if (dropTime != null) {
            cashDrop.setDropTime(dropTime.toLocalDateTime());
        }

        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            cashDrop.setCreatedAt(createdAt.toLocalDateTime());
        }

        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            cashDrop.setUpdatedAt(updatedAt.toLocalDateTime());
        }

        return cashDrop;
    }
}
