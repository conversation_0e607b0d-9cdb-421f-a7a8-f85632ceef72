package com.clothingstore.service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.InventoryAdjustmentResult;
import com.clothingstore.model.Product;
import com.clothingstore.model.RefundItem;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

/**
 * Service class for handling transaction business logic
 */
public class TransactionService {

    private static TransactionService instance;
    private final TransactionDAO transactionDAO;
    private final ProductDAO productDAO;
    private final CustomerDAO customerDAO;
    private final RefundInventoryService refundInventoryService;
    // private final WhatsAppService whatsAppService; // Commented out - WhatsApp integration disabled

    private TransactionService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.productDAO = ProductDAO.getInstance();
        this.customerDAO = CustomerDAO.getInstance();
        this.refundInventoryService = RefundInventoryService.getInstance();
        // this.whatsAppService = WhatsAppService.getInstance(); // Commented out - WhatsApp integration disabled
    }

    public static synchronized TransactionService getInstance() {
        if (instance == null) {
            instance = new TransactionService();
        }
        return instance;
    }

    /**
     * Process a complete transaction including inventory updates and customer
     * loyalty points
     */
    public Transaction processTransaction(Transaction transaction) throws SQLException, InsufficientStockException {
        // Validate stock availability
        validateStockAvailability(transaction);

        // Generate transaction number if not set
        if (transaction.getTransactionNumber() == null) {
            transaction.setTransactionNumber(transactionDAO.generateTransactionNumber());
        }

        // Set transaction date if not set
        if (transaction.getTransactionDate() == null) {
            transaction.setTransactionDate(LocalDateTime.now());
        }

        // Recalculate amounts
        transaction.recalculateAmounts();

        // Save transaction
        Transaction savedTransaction = transactionDAO.save(transaction);

        // Update product stock
        updateProductStock(savedTransaction);

        // Update customer purchase history and loyalty points
        if (savedTransaction.getCustomerId() != null) {
            updateCustomerPurchaseHistory(savedTransaction);
        }

        return savedTransaction;
    }

    /**
     * Complete a transaction and optionally send WhatsApp receipt
     */
    public Transaction completeTransaction(Transaction transaction, boolean sendWhatsAppReceipt) throws SQLException, InsufficientStockException {
        // Process the transaction normally
        Transaction completedTransaction = processTransaction(transaction);

        // Mark as completed
        completedTransaction.setStatus("COMPLETED");
        completedTransaction = transactionDAO.save(completedTransaction);

        // Send WhatsApp receipt if enabled and requested
        if (false) { // WhatsApp integration disabled
            sendWhatsAppReceiptAsync(completedTransaction, null);
        }

        return completedTransaction;
    }

    /**
     * Complete a transaction with WhatsApp receipt delivery and phone number
     * collection
     */
    public Transaction completeTransactionWithWhatsApp(Transaction transaction, String customerPhoneNumber) throws SQLException, InsufficientStockException {
        // Process the transaction normally
        Transaction completedTransaction = processTransaction(transaction);

        // Mark as completed
        completedTransaction.setStatus("COMPLETED");
        completedTransaction = transactionDAO.save(completedTransaction);

        // Send WhatsApp receipt if enabled
        if (false) { // WhatsApp integration disabled
            sendWhatsAppReceiptAsync(completedTransaction, customerPhoneNumber);
        }

        return completedTransaction;
    }

    /**
     * Send WhatsApp receipt asynchronously with enhanced phone number handling
     */
    private void sendWhatsAppReceiptAsync(Transaction transaction, String providedPhoneNumber) {
        try {
            Customer customer = null;
            String phoneNumber = providedPhoneNumber;

            // Get customer information
            if (transaction.getCustomerId() != null) {
                Optional<Customer> customerOpt = customerDAO.findById(transaction.getCustomerId());
                if (customerOpt.isPresent()) {
                    customer = customerOpt.get();
                    // Use customer's phone if no phone number provided
                    if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
                        // phoneNumber = whatsAppService.getPhoneNumberForCustomer(customer); // WhatsApp disabled
                    }
                }
            }

            // Validate phone number format
            if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
                // Format phone number for WhatsApp
                String formattedPhone = formatPhoneNumberForWhatsApp(phoneNumber);
                if (formattedPhone != null) {
                    // Check if we can send to this customer
                    if (false) { // WhatsApp disabled
                        System.out.println("INFO: WhatsApp service disabled for transaction: " + transaction.getTransactionNumber());
                    } else {
                        System.out.println("INFO: Customer opted out of WhatsApp receipts for transaction: "
                                + transaction.getTransactionNumber());
                    }
                } else {
                    System.out.println("WARNING: Invalid phone number format for WhatsApp: " + phoneNumber);
                }
            } else {
                System.out.println("INFO: No phone number available for WhatsApp receipt for transaction: "
                        + transaction.getTransactionNumber());
            }
        } catch (Exception e) {
            System.err.println("ERROR: Error preparing WhatsApp receipt: " + e.getMessage());
        }
    }

    /**
     * Format phone number for WhatsApp (international format)
     */
    private String formatPhoneNumberForWhatsApp(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return null;
        }

        // If already has whatsapp: prefix, return as is
        if (phoneNumber.startsWith("whatsapp:")) {
            return phoneNumber;
        }

        // Remove all non-digit characters except +
        String cleaned = phoneNumber.replaceAll("[^\\d+]", "");

        // Add country code if missing (assume US)
        if (!cleaned.startsWith("+")) {
            cleaned = "+1" + cleaned;
        }

        // Validate E.164 format
        if (!cleaned.matches("^\\+[1-9]\\d{1,14}$")) {
            return null;
        }

        return cleaned; // Return without whatsapp: prefix for display purposes
    }

    /**
     * Processes a refund by creating a new refund transaction and updating
     * inventory.
     *
     * @param refundTransaction The new transaction containing returned items
     * (with negative quantities).
     * @param originalTransaction The original sale being refunded.
     * @return The saved refund transaction.
     */
    public Transaction processRefund(Transaction refundTransaction, Transaction originalTransaction) throws SQLException, InsufficientStockException {
        // 1. Finalize and save the new refund transaction
        refundTransaction.setTransactionNumber(transactionDAO.generateTransactionNumber());
        refundTransaction.setStatus("REFUND"); // This is a new transaction of type REFUND
        refundTransaction.setNotes("Refund for transaction " + originalTransaction.getTransactionNumber());
        refundTransaction.setTransactionDate(LocalDateTime.now());
        refundTransaction.recalculateAmounts(); //Amounts are negative

        Transaction savedRefund = transactionDAO.save(refundTransaction);

        // 2. Restore product stock. The quantities on the items are negative,
        // so the existing updateProductStock method will correctly add them back.
        updateProductStock(savedRefund);

        // 3. Update the original transaction's status
        boolean allItemsRefunded = checkIfAllItemsRefunded(originalTransaction, savedRefund);
        if (allItemsRefunded) {
            originalTransaction.setStatus("REFUNDED");
        } else {
            originalTransaction.setStatus("PARTIALLY_REFUNDED");
        }
        transactionDAO.updateStatus(originalTransaction.getId(), originalTransaction.getStatus());

        // 4. Optionally, reverse customer's purchase history (simplified for now)
        if (originalTransaction.getCustomerId() != null) {
            reverseCustomerPurchaseHistory(originalTransaction, savedRefund.getTotalAmount().negate());
        }

        return savedRefund;
    }

    private boolean checkIfAllItemsRefunded(Transaction originalTransaction, Transaction refundTransaction) throws SQLException {
        List<TransactionItem> originalItems = transactionDAO.findItemsByTransactionId(originalTransaction.getId());
        List<Transaction> allRefunds = transactionDAO.findRefundsForTransaction(originalTransaction.getTransactionNumber());

        for (TransactionItem originalItem : originalItems) {
            int totalRefunded = 0;
            for (Transaction refund : allRefunds) {
                for (TransactionItem refundItem : refund.getItems()) {
                    if (refundItem.getProductId().equals(originalItem.getProductId())) {
                        totalRefunded += -refundItem.getQuantity(); // quantity is negative
                    }
                }
            }
            if (totalRefunded < originalItem.getQuantity()) {
                return false;
            }
        }
        return true;
    }

    /**
     * Process a full refund for a transaction
     */
    public Transaction processFullRefund(Long transactionId, String reason) throws SQLException, InvalidRefundException {
        Optional<Transaction> optionalTransaction = transactionDAO.findById(transactionId);
        if (optionalTransaction.isEmpty()) {
            throw new InvalidRefundException("Transaction not found");
        }

        Transaction transaction = optionalTransaction.get();
        if (!transaction.canBeRefunded()) {
            throw new InvalidRefundException("Transaction cannot be refunded. Current status: " + transaction.getStatus());
        }

        // Restore product stock for all items
        restoreProductStock(transaction);

        // Update customer purchase history (subtract amounts)
        if (transaction.getCustomerId() != null) {
            reverseCustomerPurchaseHistory(transaction, transaction.getTotalAmount());
        }

        // Mark transaction as refunded
        transaction.processRefund();
        transaction.setNotes((transaction.getNotes() != null ? transaction.getNotes() + "; " : "")
                + "Refunded: " + reason);

        return transactionDAO.save(transaction);
    }

    /**
     * Process a partial refund for specific items
     */
    public Transaction processPartialRefund(Long transactionId, List<TransactionItem> refundItems, String reason)
            throws SQLException, InvalidRefundException {
        Optional<Transaction> optionalTransaction = transactionDAO.findById(transactionId);
        if (optionalTransaction.isEmpty()) {
            throw new InvalidRefundException("Transaction not found");
        }

        Transaction transaction = optionalTransaction.get();
        if (!transaction.canBeRefunded() && !"PARTIALLY_REFUNDED".equals(transaction.getStatus())) {
            throw new InvalidRefundException("Transaction cannot be refunded");
        }

        BigDecimal refundAmount = BigDecimal.ZERO;

        // Process each refund item
        for (TransactionItem refundItem : refundItems) {
            // Find the original item
            TransactionItem originalItem = transaction.getItems().stream()
                    .filter(item -> item.getProductId().equals(refundItem.getProductId()))
                    .findFirst()
                    .orElseThrow(() -> new InvalidRefundException("Item not found in transaction"));

            // Validate refund quantity
            if (refundItem.getQuantity() > originalItem.getQuantity()) {
                throw new InvalidRefundException("Refund quantity exceeds original quantity");
            }

            // Restore stock for refunded items
            Optional<Product> product = productDAO.findById(refundItem.getProductId());
            if (product.isPresent()) {
                Product p = product.get();
                p.addStock(refundItem.getQuantity());
                productDAO.save(p);
            }

            // Calculate refund amount
            BigDecimal itemRefundAmount = refundItem.getUnitPrice()
                    .multiply(BigDecimal.valueOf(refundItem.getQuantity()));
            refundAmount = refundAmount.add(itemRefundAmount);
        }

        // Update transaction status
        transaction.processPartialRefund(refundAmount);
        transaction.setNotes((transaction.getNotes() != null ? transaction.getNotes() + "; " : "")
                + "Partial refund: " + reason + " (Amount: $" + refundAmount + ")");

        return transactionDAO.save(transaction);
    }

    /**
     * Process a partial refund using RefundItem list
     */
    public Transaction processPartialRefundWithItems(Long transactionId, List<RefundItem> refundItems, String reason)
            throws SQLException, InvalidRefundException {
        Optional<Transaction> optionalTransaction = transactionDAO.findById(transactionId);
        if (optionalTransaction.isEmpty()) {
            throw new InvalidRefundException("Transaction not found");
        }

        Transaction transaction = optionalTransaction.get();
        if (!transaction.canBeRefunded() && !"PARTIALLY_REFUNDED".equals(transaction.getStatus())) {
            throw new InvalidRefundException("Transaction cannot be refunded. Current status: " + transaction.getStatus());
        }

        BigDecimal totalRefundAmount = BigDecimal.ZERO;

        // Process each refund item
        for (RefundItem refundItem : refundItems) {
            if (!refundItem.isSelected() || refundItem.getRefundQuantity() <= 0) {
                continue;
            }

            // Validate refund quantity
            if (refundItem.getRefundQuantity() > refundItem.getOriginalQuantity()) {
                throw new InvalidRefundException("Cannot refund more than original quantity for item: "
                        + refundItem.getProductName());
            }

            // Restore product stock
            Long productId = refundItem.getOriginalItem().getProductId();
            Optional<Product> productOpt = productDAO.findById(productId);
            if (productOpt.isPresent()) {
                Product product = productOpt.get();
                int newStock = product.getStockQuantity() + refundItem.getRefundQuantity();
                productDAO.updateStock(productId, newStock);
            }

            totalRefundAmount = totalRefundAmount.add(refundItem.getRefundAmount());
        }

        // Update customer purchase history if applicable
        if (transaction.getCustomerId() != null && totalRefundAmount.compareTo(BigDecimal.ZERO) > 0) {
            reverseCustomerPurchaseHistoryAmount(transaction.getCustomerId(), totalRefundAmount);
        }

        // Update transaction status
        transaction.processPartialRefund(totalRefundAmount);
        transaction.setNotes((transaction.getNotes() != null ? transaction.getNotes() + "; " : "")
                + "Partial refund: " + reason + " (Amount: $" + totalRefundAmount + ")");

        return transactionDAO.save(transaction);
    }

    /**
     * Validate if a transaction can be refunded
     */
    public boolean canRefundTransaction(Long transactionId) throws SQLException {
        Optional<Transaction> optionalTransaction = transactionDAO.findById(transactionId);
        return optionalTransaction.isPresent() && optionalTransaction.get().canBeRefunded();
    }

    /**
     * Get refundable items for a transaction
     */
    public List<RefundItem> getRefundableItems(Long transactionId) throws SQLException, InvalidRefundException {
        Optional<Transaction> optionalTransaction = transactionDAO.findById(transactionId);
        if (optionalTransaction.isEmpty()) {
            throw new InvalidRefundException("Transaction not found");
        }

        Transaction transaction = optionalTransaction.get();
        if (!transaction.canBeRefunded() && !"PARTIALLY_REFUNDED".equals(transaction.getStatus())) {
            throw new InvalidRefundException("Transaction cannot be refunded. Current status: " + transaction.getStatus());
        }

        return transaction.getItems().stream()
                .map(RefundItem::new)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Get transaction history for a customer
     */
    public List<Transaction> getCustomerTransactionHistory(Long customerId) throws SQLException {
        return transactionDAO.findByCustomerId(customerId);
    }

    /**
     * Reverse customer purchase history for a specific amount
     */
    private void reverseCustomerPurchaseHistoryAmount(Long customerId, BigDecimal amount) throws SQLException {
        Optional<Customer> customerOpt = customerDAO.findById(customerId);
        if (customerOpt.isPresent()) {
            Customer customer = customerOpt.get();

            // Subtract the refund amount from total spent
            double newTotalSpent = Math.max(0, customer.getTotalSpent() - amount.doubleValue());
            customer.setTotalSpent(newTotalSpent);

            // Subtract loyalty points (1 point per dollar)
            int pointsToSubtract = amount.intValue();
            int newLoyaltyPoints = Math.max(0, customer.getLoyaltyPoints() - pointsToSubtract);
            customer.setLoyaltyPoints(newLoyaltyPoints);

            // Decrease total purchases count by 1 (minimum 0)
            int newTotalPurchases = Math.max(0, customer.getTotalPurchases() - 1);
            customer.setTotalPurchases(newTotalPurchases);

            customerDAO.save(customer);
        }
    }

    /**
     * Get transactions within a date range
     */
    public List<Transaction> getTransactionsByDateRange(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        return transactionDAO.findByDateRange(startDate, endDate);
    }

    /**
     * Get daily sales summary
     */
    public SalesSummary getDailySales(LocalDateTime date) throws SQLException {
        LocalDateTime startOfDay = date.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = date.toLocalDate().atTime(23, 59, 59);

        List<Transaction> transactions = transactionDAO.findByDateRange(startOfDay, endOfDay);
        return calculateSalesSummary(transactions);
    }

    /**
     * Calculate sales summary from transactions
     */
    private SalesSummary calculateSalesSummary(List<Transaction> transactions) {
        SalesSummary summary = new SalesSummary();

        for (Transaction transaction : transactions) {
            if ("COMPLETED".equals(transaction.getStatus())) {
                summary.addTransaction(transaction);
            }
        }

        return summary;
    }

    private void validateStockAvailability(Transaction transaction) throws InsufficientStockException, SQLException {
        for (TransactionItem item : transaction.getItems()) {
            Optional<Product> product = productDAO.findById(item.getProductId());
            if (product.isEmpty()) {
                throw new InsufficientStockException("Product not found: " + item.getProductId());
            }

            Product p = product.get();
            if (p.getStockQuantity() < item.getQuantity()) {
                throw new InsufficientStockException(
                        "Insufficient stock for " + p.getName()
                        + ". Available: " + p.getStockQuantity()
                        + ", Required: " + item.getQuantity());
            }
        }
    }

    private void updateProductStock(Transaction transaction) throws SQLException {
        for (TransactionItem item : transaction.getItems()) {
            // Get current stock from database and subtract sold quantity
            Optional<Product> productOpt = productDAO.findById(item.getProductId());
            if (productOpt.isPresent()) {
                Product product = productOpt.get();
                int newStock = product.getStockQuantity() - item.getQuantity();
                productDAO.updateStock(item.getProductId(), newStock);
            }
        }
    }

    private void restoreProductStock(Transaction transaction) throws SQLException {
        for (TransactionItem item : transaction.getItems()) {
            // Get current stock from database and add returned quantity
            Optional<Product> productOpt = productDAO.findById(item.getProductId());
            if (productOpt.isPresent()) {
                Product product = productOpt.get();
                int newStock = product.getStockQuantity() + item.getQuantity();
                productDAO.updateStock(item.getProductId(), newStock);
            }
        }
    }

    private void updateCustomerPurchaseHistory(Transaction transaction) throws SQLException {
        if (transaction.getCustomerId() == null) {
            return;
        }
        customerDAO.updateCustomerStats(transaction.getCustomerId(), transaction.getTotalAmount(), 1);
    }

    private void reverseCustomerPurchaseHistory(Transaction transaction, BigDecimal refundAmount) throws SQLException {
        if (transaction.getCustomerId() == null) {
            return;
        }
        customerDAO.updateCustomerStats(transaction.getCustomerId(), refundAmount.negate(), -1);
    }

    public Transaction getTransactionById(String transactionId) throws SQLException {
        return transactionDAO.findByTransactionNumber(transactionId)
                .orElse(null);
    }

    /**
     * Custom exception for insufficient stock
     */
    public static class InsufficientStockException extends Exception {

        public InsufficientStockException(String message) {
            super(message);
        }
    }

    /**
     * Custom exception for invalid refunds
     */
    public static class InvalidRefundException extends Exception {

        public InvalidRefundException(String message) {
            super(message);
        }
    }

    /**
     * Sales summary data class
     */
    public static class SalesSummary {

        private int transactionCount = 0;
        private int itemCount = 0;
        private BigDecimal totalSales = BigDecimal.ZERO;
        private BigDecimal totalTax = BigDecimal.ZERO;
        private BigDecimal totalDiscount = BigDecimal.ZERO;

        public void addTransaction(Transaction transaction) {
            transactionCount++;
            itemCount += transaction.getTotalItemCount();
            totalSales = totalSales.add(transaction.getTotalAmount());
            totalTax = totalTax.add(transaction.getTaxAmount());
            totalDiscount = totalDiscount.add(transaction.getDiscountAmount());
        }

        // Getters
        public int getTransactionCount() {
            return transactionCount;
        }

        public int getItemCount() {
            return itemCount;
        }

        public BigDecimal getTotalSales() {
            return totalSales;
        }

        public BigDecimal getTotalTax() {
            return totalTax;
        }

        public BigDecimal getTotalDiscount() {
            return totalDiscount;
        }

        public BigDecimal getAverageTransactionValue() {
            return transactionCount > 0 ? totalSales.divide(BigDecimal.valueOf(transactionCount), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
        }
    }
}
