package com.clothingstore.dao;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.RefundItemTracking;

/**
 * Data Access Object for RefundItemTracking operations
 */
public class RefundItemTrackingDAO {

    private static final Logger LOGGER = Logger.getLogger(RefundItemTrackingDAO.class.getName());

    /**
     * Save a new refund item tracking record
     */
    public RefundItemTracking save(RefundItemTracking refundTracking) throws SQLException {
        String sql = "INSERT INTO refund_item_tracking ("
                + "original_transaction_id, original_transaction_item_id, refund_transaction_id, "
                + "product_id, product_name, product_sku, original_quantity, refunded_quantity, "
                + "unit_price, refund_amount, refund_reason, cashier_name, refund_date) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            stmt.setLong(1, refundTracking.getOriginalTransactionId());
            stmt.setLong(2, refundTracking.getOriginalTransactionItemId());
            stmt.setLong(3, refundTracking.getRefundTransactionId());
            stmt.setLong(4, refundTracking.getProductId());
            stmt.setString(5, refundTracking.getProductName());
            stmt.setString(6, refundTracking.getProductSku());
            stmt.setInt(7, refundTracking.getOriginalQuantity());
            stmt.setInt(8, refundTracking.getRefundedQuantity());
            stmt.setBigDecimal(9, refundTracking.getUnitPrice());
            stmt.setBigDecimal(10, refundTracking.getRefundAmount());
            stmt.setString(11, refundTracking.getRefundReason());
            stmt.setString(12, refundTracking.getCashierName());
            stmt.setTimestamp(13, Timestamp.valueOf(refundTracking.getRefundDate()));

            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating refund item tracking failed, no rows affected.");
            }

            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    refundTracking.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating refund item tracking failed, no ID obtained.");
                }
            }

            return refundTracking;
        }
    }

    /**
     * Get all refund tracking records for a specific transaction
     */
    public List<RefundItemTracking> findByTransactionId(Long transactionId) throws SQLException {
        String sql = "SELECT * FROM refund_item_tracking WHERE original_transaction_id = ? ORDER BY refund_date DESC";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionId);

            try (ResultSet rs = stmt.executeQuery()) {
                List<RefundItemTracking> records = new ArrayList<>();
                while (rs.next()) {
                    records.add(mapResultSetToRefundTracking(rs));
                }
                return records;
            }
        }
    }

    /**
     * Get all refund tracking records for a specific transaction item
     */
    public List<RefundItemTracking> findByTransactionItemId(Long transactionItemId) throws SQLException {
        String sql = "SELECT * FROM refund_item_tracking WHERE original_transaction_item_id = ? ORDER BY refund_date DESC";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionItemId);

            try (ResultSet rs = stmt.executeQuery()) {
                List<RefundItemTracking> records = new ArrayList<>();
                while (rs.next()) {
                    records.add(mapResultSetToRefundTracking(rs));
                }
                return records;
            }
        }
    }

    /**
     * Get total refunded quantity for a specific transaction item
     */
    public int getTotalRefundedQuantity(Long transactionItemId) throws SQLException {
        String sql = "SELECT COALESCE(SUM(refunded_quantity), 0) as total_refunded "
                + "FROM refund_item_tracking WHERE original_transaction_item_id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionItemId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total_refunded");
                }
                return 0;
            }
        }
    }

    /**
     * Get total refunded amount for a specific transaction item
     */
    public BigDecimal getTotalRefundedAmount(Long transactionItemId) throws SQLException {
        String sql = "SELECT COALESCE(SUM(refund_amount), 0) as total_refunded_amount "
                + "FROM refund_item_tracking WHERE original_transaction_item_id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionItemId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getBigDecimal("total_refunded_amount");
                }
                return BigDecimal.ZERO;
            }
        }
    }

    /**
     * Update transaction item refund tracking columns
     */
    public void updateTransactionItemRefundTracking(Long transactionItemId) throws SQLException {
        String sql = "UPDATE transaction_items SET "
                + "total_refunded_quantity = (SELECT COALESCE(SUM(refunded_quantity), 0) "
                + "                          FROM refund_item_tracking WHERE original_transaction_item_id = ?), "
                + "remaining_quantity = quantity - (SELECT COALESCE(SUM(refunded_quantity), 0) "
                + "                                FROM refund_item_tracking WHERE original_transaction_item_id = ?) "
                + "WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionItemId);
            stmt.setLong(2, transactionItemId);
            stmt.setLong(3, transactionItemId);

            stmt.executeUpdate();
        }
    }

    /**
     * Get refund tracking records by product ID
     */
    public List<RefundItemTracking> findByProductId(Long productId) throws SQLException {
        String sql = "SELECT * FROM refund_item_tracking WHERE product_id = ? ORDER BY refund_date DESC";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, productId);

            try (ResultSet rs = stmt.executeQuery()) {
                List<RefundItemTracking> records = new ArrayList<>();
                while (rs.next()) {
                    records.add(mapResultSetToRefundTracking(rs));
                }
                return records;
            }
        }
    }

    /**
     * Get refund tracking records by transaction ID and product ID Used when
     * transaction item IDs become stale due to transaction updates
     */
    public List<RefundItemTracking> findByTransactionAndProduct(Long transactionId, Long productId) throws SQLException {
        String sql = "SELECT * FROM refund_item_tracking WHERE original_transaction_id = ? AND product_id = ? ORDER BY refund_date DESC";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionId);
            stmt.setLong(2, productId);

            try (ResultSet rs = stmt.executeQuery()) {
                List<RefundItemTracking> records = new ArrayList<>();
                while (rs.next()) {
                    records.add(mapResultSetToRefundTracking(rs));
                }
                return records;
            }
        }
    }

    /**
     * Delete refund tracking records for a transaction (for cleanup)
     */
    public void deleteByTransactionId(Long transactionId) throws SQLException {
        String sql = "DELETE FROM refund_item_tracking WHERE original_transaction_id = ? OR refund_transaction_id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionId);
            stmt.setLong(2, transactionId);

            stmt.executeUpdate();
        }
    }

    /**
     * Map ResultSet to RefundItemTracking object
     */
    private RefundItemTracking mapResultSetToRefundTracking(ResultSet rs) throws SQLException {
        RefundItemTracking tracking = new RefundItemTracking();

        tracking.setId(rs.getLong("id"));
        tracking.setOriginalTransactionId(rs.getLong("original_transaction_id"));
        tracking.setOriginalTransactionItemId(rs.getLong("original_transaction_item_id"));
        tracking.setRefundTransactionId(rs.getLong("refund_transaction_id"));
        tracking.setProductId(rs.getLong("product_id"));
        tracking.setProductName(rs.getString("product_name"));
        tracking.setProductSku(rs.getString("product_sku"));
        tracking.setOriginalQuantity(rs.getInt("original_quantity"));
        tracking.setRefundedQuantity(rs.getInt("refunded_quantity"));
        tracking.setUnitPrice(rs.getBigDecimal("unit_price"));
        tracking.setRefundAmount(rs.getBigDecimal("refund_amount"));
        tracking.setRefundReason(rs.getString("refund_reason"));
        tracking.setCashierName(rs.getString("cashier_name"));

        Timestamp refundDate = rs.getTimestamp("refund_date");
        if (refundDate != null) {
            tracking.setRefundDate(refundDate.toLocalDateTime());
        }

        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            tracking.setCreatedAt(createdAt.toLocalDateTime());
        }

        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            tracking.setUpdatedAt(updatedAt.toLocalDateTime());
        }

        return tracking;
    }
}
