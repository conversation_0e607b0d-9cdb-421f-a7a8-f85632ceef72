
import java.sql.SQLException;
import java.util.List;

import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Transaction;

public class TestOutstandingBalances {

    public static void main(String[] args) {
        try {
            // Initialize database
            DatabaseManager.getInstance();

            // Get TransactionDAO instance
            TransactionDAO transactionDAO = TransactionDAO.getInstance();

            // Test finding transactions with outstanding balances
            System.out.println("=== Testing Outstanding Balances Functionality ===");

            List<Transaction> outstandingTransactions = transactionDAO.findWithOutstandingBalances();
            System.out.println("Found " + outstandingTransactions.size() + " transactions with outstanding balances:");

            for (Transaction transaction : outstandingTransactions) {
                System.out.println("Transaction #" + transaction.getTransactionNumber()
                        + " - Total: $" + transaction.getTotalAmount()
                        + " - Paid: $" + (transaction.getAmountPaid() != null ? transaction.getAmountPaid() : "0.00")
                        + " - Remaining: $" + transaction.getRemainingBalance()
                        + " - Status: " + transaction.getStatus());
            }

            // Test installment transactions
            System.out.println("\n=== Testing Installment Transactions ===");
            List<Transaction> installmentTransactions = transactionDAO.findInstallmentTransactions();
            System.out.println("Found " + installmentTransactions.size() + " installment transactions:");

            for (Transaction transaction : installmentTransactions) {
                System.out.println("Transaction #" + transaction.getTransactionNumber()
                        + " - Total: $" + transaction.getTotalAmount()
                        + " - Paid: $" + (transaction.getAmountPaid() != null ? transaction.getAmountPaid() : "0.00")
                        + " - Remaining: $" + transaction.getRemainingBalance()
                        + " - Installment Status: " + transaction.getInstallmentStatus());
            }

        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
