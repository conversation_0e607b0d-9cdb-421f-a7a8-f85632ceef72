package com.clothingstore.util;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * Utility class for formatting data for display
 */
public class FormatUtil {

    private static final NumberFormat CURRENCY_FORMAT = NumberFormat.getCurrencyInstance(Locale.US);
    private static final NumberFormat PERCENT_FORMAT = NumberFormat.getPercentInstance(Locale.US);
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("MM/dd/yyyy");
    private static final DateTimeFormatter DATETIME_FORMAT = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");
    private static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * Formats a BigDecimal as currency
     */
    public static String formatCurrency(BigDecimal amount) {
        if (amount == null) {
            return "$0.00";
        }
        return CURRENCY_FORMAT.format(amount);
    }

    /**
     * Formats a double as currency
     */
    public static String formatCurrency(double amount) {
        return CURRENCY_FORMAT.format(amount);
    }

    /**
     * Formats a percentage (0.05 -> 5%)
     */
    public static String formatPercentage(double percentage) {
        PERCENT_FORMAT.setMaximumFractionDigits(1);
        return PERCENT_FORMAT.format(percentage);
    }

    /**
     * Formats a LocalDate
     */
    public static String formatDate(LocalDate date) {
        if (date == null) {
            return "";
        }
        return date.format(DATE_FORMAT);
    }

    /**
     * Formats a LocalDateTime
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DATETIME_FORMAT);
    }

    /**
     * Formats time only from LocalDateTime
     */
    public static String formatTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(TIME_FORMAT);
    }

    /**
     * Formats a phone number for display - updated for strict 11-digit format
     */
    public static String formatPhone(String phone) {
        if (phone == null || phone.isEmpty()) {
            return "";
        }

        // Remove all non-digits
        String digits = phone.replaceAll("\\D", "");

        // Format based on length - now enforcing 11-digit format
        if (digits.length() == 11) {
            // Format as +1 (XXX) XXX-XXXX for 11-digit numbers
            return String.format("+%s (%s) %s-%s",
                    digits.substring(0, 1), // Country code (1)
                    digits.substring(1, 4), // Area code
                    digits.substring(4, 7), // Exchange
                    digits.substring(7));      // Number
        } else if (digits.length() == 10) {
            // Legacy support for 10-digit numbers (add country code)
            return String.format("+1 (%s) %s-%s",
                    digits.substring(0, 3),
                    digits.substring(3, 6),
                    digits.substring(6));
        } else {
            // Return original if not 10 or 11 digits
            return phone;
        }
    }

    /**
     * Formats phone number specifically for 11-digit format Returns formatted
     * string or error message for invalid format
     */
    public static String formatPhoneStrict(String phone) {
        if (phone == null || phone.isEmpty()) {
            return "";
        }

        // Use ValidationUtil to check if phone is valid
        if (!com.clothingstore.util.ValidationUtil.isValidPhone(phone)) {
            return "Invalid phone format (requires 11 digits)";
        }

        // Remove all non-digits
        String digits = phone.replaceAll("\\D", "");

        // Format as +1 (XXX) XXX-XXXX
        return String.format("+%s (%s) %s-%s",
                digits.substring(0, 1), // Country code (1)
                digits.substring(1, 4), // Area code
                digits.substring(4, 7), // Exchange
                digits.substring(7));      // Number
    }

    /**
     * Formats a name for display (proper case)
     */
    public static String formatName(String name) {
        if (name == null || name.isEmpty()) {
            return "";
        }

        String[] words = name.toLowerCase().split("\\s+");
        StringBuilder formatted = new StringBuilder();

        for (int i = 0; i < words.length; i++) {
            if (i > 0) {
                formatted.append(" ");
            }
            if (words[i].length() > 0) {
                formatted.append(Character.toUpperCase(words[i].charAt(0)));
                if (words[i].length() > 1) {
                    formatted.append(words[i].substring(1));
                }
            }
        }

        return formatted.toString();
    }

    /**
     * Formats a SKU for display (uppercase)
     */
    public static String formatSku(String sku) {
        if (sku == null) {
            return "";
        }
        return sku.toUpperCase();
    }

    /**
     * Formats membership level for display
     */
    public static String formatMembershipLevel(String level) {
        if (level == null || level.isEmpty()) {
            return "BRONZE";
        }
        return level.toUpperCase();
    }

    /**
     * Formats transaction status for display
     */
    public static String formatTransactionStatus(String status) {
        if (status == null || status.isEmpty()) {
            return "UNKNOWN";
        }

        switch (status.toUpperCase()) {
            case "COMPLETED":
                return "Completed";
            case "REFUNDED":
                return "Refunded";
            case "PARTIALLY_REFUNDED":
                return "Partially Refunded";
            case "CANCELLED":
                return "Cancelled";
            case "PENDING":
                return "Pending";
            default:
                return status;
        }
    }

    /**
     * Formats payment method for display
     */
    public static String formatPaymentMethod(String method) {
        if (method == null || method.isEmpty()) {
            return "CASH";
        }

        switch (method.toUpperCase()) {
            case "CASH":
                return "Cash";
            case "CREDIT_CARD":
                return "Credit Card";
            case "DEBIT_CARD":
                return "Debit Card";
            case "GIFT_CARD":
                return "Gift Card";
            case "CHECK":
                return "Check";
            default:
                return method;
        }
    }

    /**
     * Formats a number with thousands separators
     */
    public static String formatNumber(int number) {
        return NumberFormat.getNumberInstance(Locale.US).format(number);
    }

    /**
     * Formats a decimal number with specified decimal places
     */
    public static String formatDecimal(BigDecimal number, int decimalPlaces) {
        if (number == null) {
            return "0";
        }

        NumberFormat format = NumberFormat.getNumberInstance(Locale.US);
        format.setMaximumFractionDigits(decimalPlaces);
        format.setMinimumFractionDigits(decimalPlaces);
        return format.format(number);
    }

    /**
     * Truncates text to specified length with ellipsis
     */
    public static String truncateText(String text, int maxLength) {
        if (text == null) {
            return "";
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength - 3) + "...";
    }

    /**
     * Formats file size in human readable format
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        }
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }

    /**
     * Formats address for display
     */
    public static String formatAddress(String address, String city, String state, String zipCode) {
        StringBuilder formatted = new StringBuilder();

        if (address != null && !address.trim().isEmpty()) {
            formatted.append(address.trim());
        }

        if (city != null && !city.trim().isEmpty()) {
            if (formatted.length() > 0) {
                formatted.append(", ");
            }
            formatted.append(city.trim());
        }

        if (state != null && !state.trim().isEmpty()) {
            if (formatted.length() > 0) {
                formatted.append(", ");
            }
            formatted.append(state.trim());
        }

        if (zipCode != null && !zipCode.trim().isEmpty()) {
            if (formatted.length() > 0) {
                formatted.append(" ");
            }
            formatted.append(zipCode.trim());
        }

        return formatted.toString();
    }

    /**
     * Formats stock status for display
     */
    public static String formatStockStatus(int currentStock, int minStock) {
        if (currentStock <= 0) {
            return "Out of Stock";
        } else if (currentStock <= minStock) {
            return "Low Stock";
        } else {
            return "In Stock";
        }
    }

    /**
     * Gets stock status color class for CSS styling
     */
    public static String getStockStatusClass(int currentStock, int minStock) {
        if (currentStock <= 0) {
            return "stock-out";
        } else if (currentStock <= minStock) {
            return "stock-low";
        } else {
            return "stock-ok";
        }
    }
}
