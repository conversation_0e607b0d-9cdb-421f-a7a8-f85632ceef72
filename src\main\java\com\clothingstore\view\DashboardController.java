package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;
import java.util.stream.Collectors;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.service.CustomerAnalyticsService;
import com.clothingstore.service.RealTimeDataService;
import com.clothingstore.util.AlertUtil;
import com.clothingstore.util.NavigationUtil;

import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.util.Duration;

/**
 * Controller for Dashboard interface with enhanced analytics
 */
public class DashboardController implements Initializable, RealTimeDataService.DataRefreshListener {

    // Live Status Labels
    @FXML
    private Label lblLiveStatus;
    @FXML
    private Label lblLastRefresh;
    @FXML
    private Label lblRefreshInterval;

    // Main Metric Labels with Indicators
    @FXML
    private Label lblTodaySales;
    @FXML
    private Label lblTodaySalesIndicator;
    @FXML
    private Label lblTodaySalesChange;
    @FXML
    private Label lblTotalCustomers;
    @FXML
    private Label lblCustomersIndicator;
    @FXML
    private Label lblCustomersChange;
    @FXML
    private Label lblLowStockItems;
    @FXML
    private Label lblStockIndicator;
    @FXML
    private Label lblStockChange;
    @FXML
    private Label lblMonthlyProfit;
    @FXML
    private Label lblProfitIndicator;
    @FXML
    private Label lblProfitChange;

    // Additional Metric Labels
    @FXML
    private Label lblTotalProducts;
    @FXML
    private Label lblTotalValue;
    @FXML
    private Label lblLowStockCount;
    @FXML
    private Label lblOutOfStockCount;
    @FXML
    private Label lblActiveCustomers;
    @FXML
    private Label lblTotalCustomersCount;
    @FXML
    private Label lblTotalTransactions;
    @FXML
    private Label lblAverageTransaction;
    @FXML
    private Label lblAveragePrice;
    @FXML
    private Label lblTopCategory;
    @FXML
    private Label lblTopBrand;
    @FXML
    private Label lblCategoryCount;

    // Top Products Table
    @FXML
    private TableView<TopProductItem> tblTopProducts;
    @FXML
    private TableColumn<TopProductItem, String> colRank;
    @FXML
    private TableColumn<TopProductItem, String> colProductName;
    @FXML
    private TableColumn<TopProductItem, String> colProductSku;
    @FXML
    private TableColumn<TopProductItem, String> colProductCategory;
    @FXML
    private TableColumn<TopProductItem, String> colProductPrice;
    @FXML
    private TableColumn<TopProductItem, String> colProductStock;
    @FXML
    private TableColumn<TopProductItem, String> colProductValue;

    // Action Buttons
    @FXML
    private Button btnRefreshDashboard;
    @FXML
    private Button btnQuickSale;
    @FXML
    private Button btnAddProduct;
    @FXML
    private Button btnAddProductQuick;

    // Additional Analytics Fields
    @FXML
    private Button btnLowStockReport;
    @FXML
    private Button btnOutOfStockReport;
    @FXML
    private Button btnManageProducts;
    @FXML
    private Button btnViewAllProducts;
    @FXML
    private Button btnExportData;

    // Recent Activity
    @FXML
    private TextArea txtRecentActivity;

    private ObservableList<TopProductItem> topProductsData;
    private ProductDAO productDAO;
    private CustomerDAO customerDAO;
    private TransactionDAO transactionDAO;
    private CustomerAnalyticsService customerAnalyticsService;
    private RealTimeDataService realTimeDataService;
    private Timer dashboardRefreshTimer;
    private Timer fastRefreshTimer;
    private NumberFormat currencyFormat;
    private DateTimeFormatter timeFormatter;
    private boolean isRefreshing = false;

    // Live refresh tracking
    private Timeline liveRefreshTimeline;
    private Timeline indicatorTimeline;
    private LocalDateTime lastRefreshTime;
    private Map<String, Double> previousValues = new HashMap<>();

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        productDAO = ProductDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        transactionDAO = TransactionDAO.getInstance();
        customerAnalyticsService = CustomerAnalyticsService.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        timeFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm");

        topProductsData = FXCollections.observableArrayList();

        setupTopProductsTable();
        loadDashboardData();
        updateRecentActivity();

        // Initialize real-time data service
        realTimeDataService = RealTimeDataService.getInstance();
        realTimeDataService.addListener(this);

        // Initialize live refresh functionality
        initializeLiveRefresh();

        // Start enhanced automatic dashboard refresh timers
        startDashboardAutoRefresh();
        startFastDataRefresh();
    }

    /**
     * Initialize live refresh functionality with visual indicators
     */
    private void initializeLiveRefresh() {
        // Initialize last refresh time
        lastRefreshTime = LocalDateTime.now();

        // Set initial status labels
        if (lblLiveStatus != null) {
            lblLiveStatus.setText("🔄 LIVE DATA");
            lblLiveStatus.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold; -fx-font-size: 12px;");
        }

        if (lblRefreshInterval != null) {
            lblRefreshInterval.setText("Auto-refresh: 5s");
            lblRefreshInterval.setStyle("-fx-text-fill: #3498db; -fx-font-size: 11px; -fx-font-weight: bold;");
        }

        // Start live refresh timeline (every 5 seconds)
        startLiveRefreshTimeline();

        // Start indicator animation timeline
        startIndicatorAnimation();

        // Update last refresh time display
        updateLastRefreshDisplay();
    }

    /**
     * Start live refresh timeline for automatic data updates
     */
    private void startLiveRefreshTimeline() {
        if (liveRefreshTimeline != null) {
            liveRefreshTimeline.stop();
        }

        // Create timeline that runs every 5 seconds
        liveRefreshTimeline = new Timeline(
                new KeyFrame(Duration.seconds(5), e -> {
                    refreshLiveData();
                })
        );
        liveRefreshTimeline.setCycleCount(Timeline.INDEFINITE);
        liveRefreshTimeline.play();
    }

    /**
     * Start indicator animation for visual feedback
     */
    private void startIndicatorAnimation() {
        if (indicatorTimeline != null) {
            indicatorTimeline.stop();
        }

        // Create pulsing animation for indicators
        indicatorTimeline = new Timeline(
                new KeyFrame(Duration.seconds(0.5), e -> {
                    // Animate indicators to show live status
                    animateIndicators(true);
                }),
                new KeyFrame(Duration.seconds(1.0), e -> {
                    animateIndicators(false);
                })
        );
        indicatorTimeline.setCycleCount(Timeline.INDEFINITE);
        indicatorTimeline.play();
    }

    /**
     * Animate the live data indicators
     */
    private void animateIndicators(boolean highlight) {
        String color = highlight ? "#27ae60" : "#95a5a6";
        String style = "-fx-text-fill: " + color + "; -fx-font-size: 8px;";

        if (lblTodaySalesIndicator != null) {
            lblTodaySalesIndicator.setStyle(style);
        }
        if (lblCustomersIndicator != null) {
            lblCustomersIndicator.setStyle(style);
        }
        if (lblStockIndicator != null) {
            lblStockIndicator.setStyle(style);
        }
        if (lblProfitIndicator != null) {
            lblProfitIndicator.setStyle(style);
        }
    }

    /**
     * Refresh live data and update displays
     */
    private void refreshLiveData() {
        try {
            // Store previous values for change detection
            storePreviousValues();

            // Load fresh data
            loadMainMetricsLive();

            // Update change indicators
            updateChangeIndicators();

            // Update last refresh time
            lastRefreshTime = LocalDateTime.now();
            updateLastRefreshDisplay();

            // Flash live status indicator
            flashLiveStatus();

        } catch (Exception e) {
            System.err.println("Error refreshing live data: " + e.getMessage());
        }
    }

    /**
     * Store previous values for change detection
     */
    private void storePreviousValues() {
        try {
            if (lblTodaySales != null && lblTodaySales.getText() != null) {
                String salesText = lblTodaySales.getText().replace("$", "").replace(",", "");
                if (!salesText.isEmpty() && !salesText.equals("0.00")) {
                    previousValues.put("todaySales", Double.parseDouble(salesText));
                }
            }

            if (lblTotalCustomers != null && lblTotalCustomers.getText() != null) {
                String customersText = lblTotalCustomers.getText();
                if (!customersText.isEmpty() && !customersText.equals("0")) {
                    previousValues.put("totalCustomers", Double.parseDouble(customersText));
                }
            }

            if (lblLowStockItems != null && lblLowStockItems.getText() != null) {
                String stockText = lblLowStockItems.getText();
                if (!stockText.isEmpty() && !stockText.equals("0")) {
                    previousValues.put("lowStock", Double.parseDouble(stockText));
                }
            }

            if (lblMonthlyProfit != null && lblMonthlyProfit.getText() != null) {
                String profitText = lblMonthlyProfit.getText().replace("$", "").replace(",", "");
                if (!profitText.isEmpty() && !profitText.equals("0.00")) {
                    previousValues.put("monthlyProfit", Double.parseDouble(profitText));
                }
            }
        } catch (NumberFormatException e) {
            // Ignore parsing errors
        }
    }

    /**
     * Update change indicators based on previous values
     */
    private void updateChangeIndicators() {
        updateChangeLabel(lblTodaySalesChange, "todaySales", lblTodaySales);
        updateChangeLabel(lblCustomersChange, "totalCustomers", lblTotalCustomers);
        updateChangeLabel(lblStockChange, "lowStock", lblLowStockItems);
        updateChangeLabel(lblProfitChange, "monthlyProfit", lblMonthlyProfit);
    }

    /**
     * Update individual change label
     */
    private void updateChangeLabel(Label changeLabel, String key, Label valueLabel) {
        if (changeLabel == null || valueLabel == null) {
            return;
        }

        try {
            Double previousValue = previousValues.get(key);
            if (previousValue == null) {
                changeLabel.setText("--");
                return;
            }

            String currentText = valueLabel.getText().replace("$", "").replace(",", "");
            if (currentText.isEmpty()) {
                return;
            }

            double currentValue = Double.parseDouble(currentText);
            double change = currentValue - previousValue;

            if (Math.abs(change) < 0.01) {
                changeLabel.setText("No change");
                changeLabel.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 10px;");
            } else if (change > 0) {
                changeLabel.setText("+" + String.format("%.2f", change));
                changeLabel.setStyle("-fx-text-fill: #27ae60; -fx-font-size: 10px; -fx-font-weight: bold;");
            } else {
                changeLabel.setText(String.format("%.2f", change));
                changeLabel.setStyle("-fx-text-fill: #e74c3c; -fx-font-size: 10px; -fx-font-weight: bold;");
            }
        } catch (NumberFormatException e) {
            changeLabel.setText("--");
        }
    }

    /**
     * Update last refresh time display
     */
    private void updateLastRefreshDisplay() {
        if (lblLastRefresh != null && lastRefreshTime != null) {
            String timeText = "Last updated: " + lastRefreshTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            lblLastRefresh.setText(timeText);
            lblLastRefresh.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 11px;");
        }
    }

    /**
     * Flash the live status indicator
     */
    private void flashLiveStatus() {
        if (lblLiveStatus != null) {
            // Temporarily change color to indicate refresh
            lblLiveStatus.setStyle("-fx-text-fill: #f39c12; -fx-font-weight: bold; -fx-font-size: 12px;");

            // Create a timeline to revert color after 500ms
            Timeline flashTimeline = new Timeline(
                    new KeyFrame(Duration.millis(500), e -> {
                        lblLiveStatus.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold; -fx-font-size: 12px;");
                    })
            );
            flashTimeline.play();
        }
    }

    /**
     * Load main metrics for live refresh
     */
    private void loadMainMetricsLive() {
        try {
            // Today's Sales - calculate from today's transactions
            LocalDate today = LocalDate.now();
            List<Transaction> todayTransactions = transactionDAO.findAll().stream()
                    .filter(t -> t.getTransactionDate().toLocalDate().equals(today))
                    .filter(t -> "COMPLETED".equals(t.getStatus()))
                    .collect(Collectors.toList());

            BigDecimal todaySales = todayTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (lblTodaySales != null) {
                lblTodaySales.setText(currencyFormat.format(todaySales));
            }

            // Total Customers
            int totalCustomers = customerDAO.getTotalCustomerCount();
            if (lblTotalCustomers != null) {
                lblTotalCustomers.setText(String.valueOf(totalCustomers));
            }

            // Low Stock Items
            List<Product> lowStockProducts = productDAO.findLowStockProducts();
            int lowStockCount = lowStockProducts.size();
            if (lblLowStockItems != null) {
                lblLowStockItems.setText(String.valueOf(lowStockCount));
            }

            // Monthly Profit - calculate from this month's transactions
            LocalDate startOfMonth = today.withDayOfMonth(1);
            LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());

            List<Transaction> monthlyTransactions = transactionDAO.findAll().stream()
                    .filter(t -> {
                        LocalDate transDate = t.getTransactionDate().toLocalDate();
                        return !transDate.isBefore(startOfMonth) && !transDate.isAfter(endOfMonth);
                    })
                    .filter(t -> "COMPLETED".equals(t.getStatus()))
                    .collect(Collectors.toList());

            BigDecimal monthlyRevenue = monthlyTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (lblMonthlyProfit != null) {
                lblMonthlyProfit.setText(currencyFormat.format(monthlyRevenue));
            }

        } catch (SQLException e) {
            System.err.println("Error loading main metrics: " + e.getMessage());
        }
    }

    /**
     * Start automatic dashboard refresh timer for continuous data updates
     */
    private void startDashboardAutoRefresh() {
        dashboardRefreshTimer = new Timer("DashboardAutoRefresh", true);
        dashboardRefreshTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                javafx.application.Platform.runLater(() -> {
                    try {
                        isRefreshing = true;
                        loadDashboardDataWithIndicator();
                        loadEnhancedAnalytics();
                        updateRecentActivityWithTimestamp();
                        isRefreshing = false;
                        System.out.println("Dashboard auto-refreshed at: " + LocalDateTime.now().format(timeFormatter));
                    } catch (Exception e) {
                        isRefreshing = false;
                        System.err.println("Error during dashboard auto-refresh: " + e.getMessage());
                    }
                });
            }
        }, 10000, 10000); // Start after 10 seconds, then repeat every 10 seconds
    }

    /**
     * Start fast refresh timer for critical metrics (every 5 seconds)
     */
    private void startFastDataRefresh() {
        fastRefreshTimer = new Timer("FastDataRefresh", true);
        fastRefreshTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                javafx.application.Platform.runLater(() -> {
                    try {
                        // Fast refresh for critical metrics only
                        loadSalesMetricsWithAnimation();
                        updateRealtimeIndicators();
                    } catch (Exception e) {
                        System.err.println("Error during fast data refresh: " + e.getMessage());
                    }
                });
            }
        }, 5000, 5000); // Start after 5 seconds, then repeat every 5 seconds
    }

    private void setupTopProductsTable() {
        // Check if table components exist before configuring
        if (colRank == null || colProductName == null || tblTopProducts == null) {
            System.out.println("Top products table components not found in FXML - skipping table setup");
            return;
        }

        colRank.setCellValueFactory(new PropertyValueFactory<>("rank"));
        colProductName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colProductSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colProductCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colProductPrice.setCellValueFactory(new PropertyValueFactory<>("price"));
        colProductStock.setCellValueFactory(new PropertyValueFactory<>("stock"));
        colProductValue.setCellValueFactory(new PropertyValueFactory<>("totalValue"));

        tblTopProducts.setItems(topProductsData);
    }

    private void loadDashboardData() {
        try {
            System.out.println("DEBUG: Starting dashboard data load...");

            loadInventoryMetrics();
            System.out.println("DEBUG: Inventory metrics loaded successfully");

            loadSalesMetrics();
            System.out.println("DEBUG: Sales metrics loaded successfully");

            loadProductAnalytics();
            System.out.println("DEBUG: Product analytics loaded successfully");

            loadTopProducts();
            System.out.println("DEBUG: Top products loaded successfully");

            System.out.println("DEBUG: Dashboard data load completed successfully");

        } catch (SQLException e) {
            System.err.println("DEBUG: Dashboard data load failed: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Database Error", "Failed to load dashboard data: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("DEBUG: Unexpected error during dashboard load: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Dashboard Error", "Unexpected error loading dashboard: " + e.getMessage());
        }
    }

    /**
     * Enhanced dashboard data loading with visual refresh indicators
     */
    private void loadDashboardDataWithIndicator() {
        try {
            // Add visual indicator that data is refreshing
            if (lblTotalProducts != null) {
                lblTotalProducts.setStyle("-fx-text-fill: #3498db; -fx-font-weight: bold;");
            }

            loadInventoryMetrics();
            loadSalesMetrics();
            loadProductAnalytics();
            loadTopProducts();

            // Reset visual indicators after refresh
            javafx.application.Platform.runLater(() -> {
                if (lblTotalProducts != null) {
                    lblTotalProducts.setStyle("-fx-text-fill: #2c3e50; -fx-font-weight: normal;");
                }
            });
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load dashboard data: " + e.getMessage());
        }
    }

    private void loadInventoryMetrics() throws SQLException {
        // Declare variables outside try block for proper scope
        int totalProducts = 0;
        BigDecimal totalValue = BigDecimal.ZERO;
        long lowStockCount = 0;
        int outOfStockCount = 0;

        try {
            System.out.println("DEBUG: Loading inventory metrics...");

            // Get inventory metrics using enhanced ProductDAO methods
            totalProducts = productDAO.getTotalProductCount();
            System.out.println("DEBUG: Total products: " + totalProducts);

            totalValue = productDAO.getTotalInventoryValue();
            System.out.println("DEBUG: Total inventory value: " + totalValue);

            List<Product> allProducts = productDAO.findAll();
            System.out.println("DEBUG: Retrieved " + allProducts.size() + " products");

            lowStockCount = allProducts.stream().mapToLong(p -> p.isLowStock() ? 1 : 0).sum();
            System.out.println("DEBUG: Low stock count: " + lowStockCount);

            List<Product> outOfStockProducts = productDAO.findOutOfStockProducts();
            outOfStockCount = outOfStockProducts.size();
            System.out.println("DEBUG: Out of stock count: " + outOfStockCount);

            // Update labels (only if they exist in the FXML)
            if (lblTotalProducts != null) {
                lblTotalProducts.setText("Total Products: " + totalProducts);
                System.out.println("DEBUG: Updated lblTotalProducts");
            } else {
                System.out.println("DEBUG: lblTotalProducts is null");
            }

            if (lblTotalValue != null) {
                lblTotalValue.setText("Total Value: " + currencyFormat.format(totalValue));
                System.out.println("DEBUG: Updated lblTotalValue");
            } else {
                System.out.println("DEBUG: lblTotalValue is null");
            }

            if (lblLowStockCount != null) {
                lblLowStockCount.setText("Low Stock: " + lowStockCount);
                System.out.println("DEBUG: Updated lblLowStockCount");
            } else {
                System.out.println("DEBUG: lblLowStockCount is null");
            }

            if (lblOutOfStockCount != null) {
                lblOutOfStockCount.setText("Out of Stock: " + outOfStockCount);
                System.out.println("DEBUG: Updated lblOutOfStockCount");
            } else {
                System.out.println("DEBUG: lblOutOfStockCount is null");
            }

            // Update button styles based on alerts (only if buttons exist)
            if (btnLowStockReport != null && lowStockCount > 0) {
                btnLowStockReport.getStyleClass().removeAll("button", "warning");
                btnLowStockReport.getStyleClass().addAll("button", "warning");
                System.out.println("DEBUG: Enabled low stock report button");
            }

            if (btnOutOfStockReport != null && outOfStockCount > 0) {
                btnOutOfStockReport.getStyleClass().removeAll("button", "danger");
                btnOutOfStockReport.getStyleClass().addAll("button", "danger");
                System.out.println("DEBUG: Enabled out of stock report button");
            }

        } catch (Exception e) {
            System.err.println("DEBUG: Error in loadInventoryMetrics: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    private void loadSalesMetrics() throws SQLException {
        // Get customer and transaction metrics
        List<Customer> allCustomers = customerDAO.findAll();
        int totalCustomers = allCustomers.size();
        long activeCustomers = allCustomers.stream().mapToLong(c -> "Active".equals(c.getStatus()) ? 1 : 0).sum();

        List<Transaction> allTransactions = transactionDAO.findAll();
        int totalTransactions = allTransactions.size();

        BigDecimal averageTransaction = BigDecimal.ZERO;
        if (!allTransactions.isEmpty()) {
            BigDecimal totalSales = allTransactions.stream()
                    .map(Transaction::getTotal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            averageTransaction = totalSales.divide(
                    BigDecimal.valueOf(allTransactions.size()), 2, BigDecimal.ROUND_HALF_UP
            );
        }

        // Update labels (only if they exist in the FXML)
        if (lblTotalCustomersCount != null) {
            lblTotalCustomersCount.setText("Total Customers: " + totalCustomers);
        }
        if (lblActiveCustomers != null) {
            lblActiveCustomers.setText("Active Customers: " + activeCustomers);
        }
        if (lblTotalTransactions != null) {
            lblTotalTransactions.setText("Total Transactions: " + totalTransactions);
        }
        if (lblAverageTransaction != null) {
            lblAverageTransaction.setText("Avg Transaction: " + currencyFormat.format(averageTransaction));
        }
    }

    private void loadProductAnalytics() throws SQLException {
        List<Product> allProducts = productDAO.findAll();

        if (allProducts.isEmpty()) {
            if (lblAveragePrice != null) {
                lblAveragePrice.setText("Avg Price: $0.00");
            }
            if (lblTopCategory != null) {
                lblTopCategory.setText("Top Category: -");
            }
            if (lblTopBrand != null) {
                lblTopBrand.setText("Top Brand: -");
            }
            if (lblCategoryCount != null) {
                lblCategoryCount.setText("Categories: 0");
            }
            return;
        }

        // Calculate average price
        BigDecimal averagePrice = allProducts.stream()
                .map(Product::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(allProducts.size()), 2, BigDecimal.ROUND_HALF_UP);

        // Find top category by product count
        String topCategory = allProducts.stream()
                .collect(Collectors.groupingBy(Product::getCategory, Collectors.counting()))
                .entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("-");

        // Find top brand by product count
        String topBrand = allProducts.stream()
                .filter(p -> p.getBrand() != null && !p.getBrand().trim().isEmpty())
                .collect(Collectors.groupingBy(Product::getBrand, Collectors.counting()))
                .entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("-");

        // Count unique categories
        long categoryCount = allProducts.stream()
                .map(Product::getCategory)
                .filter(c -> c != null && !c.trim().isEmpty())
                .distinct()
                .count();

        // Update labels (only if they exist in the FXML)
        if (lblAveragePrice != null) {
            lblAveragePrice.setText("Avg Price: " + currencyFormat.format(averagePrice));
        }
        if (lblTopCategory != null) {
            lblTopCategory.setText("Top Category: " + topCategory);
        }
        if (lblTopBrand != null) {
            lblTopBrand.setText("Top Brand: " + topBrand);
        }
        if (lblCategoryCount != null) {
            lblCategoryCount.setText("Categories: " + categoryCount);
        }
    }

    private void loadTopProducts() throws SQLException {
        List<Product> topProducts = productDAO.findTopSellingProducts(10);
        topProductsData.clear();

        for (int i = 0; i < topProducts.size(); i++) {
            Product product = topProducts.get(i);
            BigDecimal totalValue = product.getPrice().multiply(BigDecimal.valueOf(product.getStockQuantity()));

            TopProductItem item = new TopProductItem();
            item.setRank(String.valueOf(i + 1));
            item.setName(product.getName());
            item.setSku(product.getSku());
            item.setCategory(product.getCategory());
            item.setPrice(currencyFormat.format(product.getPrice()));
            item.setStock(String.valueOf(product.getStockQuantity()));
            item.setTotalValue(currencyFormat.format(totalValue));

            topProductsData.add(item);
        }
    }

    private void updateRecentActivity() {
        // Only update if the component exists in the FXML
        if (txtRecentActivity == null) {
            System.out.println("Recent activity text area not found in FXML - skipping update");
            return;
        }

        StringBuilder activity = new StringBuilder();
        activity.append("Dashboard loaded at ").append(LocalDateTime.now().format(timeFormatter)).append("\n");
        activity.append("System status: All services operational\n");
        activity.append("Last data refresh: ").append(LocalDateTime.now().format(timeFormatter)).append("\n");
        activity.append("Database connection: Active\n");
        activity.append("Ready for operations...\n");

        txtRecentActivity.setText(activity.toString());
    }

    /**
     * Enhanced recent activity update with real-time timestamp and refresh
     * indicators
     */
    private void updateRecentActivityWithTimestamp() {
        StringBuilder activity = new StringBuilder();
        activity.append("🔄 LIVE DATA - Auto-refreshed at ").append(LocalDateTime.now().format(timeFormatter)).append("\n");
        activity.append("📊 Real-time Analytics: ACTIVE\n");
        activity.append("🔗 Database connection: LIVE\n");
        activity.append("⚡ Fast refresh: Every 5 seconds\n");
        activity.append("🔄 Full refresh: Every 10 seconds\n");
        activity.append("✅ All systems operational\n");

        if (txtRecentActivity != null) {
            txtRecentActivity.setText(activity.toString());
        }
    }

    /**
     * Load sales metrics with visual animation effects
     */
    private void loadSalesMetricsWithAnimation() {
        try {
            // Add visual indicator for fast refresh
            if (lblTotalTransactions != null) {
                lblTotalTransactions.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");

                // Reset after short delay
                javafx.application.Platform.runLater(() -> {
                    new Timer().schedule(new TimerTask() {
                        @Override
                        public void run() {
                            javafx.application.Platform.runLater(() -> {
                                if (lblTotalTransactions != null) {
                                    lblTotalTransactions.setStyle("-fx-text-fill: #2c3e50; -fx-font-weight: normal;");
                                }
                            });
                        }
                    }, 1000);
                });
            }

            loadSalesMetrics();
        } catch (SQLException e) {
            System.err.println("Error loading sales metrics with animation: " + e.getMessage());
        }
    }

    /**
     * Update real-time indicators to show live data status
     */
    private void updateRealtimeIndicators() {
        // Update timestamp in recent activity to show live status
        if (txtRecentActivity != null) {
            String currentText = txtRecentActivity.getText();
            if (currentText.contains("LIVE DATA")) {
                // Update just the timestamp part
                String newTimestamp = LocalDateTime.now().format(timeFormatter);
                String updatedText = currentText.replaceFirst("Auto-refreshed at .*", "Auto-refreshed at " + newTimestamp);
                txtRecentActivity.setText(updatedText);
            }
        }
    }

    // Event Handlers
    @FXML
    private void handleRefresh() {
        try {
            // Show visual feedback that refresh is happening
            if (lblLiveStatus != null) {
                lblLiveStatus.setText("🔄 REFRESHING...");
                lblLiveStatus.setStyle("-fx-text-fill: #f39c12; -fx-font-weight: bold; -fx-font-size: 12px;");
            }

            // Perform comprehensive refresh
            loadDashboardData();
            loadEnhancedAnalytics();
            updateRecentActivity();

            // Update live metrics
            refreshLiveData();

            // Reset live status
            if (lblLiveStatus != null) {
                lblLiveStatus.setText("🔄 LIVE DATA");
                lblLiveStatus.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold; -fx-font-size: 12px;");
            }

            // Update last refresh time
            lastRefreshTime = LocalDateTime.now();
            updateLastRefreshDisplay();

            AlertUtil.showInfo("Dashboard Refreshed", "All dashboard data has been refreshed successfully.");

        } catch (Exception e) {
            AlertUtil.showError("Refresh Error", "Failed to refresh dashboard: " + e.getMessage());

            // Reset status on error
            if (lblLiveStatus != null) {
                lblLiveStatus.setText("🔄 LIVE DATA");
                lblLiveStatus.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold; -fx-font-size: 12px;");
            }
        }
    }

    /**
     * Handle quick sale action
     */
    @FXML
    private void handleQuickSale() {
        try {
            NavigationUtil.navigateTo(btnQuickSale, "/fxml/POS.fxml", "Point of Sale");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open POS: " + e.getMessage());
        }
    }

    /**
     * Handle add product action
     */
    @FXML
    private void handleAddProduct() {
        try {
            NavigationUtil.navigateTo(btnAddProduct, "/fxml/ProductManagement.fxml", "Product Management");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Product Management: " + e.getMessage());
        }
    }

    @FXML
    private void handleLowStockReport() {
        try {
            List<Product> lowStockProducts = productDAO.findLowStockProducts();
            if (lowStockProducts.isEmpty()) {
                AlertUtil.showInfo("Low Stock Report", "No products are currently low on stock.");
            } else {
                StringBuilder report = new StringBuilder("Low Stock Products:\n\n");
                for (Product product : lowStockProducts) {
                    report.append(String.format("• %s (%s) - Stock: %d, Min: %d\n",
                            product.getName(), product.getSku(),
                            product.getStockQuantity(), product.getMinStockLevel()));
                }
                AlertUtil.showWarning("Low Stock Report", report.toString());
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate low stock report: " + e.getMessage());
        }
    }

    @FXML
    private void handleOutOfStockReport() {
        try {
            List<Product> outOfStockProducts = productDAO.findOutOfStockProducts();
            if (outOfStockProducts.isEmpty()) {
                AlertUtil.showInfo("Out of Stock Report", "No products are currently out of stock.");
            } else {
                StringBuilder report = new StringBuilder("Out of Stock Products:\n\n");
                for (Product product : outOfStockProducts) {
                    report.append(String.format("• %s (%s) - Category: %s\n",
                            product.getName(), product.getSku(), product.getCategory()));
                }
                AlertUtil.showWarning("Out of Stock Report", report.toString());
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate out of stock report: " + e.getMessage());
        }
    }

    @FXML
    private void handleManageProducts() {
        navigateToProductManagement();
    }

    @FXML
    private void handleViewAllProducts() {
        navigateToProductManagement();
    }

    private void navigateToProductManagement() {
        NavigationUtil.navigateToProductManagement(lblTotalProducts);
    }

    @FXML
    private void handleExportData() {
        AlertUtil.showInfo("Export", "Dashboard data export functionality will be implemented in future version.");
    }

    // Inner class for top products table data
    public static class TopProductItem {

        private String rank;
        private String name;
        private String sku;
        private String category;
        private String price;
        private String stock;
        private String totalValue;

        // Getters and setters
        public String getRank() {
            return rank;
        }

        public void setRank(String rank) {
            this.rank = rank;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getStock() {
            return stock;
        }

        public void setStock(String stock) {
            this.stock = stock;
        }

        public String getTotalValue() {
            return totalValue;
        }

        public void setTotalValue(String totalValue) {
            this.totalValue = totalValue;
        }
    }

    /**
     * Enhanced dashboard data loading with new analytics
     */
    private void loadEnhancedAnalytics() {
        try {
            // Today's sales calculation
            LocalDate today = LocalDate.now();
            List<Transaction> todayTransactions = transactionDAO.findAll().stream()
                    .filter(t -> t.getTransactionDate().toLocalDate().equals(today))
                    .filter(t -> "COMPLETED".equals(t.getStatus()))
                    .collect(Collectors.toList());

            BigDecimal todaySales = todayTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            lblTodaySales.setText(currencyFormat.format(todaySales.doubleValue()));

            // Low stock items count
            List<Product> allProducts = productDAO.findAll();
            long lowStockCount = allProducts.stream()
                    .filter(p -> p.getStockQuantity() <= 10) // Assuming 10 is low stock threshold
                    .count();

            lblLowStockItems.setText(String.valueOf(lowStockCount));

            // Monthly profit calculation (simplified)
            LocalDate monthStart = today.withDayOfMonth(1);
            List<Transaction> monthlyTransactions = transactionDAO.findAll().stream()
                    .filter(t -> !t.getTransactionDate().toLocalDate().isBefore(monthStart))
                    .filter(t -> "COMPLETED".equals(t.getStatus()))
                    .collect(Collectors.toList());

            BigDecimal monthlyRevenue = monthlyTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Simplified profit calculation (assuming 30% profit margin)
            BigDecimal monthlyProfit = monthlyRevenue.multiply(new BigDecimal("0.30"));
            lblMonthlyProfit.setText(currencyFormat.format(monthlyProfit.doubleValue()));

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load enhanced analytics: " + e.getMessage());
        }
    }

    // Real-time data service listener methods
    @Override
    public void onDataRefresh(RealTimeDataService.RefreshType type) {
        try {
            switch (type) {
                case DASHBOARD_DATA:
                case ALL_DATA:
                    loadDashboardData();
                    loadEnhancedAnalytics();
                    updateRecentActivity();
                    break;
                case SALES_DATA:
                case FINANCIAL_DATA:
                    loadEnhancedAnalytics();
                    break;
                case INVENTORY_DATA:
                    loadDashboardData();
                    break;
                default:
                    // No action needed for other types
                    break;
            }
        } catch (Exception e) {
            System.err.println("Error during real-time data refresh: " + e.getMessage());
        }
    }

    @Override
    public String getListenerName() {
        return "DashboardController";
    }

    /**
     * Cleanup method to remove listener and stop timers when controller is
     * destroyed
     */
    public void cleanup() {
        if (realTimeDataService != null) {
            realTimeDataService.removeListener(this);
        }
        if (dashboardRefreshTimer != null) {
            dashboardRefreshTimer.cancel();
            dashboardRefreshTimer = null;
        }
        if (fastRefreshTimer != null) {
            fastRefreshTimer.cancel();
            fastRefreshTimer = null;
        }
    }

    // Additional Event Handlers for Dashboard_Working.fxml
    @FXML
    private void handleNewSale() {
        try {
            // Use the refresh button as source node for navigation
            if (btnRefreshDashboard != null) {
                NavigationUtil.navigateToPointOfSale(btnRefreshDashboard);
            } else {
                AlertUtil.showInfo("Navigation", "Opening Point of Sale...");
            }
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Point of Sale: " + e.getMessage());
        }
    }

    @FXML
    private void handleManageCustomers() {
        try {
            // Use the refresh button as source node for navigation
            if (btnRefreshDashboard != null) {
                NavigationUtil.navigateToCustomerManagement(btnRefreshDashboard);
            } else {
                AlertUtil.showInfo("Navigation", "Opening Customer Management...");
            }
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Customer Management: " + e.getMessage());
        }
    }

    @FXML
    private void handleViewReports() {
        try {
            // Use the refresh button as source node for navigation
            if (btnRefreshDashboard != null) {
                NavigationUtil.navigateToSalesReport(btnRefreshDashboard);
            } else {
                AlertUtil.showInfo("Navigation", "Opening Reports...");
            }
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Reports: " + e.getMessage());
        }
    }
}
