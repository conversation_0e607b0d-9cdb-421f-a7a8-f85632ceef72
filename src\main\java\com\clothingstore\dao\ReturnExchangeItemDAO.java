package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.ReturnExchangeItem;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * Data Access Object for Return/Exchange Item operations
 */
public class ReturnExchangeItemDAO {
    private static final Logger LOGGER = Logger.getLogger(ReturnExchangeItemDAO.class.getName());
    private static ReturnExchangeItemDAO instance;

    private ReturnExchangeItemDAO() {
        // Private constructor for singleton
    }

    public static synchronized ReturnExchangeItemDAO getInstance() {
        if (instance == null) {
            instance = new ReturnExchangeItemDAO();
        }
        return instance;
    }

    /**
     * Create a new return/exchange item
     */
    public ReturnExchangeItem create(ReturnExchangeItem item) throws SQLException {
        String sql = "INSERT INTO return_exchange_items (return_exchange_id, original_transaction_item_id, " +
                "product_id, product_name, product_sku, original_quantity, return_quantity, " +
                "unit_price, line_total, condition_type, reason, action_type, " +
                "exchange_product_id, exchange_product_name, exchange_quantity, " +
                "exchange_unit_price, exchange_line_total) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setLong(1, item.getReturnExchangeId());
            pstmt.setObject(2, item.getOriginalTransactionItemId());
            pstmt.setLong(3, item.getProductId());
            pstmt.setString(4, item.getProductName());
            pstmt.setString(5, item.getProductSku());
            pstmt.setInt(6, item.getOriginalQuantity());
            pstmt.setInt(7, item.getReturnQuantity());
            pstmt.setBigDecimal(8, item.getUnitPrice());
            pstmt.setBigDecimal(9, item.getLineTotal());
            pstmt.setString(10, item.getCondition());
            pstmt.setString(11, item.getReason());
            pstmt.setString(12, item.getAction());
            pstmt.setObject(13, item.getExchangeProductId());
            pstmt.setString(14, item.getExchangeProductName());
            pstmt.setInt(15, item.getExchangeQuantity());
            pstmt.setBigDecimal(16, item.getExchangeUnitPrice());
            pstmt.setBigDecimal(17, item.getExchangeLineTotal());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating return/exchange item failed, no rows affected.");
            }

            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    item.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating return/exchange item failed, no ID obtained.");
                }
            }

            LOGGER.info("Created return/exchange item: " + item.getId());
            return item;
        }
    }

    /**
     * Find items by return/exchange ID
     */
    public List<ReturnExchangeItem> findByReturnExchangeId(Long returnExchangeId) throws SQLException {
        String sql = "SELECT * FROM return_exchange_items WHERE return_exchange_id = ? ORDER BY id";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, returnExchangeId);
            
            List<ReturnExchangeItem> items = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    items.add(mapResultSetToReturnExchangeItem(rs));
                }
            }
            return items;
        }
    }

    /**
     * Find item by ID
     */
    public ReturnExchangeItem findById(Long id) throws SQLException {
        String sql = "SELECT * FROM return_exchange_items WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToReturnExchangeItem(rs);
                }
            }
        }
        return null;
    }

    /**
     * Update return/exchange item
     */
    public void update(ReturnExchangeItem item) throws SQLException {
        String sql = "UPDATE return_exchange_items SET " +
                "return_quantity = ?, unit_price = ?, line_total = ?, " +
                "condition_type = ?, reason = ?, action_type = ?, " +
                "exchange_product_id = ?, exchange_product_name = ?, " +
                "exchange_quantity = ?, exchange_unit_price = ?, exchange_line_total = ?, " +
                "updated_at = CURRENT_TIMESTAMP " +
                "WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, item.getReturnQuantity());
            pstmt.setBigDecimal(2, item.getUnitPrice());
            pstmt.setBigDecimal(3, item.getLineTotal());
            pstmt.setString(4, item.getCondition());
            pstmt.setString(5, item.getReason());
            pstmt.setString(6, item.getAction());
            pstmt.setObject(7, item.getExchangeProductId());
            pstmt.setString(8, item.getExchangeProductName());
            pstmt.setInt(9, item.getExchangeQuantity());
            pstmt.setBigDecimal(10, item.getExchangeUnitPrice());
            pstmt.setBigDecimal(11, item.getExchangeLineTotal());
            pstmt.setLong(12, item.getId());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating return/exchange item failed, no rows affected.");
            }

            LOGGER.info("Updated return/exchange item: " + item.getId());
        }
    }

    /**
     * Delete return/exchange item
     */
    public void delete(Long id) throws SQLException {
        String sql = "DELETE FROM return_exchange_items WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows == 0) {
                throw new SQLException("Deleting return/exchange item failed, no rows affected.");
            }

            LOGGER.info("Deleted return/exchange item: " + id);
        }
    }

    /**
     * Delete all items for a return/exchange
     */
    public void deleteByReturnExchangeId(Long returnExchangeId) throws SQLException {
        String sql = "DELETE FROM return_exchange_items WHERE return_exchange_id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, returnExchangeId);
            int affectedRows = pstmt.executeUpdate();
            
            LOGGER.info("Deleted " + affectedRows + " items for return/exchange: " + returnExchangeId);
        }
    }

    /**
     * Create multiple items in a batch
     */
    public void createBatch(List<ReturnExchangeItem> items) throws SQLException {
        String sql = "INSERT INTO return_exchange_items (return_exchange_id, original_transaction_item_id, " +
                "product_id, product_name, product_sku, original_quantity, return_quantity, " +
                "unit_price, line_total, condition_type, reason, action_type, " +
                "exchange_product_id, exchange_product_name, exchange_quantity, " +
                "exchange_unit_price, exchange_line_total) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            conn.setAutoCommit(false);
            
            for (ReturnExchangeItem item : items) {
                pstmt.setLong(1, item.getReturnExchangeId());
                pstmt.setObject(2, item.getOriginalTransactionItemId());
                pstmt.setLong(3, item.getProductId());
                pstmt.setString(4, item.getProductName());
                pstmt.setString(5, item.getProductSku());
                pstmt.setInt(6, item.getOriginalQuantity());
                pstmt.setInt(7, item.getReturnQuantity());
                pstmt.setBigDecimal(8, item.getUnitPrice());
                pstmt.setBigDecimal(9, item.getLineTotal());
                pstmt.setString(10, item.getCondition());
                pstmt.setString(11, item.getReason());
                pstmt.setString(12, item.getAction());
                pstmt.setObject(13, item.getExchangeProductId());
                pstmt.setString(14, item.getExchangeProductName());
                pstmt.setInt(15, item.getExchangeQuantity());
                pstmt.setBigDecimal(16, item.getExchangeUnitPrice());
                pstmt.setBigDecimal(17, item.getExchangeLineTotal());
                
                pstmt.addBatch();
            }

            pstmt.executeBatch();
            conn.commit();
            conn.setAutoCommit(true);

            LOGGER.info("Created batch of " + items.size() + " return/exchange items");
        }
    }

    private ReturnExchangeItem mapResultSetToReturnExchangeItem(ResultSet rs) throws SQLException {
        ReturnExchangeItem item = new ReturnExchangeItem();
        item.setId(rs.getLong("id"));
        item.setReturnExchangeId(rs.getLong("return_exchange_id"));
        item.setOriginalTransactionItemId(rs.getObject("original_transaction_item_id", Long.class));
        item.setProductId(rs.getLong("product_id"));
        item.setProductName(rs.getString("product_name"));
        item.setProductSku(rs.getString("product_sku"));
        item.setOriginalQuantity(rs.getInt("original_quantity"));
        item.setReturnQuantity(rs.getInt("return_quantity"));
        item.setUnitPrice(rs.getBigDecimal("unit_price"));
        item.setLineTotal(rs.getBigDecimal("line_total"));
        item.setCondition(rs.getString("condition_type"));
        item.setReason(rs.getString("reason"));
        item.setAction(rs.getString("action_type"));
        item.setExchangeProductId(rs.getObject("exchange_product_id", Long.class));
        item.setExchangeProductName(rs.getString("exchange_product_name"));
        item.setExchangeQuantity(rs.getInt("exchange_quantity"));
        item.setExchangeUnitPrice(rs.getBigDecimal("exchange_unit_price"));
        item.setExchangeLineTotal(rs.getBigDecimal("exchange_line_total"));
        return item;
    }
}
