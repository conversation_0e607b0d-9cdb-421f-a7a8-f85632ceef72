<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.ProductManagementController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="Product Management">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnManageCategories" onAction="#handleManageCategories" styleClass="button info" text="📁 Manage Categories" />
            <Button fx:id="btnManageSuppliers" onAction="#handleManageSuppliers" styleClass="button secondary" text="🏢 Manage Suppliers" />
            <Button fx:id="btnAddProduct" onAction="#handleAddProduct" styleClass="button success" text="+ Add Product" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Search and Filter Section - More compact layout -->
      <VBox spacing="12.0" styleClass="form-container">
         <!-- Consolidated Filter Row -->
         <HBox alignment="CENTER_LEFT" spacing="12.0">
            <children>
               <Label styleClass="form-label" text="Search:" />
               <TextField fx:id="txtSearch" onKeyReleased="#handleSearch" prefWidth="220.0" promptText="Search products..." />
               <Label styleClass="form-label" text="Category:" />
               <ComboBox fx:id="cmbCategory" onAction="#handleCategoryFilter" prefWidth="130.0" promptText="All Categories" />
               <Label styleClass="form-label" text="Brand:" />
               <ComboBox fx:id="cmbBrand" onAction="#handleBrandFilter" prefWidth="130.0" promptText="All Brands" />

               <Label styleClass="form-label" text="Supplier:" />
               <ComboBox fx:id="cmbSupplier" onAction="#handleSupplierFilter" prefWidth="140.0" promptText="All Suppliers" />
            </children>
         </HBox>

         <!-- Price and Status Filter Row -->
         <HBox alignment="CENTER_LEFT" spacing="12.0">
            <children>
               <Label styleClass="form-label" text="Price Range:" />
               <TextField fx:id="txtMinPrice" onKeyReleased="#handlePriceFilter" prefWidth="90.0" promptText="Min $" />
               <Label text="-" />
               <TextField fx:id="txtMaxPrice" onKeyReleased="#handlePriceFilter" prefWidth="90.0" promptText="Max $" />
               <Label styleClass="form-label" text="Stock Status:" />
               <ComboBox fx:id="cmbStockStatus" onAction="#handleStockFilter" prefWidth="130.0" promptText="All" />
               <Region HBox.hgrow="ALWAYS" />
               <Button fx:id="btnClearFilters" onAction="#handleClearFilters" styleClass="button secondary" text="Clear All Filters" />
               <Button fx:id="btnAdvancedSearch" onAction="#handleAdvancedSearch" styleClass="button info" text="🔍 Advanced Search" />
            </children>
         </HBox>
         <VBox.margin>
            <Insets bottom="12.0" />
         </VBox.margin>
      </VBox>

      <!-- Products Table - Improved column widths for better space utilization -->
      <TableView fx:id="tblProducts" VBox.vgrow="ALWAYS">
         <columns>
            <TableColumn fx:id="colSku" prefWidth="100.0" text="SKU" />
            <TableColumn fx:id="colName" prefWidth="200.0" text="Product Name" />
            <TableColumn fx:id="colCategory" prefWidth="120.0" text="Category" />
            <TableColumn fx:id="colBrand" prefWidth="100.0" text="Brand" />
            <TableColumn fx:id="colSupplier" prefWidth="130.0" text="Supplier" />
            <TableColumn fx:id="colSize" prefWidth="60.0" text="Size" />
            <TableColumn fx:id="colColor" prefWidth="80.0" text="Color" />
            <TableColumn fx:id="colPrice" prefWidth="80.0" text="Price" />
            <TableColumn fx:id="colStock" prefWidth="70.0" text="Stock" />
            <TableColumn fx:id="colMinStock" prefWidth="80.0" text="Min Stock" />
            <TableColumn fx:id="colStatus" prefWidth="80.0" text="Status" />
            <TableColumn fx:id="colActions" prefWidth="120.0" text="Actions" />
         </columns>
         <contextMenu>
            <ContextMenu>
               <items>
                  <MenuItem fx:id="menuEdit" onAction="#handleEditProduct" text="Edit Product" />
                  <MenuItem fx:id="menuDuplicate" onAction="#handleDuplicateProduct" text="Duplicate Product" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuAdjustStock" onAction="#handleAdjustStock" text="Adjust Stock" />
                  <MenuItem fx:id="menuViewHistory" onAction="#handleViewHistory" text="View History" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuDelete" onAction="#handleDeleteProduct" text="Delete Product" />
               </items>
            </ContextMenu>
         </contextMenu>
      </TableView>

      <!-- Analytics and Summary Section -->
      <VBox spacing="10.0" styleClass="form-container">
         <!-- Analytics Row 1 -->
         <HBox alignment="CENTER_LEFT" spacing="20.0">
            <children>
               <Label fx:id="lblTotalProducts" styleClass="form-label" text="Total Products: 0" />
               <Label fx:id="lblFilteredProducts" styleClass="form-label" text="Filtered: 0" />
               <Label fx:id="lblLowStockCount" styleClass="form-label" text="Low Stock: 0" />
               <Label fx:id="lblOutOfStockCount" styleClass="form-label" text="Out of Stock: 0" />
               <Label fx:id="lblTotalValue" styleClass="form-label" text="Total Value: $0.00" />
            </children>
         </HBox>

         <!-- Analytics Row 2 & Actions -->
         <HBox alignment="CENTER_LEFT" spacing="10.0">
            <children>
               <Label fx:id="lblAveragePrice" styleClass="form-label" text="Avg Price: $0.00" />
               <Label fx:id="lblTopCategory" styleClass="form-label" text="Top Category: -" />
               <Region HBox.hgrow="ALWAYS" />
               <Button fx:id="btnTopProducts" onAction="#handleTopProducts" styleClass="button info" text="🏆 Top Products" />
               <Button fx:id="btnOutOfStockReport" onAction="#handleOutOfStockReport" styleClass="button danger" text="📋 Out of Stock" />
               <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
               <Button fx:id="btnLowStockReport" onAction="#handleLowStockReport" styleClass="button warning" text="⚠ Low Stock Report" />
            </children>
         </HBox>
         <VBox.margin>
            <Insets top="10.0" />
         </VBox.margin>
      </VBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
