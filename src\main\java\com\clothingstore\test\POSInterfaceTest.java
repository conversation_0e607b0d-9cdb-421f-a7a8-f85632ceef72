package com.clothingstore.test;

import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;

/**
 * Test application to demonstrate the improved POS interface layout
 */
public class POSInterfaceTest extends Application {

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("Improved POS Interface Demo");

        // Create the improved layout
        VBox mainLayout = createImprovedPOSLayout();

        // Create scene with responsive sizing
        Scene scene = new Scene(mainLayout, 1200, 800);

        // Add some basic styling
        scene.getStylesheets().add("data:text/css," + getBasicCSS());

        primaryStage.setScene(scene);
        primaryStage.show();
    }

    private VBox createImprovedPOSLayout() {
        VBox layout = new VBox();
        layout.setSpacing(15);
        layout.setPadding(new Insets(20));
        layout.setStyle("-fx-background-color: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);");

        // Header
        HBox header = createHeader();

        // Main content with improved 55/45 split
        HBox mainContent = createImprovedMainContent();
        VBox.setVgrow(mainContent, Priority.ALWAYS);

        // Status bar
        HBox statusBar = createStatusBar();

        layout.getChildren().addAll(header, createSeparator(), mainContent, statusBar);
        return layout;
    }

    private HBox createHeader() {
        HBox header = new HBox(15);
        header.setPadding(new Insets(15));
        header.setStyle("-fx-background-color: white; -fx-background-radius: 15; "
                + "-fx-border-radius: 15; -fx-border-color: #dee2e6; -fx-border-width: 1; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 12, 0, 0, 3);");

        Label title = new Label("Point of Sale System - Improved Layout");
        title.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        Label transactionLabel = new Label("Transaction: TXN000123");
        Label cashierLabel = new Label("Cashier: Admin");

        header.getChildren().addAll(title, spacer, transactionLabel, cashierLabel);
        return header;
    }

    private HBox createImprovedMainContent() {
        HBox mainContent = new HBox(20); // Increased spacing
        mainContent.setPrefHeight(500);

        // Product section (55% width - reduced from 60%)
        VBox productSection = createProductSection();
        productSection.setPrefWidth(660); // 55% of 1200px
        productSection.setMinWidth(550);
        HBox.setHgrow(productSection, Priority.ALWAYS);

        // Cart section (45% width - increased from 40%)
        VBox cartSection = createCartSection();
        cartSection.setPrefWidth(540); // 45% of 1200px
        cartSection.setMinWidth(450);

        mainContent.getChildren().addAll(productSection, cartSection);
        return mainContent;
    }

    private VBox createProductSection() {
        VBox productSection = new VBox(15); // Increased spacing
        productSection.setPadding(new Insets(24)); // Enhanced padding
        productSection.setStyle(getImprovedCardStyle());

        // Header
        HBox productHeader = new HBox(10);
        Label productTitle = new Label("Product Search & Selection");
        productTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Region headerSpacer = new Region();
        HBox.setHgrow(headerSpacer, Priority.ALWAYS);

        Label productCount = new Label("25 products");
        productCount.setStyle("-fx-background-color: #e9ecef; -fx-text-fill: #6c757d; "
                + "-fx-font-size: 12px; -fx-font-weight: bold; -fx-background-radius: 12; "
                + "-fx-padding: 4 8; -fx-border-color: #dee2e6; -fx-border-radius: 12; -fx-border-width: 1;");

        productHeader.getChildren().addAll(productTitle, headerSpacer, productCount);

        // Search section
        HBox searchRow = new HBox(12);
        TextField searchField = new TextField();
        searchField.setPromptText("Search by name, SKU, or barcode...");
        searchField.setStyle(getImprovedInputStyle());
        HBox.setHgrow(searchField, Priority.ALWAYS);

        Button scanButton = new Button("Scan Barcode");
        scanButton.setStyle(getImprovedButtonStyle());

        Button clearButton = new Button("Clear");
        clearButton.setStyle(getImprovedSecondaryButtonStyle());

        searchRow.getChildren().addAll(searchField, scanButton, clearButton);

        // Product table placeholder
        TableView<String> productTable = new TableView<>();
        productTable.setPlaceholder(new Label("Product list will appear here"));
        productTable.setPrefHeight(250);
        VBox.setVgrow(productTable, Priority.ALWAYS);

        // Quick actions
        HBox quickActions = new HBox(10);
        Button addProductBtn = new Button("+ Add Product");
        Button editProductBtn = new Button("Edit");
        Button stockBtn = new Button("Stock");

        addProductBtn.setStyle(getImprovedButtonStyle());
        editProductBtn.setStyle(getImprovedSecondaryButtonStyle());
        stockBtn.setStyle(getImprovedSecondaryButtonStyle());

        quickActions.getChildren().addAll(addProductBtn, editProductBtn, stockBtn);

        productSection.getChildren().addAll(
                productHeader,
                createSeparator(),
                searchRow,
                productTable,
                quickActions
        );

        return productSection;
    }

    private VBox createCartSection() {
        VBox cartSection = new VBox(18); // Increased spacing
        cartSection.setPadding(new Insets(24)); // Enhanced padding
        cartSection.setStyle(getImprovedCardStyle());

        // Header
        HBox cartHeader = new HBox(10);
        Label cartTitle = new Label("Shopping Cart");
        cartTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Region headerSpacer = new Region();
        HBox.setHgrow(headerSpacer, Priority.ALWAYS);

        Label itemCount = new Label("3 items");
        itemCount.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; "
                + "-fx-font-size: 12px; -fx-font-weight: bold; -fx-background-radius: 12; "
                + "-fx-padding: 4 8; -fx-effect: dropshadow(gaussian, rgba(0,123,255,0.3), 4, 0, 0, 2);");

        cartHeader.getChildren().addAll(cartTitle, headerSpacer, itemCount);

        // Cart items list
        ListView<String> cartList = new ListView<>();
        cartList.setPrefHeight(180);
        cartList.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #e1e5e9; "
                + "-fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;");
        cartList.setPlaceholder(new Label("Cart is empty"));

        // Cart summary
        VBox cartSummary = createCartSummary();

        // Payment section
        VBox paymentSection = createPaymentSection();

        // Action buttons (changed to VBox for better stacking)
        VBox cartActions = createCartActions();

        cartSection.getChildren().addAll(
                cartHeader,
                createSeparator(),
                cartList,
                createSeparator(),
                cartSummary,
                createSeparator(),
                paymentSection,
                cartActions
        );

        return cartSection;
    }

    private VBox createCartSummary() {
        VBox summary = new VBox(8);

        HBox subtotalRow = new HBox();
        Label subtotalLabel = new Label("Subtotal:");
        Region spacer1 = new Region();
        HBox.setHgrow(spacer1, Priority.ALWAYS);
        Label subtotalValue = new Label("$127.50");
        subtotalValue.setStyle("-fx-font-weight: bold;");
        subtotalRow.getChildren().addAll(subtotalLabel, spacer1, subtotalValue);

        HBox totalRow = new HBox();
        Label totalLabel = new Label("Total:");
        totalLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 16px;");
        Region spacer2 = new Region();
        HBox.setHgrow(spacer2, Priority.ALWAYS);
        Label totalValue = new Label("$127.50");
        totalValue.setStyle("-fx-font-weight: bold; -fx-font-size: 18px; -fx-text-fill: #28a745;");
        totalRow.getChildren().addAll(totalLabel, spacer2, totalValue);

        summary.getChildren().addAll(subtotalRow, new Separator(), totalRow);
        return summary;
    }

    private VBox createPaymentSection() {
        VBox payment = new VBox(10);

        Label paymentLabel = new Label("Payment Method:");
        paymentLabel.setStyle("-fx-font-weight: bold;");

        ComboBox<String> paymentCombo = new ComboBox<>();
        paymentCombo.getItems().addAll("Cash", "Credit Card", "Debit Card", "Mobile Payment");
        paymentCombo.setValue("Cash");
        paymentCombo.setStyle(getImprovedInputStyle());

        Label amountLabel = new Label("Amount Received:");
        TextField amountField = new TextField();
        amountField.setPromptText("0.00");
        amountField.setStyle(getImprovedInputStyle());

        payment.getChildren().addAll(paymentLabel, paymentCombo, amountLabel, amountField);
        return payment;
    }

    private VBox createCartActions() {
        VBox actions = new VBox(12); // Increased spacing for better touch targets

        Button processPaymentBtn = new Button("Process Payment");
        processPaymentBtn.setStyle(getImprovedButtonStyle() + "-fx-pref-height: 50;");
        processPaymentBtn.setMaxWidth(Double.MAX_VALUE);

        Button multiplePaymentBtn = new Button("Multiple Payment Methods");
        multiplePaymentBtn.setStyle(getImprovedSecondaryButtonStyle() + "-fx-pref-height: 45;");
        multiplePaymentBtn.setMaxWidth(Double.MAX_VALUE);

        HBox quickActions = new HBox(12);
        Button holdBtn = new Button("Hold");
        Button voidBtn = new Button("Void");
        holdBtn.setStyle(getImprovedSecondaryButtonStyle());
        voidBtn.setStyle(getImprovedSecondaryButtonStyle());
        HBox.setHgrow(holdBtn, Priority.ALWAYS);
        HBox.setHgrow(voidBtn, Priority.ALWAYS);
        holdBtn.setMaxWidth(Double.MAX_VALUE);
        voidBtn.setMaxWidth(Double.MAX_VALUE);
        quickActions.getChildren().addAll(holdBtn, voidBtn);

        actions.getChildren().addAll(processPaymentBtn, multiplePaymentBtn, quickActions);
        return actions;
    }

    private HBox createStatusBar() {
        HBox statusBar = new HBox(10);
        statusBar.setPadding(new Insets(10));
        statusBar.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; "
                + "-fx-border-width: 1 0 0 0;");

        Label statusLabel = new Label("Ready for new transaction");
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        Label itemCountLabel = new Label("Items: 3");

        statusBar.getChildren().addAll(statusLabel, spacer, itemCountLabel);
        return statusBar;
    }

    private Separator createSeparator() {
        Separator separator = new Separator();
        separator.setStyle("-fx-background-color: linear-gradient(to right, transparent 0%, #ced4da 15%, #ced4da 85%, transparent 100%); "
                + "-fx-pref-height: 3; -fx-max-height: 3; -fx-opacity: 0.8;");
        return separator;
    }

    // Styling methods
    private String getImprovedCardStyle() {
        return "-fx-background-color: white; "
                + "-fx-background-radius: 15; "
                + "-fx-border-radius: 15; "
                + "-fx-border-color: #dee2e6; "
                + "-fx-border-width: 1; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 12, 0, 0, 3);";
    }

    private String getImprovedButtonStyle() {
        return "-fx-background-color: linear-gradient(to bottom, #007bff 0%, #0056b3 100%); "
                + "-fx-text-fill: white; "
                + "-fx-font-weight: bold; "
                + "-fx-background-radius: 10; "
                + "-fx-min-height: 44; "
                + "-fx-padding: 12 20; "
                + "-fx-border-radius: 10; "
                + "-fx-cursor: hand; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,123,255,0.3), 4, 0, 0, 2);";
    }

    private String getImprovedSecondaryButtonStyle() {
        return "-fx-background-color: linear-gradient(to bottom, #6c757d 0%, #545b62 100%); "
                + "-fx-text-fill: white; "
                + "-fx-font-weight: bold; "
                + "-fx-background-radius: 10; "
                + "-fx-min-height: 44; "
                + "-fx-padding: 12 20; "
                + "-fx-border-radius: 10; "
                + "-fx-cursor: hand;";
    }

    private String getImprovedInputStyle() {
        return "-fx-background-color: white; "
                + "-fx-border-color: #ced4da; "
                + "-fx-border-width: 1; "
                + "-fx-border-radius: 8; "
                + "-fx-background-radius: 8; "
                + "-fx-padding: 10 12; "
                + "-fx-font-size: 14px;";
    }

    private String getBasicCSS() {
        return ".label { -fx-font-family: 'Segoe UI', sans-serif; }";
    }

    public static void main(String[] args) {
        launch(args);
    }
}
