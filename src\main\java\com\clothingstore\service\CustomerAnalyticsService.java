package com.clothingstore.service;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.model.Product;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Service for comprehensive customer analytics and statistics
 */
public class CustomerAnalyticsService {
    
    private static CustomerAnalyticsService instance;
    private final CustomerDAO customerDAO;
    private final TransactionDAO transactionDAO;
    private final ProductDAO productDAO;
    
    private CustomerAnalyticsService() {
        this.customerDAO = CustomerDAO.getInstance();
        this.transactionDAO = TransactionDAO.getInstance();
        this.productDAO = ProductDAO.getInstance();
    }
    
    public static synchronized CustomerAnalyticsService getInstance() {
        if (instance == null) {
            instance = new CustomerAnalyticsService();
        }
        return instance;
    }
    
    /**
     * Comprehensive customer analytics data
     */
    public static class CustomerAnalytics {
        private Customer customer;
        private int totalTransactions;
        private BigDecimal totalSpent;
        private BigDecimal averageTransactionAmount;
        private LocalDateTime firstPurchaseDate;
        private LocalDateTime lastPurchaseDate;
        private String preferredPaymentMethod;
        private List<String> topProductCategories;
        private int loyaltyPoints;
        private String membershipLevel;
        private List<Transaction> recentTransactions;
        
        // Getters and setters
        public Customer getCustomer() { return customer; }
        public void setCustomer(Customer customer) { this.customer = customer; }
        
        public int getTotalTransactions() { return totalTransactions; }
        public void setTotalTransactions(int totalTransactions) { this.totalTransactions = totalTransactions; }
        
        public BigDecimal getTotalSpent() { return totalSpent; }
        public void setTotalSpent(BigDecimal totalSpent) { this.totalSpent = totalSpent; }
        
        public BigDecimal getAverageTransactionAmount() { return averageTransactionAmount; }
        public void setAverageTransactionAmount(BigDecimal averageTransactionAmount) { this.averageTransactionAmount = averageTransactionAmount; }
        
        public LocalDateTime getFirstPurchaseDate() { return firstPurchaseDate; }
        public void setFirstPurchaseDate(LocalDateTime firstPurchaseDate) { this.firstPurchaseDate = firstPurchaseDate; }
        
        public LocalDateTime getLastPurchaseDate() { return lastPurchaseDate; }
        public void setLastPurchaseDate(LocalDateTime lastPurchaseDate) { this.lastPurchaseDate = lastPurchaseDate; }
        
        public String getPreferredPaymentMethod() { return preferredPaymentMethod; }
        public void setPreferredPaymentMethod(String preferredPaymentMethod) { this.preferredPaymentMethod = preferredPaymentMethod; }
        
        public List<String> getTopProductCategories() { return topProductCategories; }
        public void setTopProductCategories(List<String> topProductCategories) { this.topProductCategories = topProductCategories; }
        
        public int getLoyaltyPoints() { return loyaltyPoints; }
        public void setLoyaltyPoints(int loyaltyPoints) { this.loyaltyPoints = loyaltyPoints; }
        
        public String getMembershipLevel() { return membershipLevel; }
        public void setMembershipLevel(String membershipLevel) { this.membershipLevel = membershipLevel; }
        
        public List<Transaction> getRecentTransactions() { return recentTransactions; }
        public void setRecentTransactions(List<Transaction> recentTransactions) { this.recentTransactions = recentTransactions; }
        
        public String getFormattedTotalSpent() {
            return totalSpent != null ? String.format("$%.2f", totalSpent.doubleValue()) : "$0.00";
        }
        
        public String getFormattedAverageTransaction() {
            return averageTransactionAmount != null ? String.format("$%.2f", averageTransactionAmount.doubleValue()) : "$0.00";
        }
        
        public String getFormattedFirstPurchase() {
            return firstPurchaseDate != null ? firstPurchaseDate.format(DateTimeFormatter.ofPattern("MMM dd, yyyy")) : "N/A";
        }
        
        public String getFormattedLastPurchase() {
            return lastPurchaseDate != null ? lastPurchaseDate.format(DateTimeFormatter.ofPattern("MMM dd, yyyy")) : "N/A";
        }
        
        public String getTopCategoriesString() {
            return topProductCategories != null && !topProductCategories.isEmpty() 
                ? String.join(", ", topProductCategories.subList(0, Math.min(3, topProductCategories.size())))
                : "N/A";
        }
    }
    
    /**
     * Get comprehensive analytics for a specific customer
     */
    public CustomerAnalytics getCustomerAnalytics(Long customerId) throws SQLException {
        Customer customer = customerDAO.findById(customerId).orElse(null);
        if (customer == null) {
            return null;
        }
        
        CustomerAnalytics analytics = new CustomerAnalytics();
        analytics.setCustomer(customer);
        
        // Get all transactions for this customer
        List<Transaction> customerTransactions = transactionDAO.findByCustomerId(customerId);
        List<Transaction> completedTransactions = customerTransactions.stream()
            .filter(t -> "COMPLETED".equals(t.getStatus()))
            .collect(Collectors.toList());
        
        // Basic transaction statistics
        analytics.setTotalTransactions(completedTransactions.size());
        
        if (!completedTransactions.isEmpty()) {
            // Calculate total spent and average
            BigDecimal totalSpent = completedTransactions.stream()
                .map(Transaction::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            analytics.setTotalSpent(totalSpent);
            
            BigDecimal averageAmount = totalSpent.divide(
                BigDecimal.valueOf(completedTransactions.size()), 2, RoundingMode.HALF_UP);
            analytics.setAverageTransactionAmount(averageAmount);
            
            // First and last purchase dates
            completedTransactions.sort(Comparator.comparing(Transaction::getTransactionDate));
            analytics.setFirstPurchaseDate(completedTransactions.get(0).getTransactionDate());
            analytics.setLastPurchaseDate(completedTransactions.get(completedTransactions.size() - 1).getTransactionDate());
            
            // Preferred payment method
            Map<String, Long> paymentMethodCounts = completedTransactions.stream()
                .collect(Collectors.groupingBy(Transaction::getPaymentMethod, Collectors.counting()));
            String preferredPayment = paymentMethodCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("N/A");
            analytics.setPreferredPaymentMethod(preferredPayment);
            
            // Top product categories
            analytics.setTopProductCategories(getTopProductCategories(completedTransactions));
            
            // Recent transactions (last 5)
            List<Transaction> recentTransactions = completedTransactions.stream()
                .sorted(Comparator.comparing(Transaction::getTransactionDate).reversed())
                .limit(5)
                .collect(Collectors.toList());
            analytics.setRecentTransactions(recentTransactions);
        } else {
            analytics.setTotalSpent(BigDecimal.ZERO);
            analytics.setAverageTransactionAmount(BigDecimal.ZERO);
            analytics.setPreferredPaymentMethod("N/A");
            analytics.setTopProductCategories(new ArrayList<>());
            analytics.setRecentTransactions(new ArrayList<>());
        }
        
        // Customer-specific data
        analytics.setLoyaltyPoints(customer.getLoyaltyPoints());
        analytics.setMembershipLevel(customer.getMembershipLevel());
        
        return analytics;
    }
    
    /**
     * Get top product categories for customer transactions
     */
    private List<String> getTopProductCategories(List<Transaction> transactions) throws SQLException {
        Map<String, Integer> categoryQuantities = new HashMap<>();
        
        for (Transaction transaction : transactions) {
            for (TransactionItem item : transaction.getItems()) {
                Optional<Product> productOpt = productDAO.findById(item.getProductId());
                if (productOpt.isPresent()) {
                    String category = productOpt.get().getCategory();
                    categoryQuantities.merge(category, item.getQuantity(), Integer::sum);
                }
            }
        }
        
        return categoryQuantities.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
    }
    
    /**
     * Search customers with enhanced criteria
     */
    public List<Customer> searchCustomersEnhanced(String searchTerm) throws SQLException {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return customerDAO.searchCustomers(searchTerm.trim());
    }
    
    /**
     * Get customer suggestions for autocomplete
     */
    public List<String> getCustomerSuggestions(String searchTerm, int limit) throws SQLException {
        List<Customer> customers = searchCustomersEnhanced(searchTerm);
        return customers.stream()
            .limit(limit)
            .map(c -> c.getFirstName() + " " + c.getLastName() + " (" + c.getPhone() + ")")
            .collect(Collectors.toList());
    }
}
