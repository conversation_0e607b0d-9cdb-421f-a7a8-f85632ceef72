package com.clothingstore.dao;

import com.clothingstore.model.MissingItem;
import com.clothingstore.model.Product;
import com.clothingstore.database.DatabaseManager;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Data Access Object for MissingItem operations
 */
public class MissingItemDAO {

    private static MissingItemDAO instance;
    private final ProductDAO productDAO;

    private MissingItemDAO() {
        this.productDAO = ProductDAO.getInstance();
        createTableIfNotExists();
    }

    public static synchronized MissingItemDAO getInstance() {
        if (instance == null) {
            instance = new MissingItemDAO();
        }
        return instance;
    }

    private void createTableIfNotExists() {
        String sql = "CREATE TABLE IF NOT EXISTS missing_items (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "product_id INTEGER NOT NULL," +
                "quantity INTEGER NOT NULL," +
                "reason TEXT NOT NULL," +
                "report_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "status TEXT DEFAULT 'REPORTED'," +
                "reported_by TEXT," +
                "notes TEXT," +
                "resolved_date TIMESTAMP," +
                "resolved_by TEXT," +
                "FOREIGN KEY (product_id) REFERENCES products (id)" +
                ")";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
        } catch (SQLException e) {
            throw new RuntimeException("Failed to create missing_items table", e);
        }
    }

    public MissingItem save(MissingItem missingItem) throws SQLException {
        if (missingItem.getId() == null) {
            return insert(missingItem);
        } else {
            return update(missingItem);
        }
    }

    private MissingItem insert(MissingItem missingItem) throws SQLException {
        String sql = "INSERT INTO missing_items (product_id, quantity, reason, report_date, status, reported_by, notes) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            stmt.setLong(1, missingItem.getProduct().getId());
            stmt.setInt(2, missingItem.getQuantity());
            stmt.setString(3, missingItem.getReason());
            stmt.setTimestamp(4, Timestamp.valueOf(missingItem.getReportDate()));
            stmt.setString(5, missingItem.getStatus());
            stmt.setString(6, missingItem.getReportedBy());
            stmt.setString(7, missingItem.getNotes());

            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating missing item failed, no rows affected.");
            }

            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    missingItem.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating missing item failed, no ID obtained.");
                }
            }
        }

        return missingItem;
    }

    private MissingItem update(MissingItem missingItem) throws SQLException {
        String sql = "UPDATE missing_items " +
                "SET product_id = ?, quantity = ?, reason = ?, status = ?, " +
                "reported_by = ?, notes = ?, resolved_date = ?, resolved_by = ? " +
                "WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, missingItem.getProduct().getId());
            stmt.setInt(2, missingItem.getQuantity());
            stmt.setString(3, missingItem.getReason());
            stmt.setString(4, missingItem.getStatus());
            stmt.setString(5, missingItem.getReportedBy());
            stmt.setString(6, missingItem.getNotes());
            
            if (missingItem.getResolvedDate() != null) {
                stmt.setTimestamp(7, Timestamp.valueOf(missingItem.getResolvedDate()));
            } else {
                stmt.setNull(7, Types.TIMESTAMP);
            }
            
            stmt.setString(8, missingItem.getResolvedBy());
            stmt.setLong(9, missingItem.getId());

            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating missing item failed, no rows affected.");
            }
        }

        return missingItem;
    }

    public Optional<MissingItem> findById(Long id) throws SQLException {
        String sql = "SELECT mi.*, p.name as product_name, p.sku, p.price, p.stock_quantity, p.category " +
                "FROM missing_items mi " +
                "JOIN products p ON mi.product_id = p.id " +
                "WHERE mi.id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToMissingItem(rs));
                }
            }
        }

        return Optional.empty();
    }

    public List<MissingItem> findAll() throws SQLException {
        String sql = "SELECT mi.*, p.name as product_name, p.sku, p.price, p.stock_quantity, p.category " +
                "FROM missing_items mi " +
                "JOIN products p ON mi.product_id = p.id " +
                "ORDER BY mi.report_date DESC";

        List<MissingItem> missingItems = new ArrayList<>();
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                missingItems.add(mapResultSetToMissingItem(rs));
            }
        }

        return missingItems;
    }

    public List<MissingItem> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT mi.*, p.name as product_name, p.sku, p.price, p.stock_quantity, p.category " +
                "FROM missing_items mi " +
                "JOIN products p ON mi.product_id = p.id " +
                "WHERE mi.report_date BETWEEN ? AND ? " +
                "ORDER BY mi.report_date DESC";

        List<MissingItem> missingItems = new ArrayList<>();
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setTimestamp(1, Timestamp.valueOf(startDate));
            stmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    missingItems.add(mapResultSetToMissingItem(rs));
                }
            }
        }

        return missingItems;
    }

    public List<MissingItem> findByStatus(String status) throws SQLException {
        String sql = "SELECT mi.*, p.name as product_name, p.sku, p.price, p.stock_quantity, p.category " +
                "FROM missing_items mi " +
                "JOIN products p ON mi.product_id = p.id " +
                "WHERE mi.status = ? " +
                "ORDER BY mi.report_date DESC";

        List<MissingItem> missingItems = new ArrayList<>();
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, status);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    missingItems.add(mapResultSetToMissingItem(rs));
                }
            }
        }

        return missingItems;
    }

    public void delete(Long id) throws SQLException {
        String sql = "DELETE FROM missing_items WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, id);
            stmt.executeUpdate();
        }
    }

    private MissingItem mapResultSetToMissingItem(ResultSet rs) throws SQLException {
        // Create Product object
        Product product = new Product();
        product.setId(rs.getLong("product_id"));
        product.setName(rs.getString("product_name"));
        product.setSku(rs.getString("sku"));
        product.setPrice(rs.getBigDecimal("price"));
        product.setStockQuantity(rs.getInt("stock_quantity"));
        product.setCategory(rs.getString("category"));

        // Create MissingItem object
        MissingItem missingItem = new MissingItem();
        missingItem.setId(rs.getLong("id"));
        missingItem.setProduct(product);
        missingItem.setQuantity(rs.getInt("quantity"));
        missingItem.setReason(rs.getString("reason"));
        missingItem.setReportDate(rs.getTimestamp("report_date").toLocalDateTime());
        missingItem.setStatus(rs.getString("status"));
        missingItem.setReportedBy(rs.getString("reported_by"));
        missingItem.setNotes(rs.getString("notes"));

        Timestamp resolvedDate = rs.getTimestamp("resolved_date");
        if (resolvedDate != null) {
            missingItem.setResolvedDate(resolvedDate.toLocalDateTime());
        }
        missingItem.setResolvedBy(rs.getString("resolved_by"));

        return missingItem;
    }
}
