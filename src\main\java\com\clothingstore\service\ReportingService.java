package com.clothingstore.service;

import com.clothingstore.dao.*;
import com.clothingstore.model.*;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Service for generating various business reports
 */
public class ReportingService {

    private static final Logger LOGGER = Logger.getLogger(ReportingService.class.getName());
    private static ReportingService instance;

    private final TransactionDAO transactionDAO;
    private final ProductDAO productDAO;
    private final CustomerDAO customerDAO;
    private final SalesAnalyticsService analyticsService;
    private final TaxService taxService;

    private ReportingService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.productDAO = ProductDAO.getInstance();
        this.customerDAO = CustomerDAO.getInstance();
        this.analyticsService = SalesAnalyticsService.getInstance();
        this.taxService = TaxService.getInstance();
    }

    public static synchronized ReportingService getInstance() {
        if (instance == null) {
            instance = new ReportingService();
        }
        return instance;
    }

    /**
     * Generate a report based on type and parameters
     */
    public Report generateReport(ReportType type, LocalDateTime startDate, LocalDateTime endDate,
            Map<String, Object> parameters) {
        try {
            Report report = new Report(type.getDisplayName(), type);
            report.setPeriodStart(startDate);
            report.setPeriodEnd(endDate);
            report.setParameters(parameters);
            report.setGeneratedBy("System"); // In a real app, would use current user

            switch (type) {
                case SALES_SUMMARY:
                    return generateSalesSummaryReport(report, startDate, endDate);
                case PRODUCT_PERFORMANCE:
                    return generateProductPerformanceReport(report, startDate, endDate);
                case CUSTOMER_ANALYSIS:
                    return generateCustomerAnalysisReport(report, startDate, endDate);
                case INVENTORY_VALUATION:
                    return generateInventoryValuationReport(report);
                case PROFIT_LOSS:
                    return generateProfitLossReport(report, startDate, endDate);
                case TAX_REPORT:
                    return generateTaxReport(report, startDate, endDate);
                case LOW_STOCK_REPORT:
                    return generateLowStockReport(report);
                case TOP_CUSTOMERS:
                    return generateTopCustomersReport(report, startDate, endDate);
                case PAYMENT_METHOD_ANALYSIS:
                    return generatePaymentMethodReport(report, startDate, endDate);
                case CATEGORY_PERFORMANCE:
                    return generateCategoryPerformanceReport(report, startDate, endDate);
                default:
                    report.setSummary("Report type not implemented yet");
                    return report;
            }

        } catch (Exception e) {
            LOGGER.severe("Error generating report: " + e.getMessage());
            Report errorReport = new Report("Error Report", type);
            errorReport.setSummary("Error generating report: " + e.getMessage());
            return errorReport;
        }
    }

    /**
     * Generate Sales Summary Report
     */
    private Report generateSalesSummaryReport(Report report, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            SalesMetrics metrics = analyticsService.generateSalesMetrics(startDate, endDate);

            List<ReportSection> sections = new ArrayList<>();

            // Summary section
            ReportSection summarySection = new ReportSection("Sales Summary");
            StringBuilder summary = new StringBuilder();
            summary.append(String.format("Total Revenue: $%.2f\n", metrics.getTotalRevenue()));
            summary.append(String.format("Total Transactions: %d\n", metrics.getTotalTransactions()));
            summary.append(String.format("Average Order Value: $%.2f\n", metrics.getAverageOrderValue()));
            summary.append(String.format("Total Items Sold: %d\n", metrics.getTotalItemsSold()));
            summary.append(String.format("Gross Profit: $%.2f\n", metrics.getGrossProfit()));
            summary.append(String.format("Profit Margin: %.2f%%", metrics.getProfitMargin()));
            summarySection.setContent(summary.toString());
            sections.add(summarySection);

            // Top categories section
            if (metrics.getTopSellingCategories() != null && !metrics.getTopSellingCategories().isEmpty()) {
                ReportSection categoriesSection = new ReportSection("Top Selling Categories");
                categoriesSection.setHeaders(Arrays.asList("Category", "Revenue"));

                List<List<String>> categoryData = new ArrayList<>();
                for (Map.Entry<String, BigDecimal> entry : metrics.getTopSellingCategories().entrySet()) {
                    categoryData.add(Arrays.asList(entry.getKey(), String.format("$%.2f", entry.getValue())));
                }
                categoriesSection.setData(categoryData);
                sections.add(categoriesSection);
            }

            report.setSections(sections);
            report.setHasData(metrics.hasData());
            report.setSummary(String.format("Sales summary for %s showing $%.2f in revenue from %d transactions",
                    report.getFormattedPeriod(), metrics.getTotalRevenue(), metrics.getTotalTransactions()));

            return report;

        } catch (Exception e) {
            LOGGER.severe("Error generating sales summary report: " + e.getMessage());
            report.setSummary("Error generating sales summary report");
            return report;
        }
    }

    /**
     * Generate Product Performance Report
     */
    private Report generateProductPerformanceReport(Report report, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            List<Transaction> transactions = getTransactionsForPeriod(startDate, endDate);

            // Calculate product performance metrics
            Map<String, ProductPerformance> productMetrics = calculateProductPerformance(transactions);

            List<ReportSection> sections = new ArrayList<>();

            // Top performing products
            ReportSection topProductsSection = new ReportSection("Top Performing Products");
            topProductsSection.setHeaders(Arrays.asList("Product", "Quantity Sold", "Revenue", "Profit Margin"));

            List<List<String>> productData = productMetrics.entrySet().stream()
                    .sorted((e1, e2) -> e2.getValue().getRevenue().compareTo(e1.getValue().getRevenue()))
                    .limit(20)
                    .map(entry -> {
                        ProductPerformance perf = entry.getValue();
                        return Arrays.asList(
                                entry.getKey(),
                                String.valueOf(perf.getQuantitySold()),
                                String.format("$%.2f", perf.getRevenue()),
                                String.format("%.1f%%", perf.getProfitMargin())
                        );
                    })
                    .collect(Collectors.toList());

            topProductsSection.setData(productData);
            sections.add(topProductsSection);

            report.setSections(sections);
            report.setHasData(!productMetrics.isEmpty());
            report.setSummary(String.format("Product performance analysis for %d products during %s",
                    productMetrics.size(), report.getFormattedPeriod()));

            return report;

        } catch (Exception e) {
            LOGGER.severe("Error generating product performance report: " + e.getMessage());
            report.setSummary("Error generating product performance report");
            return report;
        }
    }

    /**
     * Generate Low Stock Report
     */
    private Report generateLowStockReport(Report report) {
        try {
            List<Product> allProducts = productDAO.findAll();
            List<Product> lowStockProducts = allProducts.stream()
                    .filter(Product::isLowStock)
                    .sorted(Comparator.comparing(Product::getStockQuantity))
                    .collect(Collectors.toList());

            List<ReportSection> sections = new ArrayList<>();

            ReportSection lowStockSection = new ReportSection("Low Stock Products");
            lowStockSection.setHeaders(Arrays.asList("SKU", "Product Name", "Category", "Current Stock", "Min Level", "Reorder Qty"));

            List<List<String>> lowStockData = lowStockProducts.stream()
                    .map(product -> Arrays.asList(
                    product.getSku(),
                    product.getName(),
                    product.getCategory(),
                    String.valueOf(product.getStockQuantity()),
                    String.valueOf(product.getMinStockLevel()),
                    String.valueOf(product.getReorderQuantity())
            ))
                    .collect(Collectors.toList());

            lowStockSection.setData(lowStockData);
            sections.add(lowStockSection);

            report.setSections(sections);
            report.setHasData(!lowStockProducts.isEmpty());
            report.setSummary(String.format("Found %d products with low stock levels requiring attention",
                    lowStockProducts.size()));

            return report;

        } catch (Exception e) {
            LOGGER.severe("Error generating low stock report: " + e.getMessage());
            report.setSummary("Error generating low stock report");
            return report;
        }
    }

    /**
     * Generate Inventory Valuation Report
     */
    private Report generateInventoryValuationReport(Report report) {
        try {
            List<Product> allProducts = productDAO.findAll();

            BigDecimal totalRetailValue = BigDecimal.ZERO;
            BigDecimal totalCostValue = BigDecimal.ZERO;
            int totalItems = 0;

            List<ReportSection> sections = new ArrayList<>();

            ReportSection valuationSection = new ReportSection("Inventory Valuation");
            valuationSection.setHeaders(Arrays.asList("SKU", "Product", "Qty", "Cost Price", "Retail Price", "Cost Value", "Retail Value"));

            List<List<String>> valuationData = new ArrayList<>();

            for (Product product : allProducts) {
                if (product.isActive() && product.getStockQuantity() > 0) {
                    BigDecimal costPrice = product.getCostPrice() != null ? product.getCostPrice() : BigDecimal.ZERO;
                    BigDecimal retailPrice = product.getPrice();
                    int quantity = product.getStockQuantity();

                    BigDecimal costValue = costPrice.multiply(new BigDecimal(quantity));
                    BigDecimal retailValue = retailPrice.multiply(new BigDecimal(quantity));

                    totalCostValue = totalCostValue.add(costValue);
                    totalRetailValue = totalRetailValue.add(retailValue);
                    totalItems += quantity;

                    valuationData.add(Arrays.asList(
                            product.getSku(),
                            product.getName(),
                            String.valueOf(quantity),
                            String.format("$%.2f", costPrice),
                            String.format("$%.2f", retailPrice),
                            String.format("$%.2f", costValue),
                            String.format("$%.2f", retailValue)
                    ));
                }
            }

            valuationSection.setData(valuationData);
            sections.add(valuationSection);

            // Summary section
            ReportSection summarySection = new ReportSection("Valuation Summary");
            StringBuilder summary = new StringBuilder();
            summary.append(String.format("Total Items in Stock: %d\n", totalItems));
            summary.append(String.format("Total Cost Value: $%.2f\n", totalCostValue));
            summary.append(String.format("Total Retail Value: $%.2f\n", totalRetailValue));
            summary.append(String.format("Potential Profit: $%.2f\n", totalRetailValue.subtract(totalCostValue)));

            if (totalCostValue.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal markup = totalRetailValue.subtract(totalCostValue)
                        .divide(totalCostValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                summary.append(String.format("Average Markup: %.1f%%", markup));
            }

            summarySection.setContent(summary.toString());
            sections.add(summarySection);

            report.setSections(sections);
            report.setHasData(totalItems > 0);
            report.setSummary(String.format("Inventory valuation showing %d items worth $%.2f at cost, $%.2f at retail",
                    totalItems, totalCostValue, totalRetailValue));

            return report;

        } catch (Exception e) {
            LOGGER.severe("Error generating inventory valuation report: " + e.getMessage());
            report.setSummary("Error generating inventory valuation report");
            return report;
        }
    }

    /**
     * Get transactions for a specific period
     */
    private List<Transaction> getTransactionsForPeriod(LocalDateTime startDate, LocalDateTime endDate) {
        try {
            List<Transaction> allTransactions = transactionDAO.findAll();

            return allTransactions.stream()
                    .filter(t -> isTransactionInPeriod(t, startDate, endDate))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            LOGGER.severe("Error getting transactions for period: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Check if transaction is within the specified period
     */
    private boolean isTransactionInPeriod(Transaction transaction, LocalDateTime startDate, LocalDateTime endDate) {
        LocalDateTime transactionDate = transaction.getTransactionDate();

        if (startDate != null && transactionDate.isBefore(startDate)) {
            return false;
        }

        if (endDate != null && transactionDate.isAfter(endDate)) {
            return false;
        }

        return true;
    }

    /**
     * Calculate product performance metrics
     */
    private Map<String, ProductPerformance> calculateProductPerformance(List<Transaction> transactions) {
        Map<String, ProductPerformance> metrics = new HashMap<>();

        for (Transaction transaction : transactions) {
            for (TransactionItem item : transaction.getItems()) {
                if (item.getProduct() != null) {
                    String productName = item.getProduct().getName();

                    ProductPerformance perf = metrics.computeIfAbsent(productName,
                            k -> new ProductPerformance(productName));

                    perf.addSale(item.getQuantity(), item.getLineTotal(),
                            item.getProduct().getCostPrice() != null
                            ? item.getProduct().getCostPrice().multiply(new BigDecimal(item.getQuantity()))
                            : BigDecimal.ZERO);
                }
            }
        }

        return metrics;
    }

    // Placeholder methods for other report types
    private Report generateCustomerAnalysisReport(Report report, LocalDateTime startDate, LocalDateTime endDate) {
        report.setSummary("Customer analysis report - Implementation pending");
        return report;
    }

    private Report generateProfitLossReport(Report report, LocalDateTime startDate, LocalDateTime endDate) {
        report.setSummary("Profit & Loss report - Implementation pending");
        return report;
    }

    private Report generateTaxReport(Report report, LocalDateTime startDate, LocalDateTime endDate) {
        report.setSummary("Tax report - Implementation pending");
        return report;
    }

    private Report generateTopCustomersReport(Report report, LocalDateTime startDate, LocalDateTime endDate) {
        report.setSummary("Top customers report - Implementation pending");
        return report;
    }

    private Report generatePaymentMethodReport(Report report, LocalDateTime startDate, LocalDateTime endDate) {
        report.setSummary("Payment method analysis - Implementation pending");
        return report;
    }

    private Report generateCategoryPerformanceReport(Report report, LocalDateTime startDate, LocalDateTime endDate) {
        report.setSummary("Category performance report - Implementation pending");
        return report;
    }

    /**
     * Export report to specified format
     */
    public String exportReport(Report report, ReportFormat format, String filePath) {
        try {
            switch (format) {
                case CSV:
                    return exportToCSV(report, filePath);
                case HTML:
                    return exportToHTML(report, filePath);
                case JSON:
                    return exportToJSON(report, filePath);
                default:
                    throw new UnsupportedOperationException("Export format not supported: " + format);
            }
        } catch (Exception e) {
            LOGGER.severe("Error exporting report: " + e.getMessage());
            return null;
        }
    }

    /**
     * Export report to CSV format
     */
    private String exportToCSV(Report report, String filePath) throws IOException {
        StringBuilder csv = new StringBuilder();

        // Add report header
        csv.append("Report: ").append(report.getTitle()).append("\n");
        csv.append("Generated: ").append(report.getFormattedGeneratedAt()).append("\n");
        csv.append("Period: ").append(report.getFormattedPeriod()).append("\n\n");

        // Add sections
        if (report.getSections() != null) {
            for (ReportSection section : report.getSections()) {
                csv.append(section.getTitle()).append("\n");

                if (section.hasTableData()) {
                    // Add headers
                    csv.append(String.join(",", section.getHeaders())).append("\n");

                    // Add data rows
                    for (List<String> row : section.getData()) {
                        csv.append(String.join(",", row)).append("\n");
                    }
                } else if (section.getContent() != null) {
                    csv.append(section.getContent().replace("\n", " ")).append("\n");
                }

                csv.append("\n");
            }
        }

        // Write to file
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write(csv.toString());
        }

        return filePath;
    }

    /**
     * Export report to HTML format
     */
    private String exportToHTML(Report report, String filePath) throws IOException {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n<html>\n<head>\n");
        html.append("<title>").append(report.getTitle()).append("</title>\n");
        html.append("<style>body{font-family:Arial,sans-serif;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;}</style>\n");
        html.append("</head>\n<body>\n");

        html.append("<h1>").append(report.getTitle()).append("</h1>\n");
        html.append("<p><strong>Generated:</strong> ").append(report.getFormattedGeneratedAt()).append("</p>\n");
        html.append("<p><strong>Period:</strong> ").append(report.getFormattedPeriod()).append("</p>\n");

        if (report.getSections() != null) {
            for (ReportSection section : report.getSections()) {
                html.append("<h2>").append(section.getTitle()).append("</h2>\n");

                if (section.hasTableData()) {
                    html.append("<table>\n<thead>\n<tr>\n");
                    for (String header : section.getHeaders()) {
                        html.append("<th>").append(header).append("</th>\n");
                    }
                    html.append("</tr>\n</thead>\n<tbody>\n");

                    for (List<String> row : section.getData()) {
                        html.append("<tr>\n");
                        for (String cell : row) {
                            html.append("<td>").append(cell).append("</td>\n");
                        }
                        html.append("</tr>\n");
                    }
                    html.append("</tbody>\n</table>\n");
                } else if (section.getContent() != null) {
                    html.append("<pre>").append(section.getContent()).append("</pre>\n");
                }
            }
        }

        html.append("</body>\n</html>");

        // Write to file
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write(html.toString());
        }

        return filePath;
    }

    /**
     * Export report to JSON format
     */
    private String exportToJSON(Report report, String filePath) throws IOException {
        // Simple JSON export - in a real app would use a JSON library
        StringBuilder json = new StringBuilder();
        json.append("{\n");
        json.append("  \"title\": \"").append(report.getTitle()).append("\",\n");
        json.append("  \"generated\": \"").append(report.getFormattedGeneratedAt()).append("\",\n");
        json.append("  \"period\": \"").append(report.getFormattedPeriod()).append("\",\n");
        json.append("  \"summary\": \"").append(report.getSummary() != null ? report.getSummary() : "").append("\"\n");
        json.append("}");

        // Write to file
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write(json.toString());
        }

        return filePath;
    }
}

/**
 * Class to track product performance metrics
 */
class ProductPerformance {

    private final String productName;
    private int quantitySold;
    private BigDecimal revenue;
    private BigDecimal cost;

    public ProductPerformance(String productName) {
        this.productName = productName;
        this.quantitySold = 0;
        this.revenue = BigDecimal.ZERO;
        this.cost = BigDecimal.ZERO;
    }

    public void addSale(int quantity, BigDecimal saleAmount, BigDecimal costAmount) {
        this.quantitySold += quantity;
        this.revenue = this.revenue.add(saleAmount);
        this.cost = this.cost.add(costAmount);
    }

    public String getProductName() {
        return productName;
    }

    public int getQuantitySold() {
        return quantitySold;
    }

    public BigDecimal getRevenue() {
        return revenue;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public BigDecimal getProfit() {
        return revenue.subtract(cost);
    }

    public BigDecimal getProfitMargin() {
        if (revenue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return getProfit().divide(revenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }
}
