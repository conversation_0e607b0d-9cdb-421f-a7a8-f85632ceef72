package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.ReturnExchange;
import com.clothingstore.model.ReturnExchangeItem;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * Data Access Object for Return/Exchange operations
 */
public class ReturnExchangeDAO {
    private static final Logger LOGGER = Logger.getLogger(ReturnExchangeDAO.class.getName());
    private static ReturnExchangeDAO instance;

    private ReturnExchangeDAO() {
        // Private constructor for singleton
    }

    public static synchronized ReturnExchangeDAO getInstance() {
        if (instance == null) {
            instance = new ReturnExchangeDAO();
        }
        return instance;
    }

    /**
     * Create a new return/exchange record
     */
    public ReturnExchange create(ReturnExchange returnExchange) throws SQLException {
        String sql = "INSERT INTO return_exchanges (return_number, original_transaction_id, " +
                "original_transaction_number, customer_id, customer_name, type, reason, " +
                "total_amount, refund_amount, exchange_amount, payment_method, notes) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, returnExchange.getReturnNumber());
            pstmt.setLong(2, returnExchange.getOriginalTransactionId());
            pstmt.setString(3, returnExchange.getOriginalTransactionNumber());
            pstmt.setObject(4, returnExchange.getCustomerId());
            pstmt.setString(5, returnExchange.getCustomerName());
            pstmt.setString(6, returnExchange.getType());
            pstmt.setString(7, returnExchange.getReason());
            pstmt.setBigDecimal(8, returnExchange.getTotalAmount());
            pstmt.setBigDecimal(9, returnExchange.getRefundAmount());
            pstmt.setBigDecimal(10, returnExchange.getExchangeAmount());
            pstmt.setString(11, returnExchange.getPaymentMethod());
            pstmt.setString(12, returnExchange.getNotes());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating return/exchange failed, no rows affected.");
            }

            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    returnExchange.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating return/exchange failed, no ID obtained.");
                }
            }

            LOGGER.info("Created return/exchange: " + returnExchange.getReturnNumber());
            return returnExchange;
        }
    }

    /**
     * Find return/exchange by ID
     */
    public ReturnExchange findById(Long id) throws SQLException {
        String sql = "SELECT * FROM return_exchanges WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    ReturnExchange returnExchange = mapResultSetToReturnExchange(rs);
                    returnExchange.setItems(findItemsByReturnExchangeId(id));
                    return returnExchange;
                }
            }
        }
        return null;
    }

    /**
     * Find return/exchange by return number
     */
    public ReturnExchange findByReturnNumber(String returnNumber) throws SQLException {
        String sql = "SELECT * FROM return_exchanges WHERE return_number = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, returnNumber);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    ReturnExchange returnExchange = mapResultSetToReturnExchange(rs);
                    returnExchange.setItems(findItemsByReturnExchangeId(returnExchange.getId()));
                    return returnExchange;
                }
            }
        }
        return null;
    }

    /**
     * Find all return/exchanges with optional filters
     */
    public List<ReturnExchange> findAll(String status, String type, LocalDateTime startDate, 
                                       LocalDateTime endDate, String searchTerm) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT * FROM return_exchanges WHERE 1=1");
        List<Object> params = new ArrayList<>();

        if (status != null && !status.equals("All")) {
            sql.append(" AND status = ?");
            params.add(status);
        }

        if (type != null && !type.equals("All")) {
            sql.append(" AND type = ?");
            params.add(type);
        }

        if (startDate != null) {
            sql.append(" AND request_date >= ?");
            params.add(Timestamp.valueOf(startDate));
        }

        if (endDate != null) {
            sql.append(" AND request_date <= ?");
            params.add(Timestamp.valueOf(endDate));
        }

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            sql.append(" AND (return_number LIKE ? OR original_transaction_number LIKE ? OR customer_name LIKE ?)");
            String searchPattern = "%" + searchTerm.trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        sql.append(" ORDER BY request_date DESC");

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                pstmt.setObject(i + 1, params.get(i));
            }

            List<ReturnExchange> returnExchanges = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    ReturnExchange returnExchange = mapResultSetToReturnExchange(rs);
                    returnExchange.setItems(findItemsByReturnExchangeId(returnExchange.getId()));
                    returnExchanges.add(returnExchange);
                }
            }
            return returnExchanges;
        }
    }

    /**
     * Update return/exchange status
     */
    public void updateStatus(Long id, String status, String processedBy) throws SQLException {
        String sql = "UPDATE return_exchanges SET status = ?, processed_by = ?, processed_date = ?, " +
                "updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, status);
            pstmt.setString(2, processedBy);
            pstmt.setTimestamp(3, Timestamp.valueOf(LocalDateTime.now()));
            pstmt.setLong(4, id);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating return/exchange status failed, no rows affected.");
            }

            LOGGER.info("Updated return/exchange status: " + id + " to " + status);
        }
    }

    /**
     * Update return/exchange notes
     */
    public void updateNotes(Long id, String notes) throws SQLException {
        String sql = "UPDATE return_exchanges SET notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, notes);
            pstmt.setLong(2, id);

            pstmt.executeUpdate();
            LOGGER.info("Updated return/exchange notes: " + id);
        }
    }

    /**
     * Delete return/exchange
     */
    public void delete(Long id) throws SQLException {
        String sql = "DELETE FROM return_exchanges WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows == 0) {
                throw new SQLException("Deleting return/exchange failed, no rows affected.");
            }

            LOGGER.info("Deleted return/exchange: " + id);
        }
    }

    /**
     * Generate next return number
     */
    public String generateReturnNumber(String type) throws SQLException {
        String prefix = type.equals("RETURN") ? "RET" : "EXC";
        String sql = "SELECT COUNT(*) + 1 as next_number FROM return_exchanges WHERE type = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, type);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    int nextNumber = rs.getInt("next_number");
                    return String.format("%s-%03d", prefix, nextNumber);
                }
            }
        }
        return prefix + "-001";
    }

    /**
     * Get return/exchange statistics
     */
    public ReturnExchangeStats getStatistics(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT " +
                "COUNT(*) as total_count, " +
                "COUNT(CASE WHEN type = 'RETURN' THEN 1 END) as return_count, " +
                "COUNT(CASE WHEN type = 'EXCHANGE' THEN 1 END) as exchange_count, " +
                "COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_count, " +
                "SUM(CASE WHEN type = 'RETURN' AND status = 'COMPLETED' THEN refund_amount ELSE 0 END) as total_refunds " +
                "FROM return_exchanges WHERE request_date BETWEEN ? AND ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return new ReturnExchangeStats(
                            rs.getInt("total_count"),
                            rs.getInt("return_count"),
                            rs.getInt("exchange_count"),
                            rs.getInt("pending_count"),
                            rs.getBigDecimal("total_refunds")
                    );
                }
            }
        }
        return new ReturnExchangeStats(0, 0, 0, 0, BigDecimal.ZERO);
    }

    private ReturnExchange mapResultSetToReturnExchange(ResultSet rs) throws SQLException {
        ReturnExchange returnExchange = new ReturnExchange();
        returnExchange.setId(rs.getLong("id"));
        returnExchange.setReturnNumber(rs.getString("return_number"));
        returnExchange.setOriginalTransactionId(rs.getLong("original_transaction_id"));
        returnExchange.setOriginalTransactionNumber(rs.getString("original_transaction_number"));
        returnExchange.setCustomerId(rs.getObject("customer_id", Long.class));
        returnExchange.setCustomerName(rs.getString("customer_name"));
        returnExchange.setType(rs.getString("type"));
        returnExchange.setReason(rs.getString("reason"));
        returnExchange.setStatus(rs.getString("status"));
        returnExchange.setTotalAmount(rs.getBigDecimal("total_amount"));
        returnExchange.setRefundAmount(rs.getBigDecimal("refund_amount"));
        returnExchange.setExchangeAmount(rs.getBigDecimal("exchange_amount"));
        returnExchange.setPaymentMethod(rs.getString("payment_method"));
        returnExchange.setNotes(rs.getString("notes"));
        returnExchange.setProcessedBy(rs.getString("processed_by"));

        Timestamp requestDate = rs.getTimestamp("request_date");
        if (requestDate != null) {
            returnExchange.setRequestDate(requestDate.toLocalDateTime());
        }

        Timestamp processedDate = rs.getTimestamp("processed_date");
        if (processedDate != null) {
            returnExchange.setProcessedDate(processedDate.toLocalDateTime());
        }

        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            returnExchange.setCreatedAt(createdAt.toLocalDateTime());
        }

        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            returnExchange.setUpdatedAt(updatedAt.toLocalDateTime());
        }

        return returnExchange;
    }

    private List<ReturnExchangeItem> findItemsByReturnExchangeId(Long returnExchangeId) throws SQLException {
        return ReturnExchangeItemDAO.getInstance().findByReturnExchangeId(returnExchangeId);
    }

    /**
     * Statistics class for return/exchange data
     */
    public static class ReturnExchangeStats {
        private final int totalCount;
        private final int returnCount;
        private final int exchangeCount;
        private final int pendingCount;
        private final BigDecimal totalRefunds;

        public ReturnExchangeStats(int totalCount, int returnCount, int exchangeCount, 
                                 int pendingCount, BigDecimal totalRefunds) {
            this.totalCount = totalCount;
            this.returnCount = returnCount;
            this.exchangeCount = exchangeCount;
            this.pendingCount = pendingCount;
            this.totalRefunds = totalRefunds;
        }

        // Getters
        public int getTotalCount() { return totalCount; }
        public int getReturnCount() { return returnCount; }
        public int getExchangeCount() { return exchangeCount; }
        public int getPendingCount() { return pendingCount; }
        public BigDecimal getTotalRefunds() { return totalRefunds; }
    }
}
