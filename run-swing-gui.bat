@echo off
title Clothing Store - Swing GUI
echo ========================================
echo    CLOTHING STORE MANAGEMENT SYSTEM
echo         Swing GUI Application
echo ========================================
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 11 or higher
    pause
    exit /b 1
)

REM Check if compiled classes exist
if not exist "target\classes" (
    echo ERROR: Compiled classes not found!
    echo Please compile the project first
    pause
    exit /b 1
)

REM Check if SQLite JDBC exists
if not exist "lib\sqlite-jdbc-3.50.1.0.jar" (
    echo ERROR: SQLite JDBC driver not found!
    echo Please ensure lib\sqlite-jdbc-3.50.1.0.jar exists
    pause
    exit /b 1
)

echo Starting Swing GUI Application...
echo.

java -cp "target\classes;lib\sqlite-jdbc-3.50.1.0.jar" com.clothingstore.gui.SwingPOSDemo

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Application failed to start
    echo Check the error messages above
    pause
)
