# 🚀 Clothing Store Management System - Shortcuts Guide

## 📁 **AVAILABLE SHORTCUTS**

### **🖱️ Windows Batch Files (.bat)**

#### **1. `compile-project.bat`**
- **Purpose:** Compiles all Java source files
- **Usage:** Double-click or run from command prompt
- **Features:**
  - ✅ Automatic dependency checking
  - ✅ Error detection and reporting
  - ✅ Creates target/classes directory
  - ✅ Validates Java installation
  - ✅ Success/failure notifications

#### **2. `run-javafx-gui.bat`**
- **Purpose:** Launches the primary JavaFX GUI application
- **Usage:** Double-click to start the main POS system
- **Features:**
  - ✅ JavaFX SDK validation
  - ✅ Dependency checking
  - ✅ Professional GUI interface
  - ✅ Full POS functionality
  - ✅ Error handling with user-friendly messages

#### **3. `run-swing-gui.bat`**
- **Purpose:** Launches the alternative Swing GUI
- **Usage:** Double-click for traditional desktop interface
- **Features:**
  - ✅ Cross-platform compatibility
  - ✅ No JavaFX dependency required
  - ✅ Complete POS functionality
  - ✅ Reliable performance

#### **4. `run-console-demo.bat`**
- **Purpose:** Runs the console-based demonstration
- **Usage:** Double-click for command-line interface
- **Features:**
  - ✅ Works in any environment
  - ✅ No GUI dependencies
  - ✅ Perfect for testing
  - ✅ Detailed system demonstration

---

### **⚡ PowerShell Script (.ps1)**

#### **`Run-ClothingStore.ps1`**
- **Purpose:** Advanced launcher with multiple modes
- **Usage:** `.\Run-ClothingStore.ps1 -Mode <mode>`

**Available Modes:**
```powershell
# Compile the project
.\Run-ClothingStore.ps1 -Mode compile

# Launch JavaFX GUI (Default)
.\Run-ClothingStore.ps1 -Mode javafx
.\Run-ClothingStore.ps1  # (javafx is default)

# Launch Swing GUI
.\Run-ClothingStore.ps1 -Mode swing

# Run Console Demo
.\Run-ClothingStore.ps1 -Mode console

# System Status Check
.\Run-ClothingStore.ps1 -Mode status
```

**Advanced Features:**
- ✅ **Colored Output** - Easy-to-read status messages
- ✅ **Comprehensive Validation** - Checks all dependencies
- ✅ **Error Handling** - Detailed error reporting
- ✅ **Progress Indicators** - Visual feedback during operations
- ✅ **Automatic Cleanup** - Proper resource management

---

## 🛠️ **SETUP INSTRUCTIONS**

### **Prerequisites Check**
Before using shortcuts, ensure you have:

1. **Java JDK 11+** installed and in PATH
2. **JavaFX SDK 17.0.2** extracted to project root (for GUI modes)
3. **SQLite JDBC driver** in `lib/sqlite-jdbc-3.50.1.0.jar`
4. **Source files** in proper directory structure

### **First-Time Setup**
1. **Extract Project** to your desired location
2. **Download JavaFX SDK** from https://openjfx.io/
3. **Extract JavaFX** to project root as `javafx-sdk-17.0.2/`
4. **Run Compilation** using `compile-project.bat`
5. **Test Application** using `run-console-demo.bat`

---

## 🎯 **RECOMMENDED WORKFLOW**

### **For New Users:**
```
1. compile-project.bat          # Compile everything
2. run-console-demo.bat         # Test basic functionality
3. run-javafx-gui.bat          # Launch main application
```

### **For Developers:**
```powershell
1. .\Run-ClothingStore.ps1 -Mode compile    # Compile with detailed output
2. .\Run-ClothingStore.ps1 -Mode status     # Check system status
3. .\Run-ClothingStore.ps1 -Mode javafx     # Launch for testing
```

### **For Production Use:**
```
1. compile-project.bat          # Ensure latest compilation
2. run-javafx-gui.bat          # Launch primary interface
```

---

## 🔧 **CUSTOMIZATION OPTIONS**

### **Modifying Batch Scripts**
You can edit the `.bat` files to:
- Change Java memory settings (`-Xmx` parameter)
- Add custom JVM arguments
- Modify classpath settings
- Add logging options

### **PowerShell Script Parameters**
The PowerShell script supports additional customization:
```powershell
# Example: Custom memory settings
$env:JAVA_OPTS = "-Xmx1024m"
.\Run-ClothingStore.ps1 -Mode javafx
```

---

## 🚨 **TROUBLESHOOTING SHORTCUTS**

### **Common Issues & Solutions**

#### **"Java not found" Error**
```bash
# Check Java installation
java -version
javac -version

# Add Java to PATH (Windows)
# Add C:\Program Files\Java\jdk-XX\bin to system PATH
```

#### **"JavaFX not found" Error**
```bash
# Verify JavaFX SDK structure
javafx-sdk-17.0.2/
├── lib/
│   ├── javafx.base.jar
│   ├── javafx.controls.jar
│   └── javafx.fxml.jar
```

#### **"Permission Denied" (PowerShell)**
```powershell
# Enable PowerShell script execution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### **"Compilation Failed" Error**
```bash
# Check source file structure
src/main/java/com/clothingstore/
├── *.java files
├── model/*.java files
├── dao/*.java files
└── service/*.java files
```

---

## 📊 **PERFORMANCE TIPS**

### **Faster Startup**
- Use **SSD storage** for better I/O performance
- **Increase Java heap size** for large datasets:
  ```bash
  java -Xmx2048m -cp "..." com.clothingstore.ClothingStoreApp
  ```

### **Better Responsiveness**
- **Close unnecessary applications** before running
- **Use console mode** for testing/debugging
- **Monitor system resources** during operation

---

## 🎨 **INTERFACE COMPARISON**

| Feature | JavaFX GUI | Swing GUI | Console Demo |
|---------|------------|-----------|--------------|
| **Visual Appeal** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐ |
| **Performance** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Features** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Compatibility** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Dependencies** | JavaFX SDK | None | None |
| **Best For** | Production | Development | Testing |

---

## 📋 **QUICK REFERENCE**

### **Essential Commands**
```bash
# Windows Batch (Double-click)
compile-project.bat
run-javafx-gui.bat
run-console-demo.bat

# PowerShell (Command-line)
.\Run-ClothingStore.ps1 -Mode compile
.\Run-ClothingStore.ps1 -Mode javafx
.\Run-ClothingStore.ps1 -Mode console
```

### **File Locations**
- **Shortcuts:** Project root directory
- **Database:** `clothing_store.db`
- **Compiled Classes:** `target/classes/`
- **Dependencies:** `lib/`
- **JavaFX SDK:** `javafx-sdk-17.0.2/`

---

## 🎉 **SUCCESS INDICATORS**

### **Compilation Success**
```
✓ COMPILATION SUCCESSFUL!
All Java files compiled successfully.
```

### **Application Launch Success**
```
✓ Database initialized
✓ GUI loaded successfully
✓ Sample data available
```

### **System Ready**
```
CLOTHING STORE MANAGEMENT SYSTEM
Ready for operation!
```

---

**🚀 Your shortcuts are ready! Choose your preferred method and start managing your clothing store efficiently!**
