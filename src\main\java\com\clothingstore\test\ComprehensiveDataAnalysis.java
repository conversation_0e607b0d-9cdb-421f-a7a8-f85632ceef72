package com.clothingstore.test;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.service.ProfitAnalysisService;
import com.clothingstore.service.ProfitAnalysisService.ProfitMetrics;
import com.clothingstore.service.ProfitAnalysisService.CategoryProfitData;
import com.clothingstore.service.ProfitAnalysisService.ComparisonMetrics;
import com.clothingstore.model.Product;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Comprehensive analysis of actual database data
 */
public class ComprehensiveDataAnalysis {

    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("COMPREHENSIVE CLOTHING STORE DATABASE ANALYSIS");
        System.out.println("=".repeat(80));
        System.out.println("Analysis Date: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();

        try {
            // Initialize services and DAOs
            ProfitAnalysisService profitService = new ProfitAnalysisService();
            ProductDAO productDAO = ProductDAO.getInstance();
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            TransactionDAO transactionDAO = TransactionDAO.getInstance();

            // 1. DATABASE OVERVIEW
            System.out.println("📊 DATABASE OVERVIEW");
            System.out.println("-".repeat(50));
            analyzeDatabase(productDAO, customerDAO, transactionDAO);

            // 2. PRODUCT ANALYSIS
            System.out.println("\n🛍️ PRODUCT INVENTORY ANALYSIS");
            System.out.println("-".repeat(50));
            analyzeProducts(productDAO);

            // 3. CUSTOMER ANALYSIS
            System.out.println("\n👥 CUSTOMER BASE ANALYSIS");
            System.out.println("-".repeat(50));
            analyzeCustomers(customerDAO);

            // 4. TRANSACTION ANALYSIS
            System.out.println("\n💰 TRANSACTION ANALYSIS");
            System.out.println("-".repeat(50));
            analyzeTransactions(transactionDAO);

            // 5. PROFIT ANALYSIS - MULTIPLE PERIODS
            System.out.println("\n📈 COMPREHENSIVE PROFIT ANALYSIS");
            System.out.println("-".repeat(50));
            analyzeProfitMetrics(profitService);

            // 6. CATEGORY PERFORMANCE
            System.out.println("\n🏷️ CATEGORY PERFORMANCE ANALYSIS");
            System.out.println("-".repeat(50));
            analyzeCategoryPerformance(profitService);

            // 7. BUSINESS INSIGHTS
            System.out.println("\n💡 BUSINESS INSIGHTS & RECOMMENDATIONS");
            System.out.println("-".repeat(50));
            generateBusinessInsights(profitService, productDAO, transactionDAO);

            System.out.println("\n" + "=".repeat(80));
            System.out.println("✅ COMPREHENSIVE ANALYSIS COMPLETE");
            System.out.println("=".repeat(80));

        } catch (Exception e) {
            System.err.println("❌ Analysis failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void analyzeDatabase(ProductDAO productDAO, CustomerDAO customerDAO, TransactionDAO transactionDAO) {
        try {
            List<Product> products = productDAO.findAll();
            List<Customer> customers = customerDAO.findAll();
            List<Transaction> transactions = transactionDAO.findAll();

            System.out.println("Total Products: " + products.size());
            System.out.println("Total Customers: " + customers.size());
            System.out.println("Total Transactions: " + transactions.size());

            // Calculate total inventory value
            BigDecimal totalInventoryValue = products.stream()
                    .map(p -> p.getPrice().multiply(BigDecimal.valueOf(p.getStockQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalInventoryCost = products.stream()
                    .filter(p -> p.getCostPrice() != null)
                    .map(p -> p.getCostPrice().multiply(BigDecimal.valueOf(p.getStockQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            System.out.println("Total Inventory Value: $" + String.format("%.2f", totalInventoryValue));
            System.out.println("Total Inventory Cost: $" + String.format("%.2f", totalInventoryCost));
            System.out.println("Potential Inventory Profit: $" + String.format("%.2f", totalInventoryValue.subtract(totalInventoryCost)));

        } catch (Exception e) {
            System.err.println("Database analysis failed: " + e.getMessage());
        }
    }

    private static void analyzeProducts(ProductDAO productDAO) {
        try {
            List<Product> products = productDAO.findAll();

            // Category breakdown
            Map<String, Long> categoryCount = products.stream()
                    .collect(Collectors.groupingBy(
                            p -> p.getCategory() != null ? p.getCategory() : "Uncategorized",
                            Collectors.counting()
                    ));

            System.out.println("Products by Category:");
            categoryCount.forEach((category, count)
                    -> System.out.println("  " + category + ": " + count + " products"));

            // Stock analysis
            List<Product> lowStockProducts = productDAO.findLowStockProducts();
            List<Product> outOfStockProducts = productDAO.findOutOfStockProducts();

            System.out.println("\nStock Status:");
            System.out.println("  Low Stock Items: " + lowStockProducts.size());
            System.out.println("  Out of Stock Items: " + outOfStockProducts.size());

            // Price analysis
            BigDecimal avgPrice = products.stream()
                    .map(Product::getPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(products.size()), 2, BigDecimal.ROUND_HALF_UP);

            BigDecimal maxPrice = products.stream()
                    .map(Product::getPrice)
                    .max(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);

            BigDecimal minPrice = products.stream()
                    .map(Product::getPrice)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);

            System.out.println("\nPrice Analysis:");
            System.out.println("  Average Price: $" + String.format("%.2f", avgPrice));
            System.out.println("  Highest Price: $" + String.format("%.2f", maxPrice));
            System.out.println("  Lowest Price: $" + String.format("%.2f", minPrice));

        } catch (Exception e) {
            System.err.println("Product analysis failed: " + e.getMessage());
        }
    }

    private static void analyzeCustomers(CustomerDAO customerDAO) {
        try {
            List<Customer> customers = customerDAO.findAll();

            long activeCustomers = customers.stream()
                    .mapToLong(c -> c.isActive() ? 1 : 0)
                    .sum();

            System.out.println("Active Customers: " + activeCustomers + " / " + customers.size());

            // Customer spending analysis
            double totalCustomerSpending = customers.stream()
                    .mapToDouble(Customer::getTotalSpent)
                    .sum();

            if (customers.size() > 0) {
                double avgSpending = totalCustomerSpending / customers.size();
                System.out.println("Average Customer Spending: $" + String.format("%.2f", avgSpending));
            }

            System.out.println("Total Customer Lifetime Value: $" + String.format("%.2f", totalCustomerSpending));

        } catch (Exception e) {
            System.err.println("Customer analysis failed: " + e.getMessage());
        }
    }

    private static void analyzeTransactions(TransactionDAO transactionDAO) {
        try {
            List<Transaction> transactions = transactionDAO.findAll();

            // Transaction status breakdown
            Map<String, Long> statusCount = transactions.stream()
                    .collect(Collectors.groupingBy(Transaction::getStatus, Collectors.counting()));

            System.out.println("Transactions by Status:");
            statusCount.forEach((status, count)
                    -> System.out.println("  " + status + ": " + count + " transactions"));

            // Payment method analysis
            Map<String, Long> paymentMethods = transactions.stream()
                    .collect(Collectors.groupingBy(Transaction::getPaymentMethod, Collectors.counting()));

            System.out.println("\nPayment Methods:");
            paymentMethods.forEach((method, count)
                    -> System.out.println("  " + method + ": " + count + " transactions"));

            // Transaction value analysis
            BigDecimal totalRevenue = transactions.stream()
                    .filter(t -> "COMPLETED".equals(t.getStatus()))
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            System.out.println("\nRevenue Analysis:");
            System.out.println("  Total Revenue: $" + String.format("%.2f", totalRevenue));

            if (!transactions.isEmpty()) {
                BigDecimal avgTransaction = totalRevenue.divide(
                        BigDecimal.valueOf(transactions.size()), 2, RoundingMode.HALF_UP);
                System.out.println("  Average Transaction: $" + String.format("%.2f", avgTransaction));
            }

        } catch (Exception e) {
            System.err.println("Transaction analysis failed: " + e.getMessage());
        }
    }

    private static void analyzeProfitMetrics(ProfitAnalysisService profitService) {
        try {
            LocalDateTime now = LocalDateTime.now();

            // Different time periods
            LocalDateTime[] periods = {
                now.minusDays(7), // Last 7 days
                now.minusDays(30), // Last 30 days
                now.minusDays(90), // Last 90 days
                now.minusYears(1) // Last year
            };

            String[] periodNames = {"Last 7 Days", "Last 30 Days", "Last 90 Days", "Last Year"};

            for (int i = 0; i < periods.length; i++) {
                ProfitMetrics metrics = profitService.calculateProfitMetrics(periods[i], now);

                System.out.println("\n" + periodNames[i] + ":");
                System.out.println("  Revenue: " + metrics.getFormattedRevenue());
                System.out.println("  Cost: " + metrics.getFormattedCost());
                System.out.println("  Profit: " + metrics.getFormattedProfit());
                System.out.println("  Margin: " + metrics.getFormattedProfitMargin());
                System.out.println("  Transactions: " + metrics.getTransactionCount());
                System.out.println("  Items Sold: " + metrics.getTotalItemsSold());
            }

        } catch (Exception e) {
            System.err.println("Profit analysis failed: " + e.getMessage());
        }
    }

    private static void analyzeCategoryPerformance(ProfitAnalysisService profitService) {
        try {
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(90); // Last 90 days

            List<CategoryProfitData> categories = profitService.calculateCategoryProfitBreakdown(startDate, endDate);

            if (categories.isEmpty()) {
                System.out.println("No category data available for the selected period.");
                return;
            }

            System.out.println("Category Performance (Last 90 Days):");
            System.out.println();

            for (CategoryProfitData category : categories) {
                System.out.println("📂 " + category.getCategoryName());
                System.out.println("   Revenue: " + category.getFormattedRevenue());
                System.out.println("   Cost: " + category.getFormattedCost());
                System.out.println("   Profit: " + category.getFormattedProfit());
                System.out.println("   Margin: " + category.getFormattedProfitMargin());
                System.out.println("   Items Sold: " + category.getItemsSold());
                System.out.println("   Transactions: " + category.getTransactionCount());
                System.out.println();
            }

        } catch (Exception e) {
            System.err.println("Category analysis failed: " + e.getMessage());
        }
    }

    private static void generateBusinessInsights(ProfitAnalysisService profitService, ProductDAO productDAO, TransactionDAO transactionDAO) {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime last30Days = now.minusDays(30);

            ProfitMetrics currentMetrics = profitService.calculateProfitMetrics(last30Days, now);
            List<CategoryProfitData> categories = profitService.calculateCategoryProfitBreakdown(last30Days, now);

            System.out.println("🎯 KEY BUSINESS INSIGHTS:");
            System.out.println();

            // Profit margin analysis
            if (currentMetrics.getProfitMargin() > 40) {
                System.out.println("✅ EXCELLENT profit margin of " + currentMetrics.getFormattedProfitMargin() + " - well above industry average!");
            } else if (currentMetrics.getProfitMargin() > 25) {
                System.out.println("✅ GOOD profit margin of " + currentMetrics.getFormattedProfitMargin() + " - healthy business performance.");
            } else if (currentMetrics.getProfitMargin() > 15) {
                System.out.println("⚠️ MODERATE profit margin of " + currentMetrics.getFormattedProfitMargin() + " - room for improvement.");
            } else {
                System.out.println("🔴 LOW profit margin of " + currentMetrics.getFormattedProfitMargin() + " - needs immediate attention!");
            }

            // Category performance insights
            if (!categories.isEmpty()) {
                CategoryProfitData topCategory = categories.get(0);
                System.out.println("🏆 TOP PERFORMING CATEGORY: " + topCategory.getCategoryName()
                        + " (" + topCategory.getFormattedProfit() + " profit)");

                if (categories.size() > 1) {
                    CategoryProfitData bottomCategory = categories.get(categories.size() - 1);
                    System.out.println("📉 LOWEST PERFORMING CATEGORY: " + bottomCategory.getCategoryName()
                            + " (" + bottomCategory.getFormattedProfit() + " profit)");
                }
            }

            // Transaction efficiency
            if (currentMetrics.getTransactionCount() > 0) {
                double itemsPerTransaction = (double) currentMetrics.getTotalItemsSold() / currentMetrics.getTransactionCount();
                System.out.println("🛒 AVERAGE BASKET SIZE: " + String.format("%.1f", itemsPerTransaction) + " items per transaction");

                if (itemsPerTransaction > 2.0) {
                    System.out.println("✅ Good basket size - customers are buying multiple items!");
                } else {
                    System.out.println("💡 OPPORTUNITY: Consider cross-selling to increase basket size.");
                }
            }

            // Revenue insights
            double revenuePerItem = currentMetrics.getTotalItemsSold() > 0
                    ? currentMetrics.getTotalRevenue() / currentMetrics.getTotalItemsSold() : 0;
            System.out.println("💰 AVERAGE REVENUE PER ITEM: $" + String.format("%.2f", revenuePerItem));

            System.out.println();
            System.out.println("📊 RECOMMENDATIONS:");
            System.out.println("• Monitor category performance trends monthly");
            System.out.println("• Focus marketing on top-performing categories");
            System.out.println("• Review pricing strategy for low-margin items");
            System.out.println("• Implement cross-selling for higher basket values");
            System.out.println("• Track customer lifetime value growth");

        } catch (Exception e) {
            System.err.println("Business insights generation failed: " + e.getMessage());
        }
    }
}
