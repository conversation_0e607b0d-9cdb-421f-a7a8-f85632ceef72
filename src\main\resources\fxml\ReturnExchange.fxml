<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.ReturnExchangeController">
   <children>
      <!-- Header Section -->
      <VBox spacing="15.0" styleClass="header-section">
         <children>
            <!-- Title and Actions -->
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="page-title" text="Return &amp; Exchange Management" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="newReturnButton" onAction="#handleNewReturn" styleClass="secondary-button" text="🔄 New Return" />
                  <Button fx:id="newExchangeButton" onAction="#handleNewExchange" styleClass="secondary-button" text="🔁 New Exchange" />
                  <Button onAction="#handleRefreshData" styleClass="primary-button" text="🔄 Refresh" />
               </children>
            </HBox>
            
            <!-- Summary Cards -->
            <HBox spacing="15.0" styleClass="summary-section">
               <children>
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card">
                     <children>
                        <Label styleClass="summary-title" text="🔄 Total Returns" />
                        <Label fx:id="totalReturnsLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card">
                     <children>
                        <Label styleClass="summary-title" text="🔁 Total Exchanges" />
                        <Label fx:id="totalExchangesLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card warning">
                     <children>
                        <Label styleClass="summary-title" text="⏳ Pending" />
                        <Label fx:id="pendingCountLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card success">
                     <children>
                        <Label styleClass="summary-title" text="Total Refunds" />
                        <Label fx:id="totalRefundAmountLabel" styleClass="summary-value" text="$0.00" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Filter Controls -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="🔍 Transaction:" />
                  <TextField fx:id="transactionSearchField" prefWidth="150.0" promptText="Search transaction..." styleClass="search-box" />
                  <Label styleClass="filter-label" text="Type:" />
                  <ComboBox fx:id="typeFilter" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="Status:" />
                  <ComboBox fx:id="statusFilter" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="From:" />
                  <DatePicker fx:id="startDatePicker" prefWidth="130.0" styleClass="form-field" />
                  <Label styleClass="filter-label" text="To:" />
                  <DatePicker fx:id="endDatePicker" prefWidth="130.0" styleClass="form-field" />
                  <Button fx:id="searchButton" onAction="#handleSearch" styleClass="primary-button" text="🔍 Search" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </VBox>

      <!-- Main Content -->
      <SplitPane dividerPositions="0.6" VBox.vgrow="ALWAYS">
         <items>
            <!-- Left Panel - Return/Exchange List -->
            <VBox spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="📋 Return &amp; Exchange List" />
                  
                  <TableView fx:id="returnExchangeTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="returnNumberColumn" prefWidth="100.0" text="Number" />
                        <TableColumn fx:id="typeColumn" prefWidth="80.0" text="Type" />
                        <TableColumn fx:id="customerColumn" prefWidth="120.0" text="Customer" />
                        <TableColumn fx:id="originalTransactionColumn" prefWidth="100.0" text="Original Txn" />
                        <TableColumn fx:id="requestDateColumn" prefWidth="120.0" text="Request Date" />
                        <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Status" />
                        <TableColumn fx:id="amountColumn" prefWidth="100.0" text="Amount" />
                     </columns>
                  </TableView>
               </children>
               <padding>
                  <Insets bottom="10.0" left="15.0" right="5.0" top="10.0" />
               </padding>
            </VBox>
            
            <!-- Right Panel - Details -->
            <VBox spacing="15.0">
               <children>
                  <!-- Details Section -->
                  <VBox spacing="10.0" styleClass="details-section">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="15.0">
                           <children>
                              <Label styleClass="section-title" text="📄 Details" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="printButton" onAction="#handlePrint" styleClass="secondary-button" text="🖨️ Print" />
                           </children>
                        </HBox>
                        
                        <GridPane hgap="10.0" vgap="8.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           
                           <Label styleClass="detail-label" text="Number:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                           <Label fx:id="detailsReturnNumberLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                           
                           <Label styleClass="detail-label" text="Type:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                           <Label fx:id="detailsTypeLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                           
                           <Label styleClass="detail-label" text="Status:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                           <Label fx:id="detailsStatusLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                           
                           <Label styleClass="detail-label" text="Customer:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                           <Label fx:id="detailsCustomerLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           
                           <Label styleClass="detail-label" text="Original Txn:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                           <Label fx:id="detailsOriginalTransactionLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                           
                           <Label styleClass="detail-label" text="Request Date:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                           <Label fx:id="detailsRequestDateLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                           
                           <Label styleClass="detail-label" text="Processed By:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                           <Label fx:id="detailsProcessedByLabel" styleClass="detail-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                           
                           <Label styleClass="detail-label" text="Total Amount:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                           <Label fx:id="detailsTotalAmountLabel" styleClass="detail-value money" text="--" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                        </GridPane>
                        
                        <VBox spacing="5.0">
                           <children>
                              <Label styleClass="detail-label" text="Notes:" />
                              <TextArea fx:id="detailsNotesArea" editable="false" prefRowCount="3" styleClass="notes-area" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Items Section -->
                  <VBox spacing="10.0" VBox.vgrow="ALWAYS">
                     <children>
                        <Label styleClass="section-title" text="📦 Items" />
                        
                        <TableView fx:id="itemsTable" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="itemProductColumn" prefWidth="120.0" text="Product" />
                              <TableColumn fx:id="itemOriginalQtyColumn" prefWidth="70.0" text="Orig Qty" />
                              <TableColumn fx:id="itemReturnQtyColumn" prefWidth="70.0" text="Ret Qty" />
                              <TableColumn fx:id="itemConditionColumn" prefWidth="80.0" text="Condition" />
                              <TableColumn fx:id="itemActionColumn" prefWidth="80.0" text="Action" />
                              <TableColumn fx:id="itemAmountColumn" prefWidth="80.0" text="Amount" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
                  
                  <!-- Action Buttons -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Button fx:id="approveButton" onAction="#handleApprove" styleClass="action-button approve" text="✅ Approve" />
                        <Button fx:id="rejectButton" onAction="#handleReject" styleClass="action-button reject" text="❌ Reject" />
                        <Button fx:id="completeButton" onAction="#handleComplete" styleClass="action-button complete" text="🎯 Complete" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="5.0" right="15.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </children>
</VBox>
