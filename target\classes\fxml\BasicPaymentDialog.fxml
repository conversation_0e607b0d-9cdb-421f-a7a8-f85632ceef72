<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.PaymentDialogController" spacing="15.0">
   <padding>
      <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
   </padding>
   <children>
      <!-- Header -->
      <Label text="Process Outstanding Payment" />

      <!-- Transaction Summary -->
      <VBox spacing="10.0">
         <children>
            <Label text="Transaction Summary" />
            <HBox spacing="10.0">
               <children>
                  <Label text="Total Amount:" minWidth="120.0" />
                  <Label text="$0.00" />
               </children>
            </HBox>
            <HBox spacing="10.0">
               <children>
                  <Label text="Amount Paid:" minWidth="120.0" />
                  <Label text="$0.00" />
               </children>
            </HBox>
            <HBox spacing="10.0">
               <children>
                  <Label text="Remaining Balance:" minWidth="120.0" />
                  <Label text="$0.00" />
               </children>
            </HBox>
         </children>
      </VBox>

      <!-- Payment Input Section -->
      <VBox spacing="15.0">
         <children>
            <Label text="Make Payment" />

            <!-- Payment Method -->
            <HBox spacing="15.0">
               <children>
                  <Label text="Payment Method:" />
                  <ComboBox prefWidth="200.0" />
               </children>
            </HBox>

            <!-- Payment Amount -->
            <VBox spacing="8.0">
               <children>
                  <Label text="Payment Amount:" />
                  <HBox spacing="10.0">
                     <children>
                        <TextField prefWidth="150.0" promptText="Enter amount to pay" />
                        <Button text="Pay Full Balance" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
      </VBox>

      <!-- Notes -->
      <VBox spacing="8.0">
         <children>
            <Label text="Payment Notes (Optional):" />
            <TextArea prefRowCount="2" promptText="Add any notes about this payment..." />
         </children>
      </VBox>

      <!-- Buttons -->
      <HBox spacing="15.0">
         <children>
            <Button text="Cancel" />
            <Button text="Process Payment" />
         </children>
      </HBox>
   </children>
</VBox>
