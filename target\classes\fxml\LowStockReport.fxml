<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.LowStockReportController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="⚠️ Low Stock Report">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
            <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </HBox>

      <!-- Summary Cards -->
      <HBox spacing="15.0" styleClass="form-container">
         <children>
            <VBox spacing="5.0" styleClass="metric-card warning" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="metric-title" text="⚠️ Low Stock Items" />
                  <Label fx:id="lblLowStockCount" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card danger" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="metric-title" text="🚨 Critical Stock" />
                  <Label fx:id="lblCriticalStockCount" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card info" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="metric-title" text="💰 Potential Lost Sales" />
                  <Label fx:id="lblPotentialLoss" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </HBox>

      <!-- Low Stock Products Table -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="section-title" text="Low Stock Products">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>

            <TableView fx:id="tblLowStockProducts" prefHeight="400.0">
               <columns>
                  <TableColumn fx:id="colSku" prefWidth="100.0" text="SKU" />
                  <TableColumn fx:id="colName" prefWidth="200.0" text="Product Name" />
                  <TableColumn fx:id="colCategory" prefWidth="120.0" text="Category" />
                  <TableColumn fx:id="colCurrentStock" prefWidth="100.0" text="Current Stock" />
                  <TableColumn fx:id="colMinStock" prefWidth="100.0" text="Min Stock" />
                  <TableColumn fx:id="colStockDifference" prefWidth="100.0" text="Shortage" />
                  <TableColumn fx:id="colPrice" prefWidth="80.0" text="Price" />
                  <TableColumn fx:id="colPotentialLoss" prefWidth="120.0" text="Potential Loss" />
                  <TableColumn fx:id="colActions" prefWidth="100.0" text="Actions" />
               </columns>
            </TableView>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Action Buttons -->
      <HBox spacing="10.0" styleClass="form-container">
         <children>
            <Button fx:id="btnReorderSelected" onAction="#handleReorderSelected" styleClass="button primary" text="📦 Reorder Selected" />
            <Button fx:id="btnReorderAll" onAction="#handleReorderAll" styleClass="button warning" text="🛒 Reorder All Low Stock" />
            <Button fx:id="btnUpdateMinStock" onAction="#handleUpdateMinStock" styleClass="button secondary" text="⚙️ Update Min Stock Levels" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnPrintReport" onAction="#handlePrintReport" styleClass="button info" text="🖨️ Print Report" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" top="10.0" />
         </VBox.margin>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
