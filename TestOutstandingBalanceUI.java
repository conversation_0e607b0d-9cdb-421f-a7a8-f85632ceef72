import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

import com.clothingstore.database.DatabaseManager;

public class TestOutstandingBalanceUI extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Initialize database
            DatabaseManager.getInstance();
            
            System.out.println("=== Testing Outstanding Balance UI Loading ===");
            
            // Load the Outstanding Balance FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/OutstandingBalances.fxml"));
            Parent root = loader.load();
            
            System.out.println("✓ Outstanding Balance FXML loaded successfully");
            
            // Create scene and show
            Scene scene = new Scene(root, 1200, 800);
            primaryStage.setTitle("Outstanding Balance UI Test");
            primaryStage.setScene(scene);
            primaryStage.show();
            
            System.out.println("✓ Outstanding Balance UI displayed successfully");
            System.out.println("✓ Test completed - Outstanding Balance page should be working");
            
        } catch (Exception e) {
            System.err.println("✗ Error loading Outstanding Balance UI: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}
