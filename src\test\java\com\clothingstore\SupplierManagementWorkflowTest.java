package com.clothingstore;

import com.clothingstore.service.SupplierService;
import com.clothingstore.service.SupplierOperationResult;
import com.clothingstore.service.PurchaseOrderOperationResult;
import com.clothingstore.model.Supplier;
import com.clothingstore.model.SupplierStatus;
import com.clothingstore.model.PurchaseOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify the complete supplier management workflow
 * including CRUD operations, purchase orders, and business logic
 */
public class SupplierManagementWorkflowTest {

    private SupplierService supplierService;

    @BeforeEach
    void setUp() {
        supplierService = SupplierService.getInstance();
    }

    @Test
    @DisplayName("Test complete supplier CRUD workflow")
    void testSupplierCRUDWorkflow() {
        System.out.println("🧪 Testing Supplier CRUD Workflow...");

        // Step 1: Create a new supplier
        Supplier newSupplier = new Supplier();
        newSupplier.setCompanyName("Test Clothing Co.");
        newSupplier.setContactPerson("John Test");
        newSupplier.setEmail("<EMAIL>");
        newSupplier.setPhone("******-TEST");
        newSupplier.setAddress("123 Test Street");
        newSupplier.setCity("Test City");
        newSupplier.setState("TS");
        newSupplier.setZipCode("12345");
        newSupplier.setCountry("USA");
        newSupplier.setStatus(SupplierStatus.ACTIVE);
        newSupplier.setLeadTimeDays(14);
        newSupplier.setMinimumOrderAmount(new BigDecimal("500.00"));
        newSupplier.setCreditLimit(new BigDecimal("10000.00"));
        newSupplier.setNotes("Test supplier for workflow testing");

        // Create supplier
        SupplierOperationResult createResult = supplierService.createSupplier(newSupplier);
        assertTrue(createResult.isSuccess(), "Supplier creation should succeed");
        assertNotNull(createResult.getSupplier().getId(), "Created supplier should have an ID");
        System.out.println("✓ Supplier created with ID: " + createResult.getSupplier().getId());

        Supplier createdSupplier = createResult.getSupplier();

        // Step 2: Read/Retrieve the supplier
        Supplier retrievedSupplier = supplierService.getSupplier(createdSupplier.getId());
        assertNotNull(retrievedSupplier, "Should be able to retrieve created supplier");
        assertEquals(createdSupplier.getCompanyName(), retrievedSupplier.getCompanyName());
        assertEquals(createdSupplier.getEmail(), retrievedSupplier.getEmail());
        System.out.println("✓ Supplier retrieved successfully");

        // Step 3: Update the supplier
        retrievedSupplier.setContactPerson("Jane Updated");
        retrievedSupplier.setEmail("<EMAIL>");
        retrievedSupplier.setLeadTimeDays(21);
        retrievedSupplier.setStatus(SupplierStatus.UNDER_REVIEW);

        SupplierOperationResult updateResult = supplierService.updateSupplier(retrievedSupplier);
        assertTrue(updateResult.isSuccess(), "Supplier update should succeed");
        assertEquals("Jane Updated", updateResult.getSupplier().getContactPerson());
        assertEquals(21, updateResult.getSupplier().getLeadTimeDays());
        assertEquals(SupplierStatus.UNDER_REVIEW, updateResult.getSupplier().getStatus());
        System.out.println("✓ Supplier updated successfully");

        // Step 4: Test supplier listing
        List<Supplier> allSuppliers = supplierService.getAllSuppliers();
        assertTrue(allSuppliers.stream().anyMatch(s -> s.getId().equals(createdSupplier.getId())));
        System.out.println("✓ Supplier appears in supplier list");

        // Step 5: Test active suppliers filtering
        List<Supplier> activeSuppliers = supplierService.getActiveSuppliers();
        // Our supplier is UNDER_REVIEW, so it shouldn't be in active list
        assertFalse(activeSuppliers.stream().anyMatch(s -> s.getId().equals(createdSupplier.getId())));
        System.out.println("✓ Supplier filtering by status works correctly");

        // Step 6: Delete the supplier
        SupplierOperationResult deleteResult = supplierService.deleteSupplier(createdSupplier.getId());
        assertTrue(deleteResult.isSuccess(), "Supplier deletion should succeed");
        System.out.println("✓ Supplier deleted successfully");

        // Verify deletion
        Supplier deletedSupplier = supplierService.getSupplier(createdSupplier.getId());
        assertNull(deletedSupplier, "Deleted supplier should not be retrievable");
        System.out.println("✓ Supplier deletion verified");

        System.out.println("🎉 Supplier CRUD workflow test completed successfully!");
    }

    @Test
    @DisplayName("Test purchase order workflow")
    void testPurchaseOrderWorkflow() {
        System.out.println("🧪 Testing Purchase Order Workflow...");

        // Create a supplier first
        Supplier supplier = new Supplier();
        supplier.setCompanyName("PO Test Supplier");
        supplier.setContactPerson("PO Test Contact");
        supplier.setEmail("<EMAIL>");
        supplier.setStatus(SupplierStatus.ACTIVE);
        supplier.setLeadTimeDays(10);

        SupplierOperationResult supplierResult = supplierService.createSupplier(supplier);
        assertTrue(supplierResult.isSuccess());
        Supplier createdSupplier = supplierResult.getSupplier();
        System.out.println("✓ Test supplier created for PO testing");

        // Create purchase order
        PurchaseOrderOperationResult poResult = supplierService.createPurchaseOrder(
                createdSupplier.getId(), "Test User");
        assertTrue(poResult.isSuccess(), "Purchase order creation should succeed");
        assertNotNull(poResult.getPurchaseOrder().getOrderNumber());
        System.out.println("✓ Purchase order created: " + poResult.getPurchaseOrder().getOrderNumber());

        // Verify purchase order appears in list
        List<PurchaseOrder> allPOs = supplierService.getAllPurchaseOrders();
        assertTrue(allPOs.stream().anyMatch(po -> po.getId().equals(poResult.getPurchaseOrder().getId())));
        System.out.println("✓ Purchase order appears in list");

        // Clean up
        supplierService.deleteSupplier(createdSupplier.getId());
        System.out.println("✓ Test data cleaned up");

        System.out.println("🎉 Purchase Order workflow test completed successfully!");
    }

    @Test
    @DisplayName("Test supplier validation")
    void testSupplierValidation() {
        System.out.println("🧪 Testing Supplier Validation...");

        // Test missing company name
        Supplier invalidSupplier = new Supplier();
        invalidSupplier.setContactPerson("Test Contact");
        invalidSupplier.setEmail("<EMAIL>");

        SupplierOperationResult result = supplierService.createSupplier(invalidSupplier);
        assertFalse(result.isSuccess(), "Should fail validation without company name");
        assertTrue(result.getMessage().contains("Company name is required"));
        System.out.println("✓ Company name validation works");

        // Test invalid email
        Supplier invalidEmailSupplier = new Supplier();
        invalidEmailSupplier.setCompanyName("Test Company");
        invalidEmailSupplier.setContactPerson("Test Contact");
        invalidEmailSupplier.setEmail("invalid-email");

        SupplierOperationResult emailResult = supplierService.createSupplier(invalidEmailSupplier);
        assertFalse(emailResult.isSuccess(), "Should fail validation with invalid email");
        assertTrue(emailResult.getMessage().contains("Invalid email format"));
        System.out.println("✓ Email validation works");

        System.out.println("🎉 Supplier validation test completed successfully!");
    }

    @Test
    @DisplayName("Test supplier deletion with dependencies")
    void testSupplierDeletionWithDependencies() {
        System.out.println("🧪 Testing Supplier Deletion with Dependencies...");

        // Create supplier
        Supplier supplier = new Supplier();
        supplier.setCompanyName("Dependency Test Supplier");
        supplier.setContactPerson("Dependency Test");
        supplier.setEmail("<EMAIL>");
        supplier.setStatus(SupplierStatus.ACTIVE);

        SupplierOperationResult supplierResult = supplierService.createSupplier(supplier);
        assertTrue(supplierResult.isSuccess());
        Supplier createdSupplier = supplierResult.getSupplier();

        // Create purchase order for this supplier
        PurchaseOrderOperationResult poResult = supplierService.createPurchaseOrder(
                createdSupplier.getId(), "Test User");
        assertTrue(poResult.isSuccess());

        // Try to delete supplier with active purchase order
        SupplierOperationResult deleteResult = supplierService.deleteSupplier(createdSupplier.getId());
        assertFalse(deleteResult.isSuccess(), "Should not be able to delete supplier with active PO");
        assertTrue(deleteResult.getMessage().contains("active purchase orders"));
        System.out.println("✓ Supplier deletion blocked when dependencies exist");

        // Clean up - this would require completing/cancelling the PO first in real scenario
        System.out.println("✓ Dependency checking works correctly");

        System.out.println("🎉 Supplier deletion dependency test completed successfully!");
    }

    @Test
    @DisplayName("Test supplier business logic")
    void testSupplierBusinessLogic() {
        System.out.println("🧪 Testing Supplier Business Logic...");

        // Test supplier status transitions
        Supplier supplier = new Supplier();
        supplier.setCompanyName("Business Logic Test");
        supplier.setContactPerson("Logic Test");
        supplier.setEmail("<EMAIL>");
        supplier.setStatus(SupplierStatus.PENDING_APPROVAL);

        SupplierOperationResult result = supplierService.createSupplier(supplier);
        assertTrue(result.isSuccess());
        Supplier createdSupplier = result.getSupplier();

        // Test that pending approval supplier cannot receive orders
        assertFalse(createdSupplier.getStatus().canReceiveOrders());
        System.out.println("✓ Pending approval supplier cannot receive orders");

        // Update to active status
        createdSupplier.setStatus(SupplierStatus.ACTIVE);
        SupplierOperationResult updateResult = supplierService.updateSupplier(createdSupplier);
        assertTrue(updateResult.isSuccess());

        // Now should be able to receive orders
        assertTrue(updateResult.getSupplier().getStatus().canReceiveOrders());
        System.out.println("✓ Active supplier can receive orders");

        // Clean up
        supplierService.deleteSupplier(createdSupplier.getId());

        System.out.println("🎉 Supplier business logic test completed successfully!");
    }
}
