package com.clothingstore.controller;

import com.clothingstore.model.Product;
import com.clothingstore.model.Supplier;
import com.clothingstore.service.SupplierService;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.util.AlertUtil;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Enhanced Product Dialog Controller with Supplier Integration and Cost
 * Management
 */
public class ProductDialogController implements Initializable {

    // Basic Product Fields
    @FXML
    private TextField nameField;
    @FXML
    private TextField skuField;
    @FXML
    private TextField barcodeField;
    @FXML
    private TextArea descriptionArea;
    @FXML
    private ComboBox<String> categoryComboBox;
    @FXML
    private TextField brandField;
    @FXML
    private TextField colorField;
    @FXML
    private TextField sizeField;
    @FXML
    private TextField stockQuantityField;
    @FXML
    private TextField minStockLevelField;
    @FXML
    private TextField reorderQuantityField;
    @FXML
    private CheckBox lowStockAlertCheckBox;

    // Supplier Integration Fields
    @FXML
    private ComboBox<Supplier> supplierComboBox;
    @FXML
    private Button addSupplierButton;
    @FXML
    private Label supplierInfoLabel;

    // Cost Management Fields
    @FXML
    private TextField costPriceField;
    @FXML
    private TextField sellingPriceField;
    @FXML
    private Label profitMarginLabel;
    @FXML
    private Label profitPercentageLabel;

    // Financial Tracking Display
    @FXML
    private Label totalInvestmentLabel;
    @FXML
    private Label potentialRevenueLabel;
    @FXML
    private Label potentialProfitLabel;

    // Action Buttons
    @FXML
    private Button saveButton;
    @FXML
    private Button cancelButton;

    // Data
    private Product product;
    private boolean isEditMode = false;
    private SupplierService supplierService;
    private ProductDAO productDAO;
    private ObservableList<Supplier> suppliers;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        supplierService = SupplierService.getInstance();
        productDAO = ProductDAO.getInstance();
        suppliers = FXCollections.observableArrayList();

        setupSupplierComboBox();
        setupRealTimeCalculations();
        setupValidation();
        loadSuppliers();
        loadCategories();
    }

    /**
     * Set the product to edit (null for new product)
     */
    public void setProduct(Product product) {
        this.product = product;
        this.isEditMode = (product != null);

        if (isEditMode) {
            populateFields();
        } else {
            this.product = new Product();
            generateSKU();
        }

        updateFinancialDisplay();
    }

    /**
     * Setup supplier combo box with custom display
     */
    private void setupSupplierComboBox() {
        supplierComboBox.setCellFactory(listView -> new ListCell<Supplier>() {
            @Override
            protected void updateItem(Supplier supplier, boolean empty) {
                super.updateItem(supplier, empty);
                if (empty || supplier == null) {
                    setText(null);
                } else {
                    setText(supplier.getCompanyName() + " (" + supplier.getSupplierCode() + ")");
                }
            }
        });

        supplierComboBox.setButtonCell(new ListCell<Supplier>() {
            @Override
            protected void updateItem(Supplier supplier, boolean empty) {
                super.updateItem(supplier, empty);
                if (empty || supplier == null) {
                    setText("Select Supplier");
                } else {
                    setText(supplier.getCompanyName());
                }
            }
        });

        supplierComboBox.setOnAction(e -> {
            Supplier selected = supplierComboBox.getValue();
            if (selected != null) {
                updateSupplierInfo(selected);
            }
        });
    }

    /**
     * Setup real-time calculations for cost and profit
     */
    private void setupRealTimeCalculations() {
        // Add listeners to cost and selling price fields
        costPriceField.textProperty().addListener((obs, oldVal, newVal) -> updateFinancialDisplay());
        sellingPriceField.textProperty().addListener((obs, oldVal, newVal) -> updateFinancialDisplay());
        stockQuantityField.textProperty().addListener((obs, oldVal, newVal) -> updateFinancialDisplay());
    }

    /**
     * Setup field validation
     */
    private void setupValidation() {
        // Numeric validation for price fields
        costPriceField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*(\\.\\d{0,2})?")) {
                costPriceField.setText(oldVal);
            }
        });

        sellingPriceField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*(\\.\\d{0,2})?")) {
                sellingPriceField.setText(oldVal);
            }
        });

        stockQuantityField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*")) {
                stockQuantityField.setText(oldVal);
            }
        });

        minStockLevelField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*")) {
                minStockLevelField.setText(oldVal);
            }
        });

        reorderQuantityField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*")) {
                reorderQuantityField.setText(oldVal);
            }
        });
    }

    /**
     * Load suppliers from service
     */
    private void loadSuppliers() {
        try {
            List<Supplier> allSuppliers = supplierService.getActiveSuppliers();
            suppliers.clear();
            suppliers.addAll(allSuppliers);
            supplierComboBox.setItems(suppliers);
        } catch (Exception e) {
            AlertUtil.showError("Data Error", "Failed to load suppliers: " + e.getMessage());
        }
    }

    /**
     * Load categories (placeholder - integrate with existing category system)
     */
    private void loadCategories() {
        // This should integrate with your existing category management
        ObservableList<String> categories = FXCollections.observableArrayList(
                "Shirts", "Pants", "Dresses", "Jackets", "Shoes", "Accessories"
        );
        categoryComboBox.setItems(categories);
    }

    /**
     * Generate unique SKU for new products
     */
    private void generateSKU() {
        if (!isEditMode) {
            String sku = "PRD" + System.currentTimeMillis();
            skuField.setText(sku);
        }
    }

    /**
     * Populate fields when editing existing product
     */
    private void populateFields() {
        if (product == null) {
            return;
        }

        nameField.setText(product.getName());
        skuField.setText(product.getSku());
        barcodeField.setText(product.getBarcode());
        descriptionArea.setText(product.getDescription());
        categoryComboBox.setValue(product.getCategory());
        brandField.setText(product.getBrand());
        colorField.setText(product.getColor());
        sizeField.setText(product.getSize());
        stockQuantityField.setText(String.valueOf(product.getStockQuantity()));
        minStockLevelField.setText(String.valueOf(product.getMinStockLevel()));
        reorderQuantityField.setText(String.valueOf(product.getReorderQuantity()));
        lowStockAlertCheckBox.setSelected(product.isLowStockAlertEnabled());

        if (product.getCostPrice() != null) {
            costPriceField.setText(product.getCostPrice().toString());
        }
        if (product.getPrice() != null) {
            sellingPriceField.setText(product.getPrice().toString());
        }

        // Set supplier if exists
        if (product.getSupplierId() != null) {
            suppliers.stream()
                    .filter(s -> s.getId().equals(product.getSupplierId()))
                    .findFirst()
                    .ifPresent(supplier -> {
                        supplierComboBox.setValue(supplier);
                        updateSupplierInfo(supplier);
                    });
        }
    }

    /**
     * Update supplier information display
     */
    private void updateSupplierInfo(Supplier supplier) {
        if (supplier != null) {
            supplierInfoLabel.setText(String.format(
                    "Contact: %s | Email: %s | Lead Time: %d days",
                    supplier.getContactPerson(),
                    supplier.getEmail(),
                    supplier.getLeadTimeDays()
            ));
        } else {
            supplierInfoLabel.setText("No supplier selected");
        }
    }

    /**
     * Update financial display with real-time calculations
     */
    private void updateFinancialDisplay() {
        try {
            BigDecimal costPrice = null;
            BigDecimal sellingPrice = null;
            int stockQuantity = 0;

            // Parse values
            if (!costPriceField.getText().trim().isEmpty()) {
                costPrice = new BigDecimal(costPriceField.getText().trim());
            }
            if (!sellingPriceField.getText().trim().isEmpty()) {
                sellingPrice = new BigDecimal(sellingPriceField.getText().trim());
            }
            if (!stockQuantityField.getText().trim().isEmpty()) {
                stockQuantity = Integer.parseInt(stockQuantityField.getText().trim());
            }

            // Calculate and display profit information
            if (costPrice != null && sellingPrice != null) {
                BigDecimal profit = sellingPrice.subtract(costPrice);
                double profitPercentage = 0.0;

                if (costPrice.compareTo(BigDecimal.ZERO) > 0) {
                    profitPercentage = profit.divide(costPrice, 4, RoundingMode.HALF_UP).doubleValue() * 100;
                }

                profitMarginLabel.setText(String.format("$%.2f", profit));
                profitPercentageLabel.setText(String.format("%.2f%%", profitPercentage));

                // Validate pricing
                if (sellingPrice.compareTo(costPrice) <= 0) {
                    profitMarginLabel.setStyle("-fx-text-fill: red;");
                    profitPercentageLabel.setStyle("-fx-text-fill: red;");
                } else {
                    profitMarginLabel.setStyle("-fx-text-fill: green;");
                    profitPercentageLabel.setStyle("-fx-text-fill: green;");
                }

                // Calculate financial tracking
                if (stockQuantity > 0) {
                    BigDecimal totalInvestment = costPrice.multiply(new BigDecimal(stockQuantity));
                    BigDecimal potentialRevenue = sellingPrice.multiply(new BigDecimal(stockQuantity));
                    BigDecimal potentialProfit = profit.multiply(new BigDecimal(stockQuantity));

                    totalInvestmentLabel.setText(String.format("$%.2f", totalInvestment));
                    potentialRevenueLabel.setText(String.format("$%.2f", potentialRevenue));
                    potentialProfitLabel.setText(String.format("$%.2f", potentialProfit));
                } else {
                    totalInvestmentLabel.setText("$0.00");
                    potentialRevenueLabel.setText("$0.00");
                    potentialProfitLabel.setText("$0.00");
                }
            } else {
                profitMarginLabel.setText("$0.00");
                profitPercentageLabel.setText("0.00%");
                totalInvestmentLabel.setText("$0.00");
                potentialRevenueLabel.setText("$0.00");
                potentialProfitLabel.setText("$0.00");
            }

        } catch (NumberFormatException e) {
            // Invalid input - clear displays
            profitMarginLabel.setText("Invalid");
            profitPercentageLabel.setText("Invalid");
        }
    }

    @FXML
    private void handleAddSupplier() {
        // Open supplier management dialog to add new supplier
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(
                    getClass().getResource("/fxml/SupplierManagement.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Add New Supplier");
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(addSupplierButton.getScene().getWindow());
            stage.showAndWait();

            // Refresh suppliers after dialog closes
            loadSuppliers();

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open supplier management: " + e.getMessage());
        }
    }

    @FXML
    private void handleSave() {
        if (validateInput()) {
            try {
                updateProductFromFields();

                if (isEditMode) {
                    productDAO.save(product);
                    AlertUtil.showInfo("Success", "Product updated successfully!");
                } else {
                    productDAO.save(product);
                    AlertUtil.showInfo("Success", "Product created successfully!");
                }

                closeDialog();

            } catch (Exception e) {
                AlertUtil.showError("Save Error", "Failed to save product: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleCancel() {
        closeDialog();
    }

    /**
     * Validate input fields
     */
    private boolean validateInput() {
        if (nameField.getText().trim().isEmpty()) {
            AlertUtil.showWarning("Validation Error", "Product name is required.");
            nameField.requestFocus();
            return false;
        }

        if (skuField.getText().trim().isEmpty()) {
            AlertUtil.showWarning("Validation Error", "SKU is required.");
            skuField.requestFocus();
            return false;
        }

        if (categoryComboBox.getValue() == null) {
            AlertUtil.showWarning("Validation Error", "Category is required.");
            categoryComboBox.requestFocus();
            return false;
        }

        // Validate pricing
        try {
            if (!costPriceField.getText().trim().isEmpty() && !sellingPriceField.getText().trim().isEmpty()) {
                BigDecimal costPrice = new BigDecimal(costPriceField.getText().trim());
                BigDecimal sellingPrice = new BigDecimal(sellingPriceField.getText().trim());

                if (sellingPrice.compareTo(costPrice) <= 0) {
                    AlertUtil.showWarning("Pricing Error", "Selling price must be higher than cost price.");
                    sellingPriceField.requestFocus();
                    return false;
                }
            }
        } catch (NumberFormatException e) {
            AlertUtil.showWarning("Validation Error", "Please enter valid price values.");
            return false;
        }

        return true;
    }

    /**
     * Update product object from form fields
     */
    private void updateProductFromFields() {
        product.setName(nameField.getText().trim());
        product.setSku(skuField.getText().trim());
        product.setBarcode(barcodeField.getText().trim());
        product.setDescription(descriptionArea.getText().trim());
        product.setCategory(categoryComboBox.getValue());
        product.setBrand(brandField.getText().trim());
        product.setColor(colorField.getText().trim());
        product.setSize(sizeField.getText().trim());
        product.setLowStockAlertEnabled(lowStockAlertCheckBox.isSelected());

        // Set numeric fields
        if (!stockQuantityField.getText().trim().isEmpty()) {
            product.setStockQuantity(Integer.parseInt(stockQuantityField.getText().trim()));
        }
        if (!minStockLevelField.getText().trim().isEmpty()) {
            product.setMinStockLevel(Integer.parseInt(minStockLevelField.getText().trim()));
        }
        if (!reorderQuantityField.getText().trim().isEmpty()) {
            product.setReorderQuantity(Integer.parseInt(reorderQuantityField.getText().trim()));
        }

        // Set price fields
        if (!costPriceField.getText().trim().isEmpty()) {
            product.setCostPrice(new BigDecimal(costPriceField.getText().trim()));
        }
        if (!sellingPriceField.getText().trim().isEmpty()) {
            product.setPrice(new BigDecimal(sellingPriceField.getText().trim()));
        }

        // Set supplier information
        Supplier selectedSupplier = supplierComboBox.getValue();
        if (selectedSupplier != null) {
            product.setSupplierId(selectedSupplier.getId());
            product.setSupplierName(selectedSupplier.getCompanyName());
            product.setSupplierCode(selectedSupplier.getSupplierCode());
        }
    }

    /**
     * Close the dialog
     */
    private void closeDialog() {
        Stage stage = (Stage) saveButton.getScene().getWindow();
        stage.close();
    }
}
