package com.clothingstore.test;

import com.clothingstore.service.ProfitAnalysisService;
import com.clothingstore.service.ProfitAnalysisService.ProfitMetrics;
import com.clothingstore.service.ProfitAnalysisService.CategoryProfitData;
import com.clothingstore.service.ProfitAnalysisService.ComparisonMetrics;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Live demonstration of Reports page functionality with real database data
 * This simulates what users see in the actual Reports interface
 */
public class LiveReportsDemo {

    public static void main(String[] args) {
        System.out.println("🎯 LIVE REPORTS PAGE DEMONSTRATION");
        System.out.println("Using Real Database Data from Clothing Store");
        System.out.println("=".repeat(80));
        System.out.println("Timestamp: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();

        try {
            ProfitAnalysisService profitService = new ProfitAnalysisService();
            
            // Simulate the Reports page workflow
            demonstrateReportsPageWorkflow(profitService);
            
        } catch (Exception e) {
            System.err.println("❌ Live demo failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void demonstrateReportsPageWorkflow(ProfitAnalysisService profitService) {
        System.out.println("📊 SIMULATING REPORTS PAGE USER INTERACTION");
        System.out.println("-".repeat(60));
        
        // 1. User clicks "Last 30 Days" preset button
        System.out.println("\n🔘 USER ACTION: Clicked 'Last 30 Days' preset button");
        System.out.println("⏳ Generating report with real database data...");
        
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(30);
        
        // 2. Overview Tab - Main profit metrics
        System.out.println("\n📈 OVERVIEW TAB - PROFIT METRICS");
        System.out.println("-".repeat(40));
        displayOverviewTab(profitService, startDate, endDate);
        
        // 3. Category Analysis Tab - Category breakdown with pie chart
        System.out.println("\n🏷️ CATEGORY ANALYSIS TAB - PIE CHART DATA");
        System.out.println("-".repeat(40));
        displayCategoryAnalysisTab(profitService, startDate, endDate);
        
        // 4. Comparison Tab - Period comparison with bar chart
        System.out.println("\n📊 COMPARISON TAB - BAR CHART DATA");
        System.out.println("-".repeat(40));
        displayComparisonTab(profitService, startDate, endDate);
        
        // 5. Export functionality demonstration
        System.out.println("\n💾 EXPORT FUNCTIONALITY");
        System.out.println("-".repeat(40));
        demonstrateExportFeatures(profitService, startDate, endDate);
        
        // 6. Different time period analysis
        System.out.println("\n⏰ TESTING DIFFERENT TIME PERIODS");
        System.out.println("-".repeat(40));
        testDifferentTimePeriods(profitService);
        
        System.out.println("\n" + "=".repeat(80));
        System.out.println("✅ LIVE REPORTS DEMONSTRATION COMPLETE");
        System.out.println("All data shown above is exactly what appears in the Reports page UI");
        System.out.println("=".repeat(80));
    }

    private static void displayOverviewTab(ProfitAnalysisService profitService, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            ProfitMetrics metrics = profitService.calculateProfitMetrics(startDate, endDate);
            
            System.out.println("📋 METRIC CARDS DISPLAY:");
            System.out.println("┌─────────────────┬─────────────────┬─────────────────┐");
            System.out.println("│ Total Revenue   │ Total Cost      │ Gross Profit    │");
            System.out.println("│ " + String.format("%-15s", metrics.getFormattedRevenue()) + " │ " + 
                             String.format("%-15s", metrics.getFormattedCost()) + " │ " + 
                             String.format("%-15s", metrics.getFormattedProfit()) + " │");
            System.out.println("├─────────────────┼─────────────────┼─────────────────┤");
            System.out.println("│ Profit Margin   │ Items Sold      │ Transactions    │");
            System.out.println("│ " + String.format("%-15s", metrics.getFormattedProfitMargin()) + " │ " + 
                             String.format("%-15s", String.valueOf(metrics.getTotalItemsSold())) + " │ " + 
                             String.format("%-15s", String.valueOf(metrics.getTransactionCount())) + " │");
            System.out.println("└─────────────────┴─────────────────┴─────────────────┘");
            
            System.out.println("\n📝 SUMMARY TEXT:");
            System.out.println("Period: " + startDate.toLocalDate() + " to " + endDate.toLocalDate());
            System.out.println("Business generated " + metrics.getFormattedRevenue() + " in revenue");
            System.out.println("with " + metrics.getFormattedCost() + " in costs, resulting in");
            System.out.println(metrics.getFormattedProfit() + " profit (" + metrics.getFormattedProfitMargin() + " margin)");
            System.out.println("across " + metrics.getTransactionCount() + " transactions selling " + metrics.getTotalItemsSold() + " items.");
            
        } catch (Exception e) {
            System.err.println("Overview tab error: " + e.getMessage());
        }
    }

    private static void displayCategoryAnalysisTab(ProfitAnalysisService profitService, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            List<CategoryProfitData> categories = profitService.calculateCategoryProfitBreakdown(startDate, endDate);
            
            if (categories.isEmpty()) {
                System.out.println("📊 PIE CHART: No data available for selected period");
                return;
            }
            
            System.out.println("📊 PIE CHART DATA (Revenue Distribution):");
            double totalRevenue = categories.stream().mapToDouble(CategoryProfitData::getRevenue).sum();
            
            for (CategoryProfitData category : categories) {
                double percentage = (category.getRevenue() / totalRevenue) * 100;
                System.out.println("🔵 " + category.getCategoryName() + ": " + 
                                 category.getFormattedRevenue() + " (" + String.format("%.1f%%", percentage) + ")");
            }
            
            System.out.println("\n📋 CATEGORY PERFORMANCE CARDS:");
            for (CategoryProfitData category : categories) {
                System.out.println("\n📂 " + category.getCategoryName().toUpperCase());
                System.out.println("   Revenue: " + category.getFormattedRevenue());
                System.out.println("   Cost: " + category.getFormattedCost());
                System.out.println("   Profit: " + category.getFormattedProfit());
                System.out.println("   Margin: " + category.getFormattedProfitMargin());
                System.out.println("   Items: " + category.getItemsSold() + " sold in " + category.getTransactionCount() + " transactions");
            }
            
        } catch (Exception e) {
            System.err.println("Category analysis error: " + e.getMessage());
        }
    }

    private static void displayComparisonTab(ProfitAnalysisService profitService, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            ComparisonMetrics comparison = profitService.calculatePeriodComparison(startDate, endDate);
            
            System.out.println("📊 BAR CHART DATA (Current vs Previous Period):");
            System.out.println("┌─────────────┬─────────────────┬─────────────────┬─────────────┐");
            System.out.println("│ Metric      │ Current Period  │ Previous Period │ Growth      │");
            System.out.println("├─────────────┼─────────────────┼─────────────────┼─────────────┤");
            System.out.println("│ Revenue     │ " + String.format("%-15s", comparison.getCurrentPeriod().getFormattedRevenue()) + 
                             " │ " + String.format("%-15s", comparison.getPreviousPeriod().getFormattedRevenue()) + 
                             " │ " + String.format("%-11s", comparison.getFormattedRevenueGrowth()) + " │");
            System.out.println("│ Cost        │ " + String.format("%-15s", comparison.getCurrentPeriod().getFormattedCost()) + 
                             " │ " + String.format("%-15s", comparison.getPreviousPeriod().getFormattedCost()) + 
                             " │ " + String.format("%-11s", comparison.getFormattedCostGrowth()) + " │");
            System.out.println("│ Profit      │ " + String.format("%-15s", comparison.getCurrentPeriod().getFormattedProfit()) + 
                             " │ " + String.format("%-15s", comparison.getPreviousPeriod().getFormattedProfit()) + 
                             " │ " + String.format("%-11s", comparison.getFormattedProfitGrowth()) + " │");
            System.out.println("└─────────────┴─────────────────┴─────────────────┴─────────────┘");
            
            System.out.println("\n📈 GROWTH ANALYSIS:");
            if (comparison.getRevenueGrowth() > 0) {
                System.out.println("✅ Revenue increased by " + comparison.getFormattedRevenueGrowth());
            } else if (comparison.getRevenueGrowth() < 0) {
                System.out.println("📉 Revenue decreased by " + comparison.getFormattedRevenueGrowth());
            } else {
                System.out.println("➡️ Revenue remained stable");
            }
            
            if (comparison.getProfitGrowth() > 0) {
                System.out.println("✅ Profit increased by " + comparison.getFormattedProfitGrowth());
            } else if (comparison.getProfitGrowth() < 0) {
                System.out.println("📉 Profit decreased by " + comparison.getFormattedProfitGrowth());
            } else {
                System.out.println("➡️ Profit remained stable");
            }
            
        } catch (Exception e) {
            System.err.println("Comparison analysis error: " + e.getMessage());
        }
    }

    private static void demonstrateExportFeatures(ProfitAnalysisService profitService, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            ProfitMetrics metrics = profitService.calculateProfitMetrics(startDate, endDate);
            
            System.out.println("🔘 USER ACTION: Clicked 'Export CSV' button");
            System.out.println("📄 CSV EXPORT PREVIEW:");
            System.out.println("Date Range,Revenue,Cost,Profit,Margin,Items Sold,Transactions");
            System.out.println(startDate.toLocalDate() + " to " + endDate.toLocalDate() + "," +
                             metrics.getTotalRevenue() + "," + metrics.getTotalCost() + "," +
                             metrics.getTotalProfit() + "," + metrics.getProfitMargin() + "%," +
                             metrics.getTotalItemsSold() + "," + metrics.getTransactionCount());
            
            System.out.println("\n🔘 USER ACTION: Clicked 'Export PDF' button");
            System.out.println("📄 PDF EXPORT PREVIEW:");
            System.out.println("PROFIT ANALYSIS REPORT");
            System.out.println("======================");
            System.out.println("Period: " + startDate.toLocalDate() + " to " + endDate.toLocalDate());
            System.out.println("Total Revenue: " + metrics.getFormattedRevenue());
            System.out.println("Total Cost: " + metrics.getFormattedCost());
            System.out.println("Gross Profit: " + metrics.getFormattedProfit());
            System.out.println("Profit Margin: " + metrics.getFormattedProfitMargin());
            System.out.println("✅ Export functionality ready for file save");
            
        } catch (Exception e) {
            System.err.println("Export demonstration error: " + e.getMessage());
        }
    }

    private static void testDifferentTimePeriods(ProfitAnalysisService profitService) {
        LocalDateTime now = LocalDateTime.now();
        
        System.out.println("🔘 USER ACTION: Testing different preset buttons");
        
        // Today
        System.out.println("\n📅 TODAY:");
        testPeriod(profitService, now.toLocalDate().atStartOfDay(), now, "Today");
        
        // This Week
        System.out.println("\n📅 THIS WEEK:");
        testPeriod(profitService, now.minusDays(7), now, "This Week");
        
        // This Month
        System.out.println("\n📅 THIS MONTH:");
        testPeriod(profitService, now.minusDays(30), now, "This Month");
        
        // Last 90 Days
        System.out.println("\n📅 LAST 90 DAYS:");
        testPeriod(profitService, now.minusDays(90), now, "Last 90 Days");
    }

    private static void testPeriod(ProfitAnalysisService profitService, LocalDateTime start, LocalDateTime end, String periodName) {
        try {
            ProfitMetrics metrics = profitService.calculateProfitMetrics(start, end);
            System.out.println("   " + periodName + ": " + metrics.getFormattedRevenue() + " revenue, " + 
                             metrics.getFormattedProfit() + " profit (" + metrics.getFormattedProfitMargin() + ")");
        } catch (Exception e) {
            System.out.println("   " + periodName + ": Error - " + e.getMessage());
        }
    }
}
