package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Model class for Return/Exchange transactions
 */
public class ReturnExchange {
    private Long id;
    private String returnNumber;
    private Long originalTransactionId;
    private String originalTransactionNumber;
    private Long customerId;
    private String customerName;
    private String type; // RETURN, EXCHANGE
    private String reason;
    private String status; // PENDING, APPROVED, REJECTED, COMPLETED
    private BigDecimal totalAmount;
    private BigDecimal refundAmount;
    private BigDecimal exchangeAmount;
    private String paymentMethod;
    private String notes;
    private String processedBy;
    private LocalDateTime requestDate;
    private LocalDateTime processedDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Return/Exchange items
    private List<ReturnExchangeItem> items;

    // Constructors
    public ReturnExchange() {
        this.status = "PENDING";
        this.requestDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public ReturnExchange(String returnNumber, Long originalTransactionId, Long customerId, String type) {
        this();
        this.returnNumber = returnNumber;
        this.originalTransactionId = originalTransactionId;
        this.customerId = customerId;
        this.type = type;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReturnNumber() {
        return returnNumber;
    }

    public void setReturnNumber(String returnNumber) {
        this.returnNumber = returnNumber;
    }

    public Long getOriginalTransactionId() {
        return originalTransactionId;
    }

    public void setOriginalTransactionId(Long originalTransactionId) {
        this.originalTransactionId = originalTransactionId;
    }

    public String getOriginalTransactionNumber() {
        return originalTransactionNumber;
    }

    public void setOriginalTransactionNumber(String originalTransactionNumber) {
        this.originalTransactionNumber = originalTransactionNumber;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getExchangeAmount() {
        return exchangeAmount;
    }

    public void setExchangeAmount(BigDecimal exchangeAmount) {
        this.exchangeAmount = exchangeAmount;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getProcessedBy() {
        return processedBy;
    }

    public void setProcessedBy(String processedBy) {
        this.processedBy = processedBy;
    }

    public LocalDateTime getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(LocalDateTime requestDate) {
        this.requestDate = requestDate;
    }

    public LocalDateTime getProcessedDate() {
        return processedDate;
    }

    public void setProcessedDate(LocalDateTime processedDate) {
        this.processedDate = processedDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<ReturnExchangeItem> getItems() {
        return items;
    }

    public void setItems(List<ReturnExchangeItem> items) {
        this.items = items;
    }

    // Utility methods
    public boolean isReturn() {
        return "RETURN".equals(type);
    }

    public boolean isExchange() {
        return "EXCHANGE".equals(type);
    }

    public boolean isPending() {
        return "PENDING".equals(status);
    }

    public boolean isApproved() {
        return "APPROVED".equals(status);
    }

    public boolean isRejected() {
        return "REJECTED".equals(status);
    }

    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }

    public void approve(String processor) {
        this.status = "APPROVED";
        this.processedBy = processor;
        this.processedDate = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public void reject(String processor, String reason) {
        this.status = "REJECTED";
        this.processedBy = processor;
        this.processedDate = LocalDateTime.now();
        this.notes = (this.notes != null ? this.notes + "\n" : "") + "Rejected: " + reason;
        this.updatedAt = LocalDateTime.now();
    }

    public void complete(String processor) {
        this.status = "COMPLETED";
        this.processedBy = processor;
        this.processedDate = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public boolean isWithinReturnWindow(int returnWindowDays) {
        if (requestDate == null) return false;
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(returnWindowDays);
        return requestDate.isAfter(cutoffDate);
    }

    public String getFormattedRequestDate() {
        return requestDate != null ? 
            requestDate.format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm")) : "";
    }

    public String getFormattedProcessedDate() {
        return processedDate != null ? 
            processedDate.format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm")) : "";
    }

    public String getDisplayStatus() {
        switch (status) {
            case "PENDING": return "Pending";
            case "APPROVED": return "Approved";
            case "REJECTED": return "Rejected";
            case "COMPLETED": return "Completed";
            default: return status;
        }
    }

    public String getDisplayType() {
        switch (type) {
            case "RETURN": return "Return";
            case "EXCHANGE": return "Exchange";
            default: return type;
        }
    }

    @Override
    public String toString() {
        return String.format("ReturnExchange{number=%s, type=%s, status=%s, amount=%s}", 
                returnNumber, type, status, totalAmount);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ReturnExchange that = (ReturnExchange) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
