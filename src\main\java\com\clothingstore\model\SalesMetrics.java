package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Model class representing sales metrics and KPIs
 */
public class SalesMetrics {
    
    private LocalDateTime periodStart;
    private LocalDateTime periodEnd;
    
    // Revenue metrics
    private BigDecimal totalRevenue;
    private BigDecimal totalCost;
    private BigDecimal grossProfit;
    private BigDecimal netProfit;
    private BigDecimal averageOrderValue;
    
    // Transaction metrics
    private int totalTransactions;
    private int totalItemsSold;
    private int totalCustomers;
    private int newCustomers;
    private int returningCustomers;
    
    // Product metrics
    private int totalProducts;
    private int lowStockProducts;
    private int outOfStockProducts;
    
    // Performance metrics
    private BigDecimal salesGrowth; // Percentage growth compared to previous period
    private BigDecimal profitMargin; // Gross profit margin percentage
    private BigDecimal customerRetentionRate;
    private BigDecimal averageItemsPerTransaction;
    
    // Top performers
    private Map<String, BigDecimal> topSellingCategories;
    private Map<String, Integer> topSellingProducts;
    private Map<String, BigDecimal> salesByPaymentMethod;
    private Map<String, BigDecimal> salesByHour;
    private Map<String, BigDecimal> salesByDay;
    
    // Constructors
    public SalesMetrics() {
        this.totalRevenue = BigDecimal.ZERO;
        this.totalCost = BigDecimal.ZERO;
        this.grossProfit = BigDecimal.ZERO;
        this.netProfit = BigDecimal.ZERO;
        this.averageOrderValue = BigDecimal.ZERO;
        this.salesGrowth = BigDecimal.ZERO;
        this.profitMargin = BigDecimal.ZERO;
        this.customerRetentionRate = BigDecimal.ZERO;
        this.averageItemsPerTransaction = BigDecimal.ZERO;
    }
    
    public SalesMetrics(LocalDateTime periodStart, LocalDateTime periodEnd) {
        this();
        this.periodStart = periodStart;
        this.periodEnd = periodEnd;
    }
    
    // Getters and Setters
    public LocalDateTime getPeriodStart() {
        return periodStart;
    }
    
    public void setPeriodStart(LocalDateTime periodStart) {
        this.periodStart = periodStart;
    }
    
    public LocalDateTime getPeriodEnd() {
        return periodEnd;
    }
    
    public void setPeriodEnd(LocalDateTime periodEnd) {
        this.periodEnd = periodEnd;
    }
    
    public BigDecimal getTotalRevenue() {
        return totalRevenue;
    }
    
    public void setTotalRevenue(BigDecimal totalRevenue) {
        this.totalRevenue = totalRevenue;
    }
    
    public BigDecimal getTotalCost() {
        return totalCost;
    }
    
    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }
    
    public BigDecimal getGrossProfit() {
        return grossProfit;
    }
    
    public void setGrossProfit(BigDecimal grossProfit) {
        this.grossProfit = grossProfit;
    }
    
    public BigDecimal getNetProfit() {
        return netProfit;
    }
    
    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }
    
    public BigDecimal getAverageOrderValue() {
        return averageOrderValue;
    }
    
    public void setAverageOrderValue(BigDecimal averageOrderValue) {
        this.averageOrderValue = averageOrderValue;
    }
    
    public int getTotalTransactions() {
        return totalTransactions;
    }
    
    public void setTotalTransactions(int totalTransactions) {
        this.totalTransactions = totalTransactions;
    }
    
    public int getTotalItemsSold() {
        return totalItemsSold;
    }
    
    public void setTotalItemsSold(int totalItemsSold) {
        this.totalItemsSold = totalItemsSold;
    }
    
    public int getTotalCustomers() {
        return totalCustomers;
    }
    
    public void setTotalCustomers(int totalCustomers) {
        this.totalCustomers = totalCustomers;
    }
    
    public int getNewCustomers() {
        return newCustomers;
    }
    
    public void setNewCustomers(int newCustomers) {
        this.newCustomers = newCustomers;
    }
    
    public int getReturningCustomers() {
        return returningCustomers;
    }
    
    public void setReturningCustomers(int returningCustomers) {
        this.returningCustomers = returningCustomers;
    }
    
    public int getTotalProducts() {
        return totalProducts;
    }
    
    public void setTotalProducts(int totalProducts) {
        this.totalProducts = totalProducts;
    }
    
    public int getLowStockProducts() {
        return lowStockProducts;
    }
    
    public void setLowStockProducts(int lowStockProducts) {
        this.lowStockProducts = lowStockProducts;
    }
    
    public int getOutOfStockProducts() {
        return outOfStockProducts;
    }
    
    public void setOutOfStockProducts(int outOfStockProducts) {
        this.outOfStockProducts = outOfStockProducts;
    }
    
    public BigDecimal getSalesGrowth() {
        return salesGrowth;
    }
    
    public void setSalesGrowth(BigDecimal salesGrowth) {
        this.salesGrowth = salesGrowth;
    }
    
    public BigDecimal getProfitMargin() {
        return profitMargin;
    }
    
    public void setProfitMargin(BigDecimal profitMargin) {
        this.profitMargin = profitMargin;
    }
    
    public BigDecimal getCustomerRetentionRate() {
        return customerRetentionRate;
    }
    
    public void setCustomerRetentionRate(BigDecimal customerRetentionRate) {
        this.customerRetentionRate = customerRetentionRate;
    }
    
    public BigDecimal getAverageItemsPerTransaction() {
        return averageItemsPerTransaction;
    }
    
    public void setAverageItemsPerTransaction(BigDecimal averageItemsPerTransaction) {
        this.averageItemsPerTransaction = averageItemsPerTransaction;
    }
    
    public Map<String, BigDecimal> getTopSellingCategories() {
        return topSellingCategories;
    }
    
    public void setTopSellingCategories(Map<String, BigDecimal> topSellingCategories) {
        this.topSellingCategories = topSellingCategories;
    }
    
    public Map<String, Integer> getTopSellingProducts() {
        return topSellingProducts;
    }
    
    public void setTopSellingProducts(Map<String, Integer> topSellingProducts) {
        this.topSellingProducts = topSellingProducts;
    }
    
    public Map<String, BigDecimal> getSalesByPaymentMethod() {
        return salesByPaymentMethod;
    }
    
    public void setSalesByPaymentMethod(Map<String, BigDecimal> salesByPaymentMethod) {
        this.salesByPaymentMethod = salesByPaymentMethod;
    }
    
    public Map<String, BigDecimal> getSalesByHour() {
        return salesByHour;
    }
    
    public void setSalesByHour(Map<String, BigDecimal> salesByHour) {
        this.salesByHour = salesByHour;
    }
    
    public Map<String, BigDecimal> getSalesByDay() {
        return salesByDay;
    }
    
    public void setSalesByDay(Map<String, BigDecimal> salesByDay) {
        this.salesByDay = salesByDay;
    }
    
    // Utility methods
    public boolean hasData() {
        return totalTransactions > 0 || totalRevenue.compareTo(BigDecimal.ZERO) > 0;
    }
    
    public String getPeriodDescription() {
        if (periodStart == null || periodEnd == null) {
            return "All Time";
        }
        
        return String.format("%s to %s", 
                periodStart.toLocalDate().toString(), 
                periodEnd.toLocalDate().toString());
    }
    
    @Override
    public String toString() {
        return String.format("SalesMetrics{period=%s, revenue=%s, transactions=%d}", 
                getPeriodDescription(), totalRevenue, totalTransactions);
    }
}
