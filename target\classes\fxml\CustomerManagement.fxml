<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<?import java.net.URL?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.CustomerManagementController" styleClass="customer-management-container">
   <stylesheets>
      <URL value="@../css/customer-management.css" />
   </stylesheets>
   <children>
      <!-- Header - Enhanced with better spacing -->
      <HBox alignment="CENTER_LEFT" spacing="12.0" styleClass="header-section">
         <children>
            <Label styleClass="page-title" text="Customer Management">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnManageGroups" onAction="#handleManageGroups" styleClass="action-button" text="Manage Groups" />
            <Button fx:id="btnAddCustomer" onAction="#handleAddCustomer" styleClass="primary-button" text="Add Customer" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" styleClass="secondary-button" text="Refresh" />
         </children>
         <padding>
            <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
         </padding>
      </HBox>

      <!-- ENHANCED CUSTOMER ANALYTICS DASHBOARD -->
      <VBox spacing="20.0" styleClass="enhanced-analytics-container">
         <children>
            <!-- Analytics Header with Controls -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="analytics-header-bar">
               <children>
                  <Label text="Customer Analytics Dashboard" styleClass="analytics-main-title" />
                  <Region HBox.hgrow="ALWAYS" />
                  <ComboBox fx:id="cmbAnalyticsPeriod" prefWidth="150.0" styleClass="analytics-period-selector" />
                  <Button fx:id="btnRefreshAnalytics" text="Refresh" styleClass="analytics-refresh-btn" onAction="#handleRefreshAnalytics" />
                  <Button fx:id="btnExportAnalytics" text="Export" styleClass="analytics-export-btn" onAction="#handleExportAnalytics" />
               </children>
            </HBox>

            <!-- Enhanced Metrics Cards with Animations -->
            <HBox spacing="20.0" alignment="CENTER" styleClass="metrics-container">
               <children>
                  <!-- Total Customers Card -->
                  <VBox styleClass="enhanced-metric-card total-customers-card" spacing="10.0" onMouseClicked="#handleTotalCustomersClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                           <children>
                              <Label text="👥" styleClass="metric-icon" />
                              <VBox spacing="3.0">
                                 <children>
                                    <Label fx:id="lblTotalCustomers" text="0" styleClass="metric-value-large" />
                                    <Label fx:id="lblTotalCustomersTrend" text="↗ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="Total Customers" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblTotalCustomersSubtext" text="All registered customers" styleClass="metric-subtext" />
                     </children>
                  </VBox>

                  <!-- Active Customers Card -->
                  <VBox styleClass="enhanced-metric-card active-customers-card" spacing="10.0" onMouseClicked="#handleActiveCustomersClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                           <children>
                              <Label text="✅" styleClass="metric-icon" />
                              <VBox spacing="3.0">
                                 <children>
                                    <Label fx:id="lblActiveCustomers" text="0" styleClass="metric-value-large" />
                                    <Label fx:id="lblActiveCustomersTrend" text="↗ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="Active Customers" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblActiveCustomersSubtext" text="Active in last 30 days" styleClass="metric-subtext" />
                     </children>
                  </VBox>

                  <!-- Top Spenders Card -->
                  <VBox styleClass="enhanced-metric-card top-spenders-card" spacing="10.0" onMouseClicked="#handleTopSpendersClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                           <children>
                              <Label text="⭐" styleClass="metric-icon" />
                              <VBox spacing="3.0">
                                 <children>
                                    <Label fx:id="lblTopSpenders" text="0" styleClass="metric-value-large" />
                                    <Label fx:id="lblTopSpendersTrend" text="↗ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="Top Spenders" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblTopSpendersSubtext" text="Lifetime value > $500" styleClass="metric-subtext" />
                     </children>
                  </VBox>

                  <!-- New Customers Card -->
                  <VBox styleClass="enhanced-metric-card new-customers-card" spacing="10.0" onMouseClicked="#handleNewCustomersClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                           <children>
                              <Label text="🆕" styleClass="metric-icon" />
                              <VBox spacing="3.0">
                                 <children>
                                    <Label fx:id="lblNewCustomers" text="0" styleClass="metric-value-large" />
                                    <Label fx:id="lblNewCustomersTrend" text="↗ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="New This Month" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblNewCustomersSubtext" text="Registered in last 30 days" styleClass="metric-subtext" />
                     </children>
                  </VBox>

                  <!-- Average Lifetime Value Card -->
                  <VBox styleClass="enhanced-metric-card lifetime-value-card" spacing="10.0" onMouseClicked="#handleLifetimeValueClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="12.0">
                           <children>
                              <Label text="💰" styleClass="metric-icon" />
                              <VBox spacing="3.0">
                                 <children>
                                    <Label fx:id="lblAverageLifetimeValue" text="$0.00" styleClass="metric-value-large" />
                                    <Label fx:id="lblLifetimeValueTrend" text="↗ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="Avg Lifetime Value" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblLifetimeValueSubtext" text="Average per customer" styleClass="metric-subtext" />
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Loading Indicator -->
            <HBox fx:id="analyticsLoadingIndicator" alignment="CENTER" spacing="10.0" visible="false" managed="false">
               <children>
                  <Label text="Loading..." styleClass="loading-spinner" />
                  <Label text="Loading analytics data..." styleClass="loading-text" />
               </children>
            </HBox>
         </children>
         <VBox.margin>
            <Insets bottom="20.0" left="15.0" right="15.0" top="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Enhanced Search and Filter Section with Modern Design -->
      <VBox spacing="15.0" styleClass="filter-section">
         <children>
            <!-- Search Row with Icon -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="Search:" />
                  <TextField fx:id="txtSearch" onKeyReleased="#handleSearch" prefWidth="280.0" promptText="Search by name, phone, address..." styleClass="search-box" />
                  <Region HBox.hgrow="SOMETIMES" />
                  <Label fx:id="lblFilteredCount" styleClass="filter-results" text="Showing: 0 customers" />
               </children>
            </HBox>

            <!-- Filter Controls Row -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="Group:" />
                  <ComboBox fx:id="cmbMembership" onAction="#handleMembershipFilter" prefWidth="150.0" promptText="All Groups" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="Status:" />
                  <ComboBox fx:id="cmbStatus" onAction="#handleStatusFilter" prefWidth="120.0" promptText="All" styleClass="combo-box" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnClearFilters" onAction="#handleClearFilters" styleClass="clear-filters-button" text="Clear All" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="15.0" right="15.0" top="12.0" />
         </padding>
      </VBox>

      <!-- Customer Statistics - Enhanced grid layout -->
      <GridPane hgap="15.0" vgap="15.0" styleClass="stats-section">
         <columnConstraints>
            <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" prefWidth="200.0" />
            <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" prefWidth="200.0" />
            <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" prefWidth="200.0" />
            <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" prefWidth="200.0" />
            <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" prefWidth="200.0" />
         </columnConstraints>
         <children>
            <VBox alignment="CENTER" spacing="6.0" styleClass="dashboard-card" GridPane.columnIndex="0" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="title" text="Total Customers" />
                  <Label fx:id="lblTotalCustomers" styleClass="value" text="0" />
               </children>
            </VBox>
            <VBox alignment="CENTER" spacing="6.0" styleClass="dashboard-card" GridPane.columnIndex="1" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="title" text="Active Members" />
                  <Label fx:id="lblActiveMembers" styleClass="value" text="0" />
               </children>
            </VBox>
            <VBox alignment="CENTER" spacing="6.0" styleClass="dashboard-card" GridPane.columnIndex="2" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="title" text="Total Loyalty Points" />
                  <Label fx:id="lblTotalPoints" styleClass="value" text="0" />
               </children>
            </VBox>
            <VBox alignment="CENTER" spacing="6.0" styleClass="dashboard-card" GridPane.columnIndex="3" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="title" text="Average Spent" />
                  <Label fx:id="lblAverageSpent" styleClass="value" text="$0.00" />
               </children>
            </VBox>
            <VBox alignment="CENTER" spacing="6.0" styleClass="dashboard-card" GridPane.columnIndex="4" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="title" text="This Month" />
                  <Label fx:id="lblMonthlyCustomers" styleClass="value" text="0" />
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="12.0" left="12.0" right="12.0" top="8.0" />
         </padding>
      </GridPane>

      <!-- Customers Table - Optimized column widths and better organization -->
      <TableView fx:id="tblCustomers" styleClass="data-grid" VBox.vgrow="ALWAYS">
         <columns>
            <TableColumn fx:id="colName" prefWidth="180.0" text="Customer Name" />
            <TableColumn fx:id="colPhone" prefWidth="140.0" text="Phone Number" />
            <TableColumn fx:id="colMembership" prefWidth="120.0" text="Group" />
            <TableColumn fx:id="colPoints" prefWidth="90.0" text="Points" />
            <TableColumn fx:id="colTotalSpent" prefWidth="120.0" text="Total Spent" />
            <TableColumn fx:id="colTotalPurchases" prefWidth="100.0" text="Purchases" />
            <TableColumn fx:id="colLastPurchase" prefWidth="140.0" text="Last Purchase" />
            <TableColumn fx:id="colStatus" prefWidth="90.0" text="Status" />
            <TableColumn fx:id="colActions" prefWidth="140.0" text="Actions" />
         </columns>
         <contextMenu>
            <ContextMenu>
               <items>
                  <MenuItem fx:id="menuEdit" onAction="#handleEditCustomer" text="Edit Customer" />
                  <MenuItem fx:id="menuViewHistory" onAction="#handleViewHistory" text="View Purchase History" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuAdjustPoints" onAction="#handleAdjustPoints" text="Adjust Loyalty Points" />
                  <SeparatorMenuItem />
                  <MenuItem fx:id="menuDeactivate" onAction="#handleDeactivateCustomer" text="Deactivate Customer" />
               </items>
            </ContextMenu>
         </contextMenu>
      </TableView>

      <!-- Summary and Action Section - Enhanced layout -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="form-container">
         <children>
            <!-- Status Information -->
            <VBox spacing="4.0">
               <children>
                  <Label fx:id="lblSelectionInfo" styleClass="form-label" text="Select a customer for details" />
               </children>
            </VBox>
            <Region HBox.hgrow="ALWAYS" />
            <!-- Action Buttons -->
            <HBox spacing="10.0">
               <children>
                  <Button fx:id="btnExport" onAction="#handleExport" styleClass="secondary-button" text="📊 Export Data" />
                  <Button fx:id="btnLoyaltyReport" onAction="#handleLoyaltyReport" styleClass="action-button" text="🏆 Loyalty Report" />
                  <Button fx:id="btnBirthdayReport" onAction="#handleBirthdayReport" styleClass="secondary-button" text="🎂 Birthday Report" />
                  <Button fx:id="btnCustomerAnalytics" onAction="#handleCustomerAnalytics" styleClass="primary-button" text="📈 Analytics" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="12.0" left="12.0" right="12.0" top="12.0" />
         </padding>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
