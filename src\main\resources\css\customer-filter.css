/* Enhanced Customer Filter Dialog Styles */

.root {
    -fx-background-color: #f8f9fa;
    -fx-font-family: "Segoe UI", Arial, sans-serif;
}

/* Dialog Header Styles */
.dialog-header {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-padding: 0 0 10 0;
}

.dialog-subheader {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-padding: 0 0 15 0;
}

/* Filter Section Styles */
.filter-section {
    -fx-background-color: white;
    -fx-padding: 15;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.filter-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 0 0 10 0;
}

/* Search Field Styles */
.text-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-padding: 8 12 8 12;
    -fx-font-size: 12px;
}

.text-field:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
    -fx-effect: dropshadow(three-pass-box, rgba(0,123,255,0.25), 4, 0, 0, 0);
}

/* ComboBox Styles */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-font-size: 12px;
}

.combo-box:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
}

.combo-box .list-cell {
    -fx-padding: 6 12 6 12;
}

/* Button Styles */
.button {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-padding: 6 12 6 12;
    -fx-cursor: hand;
}

.clear-search-btn {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-background-radius: 50%;
    -fx-border-radius: 50%;
    -fx-padding: 4 8 4 8;
    -fx-font-size: 10px;
}

.clear-search-btn:hover {
    -fx-background-color: #c82333;
}

.clear-filters-btn {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
}

.clear-filters-btn:hover {
    -fx-background-color: #545b62;
}

.select-btn {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
}

.select-btn:hover {
    -fx-background-color: #218838;
}

.select-btn:disabled {
    -fx-background-color: #6c757d;
    -fx-opacity: 0.6;
}

.cancel-btn {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
}

.cancel-btn:hover {
    -fx-background-color: #545b62;
}

/* Label Styles */
.filter-label {
    -fx-font-weight: bold;
    -fx-text-fill: #6c757d;
    -fx-font-size: 12px;
}

.result-count-label {
    -fx-text-fill: #28a745;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

/* Table Styles */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.table-view .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-font-size: 12px;
    -fx-padding: 8 12 8 12;
}

.table-view .column-header-background {
    -fx-background-color: #f8f9fa;
}

.table-row-cell {
    -fx-background-color: white;
    -fx-border-color: #f8f9fa;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 6 12 6 12;
    -fx-font-size: 12px;
}

.table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.table-row-cell:hover {
    -fx-background-color: #f5f5f5;
}

.table-cell {
    -fx-padding: 6 12 6 12;
    -fx-alignment: CENTER_LEFT;
}

/* Scrollbar Styles */
.scroll-bar {
    -fx-background-color: #f8f9fa;
}

.scroll-bar .thumb {
    -fx-background-color: #ced4da;
    -fx-background-radius: 4;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #adb5bd;
}

/* Layout Styles */
.filter-row {
    -fx-spacing: 15;
    -fx-alignment: CENTER_LEFT;
    -fx-padding: 5 0 5 0;
}

.action-row {
    -fx-spacing: 10;
    -fx-alignment: CENTER_LEFT;
    -fx-padding: 10 0 0 0;
}

.button-box {
    -fx-spacing: 10;
    -fx-alignment: CENTER_RIGHT;
    -fx-padding: 10 0 0 0;
}

/* Responsive Design */
.dialog-content {
    -fx-min-width: 600;
    -fx-min-height: 500;
    -fx-max-width: 800;
    -fx-max-height: 700;
}

/* Animation Effects */
.table-row-cell {
    -fx-transition: -fx-background-color 0.2s ease;
}

.button {
    -fx-transition: -fx-background-color 0.2s ease;
}

/* Focus Indicators */
.text-field:focused,
.combo-box:focused,
.button:focused {
    -fx-effect: dropshadow(three-pass-box, rgba(0,123,255,0.25), 4, 0, 0, 0);
}

/* Accessibility */
.button:focused,
.text-field:focused,
.combo-box:focused,
.table-view:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2;
}

/* Error States */
.text-field.error {
    -fx-border-color: #dc3545;
    -fx-border-width: 2;
}

.text-field.error:focused {
    -fx-border-color: #dc3545;
    -fx-effect: dropshadow(three-pass-box, rgba(220,53,69,0.25), 4, 0, 0, 0);
}

/* Success States */
.text-field.success {
    -fx-border-color: #28a745;
    -fx-border-width: 2;
}

.text-field.success:focused {
    -fx-border-color: #28a745;
    -fx-effect: dropshadow(three-pass-box, rgba(40,167,69,0.25), 4, 0, 0, 0);
}
