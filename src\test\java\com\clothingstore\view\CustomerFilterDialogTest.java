package com.clothingstore.view;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.Customer;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.stage.Stage;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Test class to verify the Customer Filter Dialog functionality
 */
public class CustomerFilterDialogTest extends Application {

    private CustomerDAO customerDAO;

    public static void main(String[] args) {
        launch(args);
    }

    @Override
    public void start(Stage primaryStage) {
        try {
            // Initialize database and DAO
            DatabaseManager.getInstance().initializeDatabase();
            customerDAO = CustomerDAO.getInstance();

            // Create test customers if none exist
            createTestCustomersIfNeeded();

            // Test the customer filter dialog
            testCustomerFilterDialog();

        } catch (Exception e) {
            System.err.println("Error in CustomerFilterDialogTest: " + e.getMessage());
            e.printStackTrace();
        } finally {
            Platform.exit();
        }
    }

    private void createTestCustomersIfNeeded() {
        try {
            List<Customer> existingCustomers = customerDAO.findAll();
            System.out.println("Found " + existingCustomers.size() + " existing customers");

            if (existingCustomers.isEmpty()) {
                System.out.println("Creating test customers...");
                createTestCustomers();
                existingCustomers = customerDAO.findAll();
                System.out.println("After creation: " + existingCustomers.size() + " customers");
            }

            // Display customer information
            for (Customer customer : existingCustomers) {
                System.out.println("Customer: " + customer.getFullName() + 
                                 " | Email: " + customer.getEmail() + 
                                 " | Phone: " + customer.getPhone() + 
                                 " | Group: " + customer.getMembershipLevel() + 
                                 " | Points: " + customer.getLoyaltyPoints());
            }

        } catch (Exception e) {
            System.err.println("Error creating test customers: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void createTestCustomers() throws Exception {
        Customer[] testCustomers = {
            createCustomer("John", "Smith", "<EMAIL>", "555-0101", "GOLD", 1250, "Active"),
            createCustomer("Sarah", "Johnson", "<EMAIL>", "555-0102", "PLATINUM", 3200, "Active"),
            createCustomer("Michael", "Brown", "<EMAIL>", "555-0103", "SILVER", 750, "Active"),
            createCustomer("Emily", "Davis", "<EMAIL>", "555-0104", "PLATINUM", 5500, "Inactive"),
            createCustomer("David", "Wilson", "<EMAIL>", "555-0105", "GOLD", 2100, "Active"),
            createCustomer("Lisa", "Anderson", "<EMAIL>", "555-0106", "SILVER", 890, "Active"),
            createCustomer("Robert", "Taylor", "<EMAIL>", "555-0107", "PLATINUM", 4200, "Active"),
            createCustomer("Jennifer", "Martinez", "<EMAIL>", "555-0108", "GOLD", 1680, "Inactive"),
            createCustomer("Christopher", "Garcia", "<EMAIL>", "555-0109", "PLATINUM", 3750, "Active"),
            createCustomer("Amanda", "Rodriguez", "<EMAIL>", "555-0110", "Standard", 50, "Active")
        };

        for (Customer customer : testCustomers) {
            try {
                if (customerDAO.findByEmail(customer.getEmail()).isEmpty()) {
                    customerDAO.save(customer);
                    System.out.println("Created test customer: " + customer.getFullName());
                }
            } catch (Exception e) {
                System.err.println("Error saving customer " + customer.getFullName() + ": " + e.getMessage());
            }
        }
    }

    private Customer createCustomer(String firstName, String lastName, String email, String phone, 
                                  String membershipLevel, int loyaltyPoints, String status) {
        Customer customer = new Customer();
        customer.setFirstName(firstName);
        customer.setLastName(lastName);
        customer.setEmail(email);
        customer.setPhone(phone);
        customer.setAddress("123 Test St");
        customer.setCity("Test City");
        customer.setState("TS");
        customer.setZipCode("12345");
        customer.setActive("Active".equals(status));
        customer.setLoyaltyPoints(loyaltyPoints);
        customer.setMembershipLevel(membershipLevel);
        customer.setRegistrationDate(LocalDateTime.now().minusDays((int)(Math.random() * 365)));
        customer.setTotalSpent((double) loyaltyPoints);
        customer.setTotalPurchases(loyaltyPoints / 100);
        customer.setLastPurchaseDate(LocalDateTime.now().minusDays((int)(Math.random() * 30)));
        return customer;
    }

    private void testCustomerFilterDialog() {
        System.out.println("\n=== CUSTOMER FILTER DIALOG TEST ===");
        
        try {
            List<Customer> allCustomers = customerDAO.findAll();
            System.out.println("Total customers available: " + allCustomers.size());

            // Test filtering logic
            testSearchFiltering(allCustomers);
            testMembershipFiltering(allCustomers);
            testStatusFiltering(allCustomers);
            testPointsFiltering(allCustomers);

            System.out.println("✅ All customer filter tests completed successfully!");

        } catch (Exception e) {
            System.err.println("❌ Customer filter test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void testSearchFiltering(List<Customer> customers) {
        System.out.println("\n--- Testing Search Filtering ---");
        
        // Test name search
        long johnCount = customers.stream()
            .filter(c -> c.getFullName().toLowerCase().contains("john"))
            .count();
        System.out.println("Customers with 'john' in name: " + johnCount);

        // Test email search
        long gmailCount = customers.stream()
            .filter(c -> c.getEmail() != null && c.getEmail().toLowerCase().contains("test.com"))
            .count();
        System.out.println("Customers with 'test.com' in email: " + gmailCount);

        // Test phone search
        long phoneCount = customers.stream()
            .filter(c -> c.getPhone() != null && c.getPhone().contains("555"))
            .count();
        System.out.println("Customers with '555' in phone: " + phoneCount);
    }

    private void testMembershipFiltering(List<Customer> customers) {
        System.out.println("\n--- Testing Membership Filtering ---");
        
        long goldCount = customers.stream()
            .filter(c -> "GOLD".equals(c.getMembershipLevel()))
            .count();
        System.out.println("GOLD members: " + goldCount);

        long platinumCount = customers.stream()
            .filter(c -> "PLATINUM".equals(c.getMembershipLevel()))
            .count();
        System.out.println("PLATINUM members: " + platinumCount);

        long silverCount = customers.stream()
            .filter(c -> "SILVER".equals(c.getMembershipLevel()))
            .count();
        System.out.println("SILVER members: " + silverCount);
    }

    private void testStatusFiltering(List<Customer> customers) {
        System.out.println("\n--- Testing Status Filtering ---");
        
        long activeCount = customers.stream()
            .filter(Customer::isActive)
            .count();
        System.out.println("Active customers: " + activeCount);

        long inactiveCount = customers.stream()
            .filter(c -> !c.isActive())
            .count();
        System.out.println("Inactive customers: " + inactiveCount);
    }

    private void testPointsFiltering(List<Customer> customers) {
        System.out.println("\n--- Testing Points Filtering ---");
        
        long highPointsCount = customers.stream()
            .filter(c -> c.getLoyaltyPoints() >= 1000)
            .count();
        System.out.println("Customers with 1000+ points: " + highPointsCount);

        long mediumPointsCount = customers.stream()
            .filter(c -> c.getLoyaltyPoints() >= 500 && c.getLoyaltyPoints() < 1000)
            .count();
        System.out.println("Customers with 500-999 points: " + mediumPointsCount);

        long lowPointsCount = customers.stream()
            .filter(c -> c.getLoyaltyPoints() < 500)
            .count();
        System.out.println("Customers with <500 points: " + lowPointsCount);
    }
}
