package com.clothingstore.test;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Database diagnostic to understand the data structure and content
 */
public class DatabaseDiagnostic {

    private static final String DB_URL = "*****************************";

    public static void main(String[] args) {
        System.out.println("================================================================================");
        System.out.println("DATABASE DIAGNOSTIC - INVESTIGATING DATA STRUCTURE");
        System.out.println("================================================================================");
        System.out.println("Analysis Date: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();

        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // 1. Check transactions table
            System.out.println("[1] TRANSACTIONS TABLE ANALYSIS");
            System.out.println("----------------------------------------------------------------");
            
            String transactionQuery = "SELECT COUNT(*) as total, status, COUNT(*) as count FROM transactions GROUP BY status";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(transactionQuery)) {
                
                System.out.println("Transaction Status Breakdown:");
                while (rs.next()) {
                    System.out.println("  " + rs.getString("status") + ": " + rs.getInt("count") + " transactions");
                }
            }
            
            // Check transaction dates
            String dateQuery = "SELECT MIN(transaction_date) as earliest, MAX(transaction_date) as latest, COUNT(*) as total FROM transactions";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(dateQuery)) {
                
                if (rs.next()) {
                    System.out.println("\nTransaction Date Range:");
                    System.out.println("  Earliest: " + rs.getString("earliest"));
                    System.out.println("  Latest: " + rs.getString("latest"));
                    System.out.println("  Total: " + rs.getInt("total"));
                }
            }
            
            // 2. Check products table
            System.out.println("\n[2] PRODUCTS TABLE ANALYSIS");
            System.out.println("----------------------------------------------------------------");
            
            String productQuery = "SELECT category, COUNT(*) as count, " +
                "COUNT(CASE WHEN cost_price IS NOT NULL THEN 1 END) as with_cost_price " +
                "FROM products GROUP BY category";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(productQuery)) {
                
                System.out.println("Products by Category:");
                while (rs.next()) {
                    String category = rs.getString("category");
                    int count = rs.getInt("count");
                    int withCost = rs.getInt("with_cost_price");
                    System.out.println("  " + (category != null ? category : "NULL") + 
                        ": " + count + " products (" + withCost + " with cost_price)");
                }
            }
            
            // Check cost_price values
            String costQuery = "SELECT MIN(cost_price) as min_cost, MAX(cost_price) as max_cost, " +
                "AVG(cost_price) as avg_cost, COUNT(cost_price) as count_with_cost " +
                "FROM products WHERE cost_price IS NOT NULL";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(costQuery)) {
                
                if (rs.next()) {
                    System.out.println("\nCost Price Analysis:");
                    System.out.println("  Products with cost_price: " + rs.getInt("count_with_cost"));
                    System.out.println("  Min cost: $" + String.format("%.2f", rs.getDouble("min_cost")));
                    System.out.println("  Max cost: $" + String.format("%.2f", rs.getDouble("max_cost")));
                    System.out.println("  Avg cost: $" + String.format("%.2f", rs.getDouble("avg_cost")));
                }
            }
            
            // 3. Check transaction_items table
            System.out.println("\n[3] TRANSACTION_ITEMS TABLE ANALYSIS");
            System.out.println("----------------------------------------------------------------");
            
            String itemsQuery = "SELECT COUNT(*) as total_items, SUM(quantity) as total_quantity, " +
                "SUM(line_total) as total_revenue FROM transaction_items";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(itemsQuery)) {
                
                if (rs.next()) {
                    System.out.println("Transaction Items Summary:");
                    System.out.println("  Total line items: " + rs.getInt("total_items"));
                    System.out.println("  Total quantity: " + rs.getInt("total_quantity"));
                    System.out.println("  Total revenue: $" + String.format("%.2f", rs.getDouble("total_revenue")));
                }
            }
            
            // 4. Test the exact query used in OptimizedProfitQueries
            System.out.println("\n[4] TESTING CATEGORY PROFIT QUERY");
            System.out.println("----------------------------------------------------------------");
            
            String categoryQuery = """
                SELECT 
                    COALESCE(p.category, 'Uncategorized') as category,
                    COUNT(DISTINCT t.id) as transaction_count,
                    SUM(ti.quantity) as items_sold,
                    SUM(ti.line_total) as revenue,
                    SUM(p.cost_price * ti.quantity) as cost
                FROM transactions t
                INNER JOIN transaction_items ti ON t.id = ti.transaction_id
                INNER JOIN products p ON ti.product_id = p.id
                WHERE t.status = 'COMPLETED'
                    AND (t.refunded_amount IS NULL OR t.refunded_amount = 0)
                    AND p.cost_price IS NOT NULL
                GROUP BY p.category
                ORDER BY (SUM(ti.line_total) - SUM(p.cost_price * ti.quantity)) DESC
                """;
            
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(categoryQuery)) {
                
                boolean hasData = false;
                System.out.println("Category Profit Results:");
                while (rs.next()) {
                    hasData = true;
                    String category = rs.getString("category");
                    int transactionCount = rs.getInt("transaction_count");
                    int itemsSold = rs.getInt("items_sold");
                    double revenue = rs.getDouble("revenue");
                    double cost = rs.getDouble("cost");
                    double profit = revenue - cost;
                    double margin = revenue > 0 ? (profit / revenue) * 100 : 0;
                    
                    System.out.println("  " + category + ":");
                    System.out.println("    Revenue: $" + String.format("%.2f", revenue));
                    System.out.println("    Cost: $" + String.format("%.2f", cost));
                    System.out.println("    Profit: $" + String.format("%.2f", profit));
                    System.out.println("    Margin: " + String.format("%.2f%%", margin));
                    System.out.println("    Items: " + itemsSold);
                    System.out.println("    Transactions: " + transactionCount);
                    System.out.println();
                }
                
                if (!hasData) {
                    System.out.println("  No results returned from category query!");
                    
                    // Let's debug step by step
                    System.out.println("\n[DEBUG] Step-by-step query analysis:");
                    
                    // Check completed transactions
                    String completedQuery = "SELECT COUNT(*) as count FROM transactions WHERE status = 'COMPLETED'";
                    try (Statement debugStmt = conn.createStatement();
                         ResultSet debugRs = debugStmt.executeQuery(completedQuery)) {
                        if (debugRs.next()) {
                            System.out.println("  COMPLETED transactions: " + debugRs.getInt("count"));
                        }
                    }
                    
                    // Check transactions with no refund
                    String noRefundQuery = "SELECT COUNT(*) as count FROM transactions WHERE status = 'COMPLETED' AND (refunded_amount IS NULL OR refunded_amount = 0)";
                    try (Statement debugStmt = conn.createStatement();
                         ResultSet debugRs = debugStmt.executeQuery(noRefundQuery)) {
                        if (debugRs.next()) {
                            System.out.println("  COMPLETED + No refund: " + debugRs.getInt("count"));
                        }
                    }
                    
                    // Check transaction items for completed transactions
                    String itemsForCompletedQuery = """
                        SELECT COUNT(*) as count FROM transactions t
                        INNER JOIN transaction_items ti ON t.id = ti.transaction_id
                        WHERE t.status = 'COMPLETED' AND (t.refunded_amount IS NULL OR t.refunded_amount = 0)
                        """;
                    try (Statement debugStmt = conn.createStatement();
                         ResultSet debugRs = debugStmt.executeQuery(itemsForCompletedQuery)) {
                        if (debugRs.next()) {
                            System.out.println("  Transaction items for COMPLETED: " + debugRs.getInt("count"));
                        }
                    }
                    
                    // Check products with cost_price for completed transactions
                    String productsWithCostQuery = """
                        SELECT COUNT(*) as count FROM transactions t
                        INNER JOIN transaction_items ti ON t.id = ti.transaction_id
                        INNER JOIN products p ON ti.product_id = p.id
                        WHERE t.status = 'COMPLETED' 
                            AND (t.refunded_amount IS NULL OR t.refunded_amount = 0)
                            AND p.cost_price IS NOT NULL
                        """;
                    try (Statement debugStmt = conn.createStatement();
                         ResultSet debugRs = debugStmt.executeQuery(productsWithCostQuery)) {
                        if (debugRs.next()) {
                            System.out.println("  Items with cost_price for COMPLETED: " + debugRs.getInt("count"));
                        }
                    }
                }
            }
            
            // 5. Sample data inspection
            System.out.println("\n[5] SAMPLE DATA INSPECTION");
            System.out.println("----------------------------------------------------------------");
            
            String sampleQuery = """
                SELECT t.id, t.status, t.transaction_date, t.refunded_amount,
                       ti.product_id, ti.quantity, ti.line_total,
                       p.name, p.category, p.cost_price
                FROM transactions t
                LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                LEFT JOIN products p ON ti.product_id = p.id
                LIMIT 10
                """;
            
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sampleQuery)) {
                
                System.out.println("Sample Transaction Data:");
                while (rs.next()) {
                    System.out.println("  Transaction " + rs.getInt("id") + 
                        " | Status: " + rs.getString("status") +
                        " | Date: " + rs.getString("transaction_date") +
                        " | Refund: " + rs.getString("refunded_amount"));
                    System.out.println("    Product: " + rs.getString("name") +
                        " | Category: " + rs.getString("category") +
                        " | Cost: " + rs.getString("cost_price") +
                        " | Qty: " + rs.getInt("quantity") +
                        " | Total: $" + String.format("%.2f", rs.getDouble("line_total")));
                    System.out.println();
                }
            }

            System.out.println("================================================================================");
            System.out.println("[DIAGNOSTIC COMPLETE]");
            System.out.println("================================================================================");

        } catch (SQLException e) {
            System.err.println("[ERROR] Database diagnostic failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
