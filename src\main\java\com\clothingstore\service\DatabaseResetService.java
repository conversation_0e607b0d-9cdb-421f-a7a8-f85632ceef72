package com.clothingstore.service;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

/**
 * Service for database reset and demo data management
 */
public class DatabaseResetService {

    private static final Logger LOGGER = Logger.getLogger(DatabaseResetService.class.getName());
    private static DatabaseResetService instance;

    private DatabaseResetService() {
    }

    public static synchronized DatabaseResetService getInstance() {
        if (instance == null) {
            instance = new DatabaseResetService();
        }
        return instance;
    }

    /**
     * Completely reset the database - removes all data
     */
    public void resetDatabase() throws SQLException {
        LOGGER.info("Starting complete database reset...");

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            conn.setAutoCommit(false);

            try {
                // Delete all data in correct order (respecting foreign keys)
                deleteAllTransactionItems(conn);
                deleteAllTransactions(conn);
                deleteAllCustomers(conn);
                deleteAllProducts(conn);
                deleteAllSettings(conn);

                // Reset auto-increment counters
                resetAutoIncrementCounters(conn);

                conn.commit();
                LOGGER.info("Database reset completed successfully");

            } catch (SQLException e) {
                conn.rollback();
                LOGGER.severe("Database reset failed: " + e.getMessage());
                throw e;
            }
        }
    }

    /**
     * Reset database and populate with demo data
     */
    public void resetWithDemoData() throws SQLException {
        LOGGER.info("Starting database reset with demo data...");

        // First reset the database
        resetDatabase();

        // Then populate with demo data
        populateDemoData();

        LOGGER.info("Database reset with demo data completed successfully");
    }

    /**
     * Populate database with demo data
     */
    public void populateDemoData() throws SQLException {
        LOGGER.info("Populating database with demo data...");

        try {
            // Create demo products
            createDemoProducts();

            // Create demo customers
            createDemoCustomers();

            // Create demo transactions
            createDemoTransactions();

            LOGGER.info("Demo data population completed successfully");

        } catch (Exception e) {
            LOGGER.severe("Failed to populate demo data: " + e.getMessage());
            throw new SQLException("Demo data population failed", e);
        }
    }

    private void deleteAllTransactionItems(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement("DELETE FROM transaction_items")) {
            int deleted = stmt.executeUpdate();
            LOGGER.info("Deleted " + deleted + " transaction items");
        }
    }

    private void deleteAllTransactions(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement("DELETE FROM transactions")) {
            int deleted = stmt.executeUpdate();
            LOGGER.info("Deleted " + deleted + " transactions");
        }
    }

    private void deleteAllCustomers(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement("DELETE FROM customers")) {
            int deleted = stmt.executeUpdate();
            LOGGER.info("Deleted " + deleted + " customers");
        }
    }

    private void deleteAllProducts(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement("DELETE FROM products")) {
            int deleted = stmt.executeUpdate();
            LOGGER.info("Deleted " + deleted + " products");
        }
    }

    private void deleteAllSettings(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement("DELETE FROM settings")) {
            int deleted = stmt.executeUpdate();
            LOGGER.info("Deleted " + deleted + " settings");
        } catch (SQLException e) {
            // Settings table might not exist, ignore
            LOGGER.info("Settings table not found or empty");
        }
    }

    private void resetAutoIncrementCounters(Connection conn) throws SQLException {
        String[] tables = {"products", "customers", "transactions", "transaction_items"};

        for (String table : tables) {
            try (PreparedStatement stmt = conn.prepareStatement(
                    "DELETE FROM sqlite_sequence WHERE name = ?")) {
                stmt.setString(1, table);
                stmt.executeUpdate();
                LOGGER.info("Reset auto-increment counter for table: " + table);
            }
        }
    }

    private void createDemoProducts() throws SQLException {
        ProductDAO productDAO = ProductDAO.getInstance();

        List<Product> demoProducts = new ArrayList<>();

        // Clothing items
        demoProducts.add(createProduct("Classic Cotton T-Shirt", "TSHIRT-001", "T-Shirts", 19.99, 50, "Comfortable cotton t-shirt"));
        demoProducts.add(createProduct("Denim Jeans", "JEANS-001", "Jeans", 59.99, 30, "Classic blue denim jeans"));
        demoProducts.add(createProduct("Summer Dress", "DRESS-001", "Dresses", 79.99, 25, "Light summer dress"));
        demoProducts.add(createProduct("Wool Sweater", "SWEATER-001", "Sweaters", 89.99, 20, "Warm wool sweater"));
        demoProducts.add(createProduct("Casual Shorts", "SHORTS-001", "Shorts", 34.99, 40, "Comfortable casual shorts"));
        demoProducts.add(createProduct("Business Shirt", "SHIRT-001", "Shirts", 49.99, 35, "Professional business shirt"));
        demoProducts.add(createProduct("Leather Jacket", "JACKET-001", "Jackets", 199.99, 15, "Genuine leather jacket"));
        demoProducts.add(createProduct("Running Shoes", "SHOES-001", "Shoes", 129.99, 25, "Athletic running shoes"));
        demoProducts.add(createProduct("Baseball Cap", "CAP-001", "Accessories", 24.99, 60, "Adjustable baseball cap"));
        demoProducts.add(createProduct("Silk Scarf", "SCARF-001", "Accessories", 39.99, 30, "Elegant silk scarf"));

        for (Product product : demoProducts) {
            productDAO.save(product);
        }

        LOGGER.info("Created " + demoProducts.size() + " demo products");
    }

    private Product createProduct(String name, String sku, String category, double price, int stock, String description) {
        Product product = new Product();
        product.setName(name);
        product.setSku(sku);
        product.setCategory(category);
        product.setPrice(BigDecimal.valueOf(price));
        product.setStockQuantity(stock);
        product.setDescription(description);
        product.setActive(true);
        return product;
    }

    private void createDemoCustomers() throws SQLException {
        CustomerDAO customerDAO = CustomerDAO.getInstance();

        List<Customer> demoCustomers = new ArrayList<>();

        demoCustomers.add(createCustomer("John", "Doe", "+1234567890"));
        demoCustomers.add(createCustomer("Jane", "Smith", "+1234567891"));
        demoCustomers.add(createCustomer("Mike", "Johnson", "+1234567892"));
        demoCustomers.add(createCustomer("Sarah", "Williams", "+1234567893"));
        demoCustomers.add(createCustomer("David", "Brown", "+1234567894"));

        for (Customer customer : demoCustomers) {
            customerDAO.save(customer);
        }

        LOGGER.info("Created " + demoCustomers.size() + " demo customers");
    }

    private Customer createCustomer(String firstName, String lastName, String phone) {
        Customer customer = new Customer();
        customer.setFirstName(firstName);
        customer.setLastName(lastName);
        customer.setPhone(phone);
        customer.setActive(true);
        customer.setLoyaltyPoints(0);
        return customer;
    }

    private void createDemoTransactions() throws SQLException {
        TransactionDAO transactionDAO = TransactionDAO.getInstance();
        ProductDAO productDAO = ProductDAO.getInstance();
        CustomerDAO customerDAO = CustomerDAO.getInstance();

        List<Product> products = productDAO.findAll();
        List<Customer> customers = customerDAO.findAll();

        if (products.isEmpty() || customers.isEmpty()) {
            LOGGER.warning("No products or customers found for demo transactions");
            return;
        }

        // Create a few demo transactions
        for (int i = 0; i < 5; i++) {
            Transaction transaction = new Transaction();
            transaction.setTransactionNumber("DEMO-" + System.currentTimeMillis() + "-" + i);
            transaction.setTransactionDate(LocalDateTime.now().minusDays(i));
            transaction.setCustomer(customers.get(i % customers.size()));
            transaction.setCustomerId(customers.get(i % customers.size()).getId());
            transaction.setPaymentMethod("CREDIT_CARD");
            transaction.setStatus("COMPLETED");
            transaction.setCashierName("Demo Cashier");

            // Add some items to the transaction
            for (int j = 0; j < 2 + (i % 3); j++) {
                Product product = products.get((i + j) % products.size());
                TransactionItem item = new TransactionItem(product, 1 + (j % 2));
                transaction.addItem(item);
            }

            transaction.recalculateAmounts();
            transaction.setAmountPaid(transaction.getTotalAmount()); // Full payment

            transactionDAO.save(transaction);
        }

        LOGGER.info("Created 5 demo transactions");
    }

    /**
     * Get database statistics
     */
    public DatabaseStats getDatabaseStats() throws SQLException {
        ProductDAO productDAO = ProductDAO.getInstance();
        CustomerDAO customerDAO = CustomerDAO.getInstance();
        TransactionDAO transactionDAO = TransactionDAO.getInstance();

        int productCount = productDAO.findAll().size();
        int customerCount = customerDAO.findAll().size();
        int transactionCount = transactionDAO.findAll().size();

        return new DatabaseStats(productCount, customerCount, transactionCount);
    }

    /**
     * Database statistics holder
     */
    public static class DatabaseStats {

        private final int productCount;
        private final int customerCount;
        private final int transactionCount;

        public DatabaseStats(int productCount, int customerCount, int transactionCount) {
            this.productCount = productCount;
            this.customerCount = customerCount;
            this.transactionCount = transactionCount;
        }

        public int getProductCount() {
            return productCount;
        }

        public int getCustomerCount() {
            return customerCount;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        public boolean isEmpty() {
            return productCount == 0 && customerCount == 0 && transactionCount == 0;
        }
    }
}
