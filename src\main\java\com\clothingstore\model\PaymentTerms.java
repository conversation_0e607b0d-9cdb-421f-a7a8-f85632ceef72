package com.clothingstore.model;

/**
 * Enumeration for payment terms with suppliers
 */
public enum PaymentTerms {
    CASH_ON_DELIVERY("Cash on Delivery", "COD", 0),
    NET_15("Net 15 Days", "Net 15", 15),
    NET_30("Net 30 Days", "Net 30", 30),
    NET_45("Net 45 Days", "Net 45", 45),
    NET_60("Net 60 Days", "Net 60", 60),
    NET_90("Net 90 Days", "Net 90", 90),
    PREPAID("Prepaid", "Prepaid", -1),
    TWO_TEN_NET_30("2/10 Net 30", "2/10 N30", 30),
    ONE_FIFTEEN_NET_45("1/15 Net 45", "1/15 N45", 45);

    private final String displayName;
    private final String shortCode;
    private final int paymentDays;

    PaymentTerms(String displayName, String shortCode, int paymentDays) {
        this.displayName = displayName;
        this.shortCode = shortCode;
        this.paymentDays = paymentDays;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getShortCode() {
        return shortCode;
    }

    public int getPaymentDays() {
        return paymentDays;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Check if payment terms offer early payment discount
     */
    public boolean hasEarlyPaymentDiscount() {
        return this == TWO_TEN_NET_30 || this == ONE_FIFTEEN_NET_45;
    }

    /**
     * Get early payment discount percentage
     */
    public double getEarlyPaymentDiscountPercent() {
        switch (this) {
            case TWO_TEN_NET_30:
                return 2.0;
            case ONE_FIFTEEN_NET_45:
                return 1.0;
            default:
                return 0.0;
        }
    }

    /**
     * Get early payment discount days
     */
    public int getEarlyPaymentDiscountDays() {
        switch (this) {
            case TWO_TEN_NET_30:
                return 10;
            case ONE_FIFTEEN_NET_45:
                return 15;
            default:
                return 0;
        }
    }

    /**
     * Check if payment is required upfront
     */
    public boolean isUpfrontPayment() {
        return this == CASH_ON_DELIVERY || this == PREPAID;
    }
}
