package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.PaymentHistory;
import com.clothingstore.model.PaymentHistory.PaymentStatus;
import com.clothingstore.model.PaymentHistory.PaymentType;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * Data Access Object for PaymentHistory operations
 * Handles CRUD operations for payment history records
 */
public class PaymentHistoryDAO {
    
    private static final Logger LOGGER = Logger.getLogger(PaymentHistoryDAO.class.getName());
    private static PaymentHistoryDAO instance;
    
    private PaymentHistoryDAO() {}
    
    public static synchronized PaymentHistoryDAO getInstance() {
        if (instance == null) {
            instance = new PaymentHistoryDAO();
        }
        return instance;
    }
    
    /**
     * Create a new payment history record
     */
    public PaymentHistory create(PaymentHistory paymentHistory) throws SQLException {
        String sql = "INSERT INTO payment_history "
                + "(transaction_id, payment_sequence, payment_amount, payment_method, payment_date, "
                + "payment_reference, running_balance, remaining_balance, payment_type, notes, "
                + "cashier_name, status, created_at, updated_at) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setLong(1, paymentHistory.getTransactionId());
            stmt.setInt(2, paymentHistory.getPaymentSequence());
            stmt.setBigDecimal(3, paymentHistory.getPaymentAmount());
            stmt.setString(4, paymentHistory.getPaymentMethod());
            stmt.setTimestamp(5, Timestamp.valueOf(paymentHistory.getPaymentDate()));
            stmt.setString(6, paymentHistory.getPaymentReference());
            stmt.setBigDecimal(7, paymentHistory.getRunningBalance());
            stmt.setBigDecimal(8, paymentHistory.getRemainingBalance());
            stmt.setString(9, paymentHistory.getPaymentType().name());
            stmt.setString(10, paymentHistory.getNotes());
            stmt.setString(11, paymentHistory.getCashierName());
            stmt.setString(12, paymentHistory.getStatus().name());
            stmt.setTimestamp(13, Timestamp.valueOf(paymentHistory.getCreatedAt()));
            stmt.setTimestamp(14, Timestamp.valueOf(paymentHistory.getUpdatedAt()));
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating payment history failed, no rows affected.");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    paymentHistory.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating payment history failed, no ID obtained.");
                }
            }
            
            LOGGER.info("Created payment history record with ID: " + paymentHistory.getId());
            return paymentHistory;
        }
    }
    
    /**
     * Find payment history by ID
     */
    public PaymentHistory findById(Long id) throws SQLException {
        String sql = "SELECT * FROM payment_history WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToPaymentHistory(rs);
                }
            }
        }
        
        return null;
    }
    
    /**
     * Find all payment history records for a transaction
     */
    public List<PaymentHistory> findByTransactionId(Long transactionId) throws SQLException {
        String sql = "SELECT * FROM payment_history WHERE transaction_id = ? ORDER BY payment_sequence ASC, payment_date ASC";
        List<PaymentHistory> paymentHistories = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, transactionId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    paymentHistories.add(mapResultSetToPaymentHistory(rs));
                }
            }
        }
        
        return paymentHistories;
    }
    
    /**
     * Find all payment history records for a transaction with specific payment type
     */
    public List<PaymentHistory> findByTransactionIdAndType(Long transactionId, PaymentType paymentType) throws SQLException {
        String sql = "SELECT * FROM payment_history WHERE transaction_id = ? AND payment_type = ? ORDER BY payment_sequence ASC, payment_date ASC";
        List<PaymentHistory> paymentHistories = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, transactionId);
            stmt.setString(2, paymentType.name());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    paymentHistories.add(mapResultSetToPaymentHistory(rs));
                }
            }
        }
        
        return paymentHistories;
    }
    
    /**
     * Get the next payment sequence number for a transaction
     */
    public int getNextPaymentSequence(Long transactionId) throws SQLException {
        String sql = "SELECT COALESCE(MAX(payment_sequence), 0) + 1 FROM payment_history WHERE transaction_id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, transactionId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        
        return 1; // First payment
    }
    
    /**
     * Update payment history record
     */
    public boolean update(PaymentHistory paymentHistory) throws SQLException {
        String sql = "UPDATE payment_history SET "
                + "payment_amount = ?, payment_method = ?, payment_date = ?, payment_reference = ?, "
                + "running_balance = ?, remaining_balance = ?, payment_type = ?, notes = ?, "
                + "cashier_name = ?, status = ?, updated_at = ? "
                + "WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            paymentHistory.setUpdatedAt(LocalDateTime.now());
            
            stmt.setBigDecimal(1, paymentHistory.getPaymentAmount());
            stmt.setString(2, paymentHistory.getPaymentMethod());
            stmt.setTimestamp(3, Timestamp.valueOf(paymentHistory.getPaymentDate()));
            stmt.setString(4, paymentHistory.getPaymentReference());
            stmt.setBigDecimal(5, paymentHistory.getRunningBalance());
            stmt.setBigDecimal(6, paymentHistory.getRemainingBalance());
            stmt.setString(7, paymentHistory.getPaymentType().name());
            stmt.setString(8, paymentHistory.getNotes());
            stmt.setString(9, paymentHistory.getCashierName());
            stmt.setString(10, paymentHistory.getStatus().name());
            stmt.setTimestamp(11, Timestamp.valueOf(paymentHistory.getUpdatedAt()));
            stmt.setLong(12, paymentHistory.getId());
            
            int affectedRows = stmt.executeUpdate();
            boolean updated = affectedRows > 0;
            
            if (updated) {
                LOGGER.info("Updated payment history record with ID: " + paymentHistory.getId());
            }
            
            return updated;
        }
    }
    
    /**
     * Delete payment history record
     */
    public boolean delete(Long id) throws SQLException {
        String sql = "DELETE FROM payment_history WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, id);
            
            int affectedRows = stmt.executeUpdate();
            boolean deleted = affectedRows > 0;
            
            if (deleted) {
                LOGGER.info("Deleted payment history record with ID: " + id);
            }
            
            return deleted;
        }
    }
    
    /**
     * Get total amount paid for a transaction
     */
    public BigDecimal getTotalAmountPaid(Long transactionId) throws SQLException {
        String sql = "SELECT COALESCE(SUM(payment_amount), 0) FROM payment_history "
                + "WHERE transaction_id = ? AND payment_type = 'PAYMENT' AND status = 'COMPLETED'";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, transactionId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getBigDecimal(1);
                }
            }
        }
        
        return BigDecimal.ZERO;
    }
    
    /**
     * Get total amount refunded for a transaction
     */
    public BigDecimal getTotalAmountRefunded(Long transactionId) throws SQLException {
        String sql = "SELECT COALESCE(SUM(payment_amount), 0) FROM payment_history "
                + "WHERE transaction_id = ? AND payment_type = 'REFUND' AND status = 'COMPLETED'";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, transactionId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getBigDecimal(1);
                }
            }
        }
        
        return BigDecimal.ZERO;
    }

    /**
     * Get the current remaining balance for a transaction
     */
    public BigDecimal getCurrentRemainingBalance(Long transactionId) throws SQLException {
        String sql = "SELECT remaining_balance FROM payment_history "
                + "WHERE transaction_id = ? ORDER BY payment_sequence DESC, payment_date DESC LIMIT 1";

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, transactionId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getBigDecimal(1);
                }
            }
        }

        return BigDecimal.ZERO;
    }

    /**
     * Find all payment history records within a date range
     */
    public List<PaymentHistory> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT * FROM payment_history WHERE payment_date BETWEEN ? AND ? ORDER BY payment_date DESC";
        List<PaymentHistory> paymentHistories = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setTimestamp(1, Timestamp.valueOf(startDate));
            stmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    paymentHistories.add(mapResultSetToPaymentHistory(rs));
                }
            }
        }

        return paymentHistories;
    }

    /**
     * Map ResultSet to PaymentHistory object
     */
    private PaymentHistory mapResultSetToPaymentHistory(ResultSet rs) throws SQLException {
        PaymentHistory paymentHistory = new PaymentHistory();

        paymentHistory.setId(rs.getLong("id"));
        paymentHistory.setTransactionId(rs.getLong("transaction_id"));
        paymentHistory.setPaymentSequence(rs.getInt("payment_sequence"));
        paymentHistory.setPaymentAmount(rs.getBigDecimal("payment_amount"));
        paymentHistory.setPaymentMethod(rs.getString("payment_method"));

        Timestamp paymentDate = rs.getTimestamp("payment_date");
        if (paymentDate != null) {
            paymentHistory.setPaymentDate(paymentDate.toLocalDateTime());
        }

        paymentHistory.setPaymentReference(rs.getString("payment_reference"));
        paymentHistory.setRunningBalance(rs.getBigDecimal("running_balance"));
        paymentHistory.setRemainingBalance(rs.getBigDecimal("remaining_balance"));

        String paymentTypeStr = rs.getString("payment_type");
        if (paymentTypeStr != null) {
            paymentHistory.setPaymentType(PaymentType.valueOf(paymentTypeStr));
        }

        paymentHistory.setNotes(rs.getString("notes"));
        paymentHistory.setCashierName(rs.getString("cashier_name"));

        String statusStr = rs.getString("status");
        if (statusStr != null) {
            paymentHistory.setStatus(PaymentStatus.valueOf(statusStr));
        }

        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            paymentHistory.setCreatedAt(createdAt.toLocalDateTime());
        }

        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            paymentHistory.setUpdatedAt(updatedAt.toLocalDateTime());
        }

        return paymentHistory;
    }
}
