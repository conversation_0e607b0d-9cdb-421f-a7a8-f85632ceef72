package com.clothingstore.test;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import com.clothingstore.database.DatabaseManager;

/**
 * Comprehensive navigation test for the JavaFX application
 * Tests FXML loading, controller binding, and navigation functionality
 */
public class NavigationTest {
    
    private static int testsRun = 0;
    private static int testsPassed = 0;
    private static int testsFailed = 0;
    private static List<String> failedTests = new ArrayList<>();
    
    public static void main(String[] args) {
        System.out.println("JAVAFX NAVIGATION FUNCTIONALITY TEST");
        System.out.println("====================================");
        
        try {
            // Initialize database first
            DatabaseManager.getInstance().initializeDatabase();
            
            // Test FXML file loading
            testFXMLFileLoading();
            
            // Test controller class existence
            testControllerClasses();
            
            // Test FXML-Controller binding
            testFXMLControllerBinding();
            
            // Print results
            printTestResults();
            
        } catch (Exception e) {
            System.err.println("NAVIGATION TEST FAILED: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseManager.getInstance().closeConnection();
        }
    }
    
    private static void testFXMLFileLoading() {
        System.out.println("\n--- Testing FXML File Existence ---");

        String[] fxmlFiles = {
            "MainWindow.fxml",
            "Dashboard.fxml",
            "PointOfSaleNew.fxml",
            "ProductManagement.fxml",
            "CustomerManagement.fxml",
            "TransactionHistory.fxml",
            "SalesReport.fxml",
            "Settings.fxml",
            "LowStockReport.fxml",
            "InventoryReport.fxml",
            "CustomerReport.fxml",
            "DailySalesReport.fxml",
            "MonthlySalesReport.fxml",
            "ProfitReport.fxml"
        };

        for (String fxmlFile : fxmlFiles) {
            testFXMLFileExists(fxmlFile);
        }
    }

    private static void testFXMLFileExists(String fxmlFile) {
        testsRun++;
        try {
            URL fxmlUrl = NavigationTest.class.getResource("/fxml/" + fxmlFile);
            if (fxmlUrl != null) {
                testsPassed++;
                System.out.println("PASS: " + fxmlFile + " - File exists");
            } else {
                testsFailed++;
                failedTests.add(fxmlFile + " - FXML file not found");
                System.out.println("FAIL: " + fxmlFile + " - File not found");
            }
        } catch (Exception e) {
            testsFailed++;
            failedTests.add(fxmlFile + " - " + e.getMessage());
            System.out.println("FAIL: " + fxmlFile + " - " + e.getMessage());
        }
    }
    
    private static void testControllerClasses() {
        System.out.println("\n--- Testing Controller Class Existence ---");
        
        String[][] controllerMappings = {
            {"MainWindow.fxml", "com.clothingstore.view.MainWindowController"},
            {"Dashboard.fxml", "com.clothingstore.view.DashboardController"},
            {"PointOfSaleNew.fxml", "com.clothingstore.view.PointOfSaleController"},
            {"ProductManagement.fxml", "com.clothingstore.view.ProductManagementController"},
            {"CustomerManagement.fxml", "com.clothingstore.view.CustomerManagementController"},
            {"TransactionHistory.fxml", "com.clothingstore.view.TransactionHistoryController"},
            {"SalesReport.fxml", "com.clothingstore.view.SalesReportController"},
            {"Settings.fxml", "com.clothingstore.view.SettingsController"},
            {"LowStockReport.fxml", "com.clothingstore.view.LowStockReportController"},
            {"InventoryReport.fxml", "com.clothingstore.view.InventoryReportController"},
            {"CustomerReport.fxml", "com.clothingstore.view.CustomerReportController"},
            {"DailySalesReport.fxml", "com.clothingstore.view.DailySalesReportController"},
            {"MonthlySalesReport.fxml", "com.clothingstore.view.MonthlySalesReportController"},
            {"ProfitReport.fxml", "com.clothingstore.view.ProfitReportController"}
        };
        
        for (String[] mapping : controllerMappings) {
            testControllerClass(mapping[0], mapping[1]);
        }
    }
    
    private static void testControllerClass(String fxmlFile, String controllerClass) {
        testsRun++;
        try {
            Class.forName(controllerClass);
            testsPassed++;
            System.out.println("PASS: " + controllerClass + " - Class exists");
        } catch (ClassNotFoundException e) {
            testsFailed++;
            failedTests.add(fxmlFile + " - Controller class not found: " + controllerClass);
            System.out.println("FAIL: " + controllerClass + " - Class not found");
        }
    }
    
    private static void testFXMLControllerBinding() {
        System.out.println("\n--- Testing FXML File Content ---");

        String[][] fxmlControllerMappings = {
            {"MainWindow.fxml", "com.clothingstore.view.MainWindowController"},
            {"Dashboard.fxml", "com.clothingstore.view.DashboardController"},
            {"PointOfSaleNew.fxml", "com.clothingstore.view.PointOfSaleController"},
            {"ProductManagement.fxml", "com.clothingstore.view.ProductManagementController"},
            {"CustomerManagement.fxml", "com.clothingstore.view.CustomerManagementController"},
            {"TransactionHistory.fxml", "com.clothingstore.view.TransactionHistoryController"},
            {"SalesReport.fxml", "com.clothingstore.view.SalesReportController"},
            {"Settings.fxml", "com.clothingstore.view.SettingsController"},
            {"LowStockReport.fxml", "com.clothingstore.view.LowStockReportController"},
            {"InventoryReport.fxml", "com.clothingstore.view.InventoryReportController"},
            {"CustomerReport.fxml", "com.clothingstore.view.CustomerReportController"},
            {"DailySalesReport.fxml", "com.clothingstore.view.DailySalesReportController"},
            {"MonthlySalesReport.fxml", "com.clothingstore.view.MonthlySalesReportController"},
            {"ProfitReport.fxml", "com.clothingstore.view.ProfitReportController"}
        };

        for (String[] mapping : fxmlControllerMappings) {
            testFXMLContent(mapping[0], mapping[1]);
        }
    }

    private static void testFXMLContent(String fxmlFile, String expectedController) {
        testsRun++;
        try {
            URL fxmlUrl = NavigationTest.class.getResource("/fxml/" + fxmlFile);
            if (fxmlUrl == null) {
                testsFailed++;
                failedTests.add(fxmlFile + " - FXML file not found");
                System.out.println("FAIL: " + fxmlFile + " - File not found");
                return;
            }

            // Read FXML content to check for controller reference
            java.io.InputStream is = fxmlUrl.openStream();
            java.util.Scanner scanner = new java.util.Scanner(is).useDelimiter("\\A");
            String content = scanner.hasNext() ? scanner.next() : "";
            scanner.close();
            is.close();

            if (content.contains("fx:controller=\"" + expectedController + "\"")) {
                testsPassed++;
                System.out.println("PASS: " + fxmlFile + " - Controller reference found");
            } else {
                testsFailed++;
                failedTests.add(fxmlFile + " - Controller reference not found: " + expectedController);
                System.out.println("FAIL: " + fxmlFile + " - Controller reference not found");
            }

        } catch (Exception e) {
            testsFailed++;
            failedTests.add(fxmlFile + " - Content check error: " + e.getMessage());
            System.out.println("FAIL: " + fxmlFile + " - Content check error: " + e.getMessage());
        }
    }
    
    private static void printTestResults() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("NAVIGATION TEST RESULTS SUMMARY");
        System.out.println("=".repeat(60));
        System.out.println("Total Tests Run: " + testsRun);
        System.out.println("Tests Passed: " + testsPassed);
        System.out.println("Tests Failed: " + testsFailed);
        System.out.println("Success Rate: " + String.format("%.1f%%", (double) testsPassed / testsRun * 100));
        
        if (testsFailed > 0) {
            System.out.println("\nFAILED TESTS:");
            for (String failure : failedTests) {
                System.out.println("  - " + failure);
            }
            System.out.println("\nWARNING: Some navigation tests failed. Please review and fix issues.");
        } else {
            System.out.println("\nSUCCESS: ALL NAVIGATION TESTS PASSED! Navigation is fully functional.");
        }
    }
}
