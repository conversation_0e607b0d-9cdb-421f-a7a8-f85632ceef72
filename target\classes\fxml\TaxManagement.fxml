<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.TaxManagementController">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="header-section">
         <children>
            <Label styleClass="page-title" text="Tax Management" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="taxRateCountLabel" styleClass="info-label" text="Loading tax rates..." />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <!-- Search and Filter Section -->
      <VBox spacing="15.0" styleClass="filter-section">
         <children>
            <!-- Search Row -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="🔍 Search:" />
                  <TextField fx:id="searchField" prefWidth="250.0" promptText="Search by name, description, or jurisdiction..." styleClass="search-box" />
                  <Region HBox.hgrow="SOMETIMES" />
                  <Button fx:id="clearFiltersButton" onAction="#handleClearFilters" styleClass="clear-filters-button" text="✨ Clear Filters" />
               </children>
            </HBox>
            
            <!-- Filter Controls Row -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="📊 Status:" />
                  <ComboBox fx:id="statusFilter" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="🏷️ Type:" />
                  <ComboBox fx:id="typeFilter" prefWidth="150.0" styleClass="combo-box" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Label fx:id="selectionInfoLabel" styleClass="selection-info" text="Select a tax rate for details" />
               </children>
            </HBox>
         </children>
      </VBox>

      <!-- Main Content Area -->
      <SplitPane dividerPositions="0.6" VBox.vgrow="ALWAYS">
         <items>
            <!-- Left Side - Tax Rate Table -->
            <VBox spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="Tax Rates" />
                  
                  <!-- Tax Rate Table -->
                  <TableView fx:id="taxRateTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="nameColumn" prefWidth="140.0" text="Name" />
                        <TableColumn fx:id="typeColumn" prefWidth="120.0" text="Type" />
                        <TableColumn fx:id="rateColumn" prefWidth="70.0" text="Rate" />
                        <TableColumn fx:id="jurisdictionColumn" prefWidth="100.0" text="Jurisdiction" />
                        <TableColumn fx:id="statusColumn" prefWidth="80.0" text="Status" />
                        <TableColumn fx:id="validityColumn" prefWidth="140.0" text="Validity" />
                     </columns>
                  </TableView>

                  <!-- Table Action Buttons -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Button fx:id="addTaxRateButton" onAction="#handleAddTaxRate" styleClass="primary-button" text="➕ Add Tax Rate" />
                        <Button fx:id="editTaxRateButton" onAction="#handleEditTaxRate" styleClass="secondary-button" text="✏️ Edit" />
                        <Button fx:id="deleteTaxRateButton" onAction="#handleDeleteTaxRate" styleClass="danger-button" text="🗑️ Delete" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Button fx:id="testTaxRateButton" onAction="#handleTestTaxRate" styleClass="info-button" text="🧪 Test" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="15.0" right="10.0" top="10.0" />
               </padding>
            </VBox>

            <!-- Right Side - Tax Rate Details and Exempt Customers -->
            <VBox spacing="15.0">
               <children>
                  <TabPane VBox.vgrow="ALWAYS">
                     <tabs>
                        <!-- Tax Rate Details Tab -->
                        <Tab text="Tax Rate Details" closable="false">
                           <content>
                              <ScrollPane fitToWidth="true">
                                 <content>
                                    <VBox spacing="15.0">
                                       <children>
                                          <!-- Basic Information -->
                                          <VBox spacing="8.0" styleClass="form-section">
                                             <children>
                                                <Label styleClass="form-section-title" text="Basic Information" />
                                                
                                                <HBox alignment="CENTER_LEFT" spacing="10.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Name:" />
                                                      <TextField fx:id="nameField" prefWidth="200.0" promptText="Tax rate name" styleClass="form-field" />
                                                   </children>
                                                </HBox>
                                                
                                                <VBox spacing="5.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Description:" />
                                                      <TextArea fx:id="descriptionField" prefRowCount="2" promptText="Tax rate description" styleClass="form-field" />
                                                   </children>
                                                </VBox>
                                                
                                                <HBox alignment="CENTER_LEFT" spacing="10.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Type:" />
                                                      <ComboBox fx:id="typeComboBox" prefWidth="150.0" promptText="Select type" styleClass="form-field" />
                                                   </children>
                                                </HBox>
                                                
                                                <HBox alignment="CENTER_LEFT" spacing="10.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Rate (%):" />
                                                      <TextField fx:id="rateField" prefWidth="100.0" promptText="0.00" styleClass="form-field" />
                                                      <Label styleClass="form-help" text="(percentage rate, e.g., 8.5 for 8.5%)" />
                                                   </children>
                                                </HBox>
                                                
                                                <HBox alignment="CENTER_LEFT" spacing="10.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Jurisdiction:" />
                                                      <TextField fx:id="jurisdictionField" prefWidth="150.0" promptText="State, City, etc." styleClass="form-field" />
                                                   </children>
                                                </HBox>
                                             </children>
                                          </VBox>

                                          <!-- Applicability Rules -->
                                          <VBox spacing="8.0" styleClass="form-section">
                                             <children>
                                                <Label styleClass="form-section-title" text="Applicability Rules" />
                                                
                                                <VBox spacing="5.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Applicable Categories:" />
                                                      <TextField fx:id="applicableCategoriesField" promptText="Comma-separated list (leave empty for all)" styleClass="form-field" />
                                                   </children>
                                                </VBox>
                                                
                                                <VBox spacing="5.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Exempt Categories:" />
                                                      <TextField fx:id="exemptCategoriesField" promptText="Comma-separated list of exempt categories" styleClass="form-field" />
                                                   </children>
                                                </VBox>
                                                
                                                <HBox alignment="CENTER_LEFT" spacing="10.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Min Amount:" />
                                                      <TextField fx:id="minimumAmountField" prefWidth="100.0" promptText="0.00" styleClass="form-field" />
                                                      <Label styleClass="form-label" text="Max Amount:" />
                                                      <TextField fx:id="maximumAmountField" prefWidth="100.0" promptText="No limit" styleClass="form-field" />
                                                   </children>
                                                </HBox>
                                             </children>
                                          </VBox>

                                          <!-- Validity Period -->
                                          <VBox spacing="8.0" styleClass="form-section">
                                             <children>
                                                <Label styleClass="form-section-title" text="Validity Period" />
                                                
                                                <HBox alignment="CENTER_LEFT" spacing="10.0">
                                                   <children>
                                                      <Label styleClass="form-label" text="Effective Date:" />
                                                      <DatePicker fx:id="effectiveDatePicker" prefWidth="140.0" styleClass="form-field" />
                                                      <Label styleClass="form-label" text="Expiration Date:" />
                                                      <DatePicker fx:id="expirationDatePicker" prefWidth="140.0" styleClass="form-field" />
                                                   </children>
                                                </HBox>
                                             </children>
                                          </VBox>

                                          <!-- Options -->
                                          <VBox spacing="8.0" styleClass="form-section">
                                             <children>
                                                <Label styleClass="form-section-title" text="Options" />
                                                <CheckBox fx:id="activeCheckBox" styleClass="form-checkbox" text="Active" />
                                             </children>
                                          </VBox>

                                          <!-- Form Action Buttons -->
                                          <HBox alignment="CENTER_LEFT" spacing="10.0">
                                             <children>
                                                <Button fx:id="saveTaxRateButton" onAction="#handleSaveTaxRate" styleClass="primary-button" text="💾 Save" visible="false" />
                                                <Button fx:id="cancelTaxRateButton" onAction="#handleCancelTaxRate" styleClass="secondary-button" text="❌ Cancel" visible="false" />
                                             </children>
                                          </HBox>
                                       </children>
                                       <padding>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                       </padding>
                                    </VBox>
                                 </content>
                              </ScrollPane>
                           </content>
                        </Tab>

                        <!-- Tax Exempt Customers Tab -->
                        <Tab text="Tax Exempt Customers" closable="false">
                           <content>
                              <VBox spacing="15.0">
                                 <children>
                                    <Label styleClass="section-title" text="Tax Exempt Customers" />
                                    
                                    <!-- Add Exempt Customer -->
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Customer ID/Email:" />
                                          <TextField fx:id="exemptCustomerField" prefWidth="200.0" promptText="Enter customer ID or email" styleClass="form-field" />
                                          <Button fx:id="addExemptCustomerButton" onAction="#handleAddExemptCustomer" styleClass="primary-button" text="➕ Add" />
                                       </children>
                                    </HBox>
                                    
                                    <!-- Exempt Customers Table -->
                                    <TableView fx:id="exemptCustomersTable" VBox.vgrow="ALWAYS">
                                       <columns>
                                          <TableColumn fx:id="exemptCustomerColumn" prefWidth="300.0" text="Customer ID/Email" />
                                       </columns>
                                    </TableView>
                                    
                                    <!-- Remove Button -->
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Button fx:id="removeExemptCustomerButton" onAction="#handleRemoveExemptCustomer" styleClass="danger-button" text="🗑️ Remove Selected" />
                                       </children>
                                    </HBox>
                                 </children>
                                 <padding>
                                    <Insets bottom="10.0" left="15.0" right="15.0" top="10.0" />
                                 </padding>
                              </VBox>
                           </content>
                        </Tab>
                     </tabs>
                  </TabPane>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="15.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </children>
</VBox>
