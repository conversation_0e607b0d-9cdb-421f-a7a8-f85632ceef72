<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.DatabaseResetDialogController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #e74c3c; -fx-padding: 15;">
         <children>
            <Label text="⚠️ Database Reset" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="20.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label text="DANGER ZONE" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
         </children>
      </HBox>

      <!-- Warning Message -->
      <VBox spacing="10.0" style="-fx-background-color: #fdf2e9; -fx-padding: 15; -fx-border-color: #e67e22; -fx-border-width: 2;">
         <children>
            <Label style="-fx-font-weight: bold; -fx-font-size: 16px; -fx-text-fill: #d35400;" text="⚠️ WARNING: This action is irreversible!" />
            <Label text="This will permanently delete ALL data from the database including:" wrapText="true" />
            <VBox spacing="5.0">
               <children>
                  <Label text="• All products and inventory data" />
                  <Label text="• All customer information and purchase history" />
                  <Label text="• All transaction records and sales data" />
                  <Label text="• All settings and configuration data" />
               </children>
               <padding>
                  <Insets left="20.0" />
               </padding>
            </VBox>
            <Label style="-fx-font-weight: bold; -fx-text-fill: #c0392b;" text="Make sure you have a backup before proceeding!" />
         </children>
      </VBox>

      <!-- Current Database Stats -->
      <VBox spacing="10.0" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
         <children>
            <Label style="-fx-font-weight: bold; -fx-font-size: 14px;" text="Current Database Status" />
            <Label fx:id="lblCurrentStats" style="-fx-font-family: monospace;" text="Loading..." wrapText="true" />
         </children>
      </VBox>

      <!-- Reset Options -->
      <VBox spacing="15.0" style="-fx-padding: 20;">
         <children>
            <Label style="-fx-font-weight: bold; -fx-font-size: 16px;" text="Reset Options" />
            
            <VBox spacing="10.0">
               <children>
                  <RadioButton fx:id="rbCompleteReset" text="Complete Reset (Empty Database)">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </RadioButton>
                  <Label style="-fx-text-fill: #7f8c8d;" text="Removes all data and leaves the database completely empty." wrapText="true">
                     <padding>
                        <Insets left="25.0" />
                     </padding>
                  </Label>
                  
                  <RadioButton fx:id="rbResetWithDemo" selected="true" text="Reset with Demo Data">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </RadioButton>
                  <Label style="-fx-text-fill: #7f8c8d;" text="Removes all data and populates the database with sample products, customers, and transactions for testing." wrapText="true">
                     <padding>
                        <Insets left="25.0" />
                     </padding>
                  </Label>
               </children>
            </VBox>

            <!-- Confirmation Section -->
            <VBox spacing="10.0" style="-fx-background-color: #fff5f5; -fx-padding: 15; -fx-border-color: #e74c3c; -fx-border-width: 1; -fx-border-radius: 5;">
               <children>
                  <Label style="-fx-font-weight: bold; -fx-text-fill: #c0392b;" text="Confirmation Required" />
                  
                  <CheckBox fx:id="chkConfirmReset" text="I understand that this action will permanently delete all data" />
                  
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="Type 'RESET DATABASE' to confirm:" />
                        <TextField fx:id="txtConfirmationText" prefWidth="200.0" promptText="RESET DATABASE" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Progress Section -->
            <VBox fx:id="vboxProgress" spacing="10.0">
               <children>
                  <ProgressBar fx:id="progressBar" maxWidth="Infinity" prefHeight="20.0" />
                  <Label fx:id="lblProgress" text="Ready to reset..." />
               </children>
            </VBox>
         </children>
      </VBox>

      <!-- Action Buttons -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
         <children>
            <Button fx:id="btnCancel" cancelButton="true" onAction="#handleCancel" text="Cancel" />
            <Button fx:id="btnReset" onAction="#handleReset" style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold;" text="🗑️ Reset Database" />
         </children>
      </HBox>
   </children>
</VBox>
