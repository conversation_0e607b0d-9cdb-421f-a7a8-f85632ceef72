import com.clothingstore.database.DatabaseManager;
import com.clothingstore.dao.RefundItemTrackingDAO;
import com.clothingstore.model.RefundItemTracking;
import com.clothingstore.service.RefundItemTrackingService;
import com.clothingstore.model.TransactionItemRefundSummary;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class DebugRefundTracking {
    public static void main(String[] args) {
        try {
            System.out.println("=== DEBUGGING REFUND TRACKING ===");
            
            // Check what refund tracking records exist for transaction item 90
            try (Connection conn = DatabaseManager.getInstance().getConnection()) {
                String sql = "SELECT * FROM refund_item_tracking WHERE original_transaction_item_id = 90";
                try (PreparedStatement stmt = conn.prepareStatement(sql); ResultSet rs = stmt.executeQuery()) {
                    System.out.println("Refund tracking records for item 90:");
                    System.out.println("ID | Orig_Item_ID | Refund_TXN | Product | Qty | Amount");
                    System.out.println("--------------------------------------------------------");
                    
                    boolean found = false;
                    while (rs.next()) {
                        found = true;
                        System.out.printf("%d | %d | %d | %s | %d | %.2f%n",
                            rs.getLong("id"),
                            rs.getLong("original_transaction_item_id"),
                            rs.getLong("refund_transaction_id"),
                            rs.getString("product_name"),
                            rs.getInt("refunded_quantity"),
                            rs.getDouble("refund_amount")
                        );
                    }
                    
                    if (!found) {
                        System.out.println("No refund tracking records found for item 90!");
                    }
                }
                
                // Check all recent refund tracking records
                System.out.println("\nAll recent refund tracking records:");
                String allSql = "SELECT * FROM refund_item_tracking ORDER BY id DESC LIMIT 10";
                try (PreparedStatement stmt = conn.prepareStatement(allSql); ResultSet rs = stmt.executeQuery()) {
                    System.out.println("ID | Orig_Item_ID | Refund_TXN | Product | Qty | Amount");
                    System.out.println("--------------------------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%d | %d | %d | %s | %d | %.2f%n",
                            rs.getLong("id"),
                            rs.getLong("original_transaction_item_id"),
                            rs.getLong("refund_transaction_id"),
                            rs.getString("product_name"),
                            rs.getInt("refunded_quantity"),
                            rs.getDouble("refund_amount")
                        );
                    }
                }
            }
            
            // Test DAO directly
            System.out.println("\n=== TESTING DAO DIRECTLY ===");
            RefundItemTrackingDAO dao = new RefundItemTrackingDAO();
            List<RefundItemTracking> records = dao.findByTransactionItemId(90L);
            System.out.println("DAO found " + records.size() + " records for item 90");
            
            for (RefundItemTracking record : records) {
                System.out.printf("  - Record ID: %d, Qty: %d, Amount: %.2f%n",
                    record.getId(),
                    record.getRefundedQuantity(),
                    record.getRefundAmount().doubleValue()
                );
            }
            
            // Test service
            System.out.println("\n=== TESTING SERVICE ===");
            RefundItemTrackingService service = new RefundItemTrackingService();
            TransactionItemRefundSummary summary = service.getTransactionItemRefundSummary(90L);
            
            if (summary != null) {
                System.out.println("Service summary found:");
                System.out.println("  - Original Quantity: " + summary.getOriginalQuantity());
                System.out.println("  - Total Refunded: " + summary.getTotalRefundedQuantity());
                System.out.println("  - Remaining: " + summary.getRemainingQuantity());
                System.out.println("  - Refund History Size: " + summary.getRefundHistory().size());
                System.out.println("  - Display: " + summary.getQuantityDisplay());
            } else {
                System.out.println("Service returned null summary!");
            }
            
        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
