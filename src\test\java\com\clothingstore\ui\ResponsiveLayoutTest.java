package com.clothingstore.ui;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.testfx.framework.junit5.ApplicationExtension;
import org.testfx.framework.junit5.Start;

/**
 * Test class to verify responsive layout behavior across different screen resolutions
 */
@ExtendWith(ApplicationExtension.class)
public class ResponsiveLayoutTest {

    private Stage primaryStage;

    @Start
    public void start(Stage stage) throws Exception {
        this.primaryStage = stage;
        
        // Load the main window FXML
        FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/MainWindow.fxml"));
        Parent root = loader.load();
        
        Scene scene = new Scene(root);
        scene.getStylesheets().add(getClass().getResource("/css/main-style.css").toExternalForm());
        
        stage.setScene(scene);
        stage.setTitle("Clothing Store Management System - Layout Test");
        stage.show();
    }

    @Test
    public void testLayout1920x1080() {
        // Test layout on standard desktop resolution
        primaryStage.setWidth(1920);
        primaryStage.setHeight(1080);
        
        // Verify that components are properly sized and visible
        // This would normally include assertions about component visibility and sizing
        System.out.println("Testing layout at 1920x1080 resolution");
        
        // Allow time for layout to adjust
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    public void testLayout1366x768() {
        // Test layout on common laptop resolution
        primaryStage.setWidth(1366);
        primaryStage.setHeight(768);
        
        System.out.println("Testing layout at 1366x768 resolution");
        
        // Allow time for layout to adjust
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    public void testLayout1280x720() {
        // Test layout on smaller screen
        primaryStage.setWidth(1280);
        primaryStage.setHeight(720);
        
        System.out.println("Testing layout at 1280x720 resolution");
        
        // Allow time for layout to adjust
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    public void testNavigationPanelResponsiveness() {
        // Test that navigation panel adjusts properly
        primaryStage.setWidth(1920);
        primaryStage.setHeight(1080);
        
        // Navigation panel should be at preferred width (220px)
        System.out.println("Testing navigation panel at large resolution");
        
        primaryStage.setWidth(1024);
        primaryStage.setHeight(768);
        
        // Navigation panel should shrink but not below minimum (180px)
        System.out.println("Testing navigation panel at smaller resolution");
        
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    public void testQuickActionCardsLayout() {
        // Test that quick action cards layout properly
        primaryStage.setWidth(1920);
        primaryStage.setHeight(1080);
        
        // Should show 3 columns of cards
        System.out.println("Testing quick action cards at large resolution");
        
        primaryStage.setWidth(1024);
        primaryStage.setHeight(768);
        
        // Cards should still be visible and properly sized
        System.out.println("Testing quick action cards at smaller resolution");
        
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Manual test method to visually inspect layouts
     * This method can be run manually to see the actual layout behavior
     */
    public static void main(String[] args) {
        Application.launch(TestApplication.class, args);
    }

    /**
     * Test application for manual layout testing
     */
    public static class TestApplication extends Application {
        @Override
        public void start(Stage primaryStage) throws Exception {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/MainWindow.fxml"));
            Parent root = loader.load();
            
            Scene scene = new Scene(root);
            scene.getStylesheets().add(getClass().getResource("/css/main-style.css").toExternalForm());
            
            primaryStage.setScene(scene);
            primaryStage.setTitle("Clothing Store Management System - Manual Layout Test");
            primaryStage.setWidth(1366);
            primaryStage.setHeight(768);
            primaryStage.show();
            
            // Test different resolutions automatically
            new Thread(() -> {
                try {
                    Thread.sleep(3000);
                    javafx.application.Platform.runLater(() -> {
                        primaryStage.setWidth(1920);
                        primaryStage.setHeight(1080);
                        System.out.println("Switched to 1920x1080");
                    });
                    
                    Thread.sleep(3000);
                    javafx.application.Platform.runLater(() -> {
                        primaryStage.setWidth(1280);
                        primaryStage.setHeight(720);
                        System.out.println("Switched to 1280x720");
                    });
                    
                    Thread.sleep(3000);
                    javafx.application.Platform.runLater(() -> {
                        primaryStage.setWidth(1366);
                        primaryStage.setHeight(768);
                        System.out.println("Switched back to 1366x768");
                    });
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        }
    }
}
