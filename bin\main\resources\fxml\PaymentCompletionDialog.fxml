<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.PaymentCompletionController">
   <stylesheets>
      <URL value="@../css/enhanced-pos.css" />
   </stylesheets>
   
   <!-- Header -->
   <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #27ae60; -fx-padding: 20;">
      <Label text="✅ Payment Completed Successfully" textFill="WHITE">
         <font>
            <Font name="System Bold" size="22.0" />
         </font>
      </Label>
      <Region HBox.hgrow="ALWAYS" />
      <Label fx:id="lblTransactionNumber" text="Transaction: #12345" textFill="WHITE">
         <font>
            <Font name="System Bold" size="16.0" />
         </font>
      </Label>
   </HBox>

   <!-- Transaction Summary -->
   <VBox spacing="15" style="-fx-padding: 25; -fx-background-color: #f8f9fa;">
      <Label text="Transaction Summary" style="-fx-font-weight: bold; -fx-font-size: 18px;"/>
      
      <GridPane hgap="15" vgap="10">
         <columnConstraints>
            <ColumnConstraints hgrow="NEVER" minWidth="120" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         
         <Label text="Date & Time:" style="-fx-font-weight: bold;" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
         <Label fx:id="lblDateTime" text="12/25/2024 14:30:45" GridPane.columnIndex="1" GridPane.rowIndex="0"/>
         
         <Label text="Customer:" style="-fx-font-weight: bold;" GridPane.columnIndex="0" GridPane.rowIndex="1"/>
         <Label fx:id="lblCustomer" text="John Doe" GridPane.columnIndex="1" GridPane.rowIndex="1"/>
         
         <Label text="Payment Method:" style="-fx-font-weight: bold;" GridPane.columnIndex="0" GridPane.rowIndex="2"/>
         <Label fx:id="lblPaymentMethod" text="CASH" GridPane.columnIndex="1" GridPane.rowIndex="2"/>
         
         <Label text="Total Amount:" style="-fx-font-weight: bold;" GridPane.columnIndex="0" GridPane.rowIndex="3"/>
         <Label fx:id="lblTotalAmount" text="$45.99" style="-fx-font-weight: bold; -fx-font-size: 16px;" GridPane.columnIndex="1" GridPane.rowIndex="3"/>
         
         <Label text="Amount Paid:" style="-fx-font-weight: bold;" GridPane.columnIndex="0" GridPane.rowIndex="4"/>
         <Label fx:id="lblAmountPaid" text="$50.00" GridPane.columnIndex="1" GridPane.rowIndex="4"/>
         
         <Label fx:id="lblChangeLabel" text="Change Given:" style="-fx-font-weight: bold;" GridPane.columnIndex="0" GridPane.rowIndex="5"/>
         <Label fx:id="lblChange" text="$4.01" style="-fx-font-weight: bold; -fx-text-fill: #e74c3c;" GridPane.columnIndex="1" GridPane.rowIndex="5"/>
      </GridPane>
   </VBox>

   <!-- Items Summary -->
   <VBox spacing="10" style="-fx-padding: 0 25 15 25;">
      <Label text="Items Purchased" style="-fx-font-weight: bold; -fx-font-size: 16px;"/>
      <TableView fx:id="tblItems" prefHeight="120" style="-fx-background-color: white;">
         <columns>
            <TableColumn fx:id="colItemName" text="Item" prefWidth="200"/>
            <TableColumn fx:id="colQuantity" text="Qty" prefWidth="60"/>
            <TableColumn fx:id="colUnitPrice" text="Unit Price" prefWidth="100"/>
            <TableColumn fx:id="colTotal" text="Total" prefWidth="100"/>
         </columns>
      </TableView>
   </VBox>

   <!-- Receipt Options -->
   <VBox spacing="20" style="-fx-padding: 25; -fx-background-color: #ffffff; -fx-border-color: #dee2e6; -fx-border-width: 1 0 0 0;">
      <Label text="Receipt Options" style="-fx-font-weight: bold; -fx-font-size: 18px;"/>
      
      <!-- WhatsApp Receipt Option -->
      <VBox spacing="15" style="-fx-background-color: #e8f5e8; -fx-padding: 20; -fx-background-radius: 8; -fx-border-color: #27ae60; -fx-border-width: 1; -fx-border-radius: 8;">
         <HBox alignment="CENTER_LEFT" spacing="15">
            <Label text="📱" style="-fx-font-size: 24px;"/>
            <VBox spacing="5">
               <Label text="Send Receipt via WhatsApp" style="-fx-font-weight: bold; -fx-font-size: 16px;"/>
               <Label text="Get your receipt instantly on WhatsApp - convenient and eco-friendly!" style="-fx-text-fill: #666; -fx-font-size: 13px;"/>
            </VBox>
            <Region HBox.hgrow="ALWAYS"/>
            <Button fx:id="btnSendWhatsApp" text="Send via WhatsApp" onAction="#handleSendWhatsApp" 
                    style="-fx-background-color: #25d366; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 12 20; -fx-background-radius: 6;"/>
         </HBox>
         
         <!-- Phone Number Section (initially hidden) -->
         <VBox fx:id="vboxPhoneSection" spacing="10" visible="false" managed="false">
            <Separator/>
            <Label text="Enter your WhatsApp phone number:" style="-fx-font-weight: bold;"/>
            <HBox spacing="10" alignment="CENTER_LEFT">
               <TextField fx:id="txtPhoneNumber" promptText="+1234567890" prefWidth="200"/>
               <Button fx:id="btnConfirmPhone" text="Send Receipt" onAction="#handleConfirmPhone" 
                       style="-fx-background-color: #25d366; -fx-text-fill: white; -fx-font-weight: bold;"/>
               <Button fx:id="btnSkipWhatsApp" text="Skip" onAction="#handleSkipWhatsApp" 
                       style="-fx-background-color: #6c757d; -fx-text-fill: white;"/>
            </HBox>
            <Label fx:id="lblPhoneValidation" text="" style="-fx-text-fill: #e74c3c; -fx-font-size: 12px;"/>
         </VBox>
         
         <!-- Progress Section (initially hidden) -->
         <VBox fx:id="vboxProgress" spacing="10" visible="false" managed="false">
            <Separator/>
            <HBox spacing="10" alignment="CENTER_LEFT">
               <ProgressIndicator fx:id="progressIndicator" prefWidth="30" prefHeight="30"/>
               <Label fx:id="lblProgressStatus" text="Sending receipt via WhatsApp..." style="-fx-font-weight: bold;"/>
            </HBox>
         </VBox>
         
         <!-- Success/Error Section (initially hidden) -->
         <VBox fx:id="vboxResult" spacing="10" visible="false" managed="false">
            <Separator/>
            <HBox spacing="10" alignment="CENTER_LEFT">
               <Label fx:id="lblResultIcon" text="✅" style="-fx-font-size: 20px;"/>
               <Label fx:id="lblResultMessage" text="Receipt sent successfully!" style="-fx-font-weight: bold;"/>
            </HBox>
         </VBox>
      </VBox>
      
      <!-- Traditional Receipt Options -->
      <VBox spacing="15" style="-fx-background-color: #f8f9fa; -fx-padding: 20; -fx-background-radius: 8; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8;">
         <Label text="Traditional Receipt Options" style="-fx-font-weight: bold; -fx-font-size: 16px;"/>
         <HBox spacing="15">
            <Button fx:id="btnPrintReceipt" text="🖨️ Print Receipt" onAction="#handlePrintReceipt" 
                    style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20; -fx-background-radius: 6;"/>
            <Button fx:id="btnEmailReceipt" text="📧 Email Receipt" onAction="#handleEmailReceipt" 
                    style="-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20; -fx-background-radius: 6;"/>
            <Button fx:id="btnViewReceipt" text="👁️ View Receipt" onAction="#handleViewReceipt" 
                    style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20; -fx-background-radius: 6;"/>
         </HBox>
      </VBox>
   </VBox>

   <!-- Action Buttons -->
   <HBox spacing="15" alignment="CENTER_RIGHT" style="-fx-padding: 25; -fx-background-color: #f8f9fa;">
      <Button fx:id="btnNewTransaction" text="Start New Transaction" onAction="#handleNewTransaction" 
              style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 12 25; -fx-background-radius: 6;"/>
      <Button fx:id="btnClose" text="Close" onAction="#handleClose" 
              style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 12 25; -fx-background-radius: 6;"/>
   </HBox>
</VBox>
