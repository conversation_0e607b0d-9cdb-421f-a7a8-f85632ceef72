package com.clothingstore.view;

import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.model.PaymentHistory;
import com.clothingstore.service.PaymentHistoryService;
import com.clothingstore.service.RefundTrackingIntegrationService;

import javafx.beans.property.SimpleStringProperty;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.cell.PropertyValueFactory;

public class TransactionDetailDialogController {

    // Transaction Information
    @FXML
    private Label lblTransactionNumber;
    @FXML
    private Label lblTransactionDate;
    @FXML
    private Label lblStatus;
    @FXML
    private Label lblSubtotal;
    @FXML
    private Label lblDiscount;
    @FXML
    private Label lblTotal;
    @FXML
    private Label lblPaymentMethod;
    @FXML
    private Label lblCashierName;
    @FXML
    private TextArea txtNotes;

    // Customer Information Section
    @FXML
    private Label lblCustomerName;
    @FXML
    private Label lblCustomerEmail;
    @FXML
    private Label lblCustomerPhone;
    @FXML
    private Label lblCustomerAddress;
    @FXML
    private Label lblCustomerType;
    @FXML
    private Label lblLoyaltyPoints;
    @FXML
    private Label lblTotalSpent;
    @FXML
    private Label lblTotalPurchases;

    @FXML
    private TableView<TransactionItem> tblTransactionItems;
    @FXML
    private TableColumn<TransactionItem, String> colProductName;
    @FXML
    private TableColumn<TransactionItem, String> colSku;
    @FXML
    private TableColumn<TransactionItem, Integer> colQuantity;
    @FXML
    private TableColumn<TransactionItem, String> colUnitPrice;
    @FXML
    private TableColumn<TransactionItem, String> colLineTotal;

    // Payment History Table
    @FXML
    private TableView<PaymentHistory> tblPaymentHistory;
    @FXML
    private TableColumn<PaymentHistory, String> colPaymentDate;
    @FXML
    private TableColumn<PaymentHistory, String> colPaymentAmount;
    @FXML
    private TableColumn<PaymentHistory, String> colPaymentMethod;
    @FXML
    private TableColumn<PaymentHistory, String> colPaymentType;
    @FXML
    private TableColumn<PaymentHistory, String> colRunningBalance;
    @FXML
    private TableColumn<PaymentHistory, String> colRemainingBalance;

    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
    private PaymentHistoryService paymentHistoryService;
    private RefundTrackingIntegrationService refundTrackingService;

    @FXML
    public void initialize() {
        // Initialize services
        paymentHistoryService = PaymentHistoryService.getInstance();
        refundTrackingService = RefundTrackingIntegrationService.getInstance();

        // Set up transaction items table
        colProductName.setCellValueFactory(new PropertyValueFactory<>("productName"));
        colSku.setCellValueFactory(new PropertyValueFactory<>("productSku"));
        colQuantity.setCellValueFactory(new PropertyValueFactory<>("quantity"));

        colUnitPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice()))
        );
        colLineTotal.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getLineTotal()))
        );

        // Set up payment history table if it exists
        if (tblPaymentHistory != null) {
            setupPaymentHistoryTable();
        }
    }

    /**
     * Set up payment history table columns and formatting
     */
    private void setupPaymentHistoryTable() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm");

        // Payment Date column
        colPaymentDate.setCellValueFactory(cellData -> {
            PaymentHistory payment = cellData.getValue();
            return new SimpleStringProperty(payment.getPaymentDate().format(dateTimeFormatter));
        });

        // Payment Amount column with currency formatting
        colPaymentAmount.setCellValueFactory(cellData -> {
            PaymentHistory payment = cellData.getValue();
            return new SimpleStringProperty(currencyFormat.format(payment.getPaymentAmount()));
        });

        // Payment Method column with color coding
        colPaymentMethod.setCellValueFactory(cellData -> {
            PaymentHistory payment = cellData.getValue();
            return new SimpleStringProperty(payment.getPaymentMethod());
        });
        colPaymentMethod.setCellFactory(column -> new javafx.scene.control.TableCell<PaymentHistory, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    // Color code payment methods
                    switch (item.toUpperCase()) {
                        case "CASH":
                            setStyle("-fx-text-fill: #4caf50; -fx-font-weight: bold;");
                            break;
                        case "CARD":
                        case "CREDIT_CARD":
                        case "DEBIT_CARD":
                            setStyle("-fx-text-fill: #2196f3; -fx-font-weight: bold;");
                            break;
                        case "BANK_TRANSFER":
                            setStyle("-fx-text-fill: #ff9800; -fx-font-weight: bold;");
                            break;
                        case "MOBILE_PAYMENT":
                            setStyle("-fx-text-fill: #9c27b0; -fx-font-weight: bold;");
                            break;
                        default:
                            setStyle("-fx-text-fill: #607d8b; -fx-font-weight: bold;");
                            break;
                    }
                }
            }
        });

        // Payment Type column with icons
        colPaymentType.setCellValueFactory(cellData -> {
            PaymentHistory payment = cellData.getValue();
            String type = payment.getPaymentType().toString();
            return new SimpleStringProperty(type);
        });
        colPaymentType.setCellFactory(column -> new javafx.scene.control.TableCell<PaymentHistory, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    if ("PAYMENT".equals(item)) {
                        setText("Payment");
                        setStyle("-fx-text-fill: #4caf50; -fx-font-weight: bold;");
                    } else if ("REFUND".equals(item)) {
                        setText("Refund");
                        setStyle("-fx-text-fill: #f44336; -fx-font-weight: bold;");
                    } else {
                        setText(item);
                        setStyle("-fx-text-fill: #607d8b;");
                    }
                }
            }
        });

        // Running Balance column
        colRunningBalance.setCellValueFactory(cellData -> {
            PaymentHistory payment = cellData.getValue();
            return new SimpleStringProperty(currencyFormat.format(payment.getRunningBalance()));
        });

        // Remaining Balance column with color coding
        colRemainingBalance.setCellValueFactory(cellData -> {
            PaymentHistory payment = cellData.getValue();
            return new SimpleStringProperty(currencyFormat.format(payment.getRemainingBalance()));
        });
        colRemainingBalance.setCellFactory(column -> new javafx.scene.control.TableCell<PaymentHistory, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty) ;
                if (empty || item == null) {
                    setText(null);
                    setStyle("");
                } else {
                    setText(item);
                    // Parse the currency value to determine color
                    try {
                        String numericValue = item.replaceAll("[^\\d.-]", "");
                        double balance = Double.parseDouble(numericValue);
                        if (balance == 0.0) {
                            setStyle("-fx-background-color: #c8e6c9; -fx-text-fill: #2e7d32; -fx-font-weight: bold;");
                        } else if (balance <= 100.0) {
                            setStyle("-fx-background-color: #ffe0b2; -fx-text-fill: #ef6c00; -fx-font-weight: bold;");
                        } else {
                            setStyle("-fx-background-color: #ffcdd2; -fx-text-fill: #c62828; -fx-font-weight: bold;");
                        }
                    } catch (NumberFormatException e) {
                        setStyle("-fx-text-fill: #607d8b;");
                    }
                }
            }
        });
    }

    public void setTransaction(Transaction transaction) {
        // Set transaction information
        lblTransactionNumber.setText(transaction.getTransactionNumber());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");
        lblTransactionDate.setText(transaction.getTransactionDate().format(formatter));
        lblStatus.setText(transaction.getStatus());
        setLabelText(lblPaymentMethod, transaction.getPaymentMethod());
        setLabelText(lblCashierName, transaction.getCashierName());

        // Set financial information
        lblSubtotal.setText(currencyFormat.format(transaction.getSubtotal()));
        lblDiscount.setText(currencyFormat.format(transaction.getDiscountAmount()));
        lblTotal.setText(currencyFormat.format(transaction.getTotalAmount()));

        // Set notes
        if (txtNotes != null) {
            txtNotes.setText(transaction.getNotes() != null ? transaction.getNotes() : "");
        }

        // Set customer information
        populateCustomerInformation(transaction.getCustomer());

        // Set up the items table
        tblTransactionItems.getItems().setAll(transaction.getItems());

        // Load payment history if table exists
        if (tblPaymentHistory != null) {
            loadPaymentHistory(transaction.getId());
        }
    }

    /**
     * Load payment history for the transaction
     */
    private void loadPaymentHistory(Long transactionId) {
        try {
            if (paymentHistoryService != null && tblPaymentHistory != null) {
                java.util.List<PaymentHistory> paymentHistory = paymentHistoryService.getPaymentHistory(transactionId);
                tblPaymentHistory.getItems().setAll(paymentHistory);

                // If there's refund tracking service, get additional refund information
                if (refundTrackingService != null) {
                    RefundTrackingIntegrationService.RefundSummary refundSummary =
                        refundTrackingService.getRefundSummary(transactionId);
                    if (refundSummary != null) {
                        // Could add additional refund summary display here if needed
                        System.out.println("DEBUG: Refund summary loaded for transaction " + transactionId);
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("ERROR: Failed to load payment history for transaction " + transactionId + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Populate customer information section with complete customer data
     */
    private void populateCustomerInformation(Customer customer) {
        if (customer == null) {
            // Walk-in customer
            setLabelText(lblCustomerName, "Walk-in Customer");
            setLabelText(lblCustomerEmail, "N/A");
            setLabelText(lblCustomerPhone, "N/A");
            setLabelText(lblCustomerAddress, "N/A");
            setLabelText(lblCustomerType, "Walk-in");
            setLabelText(lblLoyaltyPoints, "0");
            setLabelText(lblTotalSpent, "$0.00");
            setLabelText(lblTotalPurchases, "0");
        } else {
            // Registered customer - populate all available information
            setLabelText(lblCustomerName, customer.getFullName());
            setLabelText(lblCustomerPhone, customer.getPhone());
            setLabelText(lblCustomerAddress, customer.getAddress());
            setLabelText(lblCustomerType, "Registered Customer");
            setLabelText(lblLoyaltyPoints, String.valueOf(customer.getLoyaltyPoints()));
            setLabelText(lblTotalSpent, currencyFormat.format(customer.getTotalSpent()));
            setLabelText(lblTotalPurchases, String.valueOf(customer.getTotalPurchases()));
        }
    }

    /**
     * Safely set label text, handling null labels gracefully
     */
    private void setLabelText(Label label, String text) {
        if (label != null) {
            label.setText(text != null ? text : "N/A");
        }
    }
}
