package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Model class for tracking detailed refund information for individual transaction items
 */
public class RefundItemTracking {
    private Long id;
    private Long originalTransactionId;
    private Long originalTransactionItemId;
    private Long refundTransactionId;
    private Long productId;
    private String productName;
    private String productSku;
    private int originalQuantity;
    private int refundedQuantity;
    private BigDecimal unitPrice;
    private BigDecimal refundAmount;
    private String refundReason;
    private String cashierName;
    private LocalDateTime refundDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public RefundItemTracking() {
        this.refundDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public RefundItemTracking(Long originalTransactionId, Long originalTransactionItemId, 
                             Long refundTransactionId, Long productId, String productName,
                             int originalQuantity, int refundedQuantity, BigDecimal unitPrice,
                             BigDecimal refundAmount, String refundReason, String cashierName) {
        this();
        this.originalTransactionId = originalTransactionId;
        this.originalTransactionItemId = originalTransactionItemId;
        this.refundTransactionId = refundTransactionId;
        this.productId = productId;
        this.productName = productName;
        this.originalQuantity = originalQuantity;
        this.refundedQuantity = refundedQuantity;
        this.unitPrice = unitPrice;
        this.refundAmount = refundAmount;
        this.refundReason = refundReason;
        this.cashierName = cashierName;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getOriginalTransactionId() { return originalTransactionId; }
    public void setOriginalTransactionId(Long originalTransactionId) { this.originalTransactionId = originalTransactionId; }

    public Long getOriginalTransactionItemId() { return originalTransactionItemId; }
    public void setOriginalTransactionItemId(Long originalTransactionItemId) { this.originalTransactionItemId = originalTransactionItemId; }

    public Long getRefundTransactionId() { return refundTransactionId; }
    public void setRefundTransactionId(Long refundTransactionId) { this.refundTransactionId = refundTransactionId; }

    public Long getProductId() { return productId; }
    public void setProductId(Long productId) { this.productId = productId; }

    public String getProductName() { return productName; }
    public void setProductName(String productName) { this.productName = productName; }

    public String getProductSku() { return productSku; }
    public void setProductSku(String productSku) { this.productSku = productSku; }

    public int getOriginalQuantity() { return originalQuantity; }
    public void setOriginalQuantity(int originalQuantity) { this.originalQuantity = originalQuantity; }

    public int getRefundedQuantity() { return refundedQuantity; }
    public void setRefundedQuantity(int refundedQuantity) { this.refundedQuantity = refundedQuantity; }

    public BigDecimal getUnitPrice() { return unitPrice; }
    public void setUnitPrice(BigDecimal unitPrice) { this.unitPrice = unitPrice; }

    public BigDecimal getRefundAmount() { return refundAmount; }
    public void setRefundAmount(BigDecimal refundAmount) { this.refundAmount = refundAmount; }

    public String getRefundReason() { return refundReason; }
    public void setRefundReason(String refundReason) { this.refundReason = refundReason; }

    public String getCashierName() { return cashierName; }
    public void setCashierName(String cashierName) { this.cashierName = cashierName; }

    public LocalDateTime getRefundDate() { return refundDate; }
    public void setRefundDate(LocalDateTime refundDate) { this.refundDate = refundDate; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    // Business methods
    public int getRemainingQuantity() {
        return originalQuantity - refundedQuantity;
    }

    public boolean isFullyRefunded() {
        return refundedQuantity >= originalQuantity;
    }

    public boolean isPartiallyRefunded() {
        return refundedQuantity > 0 && refundedQuantity < originalQuantity;
    }

    public BigDecimal getRemainingRefundableAmount() {
        if (getRemainingQuantity() <= 0) {
            return BigDecimal.ZERO;
        }
        return unitPrice.multiply(BigDecimal.valueOf(getRemainingQuantity()));
    }

    // Utility methods
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RefundItemTracking that = (RefundItemTracking) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(originalTransactionItemId, that.originalTransactionItemId) &&
               Objects.equals(refundTransactionId, that.refundTransactionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, originalTransactionItemId, refundTransactionId);
    }

    @Override
    public String toString() {
        return String.format("RefundItemTracking{id=%d, product='%s', originalQty=%d, refundedQty=%d, remainingQty=%d, refundAmount=%s}",
                id, productName, originalQuantity, refundedQuantity, getRemainingQuantity(), refundAmount);
    }
}
