package com.clothingstore.dao;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Category;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Data Access Object for Category operations
 */
public class CategoryDAO {
    
    private static CategoryDAO instance;
    
    private CategoryDAO() {}
    
    public static synchronized CategoryDAO getInstance() {
        if (instance == null) {
            instance = new CategoryDAO();
        }
        return instance;
    }
    
    /**
     * Find all active categories ordered by display order and name
     */
    public List<Category> findAll() throws SQLException {
        String sql = "SELECT * FROM categories WHERE active = 1 ORDER BY display_order, name";
        List<Category> categories = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                categories.add(mapResultSetToCategory(rs));
            }
        }
        
        return categories;
    }
    
    /**
     * Find all categories including inactive ones
     */
    public List<Category> findAllIncludingInactive() throws SQLException {
        String sql = "SELECT * FROM categories ORDER BY display_order, name";
        List<Category> categories = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                categories.add(mapResultSetToCategory(rs));
            }
        }
        
        return categories;
    }
    
    /**
     * Find category by ID
     */
    public Optional<Category> findById(Long id) throws SQLException {
        String sql = "SELECT * FROM categories WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToCategory(rs));
                }
            }
        }
        
        return Optional.empty();
    }
    
    /**
     * Find category by name
     */
    public Optional<Category> findByName(String name) throws SQLException {
        String sql = "SELECT * FROM categories WHERE name = ? AND active = 1";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, name);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToCategory(rs));
                }
            }
        }
        
        return Optional.empty();
    }
    
    /**
     * Save category (insert or update)
     */
    public Category save(Category category) throws SQLException {
        if (category.getId() == null) {
            return insert(category);
        } else {
            return update(category);
        }
    }
    
    /**
     * Insert new category
     */
    private Category insert(Category category) throws SQLException {
        String sql = "INSERT INTO categories (name, description, display_order, active) VALUES (?, ?, ?, ?)";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, category.getName());
            pstmt.setString(2, category.getDescription());
            pstmt.setInt(3, category.getDisplayOrder());
            pstmt.setBoolean(4, category.isActive());
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating category failed, no rows affected.");
            }
            
            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    category.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating category failed, no ID obtained.");
                }
            }
        }
        
        return category;
    }
    
    /**
     * Update existing category
     */
    private Category update(Category category) throws SQLException {
        String sql = "UPDATE categories SET name = ?, description = ?, display_order = ?, active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, category.getName());
            pstmt.setString(2, category.getDescription());
            pstmt.setInt(3, category.getDisplayOrder());
            pstmt.setBoolean(4, category.isActive());
            pstmt.setLong(5, category.getId());
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating category failed, no rows affected.");
            }
        }
        
        return category;
    }
    
    /**
     * Delete category (soft delete by setting active = false)
     */
    public void delete(Long id) throws SQLException {
        // Check if category is used by any products
        if (isCategoryInUse(id)) {
            throw new SQLException("Cannot delete category: it is currently used by one or more products.");
        }
        
        String sql = "UPDATE categories SET active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Deleting category failed, no rows affected.");
            }
        }
    }
    
    /**
     * Hard delete category (permanently remove from database)
     */
    public void hardDelete(Long id) throws SQLException {
        // Check if category is used by any products
        if (isCategoryInUse(id)) {
            throw new SQLException("Cannot delete category: it is currently used by one or more products.");
        }
        
        String sql = "DELETE FROM categories WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Deleting category failed, no rows affected.");
            }
        }
    }
    
    /**
     * Check if category is used by any products
     */
    public boolean isCategoryInUse(Long categoryId) throws SQLException {
        // First get the category name
        Optional<Category> category = findById(categoryId);
        if (!category.isPresent()) {
            return false;
        }
        
        String sql = "SELECT COUNT(*) FROM products WHERE category = ? AND active = 1";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, category.get().getName());
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Get product count for a category
     */
    public int getProductCount(Long categoryId) throws SQLException {
        Optional<Category> category = findById(categoryId);
        if (!category.isPresent()) {
            return 0;
        }
        
        String sql = "SELECT COUNT(*) FROM products WHERE category = ? AND active = 1";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, category.get().getName());
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Get category names for dropdown/combobox
     */
    public List<String> getCategoryNames() throws SQLException {
        String sql = "SELECT name FROM categories WHERE active = 1 ORDER BY display_order, name";
        List<String> names = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                names.add(rs.getString("name"));
            }
        }
        
        return names;
    }
    
    /**
     * Update display orders for categories
     */
    public void updateDisplayOrders(List<Category> categories) throws SQLException {
        String sql = "UPDATE categories SET display_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try (Connection conn = DatabaseManager.getInstance().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            conn.setAutoCommit(false);
            
            try {
                for (int i = 0; i < categories.size(); i++) {
                    Category category = categories.get(i);
                    pstmt.setInt(1, i + 1);
                    pstmt.setLong(2, category.getId());
                    pstmt.addBatch();
                }
                
                pstmt.executeBatch();
                conn.commit();
                
            } catch (SQLException e) {
                conn.rollback();
                throw e;
            } finally {
                conn.setAutoCommit(true);
            }
        }
    }
    
    /**
     * Map ResultSet to Category object
     */
    private Category mapResultSetToCategory(ResultSet rs) throws SQLException {
        Category category = new Category();
        category.setId(rs.getLong("id"));
        category.setName(rs.getString("name"));
        category.setDescription(rs.getString("description"));
        category.setDisplayOrder(rs.getInt("display_order"));
        category.setActive(rs.getBoolean("active"));
        
        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            category.setCreatedAt(createdAt.toLocalDateTime());
        }
        
        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            category.setUpdatedAt(updatedAt.toLocalDateTime());
        }
        
        return category;
    }
}
