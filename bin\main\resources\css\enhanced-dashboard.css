/* Enhanced Dashboard Styles */

/* Root Variables for Theme Support */
.root {
    -fx-primary-color: #007bff;
    -fx-secondary-color: #6c757d;
    -fx-success-color: #28a745;
    -fx-danger-color: #dc3545;
    -fx-warning-color: #ffc107;
    -fx-info-color: #17a2b8;
    
    /* Light Theme Colors */
    -fx-bg-primary: #ffffff;
    -fx-bg-secondary: #f8f9fa;
    -fx-text-primary: #212529;
    -fx-text-secondary: #6c757d;
    -fx-border-color: #dee2e6;
    -fx-shadow-color: rgba(0,0,0,0.1);
}

/* Dark Theme Colors */
.dark-theme {
    -fx-bg-primary: #2c3e50;
    -fx-bg-secondary: #34495e;
    -fx-text-primary: #ecf0f1;
    -fx-text-secondary: #bdc3c7;
    -fx-border-color: #4a5568;
    -fx-shadow-color: rgba(0,0,0,0.3);
}

/* Dashboard Container */
.dashboard-container {
    -fx-background-color: -fx-bg-secondary;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.light-theme .dashboard-container {
    -fx-background-color: #f8f9fa;
}

.dark-theme .dashboard-container {
    -fx-background-color: #2c3e50;
}

/* Header Styles */
.dashboard-header {
    -fx-background-color: -fx-bg-primary;
    -fx-border-color: -fx-border-color;
    -fx-border-width: 0 0 1 0;
    -fx-effect: dropshadow(gaussian, -fx-shadow-color, 5, 0, 0, 1);
}

.dashboard-title {
    -fx-text-fill: -fx-text-primary;
    -fx-font-weight: bold;
}

.dashboard-subtitle {
    -fx-text-fill: -fx-text-secondary;
}

/* Theme Toggle */
.theme-toggle {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: white;
    -fx-background-radius: 20;
    -fx-padding: 8 16 8 16;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 3, 0, 0, 1);
}

.theme-toggle:hover {
    -fx-background-color: derive(-fx-secondary-color, -10%);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.theme-toggle:selected {
    -fx-background-color: -fx-primary-color;
}

/* Controls Section */
.controls-section {
    -fx-background-color: -fx-bg-primary;
    -fx-border-color: -fx-border-color;
    -fx-border-width: 0 0 1 0;
}

.control-label {
    -fx-text-fill: -fx-text-primary;
    -fx-font-weight: bold;
}

/* Date Controls */
.date-controls {
    -fx-alignment: center-left;
}

.date-picker {
    -fx-background-color: white;
    -fx-border-color: -fx-border-color;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.date-picker:focused {
    -fx-border-color: -fx-primary-color;
    -fx-border-width: 2;
}

/* Primary Button */
.primary-button {
    -fx-background-color: -fx-primary-color;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10 20 10 20;
    -fx-background-radius: 6;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,123,255,0.3), 4, 0, 0, 2);
}

.primary-button:hover {
    -fx-background-color: derive(-fx-primary-color, -10%);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.primary-button:pressed {
    -fx-background-color: derive(-fx-primary-color, -20%);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

.primary-button:disabled {
    -fx-background-color: -fx-secondary-color;
    -fx-opacity: 0.6;
}

/* Preset Buttons */
.preset-button {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.preset-button:hover {
    -fx-background-color: derive(-fx-secondary-color, -15%);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Export Buttons */
.export-button {
    -fx-text-fill: white;
    -fx-padding: 6 12 6 12;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.csv-button {
    -fx-background-color: -fx-success-color;
}

.csv-button:hover {
    -fx-background-color: derive(-fx-success-color, -10%);
}

.pdf-button {
    -fx-background-color: -fx-danger-color;
}

.pdf-button:hover {
    -fx-background-color: derive(-fx-danger-color, -10%);
}

/* Status Label */
.status-label {
    -fx-text-fill: -fx-text-secondary;
    -fx-font-size: 12px;
    -fx-font-style: italic;
}

/* Metrics Grid */
.metrics-grid {
    -fx-background-color: transparent;
}

/* Metric Cards */
.metric-card {
    -fx-background-color: white;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);
    -fx-cursor: hand;
}

.dark-theme .metric-card {
    -fx-background-color: #34495e;
}

.metric-card:hover {
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 15, 0, 0, 3);
}

/* Charts Section */
.charts-tabpane {
    -fx-background-color: -fx-bg-primary;
    -fx-border-color: transparent;
}

.charts-tabpane .tab-header-area {
    -fx-padding: 0 20 0 20;
}

.chart-tab {
    -fx-background-color: transparent;
    -fx-text-base-color: -fx-text-primary;
    -fx-font-weight: bold;
}

.chart-tab:selected {
    -fx-background-color: -fx-primary-color;
    -fx-text-base-color: white;
}

.chart-container {
    -fx-background-color: -fx-bg-primary;
}

/* Chart Specific Styles */
.profit-trend-chart {
    -fx-background-color: transparent;
    -fx-padding: 10;
}

.profit-trend-chart .chart-plot-background {
    -fx-background-color: white;
}

.dark-theme .profit-trend-chart .chart-plot-background {
    -fx-background-color: #34495e;
}

.category-pie-chart {
    -fx-background-color: transparent;
    -fx-padding: 10;
}

.comparison-bar-chart {
    -fx-background-color: transparent;
    -fx-padding: 10;
}

.comparison-bar-chart .chart-plot-background {
    -fx-background-color: white;
}

.dark-theme .comparison-bar-chart .chart-plot-background {
    -fx-background-color: #34495e;
}

/* Chart Titles */
.chart-title {
    -fx-text-fill: -fx-text-primary;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

/* Chart Legends */
.chart-legend {
    -fx-background-color: transparent;
    -fx-text-fill: -fx-text-primary;
}

/* Chart Axes */
.axis {
    -fx-tick-label-fill: -fx-text-secondary;
    -fx-font-size: 11px;
}

.axis-label {
    -fx-text-fill: -fx-text-primary;
    -fx-font-weight: bold;
}

/* Responsive Design */
@media screen and (max-width: 1280px) {
    .dashboard-title {
        -fx-font-size: 24px;
    }
    
    .metric-card {
        -fx-padding: 15;
    }
    
    .chart-container {
        -fx-padding: 15;
    }
}

@media screen and (max-width: 1024px) {
    .dashboard-title {
        -fx-font-size: 20px;
    }
    
    .preset-button {
        -fx-font-size: 10px;
        -fx-padding: 5 10 5 10;
    }
}

/* Animation Classes */
.fade-in {
    -fx-opacity: 0;
}

.slide-up {
    -fx-translate-y: 20;
}

/* Scrollbar Styling */
.scroll-bar {
    -fx-background-color: transparent;
}

.scroll-bar .track {
    -fx-background-color: -fx-bg-secondary;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.scroll-bar .thumb {
    -fx-background-color: -fx-secondary-color;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.scroll-bar .thumb:hover {
    -fx-background-color: derive(-fx-secondary-color, -10%);
}

/* Progress Indicator */
.progress-indicator {
    -fx-progress-color: -fx-primary-color;
}

/* Tooltips */
.tooltip {
    -fx-background-color: -fx-text-primary;
    -fx-text-fill: -fx-bg-primary;
    -fx-background-radius: 4;
    -fx-font-size: 11px;
    -fx-padding: 5 8 5 8;
}
