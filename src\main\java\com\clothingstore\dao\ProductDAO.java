package com.clothingstore.dao;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Product;

/**
 * Data Access Object for Product operations
 */
public class ProductDAO {

    private static ProductDAO instance;

    private ProductDAO() {
    }

    public static synchronized ProductDAO getInstance() {
        if (instance == null) {
            instance = new ProductDAO();
        }
        return instance;
    }

    public List<Product> findAll() throws SQLException {
        String sql = "SELECT * FROM products WHERE active = 1 ORDER BY name";
        List<Product> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                products.add(mapResultSetToProduct(rs));
            }
        }

        return products;
    }

    public Optional<Product> findById(Long id) throws SQLException {
        String sql = "SELECT * FROM products WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToProduct(rs));
                }
            }
        }

        return Optional.empty();
    }

    public Optional<Product> findBySku(String sku) throws SQLException {
        String sql = "SELECT * FROM products WHERE sku = ? AND active = 1";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sku);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToProduct(rs));
                }
            }
        }

        return Optional.empty();
    }

    public Optional<Product> findByBarcode(String barcode) throws SQLException {
        String sql = "SELECT * FROM products WHERE barcode = ? AND active = 1";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, barcode);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToProduct(rs));
                }
            }
        }

        return Optional.empty();
    }

    public boolean isBarcodeExists(String barcode) throws SQLException {
        String sql = "SELECT COUNT(*) FROM products WHERE barcode = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, barcode);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }

        return false;
    }

    public List<Product> findByCategory(String category) throws SQLException {
        String sql = "SELECT * FROM products WHERE category = ? AND active = 1 ORDER BY name";
        List<Product> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, category);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        }

        return products;
    }

    public List<Product> searchProducts(String searchTerm) throws SQLException {
        String sql = "SELECT * FROM products "
                + "WHERE active = 1 AND ("
                + "name LIKE ? OR "
                + "description LIKE ? OR "
                + "sku LIKE ? OR "
                + "category LIKE ? OR "
                + "brand LIKE ?"
                + ") ORDER BY name";

        List<Product> products = new ArrayList<>();
        String searchPattern = "%" + searchTerm + "%";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (int i = 1; i <= 5; i++) {
                pstmt.setString(i, searchPattern);
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        }

        return products;
    }

    public List<Product> findLowStockProducts() throws SQLException {
        String sql = "SELECT * FROM products WHERE active = 1 AND stock_quantity <= min_stock_level ORDER BY stock_quantity";
        List<Product> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                products.add(mapResultSetToProduct(rs));
            }
        }

        return products;
    }

    public Product save(Product product) throws SQLException {
        if (product.getId() == null) {
            return insert(product);
        } else {
            return update(product);
        }
    }

    private Product insert(Product product) throws SQLException {
        String sql = "INSERT INTO products (sku, barcode, name, description, category, brand, color, size, "
                + "price, cost_price, stock_quantity, min_stock_level, reorder_quantity, low_stock_alert_enabled, "
                + "supplier_id, supplier_name, supplier_code, image_url, active) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            setPreparedStatementParameters(pstmt, product);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating product failed, no rows affected.");
            }

            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    product.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating product failed, no ID obtained.");
                }
            }
        }

        return product;
    }

    private Product update(Product product) throws SQLException {
        String sql = "UPDATE products SET sku = ?, barcode = ?, name = ?, description = ?, category = ?, brand = ?, "
                + "color = ?, size = ?, price = ?, cost_price = ?, stock_quantity = ?, "
                + "min_stock_level = ?, reorder_quantity = ?, low_stock_alert_enabled = ?, "
                + "supplier_id = ?, supplier_name = ?, supplier_code = ?, image_url = ?, active = ?, updated_at = CURRENT_TIMESTAMP "
                + "WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            setPreparedStatementParameters(pstmt, product);
            pstmt.setLong(20, product.getId());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating product failed, no rows affected.");
            }
        }

        return product;
    }

    public void delete(Long id) throws SQLException {
        // Soft delete - mark as inactive
        String sql = "UPDATE products SET active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Deleting product failed, no rows affected.");
            }
        }
    }

    public void updateStock(Long productId, int newQuantity) throws SQLException {
        String sql = "UPDATE products SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, newQuantity);
            pstmt.setLong(2, productId);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating stock failed, no rows affected.");
            }
        }
    }

    public List<String> getAllCategories() throws SQLException {
        // First try to get categories from the categories table
        try {
            CategoryDAO categoryDAO = CategoryDAO.getInstance();
            return categoryDAO.getCategoryNames();
        } catch (Exception e) {
            // Fallback to getting distinct categories from products table
            String sql = "SELECT DISTINCT category FROM products WHERE active = 1 AND category IS NOT NULL AND category != '' ORDER BY category";
            List<String> categories = new ArrayList<>();

            try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

                while (rs.next()) {
                    String category = rs.getString("category");
                    if (category != null && !category.trim().isEmpty()) {
                        categories.add(category.trim());
                    }
                }
            }

            return categories;
        }
    }

    public List<String> getAllBrands() throws SQLException {
        String sql = "SELECT DISTINCT brand FROM products WHERE active = 1 AND brand IS NOT NULL ORDER BY brand";
        List<String> brands = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                brands.add(rs.getString("brand"));
            }
        }

        return brands;
    }

    // Additional useful methods for enhanced product management
    public List<String> getAllSizes() throws SQLException {
        String sql = "SELECT DISTINCT size FROM products WHERE active = 1 AND size IS NOT NULL ORDER BY size";
        List<String> sizes = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                sizes.add(rs.getString("size"));
            }
        }

        return sizes;
    }

    public List<String> getAllColors() throws SQLException {
        String sql = "SELECT DISTINCT color FROM products WHERE active = 1 AND color IS NOT NULL ORDER BY color";
        List<String> colors = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                colors.add(rs.getString("color"));
            }
        }

        return colors;
    }

    public List<Product> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) throws SQLException {
        String sql = "SELECT * FROM products WHERE active = 1 AND price BETWEEN ? AND ? ORDER BY price";
        List<Product> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setBigDecimal(1, minPrice);
            pstmt.setBigDecimal(2, maxPrice);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        }

        return products;
    }

    public List<Product> findOutOfStockProducts() throws SQLException {
        String sql = "SELECT * FROM products WHERE active = 1 AND stock_quantity = 0 ORDER BY name";
        List<Product> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                products.add(mapResultSetToProduct(rs));
            }
        }

        return products;
    }

    public int getTotalProductCount() throws SQLException {
        String sql = "SELECT COUNT(*) FROM products WHERE active = 1";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt(1);
            }
        }

        return 0;
    }

    public BigDecimal getTotalInventoryValue() throws SQLException {
        String sql = "SELECT SUM(price * stock_quantity) FROM products WHERE active = 1";
        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {
            if (rs.next()) {
                return rs.getBigDecimal(1) != null ? rs.getBigDecimal(1) : BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    public List<Product> findTopSellingProducts(int limit) throws SQLException {
        String sql = "SELECT p.*, SUM(ti.quantity) as total_sold "
                + "FROM products p "
                + "JOIN transaction_items ti ON p.id = ti.product_id "
                + "GROUP BY p.id "
                + "ORDER BY total_sold DESC "
                + "LIMIT ?";

        List<Product> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, limit);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        }

        return products;
    }

    public boolean isSkuExists(String sku) throws SQLException {
        String sql = "SELECT COUNT(*) FROM products WHERE sku = ?";
        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sku);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }

    /**
     * Get product count by category name
     */
    public int getProductCountByCategory(String categoryName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM products WHERE category = ? AND active = 1";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, categoryName);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }

        return 0;
    }

    /**
     * Update category name for all products (used when category is renamed)
     */
    public void updateProductCategory(String oldCategoryName, String newCategoryName) throws SQLException {
        String sql = "UPDATE products SET category = ?, updated_at = CURRENT_TIMESTAMP WHERE category = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, newCategoryName);
            pstmt.setString(2, oldCategoryName);

            pstmt.executeUpdate();
        }
    }

    private void setPreparedStatementParameters(PreparedStatement pstmt, Product product) throws SQLException {
        pstmt.setString(1, product.getSku());
        pstmt.setString(2, product.getBarcode());
        pstmt.setString(3, product.getName());
        pstmt.setString(4, product.getDescription());
        pstmt.setString(5, product.getCategory());
        pstmt.setString(6, product.getBrand());
        pstmt.setString(7, product.getColor());
        pstmt.setString(8, product.getSize());
        pstmt.setBigDecimal(9, product.getPrice());
        pstmt.setBigDecimal(10, product.getCostPrice());
        pstmt.setInt(11, product.getStockQuantity());
        pstmt.setInt(12, product.getMinStockLevel());
        pstmt.setInt(13, product.getReorderQuantity());
        pstmt.setBoolean(14, product.isLowStockAlertEnabled());

        // Supplier fields
        if (product.getSupplierId() != null) {
            pstmt.setLong(15, product.getSupplierId());
        } else {
            pstmt.setNull(15, java.sql.Types.INTEGER);
        }
        pstmt.setString(16, product.getSupplierName());
        pstmt.setString(17, product.getSupplierCode());

        pstmt.setString(18, product.getImageUrl());
        pstmt.setBoolean(19, product.isActive());
    }

    private Product mapResultSetToProduct(ResultSet rs) throws SQLException {
        Product product = new Product();
        product.setId(rs.getLong("id"));
        product.setSku(rs.getString("sku"));
        product.setBarcode(rs.getString("barcode"));
        product.setName(rs.getString("name"));
        product.setDescription(rs.getString("description"));
        product.setCategory(rs.getString("category"));
        product.setBrand(rs.getString("brand"));
        product.setColor(rs.getString("color"));
        product.setSize(rs.getString("size"));
        product.setPrice(rs.getBigDecimal("price"));
        product.setCostPrice(rs.getBigDecimal("cost_price"));
        product.setStockQuantity(rs.getInt("stock_quantity"));
        product.setMinStockLevel(rs.getInt("min_stock_level"));

        // Handle new fields with default values for backward compatibility
        try {
            product.setReorderQuantity(rs.getInt("reorder_quantity"));
        } catch (SQLException e) {
            product.setReorderQuantity(0); // Default value
        }

        try {
            product.setLowStockAlertEnabled(rs.getBoolean("low_stock_alert_enabled"));
        } catch (SQLException e) {
            product.setLowStockAlertEnabled(true); // Default value
        }

        // Handle supplier fields (may not exist in older databases)
        try {
            long supplierId = rs.getLong("supplier_id");
            if (!rs.wasNull()) {
                product.setSupplierId(supplierId);
            }
        } catch (SQLException e) {
            // Column might not exist in older versions
        }

        try {
            product.setSupplierName(rs.getString("supplier_name"));
        } catch (SQLException e) {
            // Column might not exist in older versions
        }

        try {
            product.setSupplierCode(rs.getString("supplier_code"));
        } catch (SQLException e) {
            // Column might not exist in older versions
        }

        product.setImageUrl(rs.getString("image_url"));
        product.setActive(rs.getBoolean("active"));

        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            product.setCreatedAt(createdAt.toLocalDateTime());
        }

        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            product.setUpdatedAt(updatedAt.toLocalDateTime());
        }

        return product;
    }

    // Supplier-related methods
    public List<Product> findBySupplierId(Long supplierId) throws SQLException {
        String sql = "SELECT * FROM products WHERE supplier_id = ? AND active = 1 ORDER BY name";
        List<Product> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, supplierId);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        }

        return products;
    }

    public List<Product> findBySupplierName(String supplierName) throws SQLException {
        String sql = "SELECT * FROM products WHERE supplier_name LIKE ? AND active = 1 ORDER BY name";
        List<Product> products = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, "%" + supplierName + "%");

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    products.add(mapResultSetToProduct(rs));
                }
            }
        }

        return products;
    }

    public List<String> getAllSupplierNames() throws SQLException {
        String sql = "SELECT DISTINCT supplier_name FROM products WHERE supplier_name IS NOT NULL AND supplier_name != '' AND active = 1 ORDER BY supplier_name";
        List<String> suppliers = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                suppliers.add(rs.getString("supplier_name"));
            }
        }

        return suppliers;
    }
}
