package com.clothingstore.view;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.CustomerAnalyticsService;
import com.clothingstore.service.CustomerAnalyticsService.CustomerAnalytics;
import com.clothingstore.service.RefundService;
import com.clothingstore.model.RefundResult;
import com.clothingstore.model.RefundItem;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.VBox;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;

/**
 * Controller for Transaction History interface
 */
public class TransactionHistoryController implements Initializable {

    // Enhanced Filtering Components
    @FXML
    private ComboBox<String> cmbQuickFilter;
    @FXML
    private ComboBox<String> cmbTimeRange;
    @FXML
    private DatePicker dateFrom;
    @FXML
    private DatePicker dateTo;
    @FXML
    private TextField timeFrom;
    @FXML
    private TextField timeTo;
    @FXML
    private TextField txtSearchTransactionId;
    @FXML
    private ComboBox<String> cmbStatus;
    @FXML
    private ComboBox<String> cmbPaymentMethod;
    @FXML
    private Button btnApplyFilter;
    @FXML
    private Button btnClearFilter;
    @FXML
    private Label lblActiveFilter;
    @FXML
    private Label lblFilteredCount;
    @FXML
    private Button btnRefresh;
    @FXML
    private Button btnExport;

    @FXML
    private TableView<Transaction> tblTransactions;
    @FXML
    private TableColumn<Transaction, String> colTransactionNumber;
    @FXML
    private TableColumn<Transaction, String> colDate;
    @FXML
    private TableColumn<Transaction, String> colTime;
    @FXML
    private TableColumn<Transaction, String> colCustomer;
    @FXML
    private TableColumn<Transaction, Integer> colItems;
    @FXML
    private TableColumn<Transaction, String> colSubtotal;
    @FXML
    private TableColumn<Transaction, String> colDiscount;
    @FXML
    private TableColumn<Transaction, String> colTotal;
    @FXML
    private TableColumn<Transaction, String> colPaymentMethod;
    @FXML
    private TableColumn<Transaction, String> colStatus;
    @FXML
    private TableColumn<Transaction, String> colActions;

    @FXML
    private MenuItem menuViewDetails;
    @FXML
    private MenuItem menuPrintReceipt;
    @FXML
    private MenuItem menuRefund;

    @FXML
    private Label lblTotalTransactions;
    @FXML
    private Label lblTotalAmount;
    @FXML
    private Label lblAverageTransaction;
    @FXML
    private Label lblSelectedPeriod;

    // Customer Analytics Components
    @FXML
    private TextField txtCustomerSearch;
    @FXML
    private Button btnClearCustomerFilter;
    @FXML
    private Label lblSelectedCustomer;
    @FXML
    private VBox customerAnalyticsPanel;
    @FXML
    private Label lblCustomerName;
    @FXML
    private Label lblCustomerContact;
    @FXML
    private Label lblCustomerTransactions;
    @FXML
    private Label lblCustomerLifetimeValue;
    @FXML
    private Label lblCustomerAverage;
    @FXML
    private Label lblCustomerLastPurchase;
    @FXML
    private Label lblCustomerPreferredPayment;
    @FXML
    private Label lblCustomerCategories;
    @FXML
    private Label lblCustomerMembership;
    @FXML
    private Button btnExportCustomerData;

    private ObservableList<Transaction> allTransactions;
    private ObservableList<Transaction> filteredTransactions;
    private TransactionDAO transactionDAO;
    private CustomerDAO customerDAO;
    private CustomerAnalyticsService customerAnalyticsService;
    private NumberFormat currencyFormat;
    private Customer selectedCustomer;
    private CustomerAnalytics currentCustomerAnalytics;
    private DateTimeFormatter dateFormatter;
    private DateTimeFormatter timeFormatter;
    private MainWindowController mainWindowController;

    public void setMainWindowController(MainWindowController mainWindowController) {
        this.mainWindowController = mainWindowController;
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Ensure initialization happens on FX thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(() -> initialize(location, resources));
            return;
        }

        transactionDAO = TransactionDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        customerAnalyticsService = CustomerAnalyticsService.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
        timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");

        allTransactions = FXCollections.observableArrayList();
        filteredTransactions = FXCollections.observableArrayList();

        setupControls();
        setupTableColumns();
        setDefaultDates();

        // Load data in background to prevent blocking UI
        javafx.application.Platform.runLater(this::loadTransactions);
    }

    private void setupControls() {
        setupQuickFilters();
        setupTimeRangeFilters();
        setupStatusAndPaymentFilters();
        setupTimeInputValidation();
    }

    private void setupQuickFilters() {
        // Quick filter options for predefined periods
        cmbQuickFilter.setItems(FXCollections.observableArrayList(
                "Today",
                "Yesterday",
                "This Week",
                "Last Week",
                "This Month",
                "Last Month",
                "Last 7 Days",
                "Last 30 Days",
                "Last 90 Days",
                "This Year",
                "Custom Range"
        ));
        cmbQuickFilter.setValue("Last 30 Days");
    }

    private void setupTimeRangeFilters() {
        // Time range filters for hours-based filtering
        cmbTimeRange.setItems(FXCollections.observableArrayList(
                "Last 1 Hour",
                "Last 2 Hours",
                "Last 6 Hours",
                "Last 12 Hours",
                "Last 24 Hours",
                "Last 48 Hours"
        ));
        cmbTimeRange.setPromptText("Select Hours");
    }

    private void setupStatusAndPaymentFilters() {
        // Status filter options
        cmbStatus.setItems(FXCollections.observableArrayList(
                "All", "Completed", "Pending", "Refunded", "Partially Refunded", "Cancelled"
        ));
        cmbStatus.setValue("All");

        // Payment method filter options
        cmbPaymentMethod.setItems(FXCollections.observableArrayList(
                "All", "Cash", "Credit Card", "Debit Card", "Check", "Gift Card"
        ));
        cmbPaymentMethod.setValue("All");
    }

    private void setupTimeInputValidation() {
        // Add input validation for time fields (HH:MM format)
        timeFrom.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!isValidTimeFormat(newVal)) {
                timeFrom.setStyle("-fx-border-color: red;");
            } else {
                timeFrom.setStyle("");
            }
        });

        timeTo.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!isValidTimeFormat(newVal)) {
                timeTo.setStyle("-fx-border-color: red;");
            } else {
                timeTo.setStyle("");
            }
        });

        // Set default time values
        timeFrom.setText("00:00");
        timeTo.setText("23:59");
    }

    private boolean isValidTimeFormat(String time) {
        if (time == null || time.trim().isEmpty()) {
            return true; // Empty is valid
        }
        return time.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$");
    }

    private void setupTableColumns() {
        colTransactionNumber.setCellValueFactory(new PropertyValueFactory<>("transactionNumber"));
        colDate.setCellValueFactory(cellData -> {
            if (cellData.getValue() == null || cellData.getValue().getTransactionDate() == null) {
                return new SimpleStringProperty("");
            }
            String date = cellData.getValue().getTransactionDate().format(dateFormatter);
            return new SimpleStringProperty(date);
        });
        colTime.setCellValueFactory(cellData -> {
            String time = cellData.getValue().getTransactionDate().format(timeFormatter);
            return new SimpleStringProperty(time);
        });
        colCustomer.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            if (transaction == null) {
                return new SimpleStringProperty("Walk-in");
            }

            // Use complete customer information if available
            if (transaction.getCustomer() != null) {
                Customer customer = transaction.getCustomer();
                String customerInfo = customer.getFullName();

                // Add phone number if available for better identification
                if (customer.getPhone() != null && !customer.getPhone().trim().isEmpty()) {
                    customerInfo += " (" + customer.getPhone() + ")";
                }

                return new SimpleStringProperty(customerInfo);
            } else {
                // Fallback to customer name if customer object is not loaded
                String customerName = transaction.getCustomerName();
                return new SimpleStringProperty(customerName != null ? customerName : "Walk-in");
            }
        });
        colItems.setCellValueFactory(new PropertyValueFactory<>("totalItems"));
        colSubtotal.setCellValueFactory(cellData -> {
            String subtotal = currencyFormat.format(cellData.getValue().getSubtotal());
            return new SimpleStringProperty(subtotal);
        });
        colDiscount.setCellValueFactory(cellData -> {
            String discount = currencyFormat.format(cellData.getValue().getDiscount());
            return new SimpleStringProperty(discount);
        });
        colTotal.setCellValueFactory(cellData -> {
            String total = currencyFormat.format(cellData.getValue().getTotal());
            return new SimpleStringProperty(total);
        });
        colPaymentMethod.setCellValueFactory(new PropertyValueFactory<>("paymentMethod"));
        // Enhanced status column with colored badges and refund information
        colStatus.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String status = transaction.getStatus();

            // Add refund amount information for refunded transactions
            if ("REFUNDED".equals(status)) {
                return new SimpleStringProperty("REFUNDED ($" + currencyFormat.format(transaction.getRefundedAmount()) + ")");
            } else if ("PARTIALLY_REFUNDED".equals(status)) {
                return new SimpleStringProperty("PARTIAL ($" + currencyFormat.format(transaction.getRefundedAmount()) + ")");
            } else {
                return new SimpleStringProperty(status);
            }
        });

        // Custom cell factory for status column with colored badges
        colStatus.setCellFactory(col -> new TableCell<Transaction, String>() {
            @Override
            protected void updateItem(String status, boolean empty) {
                super.updateItem(status, empty);
                if (empty || status == null) {
                    setGraphic(null);
                    setText(null);
                } else {
                    javafx.scene.control.Label statusLabel = new javafx.scene.control.Label(status);

                    // Apply appropriate style class based on status
                    if (status.startsWith("COMPLETED")) {
                        statusLabel.getStyleClass().add("status-completed");
                    } else if (status.startsWith("PENDING")) {
                        statusLabel.getStyleClass().add("status-pending");
                    } else if (status.startsWith("REFUNDED") || status.startsWith("PARTIAL")) {
                        statusLabel.getStyleClass().add("status-refunded");
                    } else {
                        statusLabel.getStyleClass().add("status-completed"); // Default
                    }

                    setGraphic(statusLabel);
                    setText(null);
                }
            }
        });

        // Enhanced action buttons column with modern styling and partial refund support
        colActions.setCellFactory(col -> new TableCell<Transaction, String>() {
            private final Button viewBtn = new Button("View");
            private final Button receiptBtn = new Button("Receipt");
            private final Button fullRefundBtn = new Button("Full Refund");
            private final Button partialRefundBtn = new Button("Partial Refund");

            {
                viewBtn.setOnAction(e -> {
                    javafx.application.Platform.runLater(() -> {
                        Transaction transaction = getTableView().getItems().get(getIndex());
                        handleViewDetails(transaction);
                    });
                });

                receiptBtn.setOnAction(e -> {
                    javafx.application.Platform.runLater(() -> {
                        Transaction transaction = getTableView().getItems().get(getIndex());
                        handlePrintReceipt(transaction);
                    });
                });

                fullRefundBtn.setOnAction(e -> {
                    System.out.println("=== FULL REFUND BUTTON CLICKED ===");
                    javafx.application.Platform.runLater(() -> {
                        try {
                            Transaction transaction = getTableView().getItems().get(getIndex());
                            System.out.println("Processing full refund for: " + transaction.getTransactionNumber());
                            handleFullRefund(transaction);
                        } catch (Exception ex) {
                            System.err.println("Error in full refund button action: " + ex.getMessage());
                            ex.printStackTrace();
                        }
                    });
                });

                partialRefundBtn.setOnAction(e -> {
                    System.out.println("=== PARTIAL REFUND BUTTON CLICKED ===");
                    javafx.application.Platform.runLater(() -> {
                        try {
                            Transaction transaction = getTableView().getItems().get(getIndex());
                            System.out.println("Processing partial refund for: " + transaction.getTransactionNumber());
                            handlePartialRefund(transaction);
                        } catch (Exception ex) {
                            System.err.println("Error in partial refund button action: " + ex.getMessage());
                            ex.printStackTrace();
                        }
                    });
                });

                // Modern button styling
                viewBtn.getStyleClass().addAll("action-btn", "action-btn-view");
                receiptBtn.getStyleClass().addAll("action-btn", "action-btn-receipt");
                fullRefundBtn.getStyleClass().addAll("action-btn", "action-btn-refund");
                partialRefundBtn.getStyleClass().addAll("action-btn", "action-btn-partial-refund");

                // Set button sizes for consistency
                viewBtn.setPrefWidth(60);
                receiptBtn.setPrefWidth(60);
                fullRefundBtn.setPrefWidth(80);
                partialRefundBtn.setPrefWidth(90);
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    Transaction transaction = getTableView().getItems().get(getIndex());
                    boolean canRefund = transaction.canBeRefunded();
                    fullRefundBtn.setDisable(!canRefund);
                    partialRefundBtn.setDisable(!canRefund);

                    // Create modern button layout with proper spacing
                    javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(3);
                    buttonBox.setAlignment(javafx.geometry.Pos.CENTER);
                    buttonBox.getChildren().addAll(viewBtn, receiptBtn, fullRefundBtn, partialRefundBtn);
                    setGraphic(buttonBox);
                }
            }
        });

        tblTransactions.setItems(filteredTransactions);
    }

    private void setDefaultDates() {
        LocalDate today = LocalDate.now();
        dateTo.setValue(today);
        dateFrom.setValue(today.minusDays(30)); // Last 30 days
        timeFrom.setText("00:00");
        timeTo.setText("23:59");
        lblSelectedPeriod.setText("Last 30 Days");
        updateActiveFilterLabel();
    }

    private void setDateTimeRange(LocalDate fromDate, LocalTime fromTime, LocalDate toDate, LocalTime toTime) {
        dateFrom.setValue(fromDate);
        dateTo.setValue(toDate);
        timeFrom.setText(fromTime.format(DateTimeFormatter.ofPattern("HH:mm")));
        timeTo.setText(toTime.format(DateTimeFormatter.ofPattern("HH:mm")));
    }

    private void clearDateTimeFields() {
        dateFrom.setValue(null);
        dateTo.setValue(null);
        timeFrom.setText("00:00");
        timeTo.setText("23:59");
    }

    private void clearAllFilters() {
        cmbQuickFilter.setValue("Custom Range");
        cmbTimeRange.setValue(null);
        clearDateTimeFields();
        cmbStatus.setValue("All");
        cmbPaymentMethod.setValue("All");
        txtSearchTransactionId.clear();
        lblSelectedPeriod.setText("All Time");
        updateActiveFilterLabel();
    }

    private void loadTransactions() {
        // Ensure UI updates happen on JavaFX Application Thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(this::loadTransactions);
            return;
        }

        try {
            List<Transaction> transactions = transactionDAO.findAll();
            allTransactions.setAll(transactions);
            applyFilters();
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load transactions: " + e.getMessage());
        }
    }

    @FXML
    private void handleQuickFilter() {
        String selectedPeriod = cmbQuickFilter.getValue();
        if (selectedPeriod == null) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDate today = LocalDate.now();

        switch (selectedPeriod) {
            case "Today":
                setDateTimeRange(today, LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Yesterday":
                LocalDate yesterday = today.minusDays(1);
                setDateTimeRange(yesterday, LocalTime.MIN, yesterday, LocalTime.MAX);
                break;
            case "This Week":
                LocalDate startOfWeek = today.minusDays(today.getDayOfWeek().getValue() - 1);
                setDateTimeRange(startOfWeek, LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Last Week":
                LocalDate lastWeekStart = today.minusDays(today.getDayOfWeek().getValue() + 6);
                LocalDate lastWeekEnd = lastWeekStart.plusDays(6);
                setDateTimeRange(lastWeekStart, LocalTime.MIN, lastWeekEnd, LocalTime.MAX);
                break;
            case "This Month":
                LocalDate startOfMonth = today.withDayOfMonth(1);
                setDateTimeRange(startOfMonth, LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Last Month":
                LocalDate lastMonthStart = today.minusMonths(1).withDayOfMonth(1);
                LocalDate lastMonthEnd = lastMonthStart.plusMonths(1).minusDays(1);
                setDateTimeRange(lastMonthStart, LocalTime.MIN, lastMonthEnd, LocalTime.MAX);
                break;
            case "Last 7 Days":
                setDateTimeRange(today.minusDays(7), LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Last 30 Days":
                setDateTimeRange(today.minusDays(30), LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Last 90 Days":
                setDateTimeRange(today.minusDays(90), LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "This Year":
                LocalDate startOfYear = today.withDayOfYear(1);
                setDateTimeRange(startOfYear, LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Custom Range":
                // Clear fields for custom input
                clearDateTimeFields();
                return;
        }

        // Clear time range filter when using quick filter
        cmbTimeRange.setValue(null);
        applyFilters();
    }

    @FXML
    private void handleTimeRangeFilter() {
        String selectedRange = cmbTimeRange.getValue();
        if (selectedRange == null) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime;

        switch (selectedRange) {
            case "Last 1 Hour":
                startTime = now.minusHours(1);
                break;
            case "Last 2 Hours":
                startTime = now.minusHours(2);
                break;
            case "Last 6 Hours":
                startTime = now.minusHours(6);
                break;
            case "Last 12 Hours":
                startTime = now.minusHours(12);
                break;
            case "Last 24 Hours":
                startTime = now.minusDays(1);
                break;
            case "Last 48 Hours":
                startTime = now.minusDays(2);
                break;
            default:
                return;
        }

        // Set date and time fields
        dateFrom.setValue(startTime.toLocalDate());
        timeFrom.setText(startTime.format(DateTimeFormatter.ofPattern("HH:mm")));
        dateTo.setValue(now.toLocalDate());
        timeTo.setText(now.format(DateTimeFormatter.ofPattern("HH:mm")));

        // Clear quick filter when using time range
        cmbQuickFilter.setValue("Custom Range");
        applyFilters();
    }

    @FXML
    private void handleApplyFilter() {
        applyFilters();
    }

    @FXML
    private void handleClearFilter() {
        clearAllFilters();
        applyFilters();
    }

    @FXML
    private void handleRefresh() {
        loadTransactions();
    }

    @FXML
    private void handleExport() {
        if (filteredTransactions.isEmpty()) {
            AlertUtil.showWarning("No Data", "No transactions to export.");
            return;
        }

        exportTransactionsToCSV();
    }

    @FXML
    private void handleViewDetails() {
        Transaction selected = tblTransactions.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleViewDetails(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a transaction to view details.");
        }
    }

    @FXML
    private void handlePrintReceipt() {
        Transaction selected = tblTransactions.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handlePrintReceipt(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a transaction to print receipt.");
        }
    }

    @FXML
    private void handleRefund() {
        Transaction selected = tblTransactions.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleRefund(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a transaction to refund.");
        }
    }

    private void handleViewDetails(Transaction transaction) {
        try {
            // Try both absolute and relative resource paths for FXML
            URL fxmlUrl = getClass().getResource("/fxml/TransactionDetailDialog.fxml");
            if (fxmlUrl == null) {
                fxmlUrl = getClass().getResource("TransactionDetailDialog.fxml");
            }
            if (fxmlUrl == null) {
                fxmlUrl = getClass().getClassLoader().getResource("fxml/TransactionDetailDialog.fxml");
            }
            if (fxmlUrl == null) {
                AlertUtil.showError("Error", "FXML file not found: /fxml/TransactionDetailDialog.fxml\n"
                        + "Please ensure the file exists in src/main/resources/fxml/ and is included in your build output.");
                return;
            }
            FXMLLoader loader = new FXMLLoader(fxmlUrl);
            VBox page = loader.load();

            Stage dialogStage = new Stage();
            dialogStage.setTitle("Transaction Details");
            dialogStage.initModality(Modality.WINDOW_MODAL);
            dialogStage.initOwner(tblTransactions.getScene().getWindow());
            Scene scene = new Scene(page);
            dialogStage.setScene(scene);

            TransactionDetailDialogController controller = loader.getController();
            controller.setTransaction(transaction);

            dialogStage.showAndWait();
        } catch (Exception e) {
            // Show the full stack trace in the error dialog for debugging
            java.io.StringWriter sw = new java.io.StringWriter();
            e.printStackTrace(new java.io.PrintWriter(sw));
            AlertUtil.showError("Error", "Could not open transaction details view.\n" + sw.toString());
        }
    }

    private void handlePrintReceipt(Transaction transaction) {
        String receiptContent = generateReceiptContent(transaction);

        Alert receiptAlert = new Alert(Alert.AlertType.INFORMATION);
        receiptAlert.setTitle("Transaction Receipt");
        receiptAlert.setHeaderText("Receipt for Transaction: " + transaction.getTransactionNumber());

        TextArea textArea = new TextArea(receiptContent);
        textArea.setEditable(false);
        textArea.setWrapText(true);

        textArea.setPrefSize(400, 400);

        receiptAlert.getDialogPane().setContent(textArea);

        ButtonType saveButton = new ButtonType("Save to File");
        receiptAlert.getButtonTypes().add(saveButton);

        receiptAlert.showAndWait().ifPresent(response -> {
            if (response == saveButton) {
                saveReceiptToFile(receiptContent, transaction.getTransactionNumber());
            }
        });
    }

    private String generateReceiptContent(Transaction transaction) {
        StringBuilder sb = new StringBuilder();
        NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");

        sb.append("      CLOTHING STORE RECEIPT\n");
        sb.append("----------------------------------------\n");
        sb.append("Transaction #: ").append(transaction.getTransactionNumber()).append("\n");
        sb.append("Date: ").append(transaction.getTransactionDate().format(formatter)).append("\n");
        sb.append("Customer: ").append(transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in").append("\n");
        sb.append("Status: ").append(transaction.getStatus()).append("\n");
        sb.append("----------------------------------------\n\n");
        sb.append(String.format("%-20s %5s %10s %10s\n", "Item", "Qty", "Price", "Total"));
        sb.append("------------------------------------------------\n");

        for (TransactionItem item : transaction.getItems()) {
            sb.append(String.format("%-20.20s %5d %10s %10s\n",
                    item.getProductName(),
                    item.getQuantity(),
                    currencyFormat.format(item.getUnitPrice()),
                    currencyFormat.format(item.getLineTotal())
            ));
        }

        sb.append("------------------------------------------------\n");
        sb.append(String.format("%37s %10s\n", "Subtotal:", currencyFormat.format(transaction.getSubtotal())));
        sb.append(String.format("%37s %10s\n", "Discount:", currencyFormat.format(transaction.getDiscount())));
        sb.append(String.format("%37s %10s\n", "Total:", currencyFormat.format(transaction.getTotal())));
        sb.append("\n----------------------------------------\n");
        sb.append("           THANK YOU!\n");

        return sb.toString();
    }

    private void saveReceiptToFile(String content, String transactionNumber) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Save Receipt");
        fileChooser.setInitialFileName("Receipt-" + transactionNumber + ".txt");
        fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("Text Files", "*.txt"));

        File file = fileChooser.showSaveDialog(tblTransactions.getScene().getWindow());

        if (file != null) {
            try (PrintWriter writer = new PrintWriter(file)) {
                writer.println(content);
                AlertUtil.showInfo("Success", "Receipt saved successfully to " + file.getAbsolutePath());
            } catch (IOException e) {
                AlertUtil.showError("Error", "Failed to save receipt file: " + e.getMessage());
            }
        }
    }

    private void handleRefund(Transaction transaction) {
        // Legacy method - redirect to full refund for backward compatibility
        handleFullRefund(transaction);
    }

    /**
     * Handle full refund request
     */
    private void handleFullRefund(Transaction transaction) {
        if (transaction == null) {
            return;
        }

        if (!transaction.canBeRefunded()) {
            AlertUtil.showWarning("Cannot Refund",
                    "This transaction cannot be refunded.\n"
                    + "Status: " + transaction.getStatus() + "\n"
                    + "Only COMPLETED transactions can be refunded.");
            return;
        }

        // Show confirmation dialog for full refund
        showFullRefundConfirmationDialog(transaction);
    }

    /**
     * Handle partial refund request
     */
    private void handlePartialRefund(Transaction transaction) {
        if (transaction == null) {
            return;
        }

        if (!transaction.canBeRefunded()) {
            AlertUtil.showWarning("Cannot Refund",
                    "This transaction cannot be refunded.\n"
                    + "Status: " + transaction.getStatus() + "\n"
                    + "Only COMPLETED or PARTIALLY_REFUNDED transactions can be partially refunded.");
            return;
        }

        // Show partial refund dialog with item selection
        showPartialRefundDialog(transaction);
    }

    private void showRefundDialog(Transaction transaction) {
        try {
            System.out.println("=== REFUND DIALOG DEBUG ===");
            System.out.println("Transaction: " + transaction.getTransactionNumber());
            System.out.println("Status: " + transaction.getStatus());
            System.out.println("Can be refunded: " + transaction.canBeRefunded());

            // Check if FXML resource exists
            java.net.URL fxmlUrl = getClass().getResource("/fxml/RefundDialog_Simple.fxml");
            if (fxmlUrl == null) {
                System.out.println("ERROR: RefundDialog_Simple.fxml not found in resources");
                AlertUtil.showError("Error", "RefundDialog_Simple.fxml not found in resources");
                return;
            }

            System.out.println("Loading FXML from: " + fxmlUrl);

            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(fxmlUrl);
            javafx.scene.Parent root = loader.load();
            System.out.println("FXML loaded successfully");

            RefundDialogController controller = loader.getController();
            if (controller == null) {
                System.out.println("ERROR: RefundDialogController not found or not properly initialized");
                AlertUtil.showError("Error", "RefundDialogController not found or not properly initialized");
                return;
            }

            System.out.println("Controller loaded successfully");
            controller.setTransaction(transaction);

            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Process Refund - " + transaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(tblTransactions.getScene().getWindow());
            stage.setResizable(true);
            stage.setMinWidth(800);
            stage.setMinHeight(600);

            stage.showAndWait();

            // Refresh the transaction list if refund was processed
            if (controller.isRefundProcessed()) {
                loadTransactions();
                AlertUtil.showInfo("Refund Complete",
                        "The refund has been processed successfully.\n"
                        + "Transaction status has been updated and inventory has been restored.");
            }

        } catch (Exception e) {
            e.printStackTrace(); // Print full stack trace for debugging
            System.err.println("RefundDialog failed: " + e.getMessage());
            AlertUtil.showError("Error", "Failed to show refund dialog: " + e.getMessage());
        }
    }

    /**
     * Show full refund confirmation dialog
     */
    private void showFullRefundConfirmationDialog(Transaction transaction) {
        try {
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.CONFIRMATION);
            alert.setTitle("Process Full Refund");
            alert.setHeaderText("Full Refund Confirmation");
            alert.setContentText("Are you sure you want to process a FULL REFUND for this transaction?\n\n"
                    + "Transaction: " + transaction.getTransactionNumber() + "\n"
                    + "Total Amount: " + currencyFormat.format(transaction.getTotalAmount()) + "\n"
                    + "Items: " + transaction.getItems().size() + "\n"
                    + "Date: " + transaction.getTransactionDate().format(dateFormatter) + "\n\n"
                    + "This action will:\n"
                    + "• Refund the full transaction amount\n"
                    + "• Restore all items to inventory\n"
                    + "• Mark transaction as REFUNDED\n"
                    + "• Cannot be undone");

            javafx.scene.control.ButtonType confirmButton = new javafx.scene.control.ButtonType("Process Full Refund");
            javafx.scene.control.ButtonType cancelButton = new javafx.scene.control.ButtonType("Cancel", javafx.scene.control.ButtonBar.ButtonData.CANCEL_CLOSE);

            alert.getButtonTypes().setAll(confirmButton, cancelButton);

            java.util.Optional<javafx.scene.control.ButtonType> result = alert.showAndWait();
            if (result.isPresent() && result.get() == confirmButton) {
                processFullRefund(transaction);
            }

        } catch (Exception e) {
            System.err.println("ERROR: Failed to show full refund confirmation dialog: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Error", "Failed to show refund confirmation dialog: " + e.getMessage());
        }
    }

    /**
     * Show partial refund dialog with item selection
     */
    private void showPartialRefundDialog(Transaction transaction) {
        try {
            System.out.println("=== PARTIAL REFUND DIALOG DEBUG ===");
            System.out.println("Transaction: " + transaction.getTransactionNumber());
            System.out.println("Status: " + transaction.getStatus());
            System.out.println("Can be refunded: " + transaction.canBeRefunded());

            // Create programmatic refund dialog with product-level interface
            createProgrammaticRefundDialog(transaction);

        } catch (Exception e) {
            System.err.println("ERROR: Failed to show partial refund dialog: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Error", "Failed to show partial refund dialog: " + e.getMessage());
        }
    }

    private void createProgrammaticRefundDialog(Transaction transaction) {
        // Create the main stage
        javafx.stage.Stage stage = new javafx.stage.Stage();
        stage.setTitle("Process Refund - " + transaction.getTransactionNumber());
        stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
        stage.initOwner(tblTransactions.getScene().getWindow());
        stage.setResizable(true);
        stage.setMinWidth(900);
        stage.setMinHeight(700);

        // Create main layout
        javafx.scene.layout.VBox mainLayout = new javafx.scene.layout.VBox(10);
        mainLayout.setPadding(new javafx.geometry.Insets(20));
        mainLayout.setStyle("-fx-background-color: #f8f9fa;");

        // Transaction header section
        javafx.scene.layout.VBox headerSection = createTransactionHeaderSection(transaction);

        // Product selection table section
        javafx.scene.layout.VBox tableSection = createProductTableSection(transaction);

        // Refund calculation section
        javafx.scene.layout.HBox calculationSection = createCalculationSection();

        // Reason and action section
        javafx.scene.layout.VBox actionSection = createActionSection(stage, transaction);

        mainLayout.getChildren().addAll(headerSection, tableSection, calculationSection, actionSection);

        // Create scene and show
        javafx.scene.Scene scene = new javafx.scene.Scene(mainLayout);
        stage.setScene(scene);
        stage.showAndWait();
    }

    // Fields for the programmatic refund dialog
    private javafx.collections.ObservableList<RefundItem> refundItems;
    private javafx.scene.control.Label lblTotalRefundAmount;
    private javafx.scene.control.TextArea txtRefundReason;
    private javafx.scene.control.TableView<RefundItem> refundTable;

    private javafx.scene.layout.VBox createTransactionHeaderSection(Transaction transaction) {
        javafx.scene.layout.VBox headerSection = new javafx.scene.layout.VBox(8);
        headerSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-background-radius: 8; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);");

        // Title
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Process Refund");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Transaction details grid
        javafx.scene.layout.GridPane detailsGrid = new javafx.scene.layout.GridPane();
        detailsGrid.setHgap(20);
        detailsGrid.setVgap(8);

        // Transaction Number
        javafx.scene.control.Label lblTxnLabel = new javafx.scene.control.Label("Transaction:");
        lblTxnLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #34495e;");
        javafx.scene.control.Label lblTxnValue = new javafx.scene.control.Label(transaction.getTransactionNumber());
        lblTxnValue.setStyle("-fx-text-fill: #2c3e50;");

        // Date
        javafx.scene.control.Label lblDateLabel = new javafx.scene.control.Label("Date:");
        lblDateLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #34495e;");
        javafx.scene.control.Label lblDateValue = new javafx.scene.control.Label(
            transaction.getTransactionDate().format(dateFormatter));
        lblDateValue.setStyle("-fx-text-fill: #2c3e50;");

        // Customer
        javafx.scene.control.Label lblCustomerLabel = new javafx.scene.control.Label("Customer:");
        lblCustomerLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #34495e;");
        javafx.scene.control.Label lblCustomerValue = new javafx.scene.control.Label(
            transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in Customer");
        lblCustomerValue.setStyle("-fx-text-fill: #2c3e50;");

        // Original Total
        javafx.scene.control.Label lblTotalLabel = new javafx.scene.control.Label("Original Total:");
        lblTotalLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #34495e;");
        javafx.scene.control.Label lblTotalValue = new javafx.scene.control.Label(
            currencyFormat.format(transaction.getTotalAmount()));
        lblTotalValue.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");

        detailsGrid.add(lblTxnLabel, 0, 0);
        detailsGrid.add(lblTxnValue, 1, 0);
        detailsGrid.add(lblDateLabel, 2, 0);
        detailsGrid.add(lblDateValue, 3, 0);
        detailsGrid.add(lblCustomerLabel, 0, 1);
        detailsGrid.add(lblCustomerValue, 1, 1);
        detailsGrid.add(lblTotalLabel, 2, 1);
        detailsGrid.add(lblTotalValue, 3, 1);

        headerSection.getChildren().addAll(titleLabel, detailsGrid);
        return headerSection;
    }

    private javafx.scene.layout.VBox createProductTableSection(Transaction transaction) {
        javafx.scene.layout.VBox tableSection = new javafx.scene.layout.VBox(10);
        tableSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-background-radius: 8; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);");

        // Section title
        javafx.scene.control.Label sectionTitle = new javafx.scene.control.Label("Select Items to Refund");
        sectionTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Create table and store reference
        refundTable = new javafx.scene.control.TableView<>();
        refundTable.setPrefHeight(300);
        refundTable.setStyle("-fx-background-color: #f8f9fa;");

        // Initialize refund items
        refundItems = javafx.collections.FXCollections.observableArrayList();
        for (TransactionItem item : transaction.getItems()) {
            refundItems.add(new RefundItem(item));
        }
        refundTable.setItems(refundItems);

        // Create columns
        // Select column with checkboxes
        javafx.scene.control.TableColumn<RefundItem, Boolean> colSelect = new javafx.scene.control.TableColumn<>("Select");
        colSelect.setPrefWidth(60);
        colSelect.setCellValueFactory(cellData -> {
            RefundItem item = cellData.getValue();
            javafx.beans.property.SimpleBooleanProperty property = new javafx.beans.property.SimpleBooleanProperty(item.isSelected());
            property.addListener((obs, oldVal, newVal) -> {
                item.setSelected(newVal);
                updateRefundCalculation();
            });
            return property;
        });
        colSelect.setCellFactory(col -> {
            javafx.scene.control.TableCell<RefundItem, Boolean> cell = new javafx.scene.control.TableCell<RefundItem, Boolean>() {
                private final javafx.scene.control.CheckBox checkBox = new javafx.scene.control.CheckBox();

                @Override
                protected void updateItem(Boolean item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty) {
                        setGraphic(null);
                    } else {
                        RefundItem refundItem = getTableView().getItems().get(getIndex());
                        checkBox.setSelected(refundItem.isSelected());
                        checkBox.setOnAction(e -> {
                            refundItem.setSelected(checkBox.isSelected());
                            updateRefundCalculation();
                        });
                        setGraphic(checkBox);
                    }
                }
            };
            return cell;
        });

        // Product name column
        javafx.scene.control.TableColumn<RefundItem, String> colProduct = new javafx.scene.control.TableColumn<>("Product");
        colProduct.setPrefWidth(200);
        colProduct.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getOriginalItem().getProduct().getName()));

        // SKU column
        javafx.scene.control.TableColumn<RefundItem, String> colSku = new javafx.scene.control.TableColumn<>("SKU");
        colSku.setPrefWidth(120);
        colSku.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getOriginalItem().getProduct().getSku()));

        // Original quantity column
        javafx.scene.control.TableColumn<RefundItem, String> colOriginalQty = new javafx.scene.control.TableColumn<>("Original Qty");
        colOriginalQty.setPrefWidth(90);
        colOriginalQty.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(String.valueOf(cellData.getValue().getOriginalQuantity())));

        // Refund quantity column with Spinner controls
        javafx.scene.control.TableColumn<RefundItem, Integer> colRefundQty = new javafx.scene.control.TableColumn<>("Refund Qty");
        colRefundQty.setPrefWidth(100);
        colRefundQty.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleIntegerProperty(cellData.getValue().getRefundQuantity()).asObject());
        colRefundQty.setCellFactory(col -> {
            javafx.scene.control.TableCell<RefundItem, Integer> cell = new javafx.scene.control.TableCell<RefundItem, Integer>() {
                private final javafx.scene.control.Spinner<Integer> spinner = new javafx.scene.control.Spinner<>();

                @Override
                protected void updateItem(Integer quantity, boolean empty) {
                    super.updateItem(quantity, empty);
                    if (empty) {
                        setGraphic(null);
                    } else {
                        RefundItem refundItem = getTableView().getItems().get(getIndex());

                        // Configure spinner
                        spinner.setValueFactory(new javafx.scene.control.SpinnerValueFactory.IntegerSpinnerValueFactory(
                            0, refundItem.getOriginalQuantity(), refundItem.getRefundQuantity()));
                        spinner.setPrefWidth(80);
                        spinner.setEditable(true);

                        // Add listener for quantity changes
                        spinner.valueProperty().addListener((obs, oldVal, newVal) -> {
                            if (newVal != null && newVal >= 0 && newVal <= refundItem.getOriginalQuantity()) {
                                refundItem.setRefundQuantity(newVal);
                                // Auto-select item if quantity > 0, deselect if quantity = 0
                                refundItem.setSelected(newVal > 0);
                                updateRefundCalculation();
                                // Refresh the table to update checkbox state
                                getTableView().refresh();
                            }
                        });

                        setGraphic(spinner);
                    }
                }
            };
            return cell;
        });

        // Unit price column
        javafx.scene.control.TableColumn<RefundItem, String> colUnitPrice = new javafx.scene.control.TableColumn<>("Unit Price");
        colUnitPrice.setPrefWidth(90);
        colUnitPrice.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice())));

        // Refund amount column
        javafx.scene.control.TableColumn<RefundItem, String> colRefundAmount = new javafx.scene.control.TableColumn<>("Refund Amount");
        colRefundAmount.setPrefWidth(110);
        colRefundAmount.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(currencyFormat.format(cellData.getValue().getRefundAmount())));

        refundTable.getColumns().addAll(colSelect, colProduct, colSku, colOriginalQty, colRefundQty, colUnitPrice, colRefundAmount);

        // Add control buttons for quantity management
        javafx.scene.layout.HBox controlButtons = new javafx.scene.layout.HBox(10);
        controlButtons.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        controlButtons.setPadding(new javafx.geometry.Insets(10, 0, 0, 0));

        javafx.scene.control.Button btnSelectAll = new javafx.scene.control.Button("Select All");
        btnSelectAll.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        btnSelectAll.setOnAction(e -> selectAllItems());

        javafx.scene.control.Button btnSelectNone = new javafx.scene.control.Button("Select None");
        btnSelectNone.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        btnSelectNone.setOnAction(e -> selectNoItems());

        javafx.scene.control.Button btnSetFullQuantities = new javafx.scene.control.Button("Set Full Quantities");
        btnSetFullQuantities.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        btnSetFullQuantities.setOnAction(e -> setFullQuantitiesForSelected());

        controlButtons.getChildren().addAll(btnSelectAll, btnSelectNone, btnSetFullQuantities);

        tableSection.getChildren().addAll(sectionTitle, refundTable, controlButtons);
        return tableSection;
    }

    private void selectAllItems() {
        if (refundItems != null) {
            for (RefundItem item : refundItems) {
                item.setSelected(true);
                if (item.getRefundQuantity() == 0) {
                    item.setRefundQuantity(item.getOriginalQuantity());
                }
            }
            updateRefundCalculation();
            if (refundTable != null) {
                refundTable.refresh();
            }
        }
    }

    private void selectNoItems() {
        if (refundItems != null) {
            for (RefundItem item : refundItems) {
                item.setSelected(false);
                item.setRefundQuantity(0);
            }
            updateRefundCalculation();
            if (refundTable != null) {
                refundTable.refresh();
            }
        }
    }

    private void setFullQuantitiesForSelected() {
        if (refundItems != null) {
            for (RefundItem item : refundItems) {
                if (item.isSelected()) {
                    item.setRefundQuantity(item.getOriginalQuantity());
                }
            }
            updateRefundCalculation();
            if (refundTable != null) {
                refundTable.refresh();
            }
        }
    }

    private javafx.scene.layout.HBox createCalculationSection() {
        javafx.scene.layout.HBox calculationSection = new javafx.scene.layout.HBox(20);
        calculationSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-background-radius: 8; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);");
        calculationSection.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("Total Refund Amount:");
        totalLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        lblTotalRefundAmount = new javafx.scene.control.Label("$0.00");
        lblTotalRefundAmount.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #e74c3c; -fx-background-color: #ffeaea; -fx-padding: 8 12; -fx-background-radius: 4;");

        calculationSection.getChildren().addAll(totalLabel, lblTotalRefundAmount);
        return calculationSection;
    }

    private javafx.scene.layout.VBox createActionSection(javafx.stage.Stage stage, Transaction transaction) {
        javafx.scene.layout.VBox actionSection = new javafx.scene.layout.VBox(15);
        actionSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-background-radius: 8; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);");

        // Reason section
        javafx.scene.control.Label reasonLabel = new javafx.scene.control.Label("Refund Reason:");
        reasonLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        txtRefundReason = new javafx.scene.control.TextArea();
        txtRefundReason.setPrefRowCount(3);
        txtRefundReason.setPromptText("Enter reason for refund...");
        txtRefundReason.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-radius: 4;");

        // Button section
        javafx.scene.layout.HBox buttonSection = new javafx.scene.layout.HBox(10);
        buttonSection.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        javafx.scene.control.Button btnCancel = new javafx.scene.control.Button("Cancel");
        btnCancel.setPrefWidth(100);
        btnCancel.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        btnCancel.setOnAction(e -> stage.close());

        javafx.scene.control.Button btnProcessRefund = new javafx.scene.control.Button("Process Refund");
        btnProcessRefund.setPrefWidth(120);
        btnProcessRefund.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        btnProcessRefund.setOnAction(e -> processRefundFromDialog(stage, transaction));

        buttonSection.getChildren().addAll(btnCancel, btnProcessRefund);

        actionSection.getChildren().addAll(reasonLabel, txtRefundReason, buttonSection);
        return actionSection;
    }

    private void updateRefundCalculation() {
        if (refundItems == null) return;

        BigDecimal totalRefund = refundItems.stream()
            .filter(RefundItem::isSelected)
            .map(RefundItem::getRefundAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (lblTotalRefundAmount != null) {
            lblTotalRefundAmount.setText(currencyFormat.format(totalRefund));
        }
    }

    private void processRefundFromDialog(javafx.stage.Stage stage, Transaction transaction) {
        String reason = txtRefundReason.getText().trim();
        if (reason.isEmpty()) {
            AlertUtil.showWarning("Missing Information", "Please provide a reason for the refund.");
            return;
        }

        java.util.List<RefundItem> selectedItems = refundItems.stream()
            .filter(RefundItem::isSelected)
            .collect(java.util.stream.Collectors.toList());

        if (selectedItems.isEmpty()) {
            AlertUtil.showWarning("No Items Selected", "Please select at least one item to refund.");
            return;
        }

        // Validate refund quantities
        for (RefundItem item : selectedItems) {
            if (item.getRefundQuantity() <= 0) {
                AlertUtil.showWarning("Invalid Quantity",
                    "Please specify a valid refund quantity for: " + item.getProductName());
                return;
            }
            if (item.getRefundQuantity() > item.getOriginalQuantity()) {
                AlertUtil.showWarning("Invalid Quantity",
                    "Cannot refund more than the original quantity for: " + item.getProductName() +
                    "\nOriginal: " + item.getOriginalQuantity() + ", Requested: " + item.getRefundQuantity());
                return;
            }
        }

        // Calculate total refund amount for confirmation
        BigDecimal totalRefundAmount = selectedItems.stream()
            .map(RefundItem::getRefundAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Show confirmation dialog with details
        String confirmationMessage = String.format(
            "Confirm Refund Details:\n\n" +
            "Transaction: %s\n" +
            "Items to refund: %d\n" +
            "Total refund amount: %s\n" +
            "Reason: %s\n\n" +
            "This will:\n" +
            "• Process partial refund for selected quantities\n" +
            "• Restore inventory for refunded items\n" +
            "• Update transaction status\n" +
            "• Create audit trail\n\n" +
            "Do you want to proceed?",
            transaction.getTransactionNumber(),
            selectedItems.size(),
            currencyFormat.format(totalRefundAmount),
            reason
        );

        if (!AlertUtil.showConfirmation("Confirm Refund", confirmationMessage)) {
            return;
        }

        // Process the refund
        try {
            RefundService refundService = RefundService.getInstance();

            // Set the reason for each refund item for proper audit trail
            for (RefundItem item : selectedItems) {
                item.setReason(reason);
            }

            RefundResult result = refundService.processPartialRefund(transaction, selectedItems, reason, "System User");

            if (result.isSuccess()) {
                String successMessage = String.format(
                    "Refund processed successfully!\n\n" +
                    "Refund amount: %s\n" +
                    "Items refunded: %d\n" +
                    "Inventory restored: Yes\n" +
                    "Transaction status updated: Yes",
                    currencyFormat.format(totalRefundAmount),
                    selectedItems.size()
                );

                AlertUtil.showInfo("Refund Complete", successMessage);
                stage.close();
                loadTransactions(); // Refresh the transaction list
            } else {
                AlertUtil.showError("Refund Failed", result.getMessage());
            }
        } catch (Exception e) {
            AlertUtil.showError("Error", "Failed to process refund: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Process full refund for a transaction
     */
    private void processFullRefund(Transaction transaction) {
        try {
            System.out.println("=== PROCESSING FULL REFUND ===");
            System.out.println("Transaction: " + transaction.getTransactionNumber());

            // Use RefundService to process the full refund
            RefundService refundService = RefundService.getInstance();

            // Create refund items for all transaction items
            java.util.List<com.clothingstore.model.RefundItem> refundItems = new java.util.ArrayList<>();
            for (TransactionItem item : transaction.getItems()) {
                com.clothingstore.model.RefundItem refundItem = new com.clothingstore.model.RefundItem(item);
                // RefundItem constructor automatically sets refundQuantity to full quantity and selected to true
                refundItems.add(refundItem);
            }

            // Process the full refund
            com.clothingstore.model.RefundResult result = refundService.processPartialRefund(transaction, refundItems, "Full refund processed from Transaction History", "System");

            if (result.isSuccess()) {
                System.out.println("Full refund processed successfully");
                loadTransactions(); // Refresh the transaction list
                AlertUtil.showInfo("Full Refund Complete",
                        "The full refund has been processed successfully.\n"
                        + "Transaction: " + transaction.getTransactionNumber() + "\n"
                        + "Amount Refunded: " + currencyFormat.format(transaction.getTotalAmount()) + "\n"
                        + "Inventory has been restored and transaction status updated.");
            } else {
                System.err.println("Full refund processing failed: " + result.getMessage());
                AlertUtil.showError("Refund Failed", "Failed to process the full refund: " + result.getMessage());
            }

        } catch (Exception e) {
            System.err.println("ERROR: Failed to process full refund: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Refund Error", "An error occurred while processing the refund: " + e.getMessage());
        }
    }



    private void applyFilters() {
        // Ensure UI updates happen on JavaFX Application Thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(this::applyFilters);
            return;
        }

        List<Transaction> filtered = allTransactions.stream()
                .filter(this::matchesDateTimeFilter)
                .filter(this::matchesStatusFilter)
                .filter(this::matchesPaymentMethodFilter)
                .filter(this::matchesSearchIdFilter)
                .collect(Collectors.toList());

        filteredTransactions.setAll(filtered);
        updateSummaryStatistics();
        updatePeriodLabel();
        updateActiveFilterLabel();
        updateFilteredCount(filtered.size());
    }

    private void updateActiveFilterLabel() {
        StringBuilder filterText = new StringBuilder("Filter: ");

        String quickFilter = cmbQuickFilter.getValue();
        String timeRange = cmbTimeRange.getValue();

        if (timeRange != null && !timeRange.isEmpty()) {
            filterText.append(timeRange);
        } else if (quickFilter != null && !quickFilter.equals("Custom Range")) {
            filterText.append(quickFilter);
        } else if (dateFrom.getValue() != null || dateTo.getValue() != null) {
            filterText.append("Custom Range");
        } else {
            filterText.append("All Time");
        }

        // Add additional filter indicators
        if (!"All".equals(cmbStatus.getValue())) {
            filterText.append(" | Status: ").append(cmbStatus.getValue());
        }
        if (!"All".equals(cmbPaymentMethod.getValue())) {
            filterText.append(" | Payment: ").append(cmbPaymentMethod.getValue());
        }
        if (!txtSearchTransactionId.getText().trim().isEmpty()) {
            filterText.append(" | Search: ").append(txtSearchTransactionId.getText().trim());
        }

        lblActiveFilter.setText(filterText.toString());
    }

    private void updateFilteredCount(int count) {
        lblFilteredCount.setText("Showing: " + count + " transactions");
    }

    private boolean matchesDateTimeFilter(Transaction transaction) {
        LocalDateTime transactionDateTime = transaction.getTransactionDate();

        // Build start and end date/time for filtering
        LocalDateTime filterStart = null;
        LocalDateTime filterEnd = null;

        if (dateFrom.getValue() != null) {
            LocalTime startTime = parseTimeInput(timeFrom.getText(), LocalTime.MIN);
            filterStart = LocalDateTime.of(dateFrom.getValue(), startTime);
        }

        if (dateTo.getValue() != null) {
            LocalTime endTime = parseTimeInput(timeTo.getText(), LocalTime.MAX);
            filterEnd = LocalDateTime.of(dateTo.getValue(), endTime);
        }

        // Apply date/time filtering
        if (filterStart != null && transactionDateTime.isBefore(filterStart)) {
            return false;
        }

        if (filterEnd != null && transactionDateTime.isAfter(filterEnd)) {
            return false;
        }

        return true;
    }

    private LocalTime parseTimeInput(String timeText, LocalTime defaultTime) {
        if (timeText == null || timeText.trim().isEmpty()) {
            return defaultTime;
        }

        try {
            return LocalTime.parse(timeText.trim(), DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
            return defaultTime;
        }
    }

    private boolean matchesStatusFilter(Transaction transaction) {
        String selectedStatus = cmbStatus.getValue();
        if (selectedStatus == null || "All".equals(selectedStatus)) {
            return true;
        }

        // Handle special case for "Partially Refunded" filter option
        if ("Partially Refunded".equals(selectedStatus)) {
            return "PARTIALLY_REFUNDED".equals(transaction.getStatus());
        }

        // Convert filter option to match database status format
        String dbStatus = selectedStatus.toUpperCase().replace(" ", "_");
        return dbStatus.equals(transaction.getStatus());
    }

    private boolean matchesPaymentMethodFilter(Transaction transaction) {
        String selectedPaymentMethod = cmbPaymentMethod.getValue();
        return selectedPaymentMethod == null || "All".equals(selectedPaymentMethod)
                || selectedPaymentMethod.equals(transaction.getPaymentMethod());
    }

    private boolean matchesSearchIdFilter(Transaction transaction) {
        String searchTerm = txtSearchTransactionId.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return true;
        }
        return transaction.getTransactionNumber().toLowerCase().contains(searchTerm.toLowerCase());
    }

    private void updateSummaryStatistics() {
        // Ensure UI updates happen on JavaFX Application Thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(this::updateSummaryStatistics);
            return;
        }

        if (filteredTransactions.isEmpty()) {
            lblTotalTransactions.setText("Total Transactions: 0");
            lblTotalAmount.setText("Total Amount: $0.00");
            lblAverageTransaction.setText("Average: $0.00");
            return;
        }

        BigDecimal totalAmount = filteredTransactions.stream()
                .map(Transaction::getTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal averageTransaction = totalAmount.divide(
                BigDecimal.valueOf(filteredTransactions.size()), 2, java.math.RoundingMode.HALF_UP
        );

        lblTotalTransactions.setText("Total Transactions: " + filteredTransactions.size());
        lblTotalAmount.setText("Total Amount: " + currencyFormat.format(totalAmount));
        lblAverageTransaction.setText("Average: " + currencyFormat.format(averageTransaction));
    }

    private void updatePeriodLabel() {
        // Ensure UI updates happen on JavaFX Application Thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(this::updatePeriodLabel);
            return;
        }

        if (dateFrom.getValue() == null && dateTo.getValue() == null) {
            lblSelectedPeriod.setText("All Time");
        } else if (dateFrom.getValue() != null && dateTo.getValue() != null) {
            lblSelectedPeriod.setText(dateFrom.getValue().format(dateFormatter) + " - "
                    + dateTo.getValue().format(dateFormatter));
        } else if (dateFrom.getValue() != null) {
            lblSelectedPeriod.setText("From " + dateFrom.getValue().format(dateFormatter));
        } else {
            lblSelectedPeriod.setText("Until " + dateTo.getValue().format(dateFormatter));
        }
    }

    private void exportTransactionsToCSV() {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Export Transaction History");
            fileChooser.getExtensionFilters().add(
                    new FileChooser.ExtensionFilter("CSV Files", "*.csv"));
            fileChooser.setInitialFileName("transaction_history_"
                    + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".csv");

            File file = fileChooser.showSaveDialog(tblTransactions.getScene().getWindow());
            if (file != null) {
                try (PrintWriter writer = new PrintWriter(file)) {
                    // Write CSV header
                    writer.println("Transaction Number,Date,Time,Customer,Items,Subtotal,Discount,Total,Payment Method,Status");

                    // Write transaction data
                    for (Transaction transaction : filteredTransactions) {
                        writer.printf("\"%s\",\"%s\",\"%s\",\"%s\",%d,%.2f,%.2f,%.2f,\"%s\",\"%s\"%n",
                                transaction.getTransactionNumber(),
                                transaction.getTransactionDate().format(dateFormatter),
                                transaction.getTransactionDate().format(timeFormatter),
                                transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in",
                                transaction.getTotalItems(),
                                transaction.getSubtotal(),
                                transaction.getDiscount(),
                                transaction.getTotal(),
                                transaction.getPaymentMethod(),
                                transaction.getStatus()
                        );
                    }

                    AlertUtil.showInfo("Export Successful",
                            "Transaction history exported to: " + file.getAbsolutePath() + "\n"
                            + "Total transactions: " + filteredTransactions.size());
                }
            }
        } catch (Exception e) {
            AlertUtil.showError("Export Failed", "Failed to export transactions: " + e.getMessage());
        }
    }

    /**
     * Test all main button actions in the transaction page. This is for quick
     * manual verification, not a replacement for proper tests.
     */
    public void testAllButtons() {
        // Simulate filter button
        try {
            handleFilter();
            System.out.println("handleFilter() executed.");
        } catch (Exception e) {
            System.err.println("handleFilter() failed: " + e.getMessage());
        }

        // Simulate clear filter button
        try {
            handleClearFilter();
            System.out.println("handleClearFilter() executed.");
        } catch (Exception e) {
            System.err.println("handleClearFilter() failed: " + e.getMessage());
        }

        // Simulate refresh button
        try {
            handleRefresh();
            System.out.println("handleRefresh() executed.");
        } catch (Exception e) {
            System.err.println("handleRefresh() failed: " + e.getMessage());
        }

        // Simulate export button
        try {
            handleExport();
            System.out.println("handleExport() executed.");
        } catch (Exception e) {
            System.err.println("handleExport() failed: " + e.getMessage());
        }

        // Simulate view details, print receipt, refund (if any transaction exists)
        if (!filteredTransactions.isEmpty()) {
            Transaction tx = filteredTransactions.get(0);
            try {
                handleViewDetails(tx);
                System.out.println("handleViewDetails(tx) executed.");
            } catch (Exception e) {
                System.err.println("handleViewDetails(tx) failed: " + e.getMessage());
            }
            try {
                handlePrintReceipt(tx);
                System.out.println("handlePrintReceipt(tx) executed.");
            } catch (Exception e) {
                System.err.println("handlePrintReceipt(tx) failed: " + e.getMessage());
            }
            try {
                handleRefund(tx);
                System.out.println("handleRefund(tx) executed.");
            } catch (Exception e) {
                System.err.println("handleRefund(tx) failed: " + e.getMessage());
            }
        } else {
            System.out.println("No transactions available to test view/receipt/refund.");
        }
    }

    // Legacy method for compatibility
    @FXML
    private void handleFilter() {
        handleApplyFilter();
    }

    /**
     * Handle customer search with real-time filtering
     */
    @FXML
    private void handleCustomerSearch() {
        String searchTerm = txtCustomerSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            clearCustomerFilter();
            return;
        }

        try {
            List<Customer> customers = customerAnalyticsService.searchCustomersEnhanced(searchTerm);
            if (customers.size() == 1) {
                // Exact match - select this customer
                selectCustomer(customers.get(0));
            } else if (customers.size() > 1) {
                // Multiple matches - show first match but don't auto-select
                lblSelectedCustomer.setText("Found " + customers.size() + " customers");
                btnClearCustomerFilter.setVisible(false);
                customerAnalyticsPanel.setVisible(false);
                customerAnalyticsPanel.setManaged(false);
            } else {
                // No matches
                lblSelectedCustomer.setText("No customers found");
                btnClearCustomerFilter.setVisible(false);
                customerAnalyticsPanel.setVisible(false);
                customerAnalyticsPanel.setManaged(false);
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to search customers: " + e.getMessage());
        }
    }

    /**
     * Select a specific customer and show their analytics
     */
    private void selectCustomer(Customer customer) {
        try {
            selectedCustomer = customer;
            currentCustomerAnalytics = customerAnalyticsService.getCustomerAnalytics(customer.getId());

            // Update UI
            lblSelectedCustomer.setText("Selected: " + customer.getFirstName() + " " + customer.getLastName());
            btnClearCustomerFilter.setVisible(true);

            // Show customer analytics panel
            displayCustomerAnalytics(currentCustomerAnalytics);
            customerAnalyticsPanel.setVisible(true);
            customerAnalyticsPanel.setManaged(true);

            // Filter transactions for this customer
            filterTransactionsByCustomer(customer.getId());

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load customer analytics: " + e.getMessage());
        }
    }

    /**
     * Display customer analytics in the panel
     */
    private void displayCustomerAnalytics(CustomerAnalytics analytics) {
        if (analytics == null) {
            return;
        }

        Customer customer = analytics.getCustomer();
        lblCustomerName.setText(customer.getFirstName() + " " + customer.getLastName());
        lblCustomerContact.setText(customer.getPhone());
        lblCustomerTransactions.setText(String.valueOf(analytics.getTotalTransactions()));
        lblCustomerLifetimeValue.setText(analytics.getFormattedTotalSpent());
        lblCustomerAverage.setText(analytics.getFormattedAverageTransaction());
        lblCustomerLastPurchase.setText(analytics.getFormattedLastPurchase());
        lblCustomerPreferredPayment.setText(analytics.getPreferredPaymentMethod());
        lblCustomerCategories.setText(analytics.getTopCategoriesString());
        lblCustomerMembership.setText(analytics.getMembershipLevel());
    }

    /**
     * Filter transactions to show only those for the selected customer
     */
    private void filterTransactionsByCustomer(Long customerId) {
        filteredTransactions.clear();

        for (Transaction transaction : allTransactions) {
            if (transaction.getCustomerId() != null && transaction.getCustomerId().equals(customerId)) {
                filteredTransactions.add(transaction);
            }
        }

        tblTransactions.setItems(filteredTransactions);
        updateSummaryStatistics();
        updateActiveFilterLabel();
        updateFilteredCount(filteredTransactions.size());
    }

    /**
     * Clear customer filter and show all transactions
     */
    @FXML
    private void handleClearCustomerFilter() {
        clearCustomerFilter();
    }

    private void clearCustomerFilter() {
        selectedCustomer = null;
        currentCustomerAnalytics = null;
        txtCustomerSearch.clear();
        lblSelectedCustomer.setText("");
        btnClearCustomerFilter.setVisible(false);
        customerAnalyticsPanel.setVisible(false);
        customerAnalyticsPanel.setManaged(false);

        // Restore all transactions (apply other filters if any)
        applyFilters();
    }

    /**
     * Export customer-specific data
     */
    @FXML
    private void handleExportCustomerData() {
        if (selectedCustomer == null || currentCustomerAnalytics == null) {
            AlertUtil.showWarning("No Customer Selected", "Please select a customer first.");
            return;
        }

        exportCustomerTransactionsToCSV();
    }

    /**
     * Export customer transactions to CSV
     */
    private void exportCustomerTransactionsToCSV() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Export Customer Transactions");
        fileChooser.setInitialFileName(selectedCustomer.getFirstName() + "_" + selectedCustomer.getLastName() + "_transactions.csv");
        fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("CSV Files", "*.csv"));

        File file = fileChooser.showSaveDialog(tblTransactions.getScene().getWindow());
        if (file != null) {
            try (PrintWriter writer = new PrintWriter(file)) {
                // Write customer info header
                writer.println("Customer Transaction Report");
                writer.println("Customer: " + selectedCustomer.getFirstName() + " " + selectedCustomer.getLastName());
                writer.println("Phone: " + selectedCustomer.getPhone());
                writer.println("Total Transactions: " + currentCustomerAnalytics.getTotalTransactions());
                writer.println("Lifetime Value: " + currentCustomerAnalytics.getFormattedTotalSpent());
                writer.println("Average Order: " + currentCustomerAnalytics.getFormattedAverageTransaction());
                writer.println("Generated: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                writer.println();

                // Write CSV header
                writer.println("Transaction Number,Date,Time,Items,Subtotal,Discount,Total,Payment Method,Status");

                // Write transaction data
                for (Transaction transaction : filteredTransactions) {
                    writer.printf("%s,%s,%s,%d,%.2f,%.2f,%.2f,%s,%s%n",
                            transaction.getTransactionNumber(),
                            transaction.getTransactionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                            transaction.getTransactionDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")),
                            transaction.getItems().size(),
                            transaction.getSubtotal().doubleValue(),
                            transaction.getDiscountAmount().doubleValue(),
                            transaction.getTotalAmount().doubleValue(),
                            transaction.getPaymentMethod(),
                            transaction.getStatus()
                    );
                }

                AlertUtil.showInfo("Export Successful", "Customer transactions exported to: " + file.getAbsolutePath());

            } catch (IOException e) {
                AlertUtil.showError("Export Failed", "Failed to export customer transactions: " + e.getMessage());
            }
        }
    }
}
