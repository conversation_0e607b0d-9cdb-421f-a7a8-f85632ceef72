package com.clothingstore.model;

import java.math.BigDecimal;

/**
 * Represents a tier in a bulk discount system
 */
public class BulkDiscountTier {
    private Long id;
    private Long discountId;
    private Integer minimumQuantity;
    private Integer maximumQuantity; // null for unlimited
    private BigDecimal discountPercentage;
    private BigDecimal fixedDiscountAmount;
    private String description;
    
    // Constructors
    public BulkDiscountTier() {}
    
    public BulkDiscountTier(Integer minimumQuantity, BigDecimal discountPercentage) {
        this.minimumQuantity = minimumQuantity;
        this.discountPercentage = discountPercentage;
    }
    
    public BulkDiscountTier(Integer minimumQuantity, Integer maximumQuantity, BigDecimal discountPercentage) {
        this.minimumQuantity = minimumQuantity;
        this.maximumQuantity = maximumQuantity;
        this.discountPercentage = discountPercentage;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getDiscountId() {
        return discountId;
    }
    
    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }
    
    public Integer getMinimumQuantity() {
        return minimumQuantity;
    }
    
    public void setMinimumQuantity(Integer minimumQuantity) {
        this.minimumQuantity = minimumQuantity;
    }
    
    public Integer getMaximumQuantity() {
        return maximumQuantity;
    }
    
    public void setMaximumQuantity(Integer maximumQuantity) {
        this.maximumQuantity = maximumQuantity;
    }
    
    public BigDecimal getDiscountPercentage() {
        return discountPercentage;
    }
    
    public void setDiscountPercentage(BigDecimal discountPercentage) {
        this.discountPercentage = discountPercentage;
    }
    
    public BigDecimal getFixedDiscountAmount() {
        return fixedDiscountAmount;
    }
    
    public void setFixedDiscountAmount(BigDecimal fixedDiscountAmount) {
        this.fixedDiscountAmount = fixedDiscountAmount;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    // Utility methods
    public boolean appliesTo(int quantity) {
        if (quantity < minimumQuantity) {
            return false;
        }
        
        if (maximumQuantity != null && quantity > maximumQuantity) {
            return false;
        }
        
        return true;
    }
    
    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        sb.append("Buy ").append(minimumQuantity);
        
        if (maximumQuantity != null) {
            sb.append("-").append(maximumQuantity);
        } else {
            sb.append("+");
        }
        
        sb.append(" items, get ");
        
        if (discountPercentage != null) {
            sb.append(discountPercentage).append("% off");
        } else if (fixedDiscountAmount != null) {
            sb.append("$").append(fixedDiscountAmount).append(" off");
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return String.format("BulkDiscountTier{id=%d, minQty=%d, maxQty=%s, discount=%s}", 
                           id, minimumQuantity, maximumQuantity, 
                           discountPercentage != null ? discountPercentage + "%" : "$" + fixedDiscountAmount);
    }
}
