<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.PaymentDialogController" spacing="15.0">
   <padding>
      <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
   </padding>
   <children>
      <Label text="Process Outstanding Payment" />
      
      <VBox spacing="10.0">
         <children>
            <Label text="Transaction Summary" />
            <Label fx:id="lblTransactionNumber" text="Transaction: Loading..." />
            <Label fx:id="lblSubtotal" text="Subtotal: $0.00" />
            <Label fx:id="lblDiscount" text="Discount: $0.00" />
            <Label fx:id="lblTax" text="Tax: $0.00" />
            <Label fx:id="lblTotalAmount" text="Total Amount: $0.00" />
            <Label fx:id="lblAmountPaid" text="Amount Paid: $0.00" />
            <Label fx:id="lblRemainingBalance" text="Remaining Balance: $0.00" />
         </children>
      </VBox>

      <VBox spacing="15.0">
         <children>
            <Label text="Make Payment" />
            <Label text="Payment Method:" />
            <ComboBox fx:id="cmbPaymentMethod" />
            <Label text="Payment Amount:" />
            <TextField fx:id="txtPaymentAmount" promptText="Enter amount to pay" />
            <Button fx:id="btnFullPayment" onAction="#handleFullPayment" text="Pay Full Balance" />
            <Label fx:id="lblValidationMessage" text="" />
            <VBox fx:id="vboxChangeInfo" visible="false">
               <children>
                  <Label text="Change Information" />
                  <Label fx:id="lblChangeLabel" text="Change:" />
                  <Label fx:id="lblChange" text="$0.00" />
               </children>
            </VBox>
            <RadioButton fx:id="rbFullPayment" text="Full Payment" visible="false" />
            <RadioButton fx:id="rbPartialPayment" text="Partial Payment" visible="false" />
         </children>
      </VBox>

      <VBox spacing="8.0">
         <children>
            <Label text="Payment Notes (Optional):" />
            <TextArea fx:id="txtNotes" prefRowCount="2" promptText="Add any notes about this payment..." />
         </children>
      </VBox>

      <HBox spacing="15.0">
         <children>
            <Button fx:id="btnCancel" onAction="#handleCancel" text="Cancel" />
            <Button fx:id="btnProcessPayment" onAction="#handleProcessPayment" text="Process Payment" />
         </children>
      </HBox>
   </children>
</VBox>
