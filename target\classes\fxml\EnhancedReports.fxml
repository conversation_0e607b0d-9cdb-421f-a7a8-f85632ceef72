<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox fx:id="mainContainer" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.ReportsController">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 20; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;">
         <children>
            <Label text="Enhanced Profit Analysis Reports" textFill="#2c3e50">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label text="Advanced Business Analytics" textFill="#6c757d">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
      </HBox>

      <!-- Controls Section -->
      <VBox spacing="20.0" style="-fx-padding: 20;">
         <children>
            <!-- Date Range Selection with Presets -->
            <VBox spacing="15.0" style="-fx-background-color: white; -fx-padding: 20; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;">
               <children>
                  <Label text="Date Range Selection" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <!-- Preset Buttons -->
                  <HBox fx:id="presetButtonsContainer" alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="Quick Select:" textFill="#495057">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Label>
                     </children>
                  </HBox>
                  
                  <!-- Custom Date Range -->
                  <HBox alignment="CENTER_LEFT" spacing="15.0">
                     <children>
                        <Label text="Custom Range:" textFill="#495057">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Label>
                        <Label text="From:" textFill="#6c757d">
                           <font>
                              <Font size="12.0" />
                           </font>
                        </Label>
                        <DatePicker fx:id="startDatePicker" prefWidth="140.0" />
                        <Label text="To:" textFill="#6c757d">
                           <font>
                              <Font size="12.0" />
                           </font>
                        </Label>
                        <DatePicker fx:id="endDatePicker" prefWidth="140.0" />
                        <Button fx:id="generateReportButton" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 4px;" text="Generate Report" />
                        <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
                     </children>
                  </HBox>
                  
                  <!-- Export Options -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="Export Options:" textFill="#495057">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Label>
                        <Button fx:id="exportCSVButton" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 6px 12px; -fx-background-radius: 3px;" text="Export CSV" />
                        <Button fx:id="exportPDFButton" style="-fx-background-color: #dc3545; -fx-text-fill: white; -fx-padding: 6px 12px; -fx-background-radius: 3px;" text="Export PDF" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Status Label -->
            <Label fx:id="statusLabel" style="-fx-font-size: 12px; -fx-text-fill: #6c757d;" text="Select date range and click Generate Report" />

            <!-- Tabbed Reports Interface -->
            <TabPane fx:id="reportsTabPane" tabClosingPolicy="UNAVAILABLE">
               <tabs>
                  <Tab fx:id="overviewTab" text="Overview">
                     <content>
                        <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;">
                           <content>
                              <VBox fx:id="metricsContainer" spacing="20.0" style="-fx-padding: 20;" />
                           </content>
                        </ScrollPane>
                     </content>
                  </Tab>
                  <Tab fx:id="categoryTab" text="Category Analysis">
                     <content>
                        <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;">
                           <content>
                              <VBox fx:id="categoryAnalysisContainer" spacing="15.0" style="-fx-padding: 20;" />
                           </content>
                        </ScrollPane>
                     </content>
                  </Tab>
                  <Tab fx:id="comparisonTab" text="Period Comparison">
                     <content>
                        <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;">
                           <content>
                              <VBox fx:id="comparisonContainer" spacing="15.0" style="-fx-padding: 20;" />
                           </content>
                        </ScrollPane>
                     </content>
                  </Tab>
               </tabs>
            </TabPane>
         </children>
      </VBox>
   </children>
</VBox>
