package com.clothingstore.model;

import java.util.List;
import java.util.Map;

/**
 * Class representing a section within a report
 */
public class ReportSection {
    private String title;
    private String content;
    private List<String> headers;
    private List<List<String>> data;
    private Map<String, Object> metadata;
    
    public ReportSection(String title) {
        this.title = title;
    }
    
    public ReportSection(String title, String content) {
        this.title = title;
        this.content = content;
    }
    
    // Getters and Setters
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public List<String> getHeaders() { return headers; }
    public void setHeaders(List<String> headers) { this.headers = headers; }
    
    public List<List<String>> getData() { return data; }
    public void setData(List<List<String>> data) { this.data = data; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    public boolean hasTableData() {
        return headers != null && !headers.isEmpty() && data != null && !data.isEmpty();
    }
}
