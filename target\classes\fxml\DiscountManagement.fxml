<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.DiscountManagementController">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="header-section">
         <children>
            <Label styleClass="page-title" text="Discount Management" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="discountCountLabel" styleClass="info-label" text="Loading discounts..." />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <!-- Search and Filter Section -->
      <VBox spacing="15.0" styleClass="filter-section">
         <children>
            <!-- Search Row -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="🔍 Search:" />
                  <TextField fx:id="searchField" prefWidth="250.0" promptText="Search by name, description, or promo code..." styleClass="search-box" />
                  <Region HBox.hgrow="SOMETIMES" />
                  <Button fx:id="clearFiltersButton" onAction="#handleClearFilters" styleClass="clear-filters-button" text="✨ Clear Filters" />
               </children>
            </HBox>
            
            <!-- Filter Controls Row -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="📊 Status:" />
                  <ComboBox fx:id="statusFilter" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="🏷️ Type:" />
                  <ComboBox fx:id="typeFilter" prefWidth="150.0" styleClass="combo-box" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Label fx:id="selectionInfoLabel" styleClass="selection-info" text="Select a discount for details" />
               </children>
            </HBox>
         </children>
      </VBox>

      <!-- Main Content Area -->
      <SplitPane dividerPositions="0.6" VBox.vgrow="ALWAYS">
         <items>
            <!-- Left Side - Discount Table -->
            <VBox spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="Discounts" />
                  
                  <!-- Discount Table -->
                  <TableView fx:id="discountTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="nameColumn" prefWidth="150.0" text="Name" />
                        <TableColumn fx:id="typeColumn" prefWidth="120.0" text="Type" />
                        <TableColumn fx:id="valueColumn" prefWidth="80.0" text="Value" />
                        <TableColumn fx:id="statusColumn" prefWidth="90.0" text="Status" />
                        <TableColumn fx:id="validityColumn" prefWidth="140.0" text="Validity" />
                        <TableColumn fx:id="usageColumn" prefWidth="80.0" text="Usage" />
                     </columns>
                  </TableView>

                  <!-- Table Action Buttons -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Button fx:id="addDiscountButton" onAction="#handleAddDiscount" styleClass="primary-button" text="➕ Add Discount" />
                        <Button fx:id="editDiscountButton" onAction="#handleEditDiscount" styleClass="secondary-button" text="✏️ Edit" />
                        <Button fx:id="deleteDiscountButton" onAction="#handleDeleteDiscount" styleClass="danger-button" text="🗑️ Delete" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Button fx:id="testDiscountButton" onAction="#handleTestDiscount" styleClass="info-button" text="🧪 Test" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="15.0" right="10.0" top="10.0" />
               </padding>
            </VBox>

            <!-- Right Side - Discount Details Form -->
            <VBox spacing="15.0">
               <children>
                  <Label styleClass="section-title" text="Discount Details" />
                  
                  <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
                     <content>
                        <VBox spacing="15.0">
                           <children>
                              <!-- Basic Information -->
                              <VBox spacing="8.0" styleClass="form-section">
                                 <children>
                                    <Label styleClass="form-section-title" text="Basic Information" />
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Name:" />
                                          <TextField fx:id="nameField" prefWidth="200.0" promptText="Discount name" styleClass="form-field" />
                                       </children>
                                    </HBox>
                                    
                                    <VBox spacing="5.0">
                                       <children>
                                          <Label styleClass="form-label" text="Description:" />
                                          <TextArea fx:id="descriptionField" prefRowCount="2" promptText="Discount description" styleClass="form-field" />
                                       </children>
                                    </VBox>
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Type:" />
                                          <ComboBox fx:id="typeComboBox" prefWidth="150.0" promptText="Select type" styleClass="form-field" />
                                       </children>
                                    </HBox>
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Value:" />
                                          <TextField fx:id="valueField" prefWidth="100.0" promptText="0.00" styleClass="form-field" />
                                          <Label styleClass="form-help" text="(% for percentage, $ for fixed amount)" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>

                              <!-- Conditions -->
                              <VBox spacing="8.0" styleClass="form-section">
                                 <children>
                                    <Label styleClass="form-section-title" text="Conditions" />
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Min Amount:" />
                                          <TextField fx:id="minimumAmountField" prefWidth="100.0" promptText="0.00" styleClass="form-field" />
                                          <Label styleClass="form-help" text="(minimum purchase amount)" />
                                       </children>
                                    </HBox>
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Max Discount:" />
                                          <TextField fx:id="maximumDiscountField" prefWidth="100.0" promptText="0.00" styleClass="form-field" />
                                          <Label styleClass="form-help" text="(maximum discount amount)" />
                                       </children>
                                    </HBox>
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Min Quantity:" />
                                          <TextField fx:id="minimumQuantityField" prefWidth="80.0" promptText="1" styleClass="form-field" />
                                          <Label styleClass="form-help" text="(minimum item quantity)" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>

                              <!-- Buy X Get Y Settings -->
                              <VBox spacing="8.0" styleClass="form-section">
                                 <children>
                                    <Label styleClass="form-section-title" text="Buy X Get Y Settings" />
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Buy Quantity:" />
                                          <TextField fx:id="buyQuantityField" prefWidth="80.0" promptText="2" styleClass="form-field" />
                                          <Label styleClass="form-label" text="Get Quantity:" />
                                          <TextField fx:id="getQuantityField" prefWidth="80.0" promptText="1" styleClass="form-field" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>

                              <!-- Validity and Usage -->
                              <VBox spacing="8.0" styleClass="form-section">
                                 <children>
                                    <Label styleClass="form-section-title" text="Validity &amp; Usage" />
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Promo Code:" />
                                          <TextField fx:id="promoCodeField" prefWidth="120.0" promptText="Optional" styleClass="form-field" />
                                       </children>
                                    </HBox>
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Start Date:" />
                                          <DatePicker fx:id="startDatePicker" prefWidth="140.0" styleClass="form-field" />
                                          <Label styleClass="form-label" text="End Date:" />
                                          <DatePicker fx:id="endDatePicker" prefWidth="140.0" styleClass="form-field" />
                                       </children>
                                    </HBox>
                                    
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="form-label" text="Usage Limit:" />
                                          <TextField fx:id="usageLimitField" prefWidth="80.0" promptText="Unlimited" styleClass="form-field" />
                                          <Label styleClass="form-help" text="(leave empty for unlimited)" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>

                              <!-- Options -->
                              <VBox spacing="8.0" styleClass="form-section">
                                 <children>
                                    <Label styleClass="form-section-title" text="Options" />
                                    
                                    <CheckBox fx:id="activeCheckBox" styleClass="form-checkbox" text="Active" />
                                    <CheckBox fx:id="stackableCheckBox" styleClass="form-checkbox" text="Stackable with other discounts" />
                                 </children>
                              </VBox>

                              <!-- Form Action Buttons -->
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Button fx:id="saveDiscountButton" onAction="#handleSaveDiscount" styleClass="primary-button" text="💾 Save" visible="false" />
                                    <Button fx:id="cancelDiscountButton" onAction="#handleCancelDiscount" styleClass="secondary-button" text="❌ Cancel" visible="false" />
                                 </children>
                              </HBox>
                           </children>
                           <padding>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </padding>
                        </VBox>
                     </content>
                  </ScrollPane>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="15.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </children>
</VBox>
