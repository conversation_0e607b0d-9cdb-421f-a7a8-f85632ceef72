package com.clothingstore.test;

import com.clothingstore.database.ConnectionPool;
import com.clothingstore.database.OptimizedProfitQueries;
import com.clothingstore.database.OptimizedProfitQueries.ProfitQueryResult;
import com.clothingstore.database.OptimizedProfitQueries.CategoryProfitResult;
import com.clothingstore.service.ProfitAnalysisService;
import com.clothingstore.service.ProfitAnalysisService.ProfitMetrics;
import com.clothingstore.service.ProfitAnalysisService.CategoryProfitData;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Diagnostic tool to test database connection and profit analysis functionality
 */
public class ProfitAnalysisDiagnostic {

    private static final String DB_URL = "*****************************";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void main(String[] args) {
        System.out.println("=== PROFIT ANALYSIS DIAGNOSTIC ===");
        System.out.println("Testing database connection and data availability");
        System.out.println();

        try {
            // Test 1: Basic database connection
            testDatabaseConnection();
            
            // Test 2: Check table structure and data
            checkDatabaseStructure();
            
            // Test 3: Test connection pool
            testConnectionPool();
            
            // Test 4: Test OptimizedProfitQueries directly
            testOptimizedQueries();
            
            // Test 5: Test ProfitAnalysisService
            testProfitAnalysisService();
            
            System.out.println("\n=== DIAGNOSTIC COMPLETE ===");
            
        } catch (Exception e) {
            System.err.println("Diagnostic failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testDatabaseConnection() {
        System.out.println("1. TESTING BASIC DATABASE CONNECTION");
        System.out.println("-----------------------------------");
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            System.out.println("✓ Database connection successful");
            System.out.println("  Database URL: " + DB_URL);
            System.out.println("  Connection valid: " + conn.isValid(5));
            System.out.println();
        } catch (SQLException e) {
            System.err.println("✗ Database connection failed: " + e.getMessage());
            System.out.println();
        }
    }

    private static void checkDatabaseStructure() {
        System.out.println("2. CHECKING DATABASE STRUCTURE AND DATA");
        System.out.println("---------------------------------------");
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // Check tables exist
            String[] tables = {"products", "customers", "transactions", "transaction_items"};
            for (String table : tables) {
                if (tableExists(conn, table)) {
                    int count = getTableRowCount(conn, table);
                    System.out.println("✓ Table '" + table + "' exists with " + count + " rows");
                } else {
                    System.out.println("✗ Table '" + table + "' does not exist");
                }
            }
            
            // Check specific data for profit analysis
            System.out.println("\nDATA ANALYSIS:");
            
            // Products with cost_price
            String productQuery = "SELECT COUNT(*) as total, COUNT(cost_price) as with_cost FROM products WHERE active = 1";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(productQuery)) {
                if (rs.next()) {
                    System.out.println("  Products: " + rs.getInt("total") + " total, " + rs.getInt("with_cost") + " with cost_price");
                }
            }
            
            // Transactions by status
            String transactionQuery = "SELECT status, COUNT(*) as count FROM transactions GROUP BY status";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(transactionQuery)) {
                System.out.println("  Transactions by status:");
                while (rs.next()) {
                    System.out.println("    " + rs.getString("status") + ": " + rs.getInt("count"));
                }
            }
            
            // Transaction items
            String itemQuery = "SELECT COUNT(*) as count FROM transaction_items";
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(itemQuery)) {
                if (rs.next()) {
                    System.out.println("  Transaction items: " + rs.getInt("count"));
                }
            }
            
            // Sample transaction data
            String sampleQuery = "SELECT t.id, t.transaction_number, t.status, t.total_amount, " +
                    "t.transaction_date, COUNT(ti.id) as item_count " +
                    "FROM transactions t " +
                    "LEFT JOIN transaction_items ti ON t.id = ti.transaction_id " +
                    "GROUP BY t.id " +
                    "ORDER BY t.transaction_date DESC LIMIT 5";
            
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(sampleQuery)) {
                System.out.println("\n  Recent transactions:");
                while (rs.next()) {
                    System.out.println("    " + rs.getString("transaction_number") + 
                            " | " + rs.getString("status") + 
                            " | $" + rs.getDouble("total_amount") + 
                            " | " + rs.getInt("item_count") + " items" +
                            " | " + rs.getString("transaction_date"));
                }
            }
            
            System.out.println();
            
        } catch (SQLException e) {
            System.err.println("✗ Database structure check failed: " + e.getMessage());
            System.out.println();
        }
    }

    private static void testConnectionPool() {
        System.out.println("3. TESTING CONNECTION POOL");
        System.out.println("--------------------------");
        
        try {
            ConnectionPool pool = ConnectionPool.getInstance();
            Connection conn = pool.getConnection();
            
            if (conn != null && conn.isValid(5)) {
                System.out.println("✓ Connection pool working");
                System.out.println("  Connection obtained successfully");
                pool.returnConnection(conn);
                System.out.println("  Connection returned successfully");
            } else {
                System.out.println("✗ Connection pool returned invalid connection");
            }
            
            System.out.println();
            
        } catch (Exception e) {
            System.err.println("✗ Connection pool test failed: " + e.getMessage());
            System.out.println();
        }
    }

    private static void testOptimizedQueries() {
        System.out.println("4. TESTING OPTIMIZED PROFIT QUERIES");
        System.out.println("-----------------------------------");
        
        try {
            OptimizedProfitQueries queries = new OptimizedProfitQueries();
            
            // Test date range - last 30 days
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(30);
            
            System.out.println("Date range: " + startDate.format(FORMATTER) + " to " + endDate.format(FORMATTER));
            
            // Test profit metrics query
            ProfitQueryResult result = queries.calculateProfitMetrics(startDate, endDate);
            System.out.println("\nProfit Metrics Query Result:");
            System.out.println("  Transactions: " + result.transactionCount);
            System.out.println("  Items Sold: " + result.totalItemsSold);
            System.out.println("  Revenue: $" + String.format("%.2f", result.totalRevenue));
            System.out.println("  Cost: $" + String.format("%.2f", result.totalCost));
            System.out.println("  Profit: $" + String.format("%.2f", (result.totalRevenue - result.totalCost)));
            
            // Test category breakdown query
            List<CategoryProfitResult> categories = queries.calculateCategoryProfitBreakdown(startDate, endDate);
            System.out.println("\nCategory Breakdown Query Result:");
            System.out.println("  Categories found: " + categories.size());
            
            for (CategoryProfitResult category : categories) {
                double profit = category.revenue - category.cost;
                System.out.println("  " + category.category + ":");
                System.out.println("    Revenue: $" + String.format("%.2f", category.revenue));
                System.out.println("    Cost: $" + String.format("%.2f", category.cost));
                System.out.println("    Profit: $" + String.format("%.2f", profit));
                System.out.println("    Items: " + category.itemsSold);
                System.out.println("    Transactions: " + category.transactionCount);
            }
            
            System.out.println();
            
        } catch (Exception e) {
            System.err.println("✗ Optimized queries test failed: " + e.getMessage());
            e.printStackTrace();
            System.out.println();
        }
    }

    private static void testProfitAnalysisService() {
        System.out.println("5. TESTING PROFIT ANALYSIS SERVICE");
        System.out.println("----------------------------------");
        
        try {
            ProfitAnalysisService service = new ProfitAnalysisService();
            
            // Test date range - last 30 days
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(30);
            
            // Test profit metrics
            ProfitMetrics metrics = service.calculateProfitMetrics(startDate, endDate);
            System.out.println("Service Profit Metrics:");
            System.out.println("  Revenue: " + metrics.getFormattedRevenue());
            System.out.println("  Cost: " + metrics.getFormattedCost());
            System.out.println("  Profit: " + metrics.getFormattedProfit());
            System.out.println("  Margin: " + metrics.getFormattedProfitMargin());
            System.out.println("  Transactions: " + metrics.getTransactionCount());
            System.out.println("  Items Sold: " + metrics.getTotalItemsSold());
            
            // Test category breakdown
            List<CategoryProfitData> categories = service.calculateCategoryProfitBreakdown(startDate, endDate);
            System.out.println("\nService Category Breakdown:");
            System.out.println("  Categories: " + categories.size());
            
            for (CategoryProfitData category : categories) {
                System.out.println("  " + category.getCategoryName() + ":");
                System.out.println("    Revenue: " + category.getFormattedRevenue());
                System.out.println("    Profit: " + category.getFormattedProfit());
                System.out.println("    Margin: " + category.getFormattedProfitMargin());
            }
            
            System.out.println();
            
        } catch (Exception e) {
            System.err.println("✗ Profit analysis service test failed: " + e.getMessage());
            e.printStackTrace();
            System.out.println();
        }
    }

    private static boolean tableExists(Connection conn, String tableName) throws SQLException {
        DatabaseMetaData meta = conn.getMetaData();
        try (ResultSet rs = meta.getTables(null, null, tableName, new String[]{"TABLE"})) {
            return rs.next();
        }
    }

    private static int getTableRowCount(Connection conn, String tableName) throws SQLException {
        String query = "SELECT COUNT(*) FROM " + tableName;
        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(query)) {
            return rs.next() ? rs.getInt(1) : 0;
        }
    }
}
