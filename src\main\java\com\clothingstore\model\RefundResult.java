package com.clothingstore.model;

import java.util.ArrayList;
import java.util.List;

/**
 * Result class for refund operations
 */
public class RefundResult {
    private boolean success;
    private String message;
    private Transaction refundTransaction;
    private String errorCode;
    private List<String> warnings;

    public RefundResult() {
        this.warnings = new ArrayList<>();
    }

    public RefundResult(boolean success, String message, Transaction refundTransaction) {
        this();
        this.success = success;
        this.message = message;
        this.refundTransaction = refundTransaction;
    }

    public RefundResult(boolean success, String message, Transaction refundTransaction, String errorCode) {
        this();
        this.success = success;
        this.message = message;
        this.refundTransaction = refundTransaction;
        this.errorCode = errorCode;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Transaction getRefundTransaction() {
        return refundTransaction;
    }

    public void setRefundTransaction(Transaction refundTransaction) {
        this.refundTransaction = refundTransaction;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public List<String> getWarnings() {
        return warnings;
    }

    public void setWarnings(List<String> warnings) {
        this.warnings = warnings != null ? warnings : new ArrayList<>();
    }

    public void addWarning(String warning) {
        if (warning != null && !warning.trim().isEmpty()) {
            this.warnings.add(warning);
        }
    }

    public void addWarnings(List<String> warnings) {
        if (warnings != null) {
            for (String warning : warnings) {
                addWarning(warning);
            }
        }
    }

    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }

    @Override
    public String toString() {
        return String.format("RefundResult{success=%s, message='%s', errorCode='%s', warnings=%d}",
                           success, message, errorCode, warnings != null ? warnings.size() : 0);
    }
}
