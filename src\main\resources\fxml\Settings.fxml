<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.SettingsController">
   <children>
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label styleClass="page-title" text="Application Settings" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnSave" mnemonicParsing="false" onAction="#handleSave" text="Save Settings" />
            <Button fx:id="btnReset" mnemonicParsing="false" onAction="#handleReset" text="Reset to Defaults" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>
      
      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="20.0">
               <children>
                  <TitledPane expanded="true" text="General Settings">
                     <content>
                        <GridPane hgap="10.0" vgap="10.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                            <ColumnConstraints hgrow="ALWAYS" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints />
                            <RowConstraints />
                            <RowConstraints />
                            <RowConstraints />
                          </rowConstraints>
                           <children>
                              <Label text="Store Name:" />
                              <TextField fx:id="txtStoreName" promptText="Enter store name" GridPane.columnIndex="1" />
                              <Label text="Currency:" GridPane.rowIndex="1" />
                              <ComboBox fx:id="cmbCurrency" promptText="Select currency" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              <Label text="Tax Rate (%):" GridPane.rowIndex="2" />
                              <TextField fx:id="txtTaxRate" promptText="0.00" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                              <Label text="Low Stock Threshold:" GridPane.rowIndex="3" />
                              <TextField fx:id="txtLowStockThreshold" promptText="5" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           </children>
                           <padding>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </padding>
                        </GridPane>
                     </content>
                  </TitledPane>
                  
                  <TitledPane expanded="true" text="Receipt Settings">
                     <content>
                        <GridPane hgap="10.0" vgap="10.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                            <ColumnConstraints hgrow="ALWAYS" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints />
                            <RowConstraints />
                            <RowConstraints />
                            <RowConstraints />
                          </rowConstraints>
                           <children>
                              <Label text="Receipt Header:" />
                              <TextArea fx:id="txtReceiptHeader" prefRowCount="3" promptText="Enter receipt header text" GridPane.columnIndex="1" />
                              <Label text="Receipt Footer:" GridPane.rowIndex="1" />
                              <TextArea fx:id="txtReceiptFooter" prefRowCount="3" promptText="Enter receipt footer text" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              <Label text="Auto Print Receipt:" GridPane.rowIndex="2" />
                              <CheckBox fx:id="chkAutoPrint" text="Automatically print receipt after transaction" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                              <Label text="Receipt Printer:" GridPane.rowIndex="3" />
                              <ComboBox fx:id="cmbPrinter" promptText="Select printer" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           </children>
                           <padding>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </padding>
                        </GridPane>
                     </content>
                  </TitledPane>
                  
                  <TitledPane expanded="true" text="Loyalty Program">
                     <content>
                        <GridPane hgap="10.0" vgap="10.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                            <ColumnConstraints hgrow="ALWAYS" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints />
                            <RowConstraints />
                            <RowConstraints />
                            <RowConstraints />
                          </rowConstraints>
                           <children>
                              <Label text="Points per Dollar:" />
                              <TextField fx:id="txtPointsPerDollar" promptText="1" GridPane.columnIndex="1" />
                              <Label text="Bronze Threshold:" GridPane.rowIndex="1" />
                              <TextField fx:id="txtBronzeThreshold" promptText="100" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              <Label text="Silver Threshold:" GridPane.rowIndex="2" />
                              <TextField fx:id="txtSilverThreshold" promptText="500" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                              <Label text="Gold Threshold:" GridPane.rowIndex="3" />
                              <TextField fx:id="txtGoldThreshold" promptText="1000" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           </children>
                           <padding>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </padding>
                        </GridPane>
                     </content>
                  </TitledPane>
                  

                  <TitledPane expanded="false" text="Database Settings">
                     <content>
                        <GridPane hgap="10.0" vgap="10.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                            <ColumnConstraints hgrow="ALWAYS" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints />
                            <RowConstraints />
                            <RowConstraints />
                          </rowConstraints>
                           <children>
                              <Label text="Database Path:" />
                              <HBox spacing="5.0" GridPane.columnIndex="1">
                                 <children>
                                    <TextField fx:id="txtDatabasePath" HBox.hgrow="ALWAYS" />
                                    <Button fx:id="btnBrowseDatabase" mnemonicParsing="false" onAction="#handleBrowseDatabase" text="Browse" />
                                 </children>
                              </HBox>
                              <Label text="Auto Backup:" GridPane.rowIndex="1" />
                              <CheckBox fx:id="chkAutoBackup" text="Enable automatic daily backups" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              <Label text="Backup Location:" GridPane.rowIndex="2" />
                              <HBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                 <children>
                                    <TextField fx:id="txtBackupPath" HBox.hgrow="ALWAYS" />
                                    <Button fx:id="btnBrowseBackup" mnemonicParsing="false" onAction="#handleBrowseBackup" text="Browse" />
                                 </children>
                              </HBox>
                           </children>
                           <padding>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </padding>
                        </GridPane>
                     </content>
                  </TitledPane>
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
