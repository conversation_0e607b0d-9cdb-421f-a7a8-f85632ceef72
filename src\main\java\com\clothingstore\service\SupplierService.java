package com.clothingstore.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Product;
import com.clothingstore.model.PurchaseOrder;
import com.clothingstore.model.PurchaseOrderItem;
import com.clothingstore.model.PurchaseOrderStatus;
import com.clothingstore.model.Supplier;

/**
 * Service for managing suppliers and purchase orders
 */
public class SupplierService {

    private static final Logger LOGGER = Logger.getLogger(SupplierService.class.getName());
    private static SupplierService instance;

    // In-memory storage for demo - in real app would use database
    private final Map<Long, Supplier> suppliers;
    private final Map<Long, PurchaseOrder> purchaseOrders;
    private final List<PurchaseOrderItem> allPurchaseOrderItems;
    private final ProductDAO productDAO;

    private long nextSupplierId = 1;
    private long nextPurchaseOrderId = 1;
    private long nextPurchaseOrderItemId = 1;

    private SupplierService() {
        this.suppliers = new HashMap<>();
        this.purchaseOrders = new HashMap<>();
        this.allPurchaseOrderItems = new ArrayList<>();
        this.productDAO = ProductDAO.getInstance();
        initializeDefaultSuppliers();
    }

    public static synchronized SupplierService getInstance() {
        if (instance == null) {
            instance = new SupplierService();
        }
        return instance;
    }

    /**
     * Initialize some default suppliers for demo
     */
    private void initializeDefaultSuppliers() {
        // Fashion supplier
        Supplier fashionSupplier = new Supplier("Fashion Forward Inc.", "Sarah Johnson");
        fashionSupplier.setId(nextSupplierId++);
        fashionSupplier.setEmail("<EMAIL>");
        fashionSupplier.setPhone("555-0123");
        fashionSupplier.setAddress("123 Fashion Ave");
        fashionSupplier.setCity("New York");
        fashionSupplier.setState("NY");
        fashionSupplier.setZipCode("10001");
        fashionSupplier.setCountry("USA");
        fashionSupplier.setLeadTimeDays(14);
        fashionSupplier.setMinimumOrderAmount(new BigDecimal("500"));
        fashionSupplier.setCreditLimit(new BigDecimal("10000"));
        suppliers.put(fashionSupplier.getId(), fashionSupplier);

        // Accessories supplier
        Supplier accessoriesSupplier = new Supplier("Accessory World Ltd.", "Mike Chen");
        accessoriesSupplier.setId(nextSupplierId++);
        accessoriesSupplier.setEmail("<EMAIL>");
        accessoriesSupplier.setPhone("555-0456");
        accessoriesSupplier.setAddress("456 Accessory Blvd");
        accessoriesSupplier.setCity("Los Angeles");
        accessoriesSupplier.setState("CA");
        accessoriesSupplier.setZipCode("90210");
        accessoriesSupplier.setCountry("USA");
        accessoriesSupplier.setLeadTimeDays(7);
        accessoriesSupplier.setMinimumOrderAmount(new BigDecimal("200"));
        accessoriesSupplier.setCreditLimit(new BigDecimal("5000"));
        suppliers.put(accessoriesSupplier.getId(), accessoriesSupplier);

        // Footwear supplier
        Supplier footwearSupplier = new Supplier("Shoe Excellence Co.", "Lisa Rodriguez");
        footwearSupplier.setId(nextSupplierId++);
        footwearSupplier.setEmail("<EMAIL>");
        footwearSupplier.setPhone("555-0789");
        footwearSupplier.setAddress("789 Shoe Street");
        footwearSupplier.setCity("Chicago");
        footwearSupplier.setState("IL");
        footwearSupplier.setZipCode("60601");
        footwearSupplier.setCountry("USA");
        footwearSupplier.setLeadTimeDays(21);
        footwearSupplier.setMinimumOrderAmount(new BigDecimal("1000"));
        footwearSupplier.setCreditLimit(new BigDecimal("15000"));
        suppliers.put(footwearSupplier.getId(), footwearSupplier);
    }

    /**
     * Create a new supplier
     */
    public SupplierOperationResult createSupplier(Supplier supplier) {
        try {
            // Validate supplier data
            String validationError = validateSupplier(supplier);
            if (validationError != null) {
                return new SupplierOperationResult(false, validationError, null);
            }

            // Generate supplier code if not provided
            if (supplier.getSupplierCode() == null || supplier.getSupplierCode().trim().isEmpty()) {
                supplier.setSupplierCode(generateSupplierCode());
            }

            // Check for duplicate supplier code
            if (isSupplierCodeExists(supplier.getSupplierCode())) {
                return new SupplierOperationResult(false, "Supplier code already exists", null);
            }

            // Set ID and save
            supplier.setId(nextSupplierId++);
            supplier.setCreatedAt(LocalDateTime.now());
            supplier.setUpdatedAt(LocalDateTime.now());

            suppliers.put(supplier.getId(), supplier);

            LOGGER.info("Created new supplier: " + supplier.getDisplayName());
            return new SupplierOperationResult(true, "Supplier created successfully", supplier);

        } catch (Exception e) {
            LOGGER.severe("Error creating supplier: " + e.getMessage());
            return new SupplierOperationResult(false, "Error creating supplier: " + e.getMessage(), null);
        }
    }

    /**
     * Update an existing supplier
     */
    public SupplierOperationResult updateSupplier(Supplier supplier) {
        try {
            if (supplier.getId() == null || !suppliers.containsKey(supplier.getId())) {
                return new SupplierOperationResult(false, "Supplier not found", null);
            }

            // Validate supplier data
            String validationError = validateSupplier(supplier);
            if (validationError != null) {
                return new SupplierOperationResult(false, validationError, null);
            }

            supplier.setUpdatedAt(LocalDateTime.now());
            suppliers.put(supplier.getId(), supplier);

            LOGGER.info("Updated supplier: " + supplier.getDisplayName());
            return new SupplierOperationResult(true, "Supplier updated successfully", supplier);

        } catch (Exception e) {
            LOGGER.severe("Error updating supplier: " + e.getMessage());
            return new SupplierOperationResult(false, "Error updating supplier: " + e.getMessage(), null);
        }
    }

    /**
     * Get supplier by ID
     */
    public Supplier getSupplier(Long supplierId) {
        return suppliers.get(supplierId);
    }

    /**
     * Get all suppliers
     */
    public List<Supplier> getAllSuppliers() {
        return new ArrayList<>(suppliers.values());
    }

    /**
     * Get active suppliers
     */
    public List<Supplier> getActiveSuppliers() {
        return suppliers.values().stream()
                .filter(Supplier::isActive)
                .collect(Collectors.toList());
    }

    /**
     * Delete a supplier
     */
    public SupplierOperationResult deleteSupplier(Long supplierId) {
        try {
            Supplier supplier = suppliers.get(supplierId);
            if (supplier == null) {
                return new SupplierOperationResult(false, "Supplier not found", null);
            }

            // Check if supplier has active purchase orders
            boolean hasActivePurchaseOrders = purchaseOrders.values().stream()
                    .anyMatch(po -> po.getSupplierId().equals(supplierId) && po.getStatus().isActive());

            if (hasActivePurchaseOrders) {
                return new SupplierOperationResult(false,
                        "Cannot delete supplier with active purchase orders. Please complete or cancel all orders first.", null);
            }

            // Check if supplier has products assigned
            try {
                List<Product> supplierProducts = productDAO.findBySupplierId(supplierId);
                if (!supplierProducts.isEmpty()) {
                    return new SupplierOperationResult(false,
                            "Cannot delete supplier with assigned products. Please reassign or remove products first.", null);
                }
            } catch (Exception e) {
                LOGGER.warning("Could not check supplier products: " + e.getMessage());
            }

            // Remove supplier
            suppliers.remove(supplierId);

            LOGGER.info("Deleted supplier: " + supplier.getDisplayName());
            return new SupplierOperationResult(true, "Supplier deleted successfully", supplier);

        } catch (Exception e) {
            LOGGER.severe("Error deleting supplier: " + e.getMessage());
            return new SupplierOperationResult(false, "Error deleting supplier: " + e.getMessage(), null);
        }
    }

    /**
     * Create a new purchase order
     */
    public PurchaseOrderOperationResult createPurchaseOrder(Long supplierId, String createdBy) {
        try {
            Supplier supplier = suppliers.get(supplierId);
            if (supplier == null) {
                return new PurchaseOrderOperationResult(false, "Supplier not found", null);
            }

            if (!supplier.getStatus().canReceiveOrders()) {
                return new PurchaseOrderOperationResult(false, "Supplier cannot receive orders", null);
            }

            PurchaseOrder purchaseOrder = new PurchaseOrder(supplierId);
            purchaseOrder.setId(nextPurchaseOrderId++);
            purchaseOrder.setSupplier(supplier);
            purchaseOrder.setCreatedBy(createdBy);

            // Set expected delivery date based on supplier lead time
            LocalDateTime expectedDelivery = LocalDateTime.now().plusDays(supplier.getLeadTimeDays());
            purchaseOrder.setExpectedDeliveryDate(expectedDelivery);

            purchaseOrders.put(purchaseOrder.getId(), purchaseOrder);

            LOGGER.info("Created purchase order: " + purchaseOrder.getOrderNumber());
            return new PurchaseOrderOperationResult(true, "Purchase order created successfully", purchaseOrder);

        } catch (Exception e) {
            LOGGER.severe("Error creating purchase order: " + e.getMessage());
            return new PurchaseOrderOperationResult(false, "Error creating purchase order: " + e.getMessage(), null);
        }
    }

    /**
     * Add item to purchase order
     */
    public PurchaseOrderOperationResult addItemToPurchaseOrder(Long purchaseOrderId, Long productId,
            int quantity, BigDecimal unitCost) {
        try {
            PurchaseOrder purchaseOrder = purchaseOrders.get(purchaseOrderId);
            if (purchaseOrder == null) {
                return new PurchaseOrderOperationResult(false, "Purchase order not found", null);
            }

            if (!purchaseOrder.canBeModified()) {
                return new PurchaseOrderOperationResult(false, "Purchase order cannot be modified", null);
            }

            // Get product information
            Optional<Product> productOpt = productDAO.findById(productId);
            if (!productOpt.isPresent()) {
                return new PurchaseOrderOperationResult(false, "Product not found", null);
            }

            Product product = productOpt.get();

            // Create purchase order item
            PurchaseOrderItem item = new PurchaseOrderItem(productId, quantity, unitCost);
            item.setId(nextPurchaseOrderItemId++);
            item.setPurchaseOrderId(purchaseOrderId);
            item.setProduct(product);

            // Add item to purchase order
            purchaseOrder.addItem(item);
            allPurchaseOrderItems.add(item);

            LOGGER.info("Added item to purchase order: " + product.getName());
            return new PurchaseOrderOperationResult(true, "Item added successfully", purchaseOrder);

        } catch (Exception e) {
            LOGGER.severe("Error adding item to purchase order: " + e.getMessage());
            return new PurchaseOrderOperationResult(false, "Error adding item: " + e.getMessage(), null);
        }
    }

    /**
     * Update purchase order status
     */
    public PurchaseOrderOperationResult updatePurchaseOrderStatus(Long purchaseOrderId,
            PurchaseOrderStatus newStatus, String updatedBy) {
        try {
            PurchaseOrder purchaseOrder = purchaseOrders.get(purchaseOrderId);
            if (purchaseOrder == null) {
                return new PurchaseOrderOperationResult(false, "Purchase order not found", null);
            }

            // Validate status transition
            PurchaseOrderStatus[] allowedStatuses = purchaseOrder.getStatus().getNextPossibleStatuses();
            boolean isValidTransition = Arrays.asList(allowedStatuses).contains(newStatus);

            if (!isValidTransition) {
                return new PurchaseOrderOperationResult(false,
                        "Invalid status transition from " + purchaseOrder.getStatus() + " to " + newStatus, null);
            }

            // Update status
            purchaseOrder.setStatus(newStatus);
            purchaseOrder.setUpdatedAt(LocalDateTime.now());

            // Set specific fields based on status
            switch (newStatus) {
                case APPROVED:
                    purchaseOrder.setApprovedBy(updatedBy);
                    purchaseOrder.setApprovedDate(LocalDateTime.now());
                    break;
                case RECEIVED:
                    purchaseOrder.setReceivedBy(updatedBy);
                    purchaseOrder.setReceivedDate(LocalDateTime.now());
                    purchaseOrder.setActualDeliveryDate(LocalDateTime.now());
                    break;
                default:
                    // No special handling for other statuses
                    break;
            }

            LOGGER.info("Updated purchase order status: " + purchaseOrder.getOrderNumber() + " to " + newStatus);
            return new PurchaseOrderOperationResult(true, "Status updated successfully", purchaseOrder);

        } catch (Exception e) {
            LOGGER.severe("Error updating purchase order status: " + e.getMessage());
            return new PurchaseOrderOperationResult(false, "Error updating status: " + e.getMessage(), null);
        }
    }

    /**
     * Get purchase order by ID
     */
    public PurchaseOrder getPurchaseOrder(Long purchaseOrderId) {
        return purchaseOrders.get(purchaseOrderId);
    }

    /**
     * Get all purchase orders
     */
    public List<PurchaseOrder> getAllPurchaseOrders() {
        return new ArrayList<>(purchaseOrders.values());
    }

    /**
     * Get purchase orders by status
     */
    public List<PurchaseOrder> getPurchaseOrdersByStatus(PurchaseOrderStatus status) {
        return purchaseOrders.values().stream()
                .filter(po -> po.getStatus() == status)
                .collect(Collectors.toList());
    }

    /**
     * Get overdue purchase orders
     */
    public List<PurchaseOrder> getOverduePurchaseOrders() {
        return purchaseOrders.values().stream()
                .filter(PurchaseOrder::isOverdue)
                .collect(Collectors.toList());
    }

    /**
     * Generate reorder suggestions based on low stock
     */
    public List<ReorderSuggestion> generateReorderSuggestions() {
        try {
            List<Product> allProducts = productDAO.findAll();
            List<ReorderSuggestion> suggestions = new ArrayList<>();

            for (Product product : allProducts) {
                if (product.isLowStock() && product.isActive()) {
                    // Find preferred supplier for this product (simplified logic)
                    Supplier preferredSupplier = findPreferredSupplierForProduct(product);

                    if (preferredSupplier != null && preferredSupplier.isActive()) {
                        int suggestedQuantity = calculateSuggestedReorderQuantity(product);
                        BigDecimal estimatedCost = product.getCostPrice() != null
                                ? product.getCostPrice().multiply(new BigDecimal(suggestedQuantity)) : BigDecimal.ZERO;

                        ReorderSuggestion suggestion = new ReorderSuggestion(
                                product, preferredSupplier, suggestedQuantity, estimatedCost);
                        suggestions.add(suggestion);
                    }
                }
            }

            return suggestions.stream()
                    .sorted((s1, s2) -> s2.getPriority().compareTo(s1.getPriority()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            LOGGER.severe("Error generating reorder suggestions: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Create purchase order from reorder suggestions
     */
    public PurchaseOrderOperationResult createPurchaseOrderFromSuggestions(
            List<ReorderSuggestion> suggestions, String createdBy) {
        try {
            if (suggestions.isEmpty()) {
                return new PurchaseOrderOperationResult(false, "No suggestions provided", null);
            }

            // Group suggestions by supplier
            Map<Long, List<ReorderSuggestion>> suggestionsBySupplier = suggestions.stream()
                    .collect(Collectors.groupingBy(s -> s.getSupplier().getId()));

            List<PurchaseOrder> createdOrders = new ArrayList<>();

            for (Map.Entry<Long, List<ReorderSuggestion>> entry : suggestionsBySupplier.entrySet()) {
                Long supplierId = entry.getKey();
                List<ReorderSuggestion> supplierSuggestions = entry.getValue();

                // Create purchase order for this supplier
                PurchaseOrderOperationResult result = createPurchaseOrder(supplierId, createdBy);

                if (result.isSuccess()) {
                    PurchaseOrder purchaseOrder = result.getPurchaseOrder();

                    // Add items from suggestions
                    for (ReorderSuggestion suggestion : supplierSuggestions) {
                        BigDecimal unitCost = suggestion.getProduct().getCostPrice() != null
                                ? suggestion.getProduct().getCostPrice() : BigDecimal.ZERO;

                        addItemToPurchaseOrder(purchaseOrder.getId(),
                                suggestion.getProduct().getId(),
                                suggestion.getSuggestedQuantity(),
                                unitCost);
                    }

                    createdOrders.add(purchaseOrder);
                }
            }

            if (createdOrders.isEmpty()) {
                return new PurchaseOrderOperationResult(false, "Failed to create any purchase orders", null);
            }

            String message = String.format("Created %d purchase order(s) from reorder suggestions", createdOrders.size());
            return new PurchaseOrderOperationResult(true, message, createdOrders.get(0));

        } catch (Exception e) {
            LOGGER.severe("Error creating purchase orders from suggestions: " + e.getMessage());
            return new PurchaseOrderOperationResult(false, "Error creating purchase orders: " + e.getMessage(), null);
        }
    }

    /**
     * Validate supplier data
     */
    private String validateSupplier(Supplier supplier) {
        if (supplier.getCompanyName() == null || supplier.getCompanyName().trim().isEmpty()) {
            return "Company name is required";
        }

        if (supplier.getEmail() != null && !supplier.getEmail().trim().isEmpty()) {
            if (!isValidEmail(supplier.getEmail())) {
                return "Invalid email format";
            }
        }

        return null; // Valid
    }

    /**
     * Check if supplier code exists
     */
    private boolean isSupplierCodeExists(String supplierCode) {
        if (supplierCode == null) {
            return false;
        }
        return suppliers.values().stream()
                .anyMatch(s -> supplierCode.equals(s.getSupplierCode()));
    }

    /**
     * Generate a unique supplier code
     */
    private String generateSupplierCode() {
        String baseCode;
        int attempts = 0;
        do {
            baseCode = "SUP" + System.currentTimeMillis() + (attempts > 0 ? "_" + attempts : "");
            attempts++;
        } while (isSupplierCodeExists(baseCode) && attempts < 10);

        return baseCode;
    }

    /**
     * Simple email validation
     */
    private boolean isValidEmail(String email) {
        return email.contains("@") && email.contains(".");
    }

    /**
     * Find preferred supplier for a product (simplified logic)
     */
    private Supplier findPreferredSupplierForProduct(Product product) {
        // In a real system, this would check product-supplier relationships
        // For demo, return first active supplier
        return suppliers.values().stream()
                .filter(Supplier::isActive)
                .findFirst()
                .orElse(null);
    }

    /**
     * Calculate suggested reorder quantity
     */
    private int calculateSuggestedReorderQuantity(Product product) {
        // Simple logic: reorder to reach reorder quantity level
        int currentStock = product.getStockQuantity();
        int reorderQuantity = product.getReorderQuantity();
        int minLevel = product.getMinStockLevel();

        if (reorderQuantity > 0 && reorderQuantity > currentStock) {
            return reorderQuantity - currentStock;
        } else if (minLevel > currentStock) {
            return minLevel - currentStock + 10; // Add buffer
        }

        // Default to minimum reorder quantity
        return Math.max(10, minLevel);
    }

    /**
     * Get supplier performance metrics
     */
    public SupplierPerformanceMetrics getSupplierPerformance(Long supplierId) {
        try {
            Supplier supplier = suppliers.get(supplierId);
            if (supplier == null) {
                return null;
            }

            List<PurchaseOrder> supplierOrders = purchaseOrders.values().stream()
                    .filter(po -> po.getSupplierId().equals(supplierId))
                    .collect(Collectors.toList());

            return calculateSupplierMetrics(supplier, supplierOrders);

        } catch (Exception e) {
            LOGGER.severe("Error calculating supplier performance: " + e.getMessage());
            return null;
        }
    }

    /**
     * Calculate supplier performance metrics
     */
    private SupplierPerformanceMetrics calculateSupplierMetrics(Supplier supplier, List<PurchaseOrder> orders) {
        int totalOrders = orders.size();
        int completedOrders = (int) orders.stream().filter(po -> po.getStatus() == PurchaseOrderStatus.RECEIVED).count();
        int overdueOrders = (int) orders.stream().filter(PurchaseOrder::isOverdue).count();

        BigDecimal totalOrderValue = orders.stream()
                .map(PurchaseOrder::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        double onTimeDeliveryRate = totalOrders > 0
                ? (double) (completedOrders - overdueOrders) / totalOrders * 100 : 0;

        double completionRate = totalOrders > 0
                ? (double) completedOrders / totalOrders * 100 : 0;

        return new SupplierPerformanceMetrics(supplier, totalOrders, completedOrders,
                overdueOrders, totalOrderValue, onTimeDeliveryRate, completionRate);
    }
}

/**
 * Class for supplier performance metrics
 */
class SupplierPerformanceMetrics {

    private final Supplier supplier;
    private final int totalOrders;
    private final int completedOrders;
    private final int overdueOrders;
    private final BigDecimal totalOrderValue;
    private final double onTimeDeliveryRate;
    private final double completionRate;

    public SupplierPerformanceMetrics(Supplier supplier, int totalOrders, int completedOrders,
            int overdueOrders, BigDecimal totalOrderValue,
            double onTimeDeliveryRate, double completionRate) {
        this.supplier = supplier;
        this.totalOrders = totalOrders;
        this.completedOrders = completedOrders;
        this.overdueOrders = overdueOrders;
        this.totalOrderValue = totalOrderValue;
        this.onTimeDeliveryRate = onTimeDeliveryRate;
        this.completionRate = completionRate;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    public int getTotalOrders() {
        return totalOrders;
    }

    public int getCompletedOrders() {
        return completedOrders;
    }

    public int getOverdueOrders() {
        return overdueOrders;
    }

    public BigDecimal getTotalOrderValue() {
        return totalOrderValue;
    }

    public double getOnTimeDeliveryRate() {
        return onTimeDeliveryRate;
    }

    public double getCompletionRate() {
        return completionRate;
    }

    public String getPerformanceGrade() {
        double avgScore = (onTimeDeliveryRate + completionRate) / 2;

        if (avgScore >= 95) {
            return "A+";
        } else if (avgScore >= 90) {
            return "A";
        } else if (avgScore >= 85) {
            return "B+";
        } else if (avgScore >= 80) {
            return "B";
        } else if (avgScore >= 75) {
            return "C+";
        } else if (avgScore >= 70) {
            return "C";
        } else if (avgScore >= 65) {
            return "D";
        } else {
            return "F";
        }
    }
}
