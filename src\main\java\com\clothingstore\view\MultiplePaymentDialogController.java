package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;

import com.clothingstore.model.Transaction;
import com.clothingstore.service.PaymentHistoryService;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Controller for multiple payment methods dialog Allows customers to pay with
 * multiple payment methods in a single transaction
 */
public class MultiplePaymentDialogController implements Initializable {

    @FXML
    private Label lblTransactionNumber;
    @FXML
    private Label lblTotalAmount;
    @FXML
    private Label lblTotalPaid;
    @FXML
    private Label lblRemainingBalance;
    @FXML
    private Label lblStatus;

    // Payment entry controls
    @FXML
    private ComboBox<String> cmbPaymentMethod;
    @FXML
    private TextField txtPaymentAmount;
    @FXML
    private Button btnAddPayment;
    @FXML
    private Button btnRemovePayment;

    // Payments table
    @FXML
    private TableView<PaymentEntry> tblPayments;
    @FXML
    private TableColumn<PaymentEntry, String> colPaymentMethod;
    @FXML
    private TableColumn<PaymentEntry, String> colAmount;

    // Action buttons
    @FXML
    private Button btnCompleteTransaction;
    @FXML
    private Button btnCancel;

    // Change information
    @FXML
    private VBox vboxChangeInfo;
    @FXML
    private Label lblChange;

    private Transaction transaction;
    private ObservableList<PaymentEntry> payments;
    private boolean transactionCompleted = false;
    private final NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupPaymentMethods();
        setupPaymentsTable();
        setupEventHandlers();

        payments = FXCollections.observableArrayList();
        tblPayments.setItems(payments);

        updateDisplay();
    }

    private void setupPaymentMethods() {
        cmbPaymentMethod.setItems(FXCollections.observableArrayList(
                "CASH", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "GIFT_CARD", "STORE_CREDIT"
        ));
        cmbPaymentMethod.setValue("CASH");
    }

    private void setupPaymentsTable() {
        colPaymentMethod.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getPaymentMethod()));
        colAmount.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getAmount())));
    }

    private void setupEventHandlers() {
        txtPaymentAmount.textProperty().addListener((obs, oldVal, newVal) -> {
            validatePaymentEntry();
        });

        cmbPaymentMethod.setOnAction(e -> validatePaymentEntry());

        // Allow removing payments by double-clicking
        tblPayments.setRowFactory(tv -> {
            TableRow<PaymentEntry> row = new TableRow<>();
            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    removeSelectedPayment();
                }
            });
            return row;
        });
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
        updateDisplay();
    }

    private void updateDisplay() {
        if (transaction == null) {
            return;
        }

        lblTransactionNumber.setText("Transaction: " + transaction.getTransactionNumber());
        lblTotalAmount.setText(currencyFormat.format(transaction.getTotalAmount()));

        updatePaymentSummary();
    }

    private void updatePaymentSummary() {
        if (transaction == null) {
            return;
        }

        BigDecimal totalPaid = payments.stream()
                .map(PaymentEntry::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal remainingBalance = transaction.getTotalAmount().subtract(totalPaid);

        lblTotalPaid.setText(currencyFormat.format(totalPaid));
        lblRemainingBalance.setText(currencyFormat.format(remainingBalance));

        // Update status and change information
        if (remainingBalance.compareTo(BigDecimal.ZERO) == 0) {
            lblStatus.setText("✓ Payment Complete");
            lblStatus.setStyle("-fx-text-fill: green; -fx-font-weight: bold;");
            btnCompleteTransaction.setDisable(false);
            vboxChangeInfo.setVisible(false);
        } else if (remainingBalance.compareTo(BigDecimal.ZERO) < 0) {
            // Overpayment - show change
            BigDecimal change = remainingBalance.abs();
            lblStatus.setText("⚠ Overpayment - Change Due");
            lblStatus.setStyle("-fx-text-fill: orange; -fx-font-weight: bold;");
            lblChange.setText("Change: " + currencyFormat.format(change));
            vboxChangeInfo.setVisible(true);
            btnCompleteTransaction.setDisable(false);
        } else {
            // Underpayment
            lblStatus.setText("Partial Payment - " + currencyFormat.format(remainingBalance) + " remaining");
            lblStatus.setStyle("-fx-text-fill: red; -fx-font-weight: bold;");
            btnCompleteTransaction.setDisable(true);
            vboxChangeInfo.setVisible(false);
        }

        // Update suggested payment amount
        if (remainingBalance.compareTo(BigDecimal.ZERO) > 0) {
            txtPaymentAmount.setPromptText("Remaining: " + currencyFormat.format(remainingBalance));
        }
    }

    private void validatePaymentEntry() {
        try {
            String amountText = txtPaymentAmount.getText().trim();
            String paymentMethod = cmbPaymentMethod.getValue();

            boolean isValid = !amountText.isEmpty()
                    && paymentMethod != null
                    && new BigDecimal(amountText).compareTo(BigDecimal.ZERO) > 0;

            btnAddPayment.setDisable(!isValid);
        } catch (NumberFormatException e) {
            btnAddPayment.setDisable(true);
        }
    }

    @FXML
    private void handleAddPayment() {
        try {
            String paymentMethod = cmbPaymentMethod.getValue();
            BigDecimal amount = new BigDecimal(txtPaymentAmount.getText().trim());

            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                AlertUtil.showWarning("Invalid Amount", "Payment amount must be greater than zero.");
                return;
            }

            // Check if this would create an overpayment beyond reasonable change
            BigDecimal totalPaid = payments.stream()
                    .map(PaymentEntry::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal newTotal = totalPaid.add(amount);
            BigDecimal overpayment = newTotal.subtract(transaction.getTotalAmount());

            if (overpayment.compareTo(new BigDecimal("100.00")) > 0) {
                boolean confirm = AlertUtil.showConfirmation("Large Overpayment",
                        "This payment would result in " + currencyFormat.format(overpayment)
                        + " in change. Are you sure this is correct?");
                if (!confirm) {
                    return;
                }
            }

            // Add the payment
            PaymentEntry payment = new PaymentEntry(paymentMethod, amount);
            payments.add(payment);

            // Clear input fields
            txtPaymentAmount.clear();
            cmbPaymentMethod.setValue("CASH");

            updatePaymentSummary();

        } catch (NumberFormatException e) {
            AlertUtil.showWarning("Invalid Amount", "Please enter a valid payment amount.");
        }
    }

    @FXML
    private void handleRemovePayment() {
        removeSelectedPayment();
    }

    private void removeSelectedPayment() {
        PaymentEntry selected = tblPayments.getSelectionModel().getSelectedItem();
        if (selected != null) {
            payments.remove(selected);
            updatePaymentSummary();
        } else {
            AlertUtil.showWarning("No Selection", "Please select a payment to remove.");
        }
    }

    @FXML
    private void handleQuickAmount() {
        if (transaction == null) {
            return;
        }

        BigDecimal totalPaid = payments.stream()
                .map(PaymentEntry::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal remaining = transaction.getTotalAmount().subtract(totalPaid);

        if (remaining.compareTo(BigDecimal.ZERO) > 0) {
            txtPaymentAmount.setText(remaining.toString());
        }
    }

    @FXML
    private void handleCompleteTransaction() {
        if (payments.isEmpty()) {
            AlertUtil.showWarning("No Payments", "Please add at least one payment method.");
            return;
        }

        try {
            // Apply all payments to the transaction
            BigDecimal totalPaid = BigDecimal.ZERO;
            List<String> paymentMethods = new ArrayList<>();

            for (PaymentEntry payment : payments) {
                totalPaid = totalPaid.add(payment.getAmount());
                paymentMethods.add(payment.getPaymentMethod() + ": " + currencyFormat.format(payment.getAmount()));
            }

            // Set the primary payment method (first one) and create notes for others
            if (!payments.isEmpty()) {
                transaction.setPaymentMethod(payments.get(0).getPaymentMethod());

                if (payments.size() > 1) {
                    String paymentNotes = "Multiple payment methods used:\n" + String.join("\n", paymentMethods);
                    String existingNotes = transaction.getNotes();
                    if (existingNotes != null && !existingNotes.isEmpty()) {
                        transaction.setNotes(existingNotes + "\n\n" + paymentNotes);
                    } else {
                        transaction.setNotes(paymentNotes);
                    }
                }
            }

            // Process the payment as a complete transaction (not installment)
            if (totalPaid.compareTo(transaction.getTotalAmount()) >= 0) {
                // Full payment or overpayment - mark as COMPLETED
                transaction.processFullPayment(totalPaid);
                System.out.println("DEBUG: Multiple payment processed as COMPLETED transaction");
            } else {
                // Partial payment - mark as PARTIAL_PAYMENT
                transaction.processPartialPayment(totalPaid);
                System.out.println("DEBUG: Multiple payment processed as PARTIAL_PAYMENT transaction");
            }

            transactionCompleted = true;

            closeDialog();

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process payment: " + e.getMessage());
        }
    }

    @FXML
    private void handleCancel() {
        closeDialog();
    }

    private void closeDialog() {
        Stage stage = (Stage) btnCancel.getScene().getWindow();
        stage.close();
    }

    public boolean isTransactionCompleted() {
        return transactionCompleted;
    }

    /**
     * Get payment entries for history recording
     */
    public List<PaymentEntry> getPaymentEntries() {
        return new ArrayList<>(payments);
    }

    /**
     * Record payment history for all payment entries This should be called
     * after the transaction is saved to the database
     */
    public void recordPaymentHistory(Long transactionId) {
        if (transactionCompleted && transactionId != null) {
            try {
                PaymentHistoryService paymentHistoryService = PaymentHistoryService.getInstance();
                String cashierName = "System"; // In a real system, this would be the logged-in user

                for (PaymentEntry entry : payments) {
                    String notes = "Multiple payment method: " + entry.getPaymentMethod();
                    paymentHistoryService.recordPayment(
                            transactionId,
                            entry.getAmount(),
                            entry.getPaymentMethod(),
                            cashierName,
                            notes
                    );
                }
                System.out.println("DEBUG: Multiple payment history recorded for transaction " + transactionId);
            } catch (Exception e) {
                System.err.println("WARNING: Failed to record multiple payment history: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public List<PaymentEntry> getPayments() {
        return new ArrayList<>(payments);
    }

    public BigDecimal getTotalPaid() {
        return payments.stream()
                .map(PaymentEntry::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getChangeAmount() {
        if (transaction != null) {
            BigDecimal totalPaid = getTotalPaid();
            BigDecimal change = totalPaid.subtract(transaction.getTotalAmount());
            return change.compareTo(BigDecimal.ZERO) > 0 ? change : BigDecimal.ZERO;
        }
        return BigDecimal.ZERO;
    }

    /**
     * Data class for payment entries
     */
    public static class PaymentEntry {

        private final String paymentMethod;
        private final BigDecimal amount;

        public PaymentEntry(String paymentMethod, BigDecimal amount) {
            this.paymentMethod = paymentMethod;
            this.amount = amount;
        }

        public String getPaymentMethod() {
            return paymentMethod;
        }

        public BigDecimal getAmount() {
            return amount;
        }
    }
}
