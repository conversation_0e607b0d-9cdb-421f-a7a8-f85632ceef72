package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.ResourceBundle;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Product;
import com.clothingstore.util.AlertUtil;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.PropertyValueFactory;

/**
 * Controller for Low Stock Report interface
 */
public class LowStockReportController implements Initializable {

    @FXML private Button btnRefresh;
    @FXML private Button btnExport;
    @FXML private Button btnReorderSelected;
    @FXML private Button btnReorderAll;
    @FXML private Button btnUpdateMinStock;
    @FXML private Button btnPrintReport;

    @FXML private Label lblLowStockCount;
    @FXML private Label lblCriticalStockCount;
    @FXML private Label lblPotentialLoss;

    @FXML private TableView<LowStockItem> tblLowStockProducts;
    @FXML private TableColumn<LowStockItem, String> colSku;
    @FXML private TableColumn<LowStockItem, String> colName;
    @FXML private TableColumn<LowStockItem, String> colCategory;
    @FXML private TableColumn<LowStockItem, String> colCurrentStock;
    @FXML private TableColumn<LowStockItem, String> colMinStock;
    @FXML private TableColumn<LowStockItem, String> colStockDifference;
    @FXML private TableColumn<LowStockItem, String> colPrice;
    @FXML private TableColumn<LowStockItem, String> colPotentialLoss;
    @FXML private TableColumn<LowStockItem, String> colActions;

    private ObservableList<LowStockItem> lowStockData;
    private ProductDAO productDAO;
    private NumberFormat currencyFormat;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        productDAO = ProductDAO.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        lowStockData = FXCollections.observableArrayList();
        
        setupTable();
        loadLowStockData();
    }

    private void setupTable() {
        colSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colCurrentStock.setCellValueFactory(new PropertyValueFactory<>("currentStock"));
        colMinStock.setCellValueFactory(new PropertyValueFactory<>("minStock"));
        colStockDifference.setCellValueFactory(new PropertyValueFactory<>("stockDifference"));
        colPrice.setCellValueFactory(new PropertyValueFactory<>("price"));
        colPotentialLoss.setCellValueFactory(new PropertyValueFactory<>("potentialLoss"));
        
        // Action buttons column
        colActions.setCellFactory(col -> new TableCell<LowStockItem, String>() {
            private final Button reorderBtn = new Button("Reorder");
            
            {
                reorderBtn.setOnAction(e -> {
                    LowStockItem item = getTableView().getItems().get(getIndex());
                    handleReorderProduct(item);
                });
                reorderBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
            }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(reorderBtn);
                }
            }
        });
        
        tblLowStockProducts.setItems(lowStockData);
    }

    private void loadLowStockData() {
        try {
            List<Product> lowStockProducts = productDAO.findLowStockProducts();
            lowStockData.clear();
            
            int criticalCount = 0;
            BigDecimal totalPotentialLoss = BigDecimal.ZERO;
            
            for (Product product : lowStockProducts) {
                int shortage = product.getMinStockLevel() - product.getStockQuantity();
                BigDecimal potentialLoss = product.getPrice().multiply(BigDecimal.valueOf(shortage));
                totalPotentialLoss = totalPotentialLoss.add(potentialLoss);
                
                if (product.getStockQuantity() <= 2) { // Critical stock threshold
                    criticalCount++;
                }
                
                LowStockItem item = new LowStockItem();
                item.setSku(product.getSku());
                item.setName(product.getName());
                item.setCategory(product.getCategory());
                item.setCurrentStock(String.valueOf(product.getStockQuantity()));
                item.setMinStock(String.valueOf(product.getMinStockLevel()));
                item.setStockDifference(String.valueOf(shortage));
                item.setPrice(currencyFormat.format(product.getPrice()));
                item.setPotentialLoss(currencyFormat.format(potentialLoss));
                item.setProduct(product); // Store reference for actions
                
                lowStockData.add(item);
            }
            
            // Update summary labels
            lblLowStockCount.setText(String.valueOf(lowStockProducts.size()));
            lblCriticalStockCount.setText(String.valueOf(criticalCount));
            lblPotentialLoss.setText(currencyFormat.format(totalPotentialLoss));
            
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load low stock data: " + e.getMessage());
        }
    }

    @FXML
    private void handleRefresh() {
        loadLowStockData();
        AlertUtil.showInfo("Refreshed", "Low stock report has been refreshed.");
    }

    @FXML
    private void handleExport() {
        AlertUtil.showInfo("Export", "Low stock report export functionality will be implemented in future version.");
    }

    @FXML
    private void handleReorderSelected() {
        LowStockItem selected = tblLowStockProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleReorderProduct(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to reorder.");
        }
    }

    @FXML
    private void handleReorderAll() {
        if (lowStockData.isEmpty()) {
            AlertUtil.showInfo("No Low Stock", "No products are currently low on stock.");
            return;
        }
        
        if (AlertUtil.showConfirmation("Reorder All", 
            "This will create reorder requests for all " + lowStockData.size() + " low stock products. Continue?")) {
            
            StringBuilder report = new StringBuilder("Reorder Report:\n\n");
            for (LowStockItem item : lowStockData) {
                int reorderQty = Integer.parseInt(item.getStockDifference()) + 10; // Add buffer
                report.append(String.format("• %s (%s): Reorder %d units\n", 
                    item.getName(), item.getSku(), reorderQty));
            }
            
            AlertUtil.showInfo("Reorder All Products", report.toString());
        }
    }

    @FXML
    private void handleUpdateMinStock() {
        AlertUtil.showInfo("Update Min Stock", "Minimum stock level update functionality will be implemented in future version.");
    }

    @FXML
    private void handlePrintReport() {
        AlertUtil.showInfo("Print Report", "Print functionality will be implemented in future version.");
    }

    private void handleReorderProduct(LowStockItem item) {
        String input = AlertUtil.showTextInput("Reorder Product",
            "Product: " + item.getName() + "\nCurrent Stock: " + item.getCurrentStock() +
            "\nMin Stock: " + item.getMinStock(),
            "Enter quantity to reorder:").orElse(null);

        if (input != null && !input.trim().isEmpty()) {
            try {
                int quantity = Integer.parseInt(input.trim());
                if (quantity > 0) {
                    AlertUtil.showInfo("Reorder Confirmed", 
                        String.format("Reorder request created:\nProduct: %s\nQuantity: %d units\nEstimated Cost: %s", 
                        item.getName(), quantity, 
                        currencyFormat.format(item.getProduct().getPrice().multiply(BigDecimal.valueOf(quantity)))));
                } else {
                    AlertUtil.showError("Invalid Quantity", "Please enter a positive quantity.");
                }
            } catch (NumberFormatException e) {
                AlertUtil.showError("Invalid Input", "Please enter a valid number.");
            }
        }
    }

    // Inner class for table data
    public static class LowStockItem {
        private String sku;
        private String name;
        private String category;
        private String currentStock;
        private String minStock;
        private String stockDifference;
        private String price;
        private String potentialLoss;
        private Product product; // Reference to original product

        // Getters and setters
        public String getSku() { return sku; }
        public void setSku(String sku) { this.sku = sku; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        
        public String getCurrentStock() { return currentStock; }
        public void setCurrentStock(String currentStock) { this.currentStock = currentStock; }
        
        public String getMinStock() { return minStock; }
        public void setMinStock(String minStock) { this.minStock = minStock; }
        
        public String getStockDifference() { return stockDifference; }
        public void setStockDifference(String stockDifference) { this.stockDifference = stockDifference; }
        
        public String getPrice() { return price; }
        public void setPrice(String price) { this.price = price; }
        
        public String getPotentialLoss() { return potentialLoss; }
        public void setPotentialLoss(String potentialLoss) { this.potentialLoss = potentialLoss; }
        
        public Product getProduct() { return product; }
        public void setProduct(Product product) { this.product = product; }
    }
}
