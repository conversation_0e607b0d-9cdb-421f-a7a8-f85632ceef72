package com.clothingstore.view;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.SupplierDAO;
import com.clothingstore.model.Product;
import com.clothingstore.model.Supplier;
import com.clothingstore.service.InventoryService;
import com.clothingstore.util.AlertUtil;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Controller for Advanced Inventory Management
 */
public class AdvancedInventoryController implements Initializable {

    // FXML Components - Tabs
    @FXML private TabPane mainTabPane;
    @FXML private Tab stockLevelsTab;
    @FXML private Tab reorderTab;
    @FXML private Tab adjustmentsTab;
    @FXML private Tab movementsTab;

    // Stock Levels Tab
    @FXML private TableView<Product> stockLevelsTable;
    @FXML private TableColumn<Product, String> productNameColumn;
    @FXML private TableColumn<Product, String> skuColumn;
    @FXML private TableColumn<Product, String> currentStockColumn;
    @FXML private TableColumn<Product, String> minStockColumn;
    @FXML private TableColumn<Product, String> reorderQtyColumn;
    @FXML private TableColumn<Product, String> stockStatusColumn;
    @FXML private TableColumn<Product, String> stockValueColumn;

    // Reorder Management Tab
    @FXML private TableView<ReorderItem> reorderTable;
    @FXML private TableColumn<ReorderItem, String> reorderProductColumn;
    @FXML private TableColumn<ReorderItem, String> reorderSupplierColumn;
    @FXML private TableColumn<ReorderItem, String> reorderCurrentStockColumn;
    @FXML private TableColumn<ReorderItem, String> reorderMinStockColumn;
    @FXML private TableColumn<ReorderItem, String> reorderSuggestedQtyColumn;
    @FXML private TableColumn<ReorderItem, String> reorderActionsColumn;

    // Stock Adjustments Tab
    @FXML private ComboBox<Product> adjustmentProductCombo;
    @FXML private TextField adjustmentQuantityField;
    @FXML private ComboBox<String> adjustmentTypeCombo;
    @FXML private TextArea adjustmentReasonField;
    @FXML private Button submitAdjustmentButton;
    @FXML private TableView<StockAdjustment> adjustmentsHistoryTable;

    // Stock Movements Tab
    @FXML private DatePicker movementStartDate;
    @FXML private DatePicker movementEndDate;
    @FXML private ComboBox<String> movementTypeFilter;
    @FXML private TableView<StockMovement> movementsTable;

    // Filter Controls
    @FXML private TextField searchField;
    @FXML private ComboBox<String> categoryFilter;
    @FXML private ComboBox<String> stockStatusFilter;
    @FXML private ComboBox<Supplier> supplierFilter;

    // Summary Labels
    @FXML private Label totalProductsLabel;
    @FXML private Label lowStockCountLabel;
    @FXML private Label outOfStockCountLabel;
    @FXML private Label totalInventoryValueLabel;
    @FXML private Label reorderRequiredLabel;

    // Action Buttons
    @FXML private Button refreshDataButton;
    @FXML private Button generateReorderReportButton;
    @FXML private Button bulkAdjustmentButton;
    @FXML private Button exportDataButton;

    // Data and Services
    private ProductDAO productDAO;
    private SupplierDAO supplierDAO;
    private InventoryService inventoryService;
    private ObservableList<Product> allProducts;
    private ObservableList<Product> filteredProducts;
    private ObservableList<ReorderItem> reorderItems;
    private ObservableList<StockAdjustment> adjustmentHistory;
    private ObservableList<StockMovement> stockMovements;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateTimeFormatter;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeServices();
        initializeFormatters();
        initializeTableColumns();
        initializeComboBoxes();
        initializeEventHandlers();
        loadInitialData();
    }

    private void initializeServices() {
        productDAO = ProductDAO.getInstance();
        supplierDAO = SupplierDAO.getInstance();
        inventoryService = new InventoryService();
        
        allProducts = FXCollections.observableArrayList();
        filteredProducts = FXCollections.observableArrayList();
        reorderItems = FXCollections.observableArrayList();
        adjustmentHistory = FXCollections.observableArrayList();
        stockMovements = FXCollections.observableArrayList();
        
        stockLevelsTable.setItems(filteredProducts);
        reorderTable.setItems(reorderItems);
        adjustmentsHistoryTable.setItems(adjustmentHistory);
        movementsTable.setItems(stockMovements);
    }

    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");
    }

    private void initializeTableColumns() {
        // Stock Levels Table
        productNameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        skuColumn.setCellValueFactory(new PropertyValueFactory<>("sku"));
        currentStockColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(String.valueOf(cellData.getValue().getStockQuantity())));
        minStockColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(String.valueOf(cellData.getValue().getMinStockLevel())));
        reorderQtyColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(String.valueOf(cellData.getValue().getReorderQuantity())));
        
        stockStatusColumn.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            String status;
            if (product.getStockQuantity() == 0) {
                status = "Out of Stock";
            } else if (product.isLowStock()) {
                status = "Low Stock";
            } else {
                status = "In Stock";
            }
            return new SimpleStringProperty(status);
        });
        
        stockValueColumn.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            BigDecimal value = product.getPrice().multiply(new BigDecimal(product.getStockQuantity()));
            return new SimpleStringProperty(currencyFormat.format(value));
        });

        // Reorder Table
        reorderProductColumn.setCellValueFactory(new PropertyValueFactory<>("productName"));
        reorderSupplierColumn.setCellValueFactory(new PropertyValueFactory<>("supplierName"));
        reorderCurrentStockColumn.setCellValueFactory(new PropertyValueFactory<>("currentStock"));
        reorderMinStockColumn.setCellValueFactory(new PropertyValueFactory<>("minStock"));
        reorderSuggestedQtyColumn.setCellValueFactory(new PropertyValueFactory<>("suggestedQuantity"));
        
        reorderActionsColumn.setCellFactory(column -> new TableCell<ReorderItem, String>() {
            private final Button orderButton = new Button("Order");
            private final Button skipButton = new Button("Skip");
            
            {
                orderButton.setOnAction(e -> {
                    ReorderItem item = getTableView().getItems().get(getIndex());
                    handleCreatePurchaseOrder(item);
                });
                
                skipButton.setOnAction(e -> {
                    ReorderItem item = getTableView().getItems().get(getIndex());
                    handleSkipReorder(item);
                });
            }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(new HBox(5, orderButton, skipButton));
                }
            }
        });

        // Add row factory for color coding stock levels
        stockLevelsTable.setRowFactory(tv -> new TableRow<Product>() {
            @Override
            protected void updateItem(Product product, boolean empty) {
                super.updateItem(product, empty);
                if (empty || product == null) {
                    setStyle("");
                } else if (product.getStockQuantity() == 0) {
                    setStyle("-fx-background-color: #ffebee;"); // Light red for out of stock
                } else if (product.isLowStock()) {
                    setStyle("-fx-background-color: #fff3e0;"); // Light orange for low stock
                } else {
                    setStyle("-fx-background-color: #e8f5e8;"); // Light green for good stock
                }
            }
        });
    }

    private void initializeComboBoxes() {
        // Stock status filter
        stockStatusFilter.setItems(FXCollections.observableArrayList(
            "All", "In Stock", "Low Stock", "Out of Stock"));
        stockStatusFilter.setValue("All");

        // Adjustment type
        adjustmentTypeCombo.setItems(FXCollections.observableArrayList(
            "Stock In", "Stock Out", "Correction", "Damage", "Theft", "Transfer"));

        // Movement type filter
        movementTypeFilter.setItems(FXCollections.observableArrayList(
            "All", "Sale", "Purchase", "Adjustment", "Transfer", "Return"));
        movementTypeFilter.setValue("All");

        loadCategories();
        loadSuppliers();
        loadProducts();
    }

    private void loadCategories() {
        try {
            List<String> categories = productDAO.getAllCategories();
            ObservableList<String> categoryItems = FXCollections.observableArrayList("All Categories");
            categoryItems.addAll(categories);
            categoryFilter.setItems(categoryItems);
            categoryFilter.setValue("All Categories");
        } catch (SQLException e) {
            AlertUtil.showError("Load Error", "Failed to load categories: " + e.getMessage());
        }
    }

    private void loadSuppliers() {
        try {
            List<Supplier> suppliers = supplierDAO.findAll();
            ObservableList<Supplier> supplierItems = FXCollections.observableArrayList();
            Supplier allSupplier = new Supplier("All Suppliers", "");
            supplierItems.add(allSupplier); // Placeholder for "All"
            supplierItems.addAll(suppliers);
            supplierFilter.setItems(supplierItems);
            supplierFilter.setValue(supplierItems.get(0));
        } catch (SQLException e) {
            AlertUtil.showError("Load Error", "Failed to load suppliers: " + e.getMessage());
        }
    }

    private void loadProducts() {
        try {
            List<Product> products = productDAO.findAll();
            adjustmentProductCombo.setItems(FXCollections.observableArrayList(products));
        } catch (SQLException e) {
            AlertUtil.showError("Load Error", "Failed to load products: " + e.getMessage());
        }
    }

    private void initializeEventHandlers() {
        // Search and filter handlers
        searchField.textProperty().addListener((obs, oldText, newText) -> applyFilters());
        categoryFilter.setOnAction(e -> applyFilters());
        stockStatusFilter.setOnAction(e -> applyFilters());
        supplierFilter.setOnAction(e -> applyFilters());

        // Tab selection handler
        mainTabPane.getSelectionModel().selectedItemProperty().addListener((obs, oldTab, newTab) -> {
            if (newTab == reorderTab) {
                loadReorderData();
            } else if (newTab == adjustmentsTab) {
                loadAdjustmentHistory();
            } else if (newTab == movementsTab) {
                loadMovementHistory();
            }
        });
    }

    private void loadInitialData() {
        refreshStockData();
        updateSummaryLabels();
    }

    @FXML
    private void refreshStockData() {
        try {
            List<Product> products = productDAO.findAll();
            allProducts.setAll(products);
            applyFilters();
            updateSummaryLabels();
        } catch (SQLException e) {
            AlertUtil.showError("Load Error", "Failed to load stock data: " + e.getMessage());
        }
    }

    private void applyFilters() {
        String searchText = searchField.getText().toLowerCase().trim();
        String selectedCategory = categoryFilter.getValue();
        String selectedStatus = stockStatusFilter.getValue();
        Supplier selectedSupplier = supplierFilter.getValue();

        filteredProducts.setAll(allProducts.stream()
            .filter(product -> {
                // Search filter
                if (!searchText.isEmpty()) {
                    return product.getName().toLowerCase().contains(searchText) ||
                           product.getSku().toLowerCase().contains(searchText) ||
                           (product.getBarcode() != null && product.getBarcode().toLowerCase().contains(searchText));
                }
                return true;
            })
            .filter(product -> {
                // Category filter
                return "All Categories".equals(selectedCategory) || 
                       selectedCategory.equals(product.getCategory());
            })
            .filter(product -> {
                // Stock status filter
                if ("All".equals(selectedStatus)) return true;
                switch (selectedStatus) {
                    case "In Stock": return product.getStockQuantity() > product.getMinStockLevel();
                    case "Low Stock": return product.isLowStock() && product.getStockQuantity() > 0;
                    case "Out of Stock": return product.getStockQuantity() == 0;
                    default: return true;
                }
            })
            .filter(product -> {
                // Supplier filter
                return selectedSupplier == null || 
                       "All Suppliers".equals(selectedSupplier.getCompanyName()) ||
                       (product.getSupplierId() != null && product.getSupplierId().equals(selectedSupplier.getId()));
            })
            .collect(Collectors.toList()));
    }

    private void updateSummaryLabels() {
        int totalProducts = allProducts.size();
        long lowStockCount = allProducts.stream().mapToLong(p -> p.isLowStock() ? 1 : 0).sum();
        long outOfStockCount = allProducts.stream().mapToLong(p -> p.getStockQuantity() == 0 ? 1 : 0).sum();
        
        BigDecimal totalValue = allProducts.stream()
            .map(p -> p.getPrice().multiply(new BigDecimal(p.getStockQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        long reorderRequired = allProducts.stream()
            .mapToLong(p -> (p.isLowStock() || p.getStockQuantity() == 0) ? 1 : 0)
            .sum();

        totalProductsLabel.setText(String.valueOf(totalProducts));
        lowStockCountLabel.setText(String.valueOf(lowStockCount));
        outOfStockCountLabel.setText(String.valueOf(outOfStockCount));
        totalInventoryValueLabel.setText(currencyFormat.format(totalValue));
        reorderRequiredLabel.setText(String.valueOf(reorderRequired));
    }

    private void loadReorderData() {
        List<ReorderItem> items = allProducts.stream()
            .filter(p -> p.isLowStock() || p.getStockQuantity() == 0)
            .map(this::createReorderItem)
            .collect(Collectors.toList());
        
        reorderItems.setAll(items);
    }

    private ReorderItem createReorderItem(Product product) {
        String supplierName = "Unknown Supplier";
        if (product.getSupplierId() != null) {
            try {
                Optional<Supplier> supplierOpt = supplierDAO.findById(product.getSupplierId());
                if (supplierOpt.isPresent()) {
                    supplierName = supplierOpt.get().getCompanyName();
                }
            } catch (SQLException e) {
                // Use default supplier name
            }
        }
        
        int suggestedQuantity = Math.max(product.getReorderQuantity(), 
                                       product.getMinStockLevel() - product.getStockQuantity());
        
        return new ReorderItem(
            product.getId(),
            product.getName(),
            supplierName,
            product.getStockQuantity(),
            product.getMinStockLevel(),
            suggestedQuantity
        );
    }

    private void loadAdjustmentHistory() {
        // TODO: Load from database when StockAdjustment table is implemented
        adjustmentHistory.clear();
    }

    private void loadMovementHistory() {
        // TODO: Load from database when StockMovement table is implemented
        stockMovements.clear();
    }

    @FXML
    private void handleSubmitAdjustment() {
        Product selectedProduct = adjustmentProductCombo.getValue();
        String quantityText = adjustmentQuantityField.getText().trim();
        String adjustmentType = adjustmentTypeCombo.getValue();
        String reason = adjustmentReasonField.getText().trim();

        if (selectedProduct == null) {
            AlertUtil.showWarning("Validation Error", "Please select a product.");
            return;
        }

        if (quantityText.isEmpty()) {
            AlertUtil.showWarning("Validation Error", "Please enter a quantity.");
            return;
        }

        if (adjustmentType == null) {
            AlertUtil.showWarning("Validation Error", "Please select an adjustment type.");
            return;
        }

        try {
            int quantity = Integer.parseInt(quantityText);
            
            // Apply the adjustment
            if ("Stock In".equals(adjustmentType) || "Correction".equals(adjustmentType)) {
                selectedProduct.addStock(quantity);
            } else {
                selectedProduct.reduceStock(quantity);
            }
            
            // Update in database
            productDAO.save(selectedProduct);
            
            // Clear form
            adjustmentQuantityField.clear();
            adjustmentReasonField.clear();
            adjustmentTypeCombo.setValue(null);
            
            // Refresh data
            refreshStockData();
            
            AlertUtil.showInfo("Success", "Stock adjustment applied successfully.");
            
        } catch (NumberFormatException e) {
            AlertUtil.showWarning("Validation Error", "Please enter a valid quantity.");
        } catch (Exception e) {
            AlertUtil.showError("Error", "Failed to apply stock adjustment: " + e.getMessage());
        }
    }

    private void handleCreatePurchaseOrder(ReorderItem item) {
        // TODO: Integrate with purchase order system
        AlertUtil.showInfo("Purchase Order", 
            "Purchase order functionality will be implemented in a future update.\n\n" +
            "Product: " + item.getProductName() + "\n" +
            "Suggested Quantity: " + item.getSuggestedQuantity());
    }

    private void handleSkipReorder(ReorderItem item) {
        reorderItems.remove(item);
        AlertUtil.showInfo("Skipped", "Reorder skipped for " + item.getProductName());
    }

    @FXML
    private void handleGenerateReorderReport() {
        // TODO: Generate comprehensive reorder report
        AlertUtil.showInfo("Reorder Report", "Reorder report generation will be implemented in a future update.");
    }

    @FXML
    private void handleBulkAdjustment() {
        // TODO: Implement bulk stock adjustment functionality
        AlertUtil.showInfo("Bulk Adjustment", "Bulk adjustment functionality will be implemented in a future update.");
    }

    @FXML
    private void handleExportData() {
        // TODO: Export inventory data to CSV/Excel
        AlertUtil.showInfo("Export", "Data export functionality will be implemented in a future update.");
    }

    // Data classes
    public static class ReorderItem {
        private Long productId;
        private String productName;
        private String supplierName;
        private int currentStock;
        private int minStock;
        private int suggestedQuantity;

        public ReorderItem(Long productId, String productName, String supplierName, 
                          int currentStock, int minStock, int suggestedQuantity) {
            this.productId = productId;
            this.productName = productName;
            this.supplierName = supplierName;
            this.currentStock = currentStock;
            this.minStock = minStock;
            this.suggestedQuantity = suggestedQuantity;
        }

        // Getters
        public Long getProductId() { return productId; }
        public String getProductName() { return productName; }
        public String getSupplierName() { return supplierName; }
        public int getCurrentStock() { return currentStock; }
        public int getMinStock() { return minStock; }
        public int getSuggestedQuantity() { return suggestedQuantity; }
    }

    public static class StockAdjustment {
        private LocalDateTime adjustmentDate;
        private String productName;
        private String adjustmentType;
        private int quantity;
        private String reason;

        // Constructor and getters would be here
    }

    public static class StockMovement {
        private LocalDateTime movementDate;
        private String productName;
        private String movementType;
        private int quantity;
        private String reference;

        // Constructor and getters would be here
    }
}
