/* Enhanced POS System Styles */

/* Root Variables for Consistent Theming */
.root {
    -fx-primary-color: #3498db;
    -fx-secondary-color: #2c3e50;
    -fx-success-color: #27ae60;
    -fx-warning-color: #f39c12;
    -fx-danger-color: #e74c3c;
    -fx-light-gray: #ecf0f1;
    -fx-dark-gray: #34495e;
    -fx-text-color: #2c3e50;
    -fx-background-color: #ffffff;
}

/* Payment Status Indicators */
.payment-status-completed {
    -fx-background-color: #d5f4e6;
    -fx-border-color: #27ae60;
    -fx-border-width: 2px;
    -fx-border-radius: 5px;
    -fx-padding: 5px 10px;
}

.payment-status-partial {
    -fx-background-color: #fef9e7;
    -fx-border-color: #f39c12;
    -fx-border-width: 2px;
    -fx-border-radius: 5px;
    -fx-padding: 5px 10px;
}

.payment-status-pending {
    -fx-background-color: #fdf2e9;
    -fx-border-color: #e67e22;
    -fx-border-width: 2px;
    -fx-border-radius: 5px;
    -fx-padding: 5px 10px;
}

.payment-status-overdue {
    -fx-background-color: #fadbd8;
    -fx-border-color: #e74c3c;
    -fx-border-width: 2px;
    -fx-border-radius: 5px;
    -fx-padding: 5px 10px;
}

/* Enhanced Buttons */
.btn-primary {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10px 20px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-cursor: hand;
}

.btn-primary:hover {
    -fx-background-color: #2980b9;
}

.btn-success {
    -fx-background-color: #27ae60;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10px 20px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-cursor: hand;
}

.btn-success:hover {
    -fx-background-color: #229954;
}

.btn-warning {
    -fx-background-color: #f39c12;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10px 20px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-cursor: hand;
}

.btn-warning:hover {
    -fx-background-color: #e67e22;
}

.btn-danger {
    -fx-background-color: #e74c3c;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10px 20px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-cursor: hand;
}

.btn-danger:hover {
    -fx-background-color: #c0392b;
}

/* Enhanced Cards */
.card {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.card-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 10px 15px;
    -fx-font-weight: bold;
    -fx-font-size: 16px;
}

/* Enhanced Tables */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
}

.table-view .column-header {
    -fx-background-color: #34495e;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10px;
}

.table-view .table-row-cell {
    -fx-border-color: transparent;
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: #ecf0f1;
}

.table-view .table-row-cell:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.table-view .table-row-cell:hover {
    -fx-background-color: #ecf0f1;
}

/* Enhanced Form Controls */
.text-field, .text-area, .combo-box {
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 8px 12px;
    -fx-font-size: 14px;
}

.text-field:focused, .text-area:focused, .combo-box:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2px;
}

/* Payment Amount Display */
.amount-display {
    -fx-font-family: "Courier New", monospace;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-padding: 10px;
    -fx-alignment: center-right;
}

.amount-positive {
    -fx-text-fill: #27ae60;
}

.amount-negative {
    -fx-text-fill: #e74c3c;
}

.amount-zero {
    -fx-text-fill: #7f8c8d;
}

/* Outstanding Balance Indicators */
.balance-high {
    -fx-background-color: #fadbd8;
    -fx-text-fill: #c0392b;
    -fx-font-weight: bold;
}

.balance-medium {
    -fx-background-color: #fef9e7;
    -fx-text-fill: #d68910;
    -fx-font-weight: bold;
}

.balance-low {
    -fx-background-color: #d5f4e6;
    -fx-text-fill: #196f3d;
    -fx-font-weight: bold;
}

/* Progress Indicators */
.progress-bar {
    -fx-background-color: #ecf0f1;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
}

.progress-bar .bar {
    -fx-background-color: #3498db;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
}

.progress-bar.success .bar {
    -fx-background-color: #27ae60;
}

.progress-bar.warning .bar {
    -fx-background-color: #f39c12;
}

.progress-bar.danger .bar {
    -fx-background-color: #e74c3c;
}

/* Responsive Layout Helpers */
.responsive-grid {
    -fx-hgap: 15px;
    -fx-vgap: 15px;
    -fx-padding: 15px;
}

.responsive-hbox {
    -fx-spacing: 15px;
    -fx-alignment: center-left;
    -fx-padding: 10px;
}

.responsive-vbox {
    -fx-spacing: 10px;
    -fx-padding: 10px;
}

/* Status Badges */
.status-badge {
    -fx-padding: 4px 8px;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.status-completed {
    -fx-background-color: #27ae60;
}

.status-pending {
    -fx-background-color: #f39c12;
}

.status-partial {
    -fx-background-color: #e67e22;
}

.status-overdue {
    -fx-background-color: #e74c3c;
}

.status-cancelled {
    -fx-background-color: #95a5a6;
}

/* Enhanced Dialogs */
.dialog-pane {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.dialog-pane .header-panel {
    -fx-background-color: #34495e;
    -fx-text-fill: white;
    -fx-padding: 15px;
    -fx-font-weight: bold;
    -fx-font-size: 16px;
}

.dialog-pane .content {
    -fx-padding: 20px;
}

/* Sidebar Navigation */
.nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: #ecf0f1;
    -fx-font-size: 14px;
    -fx-padding: 12px 15px;
    -fx-alignment: center-left;
    -fx-cursor: hand;
    -fx-border-width: 0;
}

.nav-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
}

.nav-button.selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

/* Animations and Transitions */
.fade-in {
    -fx-opacity: 0;
}

.slide-in {
    -fx-translate-x: -100;
}

/* Enhanced Customer Section Styles */
.btn-clear {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 50%;
    -fx-border-radius: 50%;
    -fx-padding: 4px 8px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.btn-clear:hover {
    -fx-background-color: #c82333;
}

.btn-remove {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
    -fx-font-weight: bold;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-padding: 4px 8px;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.btn-remove:hover {
    -fx-background-color: #e0a800;
}

.btn-action {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-padding: 6px 12px;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.btn-action:hover {
    -fx-background-color: #138496;
}

.search-results-label {
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-text-fill: #6c757d;
}

.search-results-scroll {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.selected-customer-info {
    -fx-background-color: #e8f5e8;
    -fx-border-color: #28a745;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px;
}

.customer-name {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #155724;
}

.customer-detail {
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
}

.customer-points {
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
}

.walk-in-customer {
    -fx-font-size: 12px;
    -fx-font-style: italic;
    -fx-text-fill: #6c757d;
    -fx-padding: 8px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

/* Print Styles */
@media print {
    .no-print {
        -fx-opacity: 0;
    }
}
