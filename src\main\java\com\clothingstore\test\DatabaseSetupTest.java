package com.clothingstore.test;

import com.clothingstore.util.DatabaseSetupUtil;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.Separator;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Test application to demonstrate the enhanced database setup dialog with
 * comprehensive progress tracking and statistics
 */
public class DatabaseSetupTest extends Application {

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("Database Setup Test - Enhanced Progress Tracking");

        Label titleLabel = new Label("Enhanced Database Setup Dialog Test");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Label descriptionLabel = new Label(
                "This test will demonstrate the enhanced database setup dialog with comprehensive progress tracking.\n\n"
                + "Features being tested:\n"
                + "• Real-time operation tracking (successful vs failed operations)\n"
                + "• Financial impact monitoring (transaction amounts, refunds processed)\n"
                + "• Detailed progress information with specific counts and statistics\n"
                + "• Comprehensive success/failure summary with performance metrics\n"
                + "• Database schema updates (tables created, indexes built)\n"
                + "• Transaction data analysis and refund tracking setup\n\n"
                + "Click the button below to launch the enhanced setup dialog:"
        );
        descriptionLabel.setWrapText(true);
        descriptionLabel.setPrefWidth(500);
        descriptionLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #34495e;");

        Button testButton = new Button("Launch Enhanced Database Setup Dialog");
        testButton.setStyle(
                "-fx-font-size: 14px; "
                + "-fx-padding: 12px 24px; "
                + "-fx-background-color: #3498db; "
                + "-fx-text-fill: white; "
                + "-fx-font-weight: bold; "
                + "-fx-background-radius: 5px; "
                + "-fx-cursor: hand;"
        );
        testButton.setOnAction(e -> {
            // Run the enhanced database setup dialog
            DatabaseSetupUtil.showDatabaseSetupDialog();
        });

        Button quickSetupButton = new Button("Run Quick Setup (Command Line)");
        quickSetupButton.setStyle(
                "-fx-font-size: 12px; "
                + "-fx-padding: 8px 16px; "
                + "-fx-background-color: #27ae60; "
                + "-fx-text-fill: white; "
                + "-fx-background-radius: 5px; "
                + "-fx-cursor: hand;"
        );
        quickSetupButton.setOnAction(e -> {
            // Run quick setup in background thread
            new Thread(() -> {
                System.out.println("=== RUNNING QUICK SETUP TEST ===");
                boolean success = DatabaseSetupUtil.quickSetup();
                System.out.println("Quick setup result: " + (success ? "SUCCESS" : "FAILED"));
                System.out.println("=== QUICK SETUP TEST COMPLETE ===");
            }).start();
        });

        Button exitButton = new Button("Exit");
        exitButton.setStyle(
                "-fx-font-size: 12px; "
                + "-fx-padding: 8px 16px; "
                + "-fx-background-color: #e74c3c; "
                + "-fx-text-fill: white; "
                + "-fx-background-radius: 5px; "
                + "-fx-cursor: hand;"
        );
        exitButton.setOnAction(e -> Platform.exit());

        Separator separator = new Separator();

        VBox root = new VBox(15);
        root.getChildren().addAll(titleLabel, descriptionLabel, testButton, separator, quickSetupButton, exitButton);
        root.setStyle("-fx-padding: 30px; -fx-background-color: #ecf0f1;");
        root.setAlignment(Pos.CENTER);

        Scene scene = new Scene(root, 600, 450);
        primaryStage.setScene(scene);
        primaryStage.show();

        // Center the window
        primaryStage.centerOnScreen();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
