package com.clothingstore.service;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;

import com.clothingstore.dao.MissingItemDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.MissingItem;
import com.clothingstore.model.Product;

/**
 * Service class for inventory management operations
 */
public class InventoryService {

    private final MissingItemDAO missingItemDAO;
    private final ProductDAO productDAO;

    public InventoryService() {
        this.missingItemDAO = MissingItemDAO.getInstance();
        this.productDAO = ProductDAO.getInstance();
    }

    /**
     * Report a missing item
     */
    public MissingItem reportMissingItem(MissingItem missingItem) throws SQLException {
        // Validate the missing item
        if (missingItem.getProduct() == null) {
            throw new IllegalArgumentException("Product cannot be null");
        }

        if (missingItem.getQuantity() <= 0) {
            throw new IllegalArgumentException("Quantity must be greater than 0");
        }

        if (missingItem.getReason() == null || missingItem.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("Reason cannot be empty");
        }

        // Save the missing item
        MissingItem savedItem = missingItemDAO.save(missingItem);

        // Update product stock if needed (reduce by missing quantity)
        Product product = missingItem.getProduct();
        int newStock = Math.max(0, product.getStockQuantity() - missingItem.getQuantity());
        productDAO.updateStock(product.getId(), newStock);

        return savedItem;
    }

    /**
     * Get all missing items within a date range
     */
    public List<MissingItem> getMissingItems(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        return missingItemDAO.findByDateRange(startDate, endDate);
    }

    /**
     * Get all missing items
     */
    public List<MissingItem> getAllMissingItems() throws SQLException {
        return missingItemDAO.findAll();
    }

    /**
     * Get missing items by status
     */
    public List<MissingItem> getMissingItemsByStatus(String status) throws SQLException {
        return missingItemDAO.findByStatus(status);
    }

    /**
     * Resolve a missing item
     */
    public void resolveMissingItem(Long missingItemId, String resolvedBy, String notes) throws SQLException {
        MissingItem item = missingItemDAO.findById(missingItemId)
                .orElseThrow(() -> new IllegalArgumentException("Missing item not found"));

        item.resolve(resolvedBy, notes);
        missingItemDAO.save(item);
    }

    /**
     * Write off a missing item
     */
    public void writeOffMissingItem(Long missingItemId, String resolvedBy, String notes) throws SQLException {
        MissingItem item = missingItemDAO.findById(missingItemId)
                .orElseThrow(() -> new IllegalArgumentException("Missing item not found"));

        item.writeOff(resolvedBy, notes);
        missingItemDAO.save(item);
    }

    /**
     * Get missing items summary statistics
     */
    public MissingItemsSummary getMissingItemsSummary() throws SQLException {
        List<MissingItem> allItems = getAllMissingItems();

        int totalReported = allItems.size();
        int resolved = (int) allItems.stream().filter(item -> "RESOLVED".equals(item.getStatus())).count();
        int writtenOff = (int) allItems.stream().filter(item -> "WRITTEN_OFF".equals(item.getStatus())).count();
        int pending = totalReported - resolved - writtenOff;

        return new MissingItemsSummary(totalReported, resolved, writtenOff, pending);
    }

    /**
     * Summary statistics for missing items
     */
    public static class MissingItemsSummary {

        private final int totalReported;
        private final int resolved;
        private final int writtenOff;
        private final int pending;

        public MissingItemsSummary(int totalReported, int resolved, int writtenOff, int pending) {
            this.totalReported = totalReported;
            this.resolved = resolved;
            this.writtenOff = writtenOff;
            this.pending = pending;
        }

        public int getTotalReported() {
            return totalReported;
        }

        public int getResolved() {
            return resolved;
        }

        public int getWrittenOff() {
            return writtenOff;
        }

        public int getPending() {
            return pending;
        }
    }
}
