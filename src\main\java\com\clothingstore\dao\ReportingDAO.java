package com.clothingstore.dao;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.clothingstore.database.DatabaseManager;

/**
 * Data Access Object for Advanced Reporting operations
 */
public class ReportingDAO {

    private static final Logger LOGGER = Logger.getLogger(ReportingDAO.class.getName());
    private static ReportingDAO instance;

    private ReportingDAO() {
        // Private constructor for singleton
    }

    public static synchronized ReportingDAO getInstance() {
        if (instance == null) {
            instance = new ReportingDAO();
        }
        return instance;
    }

    /**
     * Get comprehensive profit/loss statement
     */
    public ProfitLossReport getProfitLossReport(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT "
                + "SUM(ti.line_total) as total_revenue, "
                + "SUM(ti.quantity * p.cost_price) as total_cost, "
                + "COUNT(DISTINCT t.id) as transaction_count, "
                + "SUM(ti.quantity) as total_items_sold "
                + "FROM transactions t "
                + "JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "JOIN products p ON ti.product_id = p.id "
                + "WHERE t.transaction_date BETWEEN ? AND ? "
                + "AND t.status = 'COMPLETED'";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    BigDecimal revenue = rs.getBigDecimal("total_revenue");
                    BigDecimal cost = rs.getBigDecimal("total_cost");
                    int transactionCount = rs.getInt("transaction_count");
                    int itemsSold = rs.getInt("total_items_sold");

                    if (revenue == null) {
                        revenue = BigDecimal.ZERO;
                    }
                    if (cost == null) {
                        cost = BigDecimal.ZERO;
                    }

                    BigDecimal grossProfit = revenue.subtract(cost);
                    BigDecimal profitMargin = revenue.compareTo(BigDecimal.ZERO) > 0
                            ? grossProfit.divide(revenue, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100))
                            : BigDecimal.ZERO;

                    return new ProfitLossReport(revenue, cost, grossProfit, profitMargin,
                            transactionCount, itemsSold, startDate, endDate);
                }
            }
        }
        return new ProfitLossReport(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
                BigDecimal.ZERO, 0, 0, startDate, endDate);
    }

    /**
     * Get inventory valuation report
     */
    public List<InventoryValuationItem> getInventoryValuation() throws SQLException {
        String sql = "SELECT "
                + "p.id, p.name, p.sku, p.category, p.cost_price, p.selling_price, "
                + "p.stock_quantity, "
                + "(p.stock_quantity * p.cost_price) as total_cost_value, "
                + "(p.stock_quantity * p.selling_price) as total_retail_value, "
                + "((p.selling_price - p.cost_price) * p.stock_quantity) as potential_profit "
                + "FROM products p "
                + "WHERE p.stock_quantity > 0 "
                + "ORDER BY total_cost_value DESC";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            List<InventoryValuationItem> items = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    items.add(new InventoryValuationItem(
                            rs.getLong("id"),
                            rs.getString("name"),
                            rs.getString("sku"),
                            rs.getString("category"),
                            rs.getBigDecimal("cost_price"),
                            rs.getBigDecimal("selling_price"),
                            rs.getInt("stock_quantity"),
                            rs.getBigDecimal("total_cost_value"),
                            rs.getBigDecimal("total_retail_value"),
                            rs.getBigDecimal("potential_profit")
                    ));
                }
            }
            return items;
        }
    }

    /**
     * Get product performance report
     */
    public List<ProductPerformanceReport> getProductPerformanceReport(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT "
                + "p.id, p.name, p.sku, p.category, p.cost_price, p.selling_price, "
                + "SUM(ti.quantity) as total_sold, "
                + "SUM(ti.line_total) as total_revenue, "
                + "SUM(ti.quantity * p.cost_price) as total_cost, "
                + "COUNT(DISTINCT t.id) as transaction_count, "
                + "AVG(ti.unit_price) as avg_selling_price, "
                + "MAX(t.transaction_date) as last_sold_date "
                + "FROM products p "
                + "LEFT JOIN transaction_items ti ON p.id = ti.product_id "
                + "LEFT JOIN transactions t ON ti.transaction_id = t.id "
                + "AND t.transaction_date BETWEEN ? AND ? "
                + "AND t.status = 'COMPLETED' "
                + "GROUP BY p.id, p.name, p.sku, p.category, p.cost_price, p.selling_price "
                + "ORDER BY total_revenue DESC NULLS LAST";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            List<ProductPerformanceReport> reports = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    int totalSold = rs.getInt("total_sold");
                    BigDecimal totalRevenue = rs.getBigDecimal("total_revenue");
                    BigDecimal totalCost = rs.getBigDecimal("total_cost");

                    if (totalRevenue == null) {
                        totalRevenue = BigDecimal.ZERO;
                    }
                    if (totalCost == null) {
                        totalCost = BigDecimal.ZERO;
                    }

                    BigDecimal profit = totalRevenue.subtract(totalCost);
                    BigDecimal profitMargin = totalRevenue.compareTo(BigDecimal.ZERO) > 0
                            ? profit.divide(totalRevenue, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100))
                            : BigDecimal.ZERO;

                    Timestamp lastSoldTimestamp = rs.getTimestamp("last_sold_date");
                    LocalDateTime lastSoldDate = lastSoldTimestamp != null ? lastSoldTimestamp.toLocalDateTime() : null;

                    reports.add(new ProductPerformanceReport(
                            rs.getLong("id"),
                            rs.getString("name"),
                            rs.getString("sku"),
                            rs.getString("category"),
                            rs.getBigDecimal("cost_price"),
                            rs.getBigDecimal("selling_price"),
                            totalSold,
                            totalRevenue,
                            totalCost,
                            profit,
                            profitMargin,
                            rs.getInt("transaction_count"),
                            rs.getBigDecimal("avg_selling_price"),
                            lastSoldDate
                    ));
                }
            }
            return reports;
        }
    }

    /**
     * Get customer analytics report
     */
    public List<CustomerAnalyticsReport> getCustomerAnalyticsReport(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT "
                + "c.id, c.first_name, c.last_name, c.phone, "
                + "COUNT(t.id) as transaction_count, "
                + "SUM(t.total) as total_spent, "
                + "AVG(t.total) as avg_order_value, "
                + "MAX(t.transaction_date) as last_purchase_date, "
                + "MIN(t.transaction_date) as first_purchase_date, "
                + "SUM(CASE WHEN t.transaction_date >= ? THEN t.total ELSE 0 END) as recent_spending "
                + "FROM customers c "
                + "LEFT JOIN transactions t ON c.id = t.customer_id "
                + "AND t.transaction_date BETWEEN ? AND ? "
                + "AND t.status = 'COMPLETED' "
                + "GROUP BY c.id, c.first_name, c.last_name, c.phone "
                + "ORDER BY total_spent DESC NULLS LAST";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            LocalDateTime recentThreshold = endDate.minusDays(30);
            pstmt.setTimestamp(1, Timestamp.valueOf(recentThreshold));
            pstmt.setTimestamp(2, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(3, Timestamp.valueOf(endDate));

            List<CustomerAnalyticsReport> reports = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    BigDecimal totalSpent = rs.getBigDecimal("total_spent");
                    BigDecimal avgOrderValue = rs.getBigDecimal("avg_order_value");
                    BigDecimal recentSpending = rs.getBigDecimal("recent_spending");

                    if (totalSpent == null) {
                        totalSpent = BigDecimal.ZERO;
                    }
                    if (avgOrderValue == null) {
                        avgOrderValue = BigDecimal.ZERO;
                    }
                    if (recentSpending == null) {
                        recentSpending = BigDecimal.ZERO;
                    }

                    Timestamp lastPurchaseTimestamp = rs.getTimestamp("last_purchase_date");
                    Timestamp firstPurchaseTimestamp = rs.getTimestamp("first_purchase_date");

                    LocalDateTime lastPurchaseDate = lastPurchaseTimestamp != null ? lastPurchaseTimestamp.toLocalDateTime() : null;
                    LocalDateTime firstPurchaseDate = firstPurchaseTimestamp != null ? firstPurchaseTimestamp.toLocalDateTime() : null;

                    reports.add(new CustomerAnalyticsReport(
                            rs.getLong("id"),
                            rs.getString("first_name") + " " + rs.getString("last_name"),
                            rs.getString("phone"),
                            rs.getInt("transaction_count"),
                            totalSpent,
                            avgOrderValue,
                            lastPurchaseDate,
                            firstPurchaseDate,
                            recentSpending
                    ));
                }
            }
            return reports;
        }
    }

    /**
     * Get low stock report
     */
    public List<LowStockItem> getLowStockReport(int threshold) throws SQLException {
        String sql = "SELECT "
                + "p.id, p.name, p.sku, p.category, p.stock_quantity, "
                + "p.cost_price, p.selling_price, "
                + "COALESCE(s.name, 'No Supplier') as supplier_name, "
                + "COALESCE(s.contact_email, '') as supplier_email, "
                + "COALESCE(s.contact_phone, '') as supplier_phone "
                + "FROM products p "
                + "LEFT JOIN suppliers s ON p.supplier_id = s.id "
                + "WHERE p.stock_quantity <= ? "
                + "ORDER BY p.stock_quantity ASC, p.name";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, threshold);

            List<LowStockItem> items = new ArrayList<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    items.add(new LowStockItem(
                            rs.getLong("id"),
                            rs.getString("name"),
                            rs.getString("sku"),
                            rs.getString("category"),
                            rs.getInt("stock_quantity"),
                            rs.getBigDecimal("cost_price"),
                            rs.getBigDecimal("selling_price"),
                            rs.getString("supplier_name"),
                            rs.getString("supplier_email"),
                            rs.getString("supplier_phone")
                    ));
                }
            }
            return items;
        }
    }

    /**
     * Get sales summary by period
     */
    public Map<String, BigDecimal> getSalesSummaryByPeriod(LocalDateTime startDate, LocalDateTime endDate, String groupBy) throws SQLException {
        String dateFormat;
        switch (groupBy.toLowerCase()) {
            case "day":
                dateFormat = "%Y-%m-%d";
                break;
            case "week":
                dateFormat = "%Y-W%W";
                break;
            case "month":
                dateFormat = "%Y-%m";
                break;
            case "year":
                dateFormat = "%Y";
                break;
            default:
                dateFormat = "%Y-%m-%d";
        }

        String sql = "SELECT "
                + "strftime('" + dateFormat + "', transaction_date) as period, "
                + "SUM(total) as total_sales "
                + "FROM transactions "
                + "WHERE transaction_date BETWEEN ? AND ? "
                + "AND status = 'COMPLETED' "
                + "GROUP BY strftime('" + dateFormat + "', transaction_date) "
                + "ORDER BY period";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            Map<String, BigDecimal> salesByPeriod = new HashMap<>();
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    salesByPeriod.put(rs.getString("period"), rs.getBigDecimal("total_sales"));
                }
            }
            return salesByPeriod;
        }
    }

    // Data classes for reporting results
    public static class ProfitLossReport {

        private final BigDecimal totalRevenue;
        private final BigDecimal totalCost;
        private final BigDecimal grossProfit;
        private final BigDecimal profitMargin;
        private final int transactionCount;
        private final int itemsSold;
        private final LocalDateTime startDate;
        private final LocalDateTime endDate;

        public ProfitLossReport(BigDecimal totalRevenue, BigDecimal totalCost, BigDecimal grossProfit,
                BigDecimal profitMargin, int transactionCount, int itemsSold,
                LocalDateTime startDate, LocalDateTime endDate) {
            this.totalRevenue = totalRevenue;
            this.totalCost = totalCost;
            this.grossProfit = grossProfit;
            this.profitMargin = profitMargin;
            this.transactionCount = transactionCount;
            this.itemsSold = itemsSold;
            this.startDate = startDate;
            this.endDate = endDate;
        }

        // Getters
        public BigDecimal getTotalRevenue() {
            return totalRevenue;
        }

        public BigDecimal getTotalCost() {
            return totalCost;
        }

        public BigDecimal getGrossProfit() {
            return grossProfit;
        }

        public BigDecimal getProfitMargin() {
            return profitMargin;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        public int getItemsSold() {
            return itemsSold;
        }

        public LocalDateTime getStartDate() {
            return startDate;
        }

        public LocalDateTime getEndDate() {
            return endDate;
        }
    }

    public static class InventoryValuationItem {

        private final Long productId;
        private final String productName;
        private final String sku;
        private final String category;
        private final BigDecimal costPrice;
        private final BigDecimal sellingPrice;
        private final int stockQuantity;
        private final BigDecimal totalCostValue;
        private final BigDecimal totalRetailValue;
        private final BigDecimal potentialProfit;

        public InventoryValuationItem(Long productId, String productName, String sku, String category,
                BigDecimal costPrice, BigDecimal sellingPrice, int stockQuantity,
                BigDecimal totalCostValue, BigDecimal totalRetailValue, BigDecimal potentialProfit) {
            this.productId = productId;
            this.productName = productName;
            this.sku = sku;
            this.category = category;
            this.costPrice = costPrice;
            this.sellingPrice = sellingPrice;
            this.stockQuantity = stockQuantity;
            this.totalCostValue = totalCostValue;
            this.totalRetailValue = totalRetailValue;
            this.potentialProfit = potentialProfit;
        }

        // Getters
        public Long getProductId() {
            return productId;
        }

        public String getProductName() {
            return productName;
        }

        public String getSku() {
            return sku;
        }

        public String getCategory() {
            return category;
        }

        public BigDecimal getCostPrice() {
            return costPrice;
        }

        public BigDecimal getSellingPrice() {
            return sellingPrice;
        }

        public int getStockQuantity() {
            return stockQuantity;
        }

        public BigDecimal getTotalCostValue() {
            return totalCostValue;
        }

        public BigDecimal getTotalRetailValue() {
            return totalRetailValue;
        }

        public BigDecimal getPotentialProfit() {
            return potentialProfit;
        }
    }

    public static class ProductPerformanceReport {

        private final Long productId;
        private final String productName;
        private final String sku;
        private final String category;
        private final BigDecimal costPrice;
        private final BigDecimal sellingPrice;
        private final int totalSold;
        private final BigDecimal totalRevenue;
        private final BigDecimal totalCost;
        private final BigDecimal profit;
        private final BigDecimal profitMargin;
        private final int transactionCount;
        private final BigDecimal avgSellingPrice;
        private final LocalDateTime lastSoldDate;

        public ProductPerformanceReport(Long productId, String productName, String sku, String category,
                BigDecimal costPrice, BigDecimal sellingPrice, int totalSold,
                BigDecimal totalRevenue, BigDecimal totalCost, BigDecimal profit,
                BigDecimal profitMargin, int transactionCount, BigDecimal avgSellingPrice,
                LocalDateTime lastSoldDate) {
            this.productId = productId;
            this.productName = productName;
            this.sku = sku;
            this.category = category;
            this.costPrice = costPrice;
            this.sellingPrice = sellingPrice;
            this.totalSold = totalSold;
            this.totalRevenue = totalRevenue;
            this.totalCost = totalCost;
            this.profit = profit;
            this.profitMargin = profitMargin;
            this.transactionCount = transactionCount;
            this.avgSellingPrice = avgSellingPrice;
            this.lastSoldDate = lastSoldDate;
        }

        // Getters
        public Long getProductId() {
            return productId;
        }

        public String getProductName() {
            return productName;
        }

        public String getSku() {
            return sku;
        }

        public String getCategory() {
            return category;
        }

        public BigDecimal getCostPrice() {
            return costPrice;
        }

        public BigDecimal getSellingPrice() {
            return sellingPrice;
        }

        public int getTotalSold() {
            return totalSold;
        }

        public BigDecimal getTotalRevenue() {
            return totalRevenue;
        }

        public BigDecimal getTotalCost() {
            return totalCost;
        }

        public BigDecimal getProfit() {
            return profit;
        }

        public BigDecimal getProfitMargin() {
            return profitMargin;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        public BigDecimal getAvgSellingPrice() {
            return avgSellingPrice;
        }

        public LocalDateTime getLastSoldDate() {
            return lastSoldDate;
        }
    }

    public static class CustomerAnalyticsReport {

        private final Long customerId;
        private final String customerName;
        private final String phone;
        private final int transactionCount;
        private final BigDecimal totalSpent;
        private final BigDecimal avgOrderValue;
        private final LocalDateTime lastPurchaseDate;
        private final LocalDateTime firstPurchaseDate;
        private final BigDecimal recentSpending;

        public CustomerAnalyticsReport(Long customerId, String customerName, String phone,
                int transactionCount, BigDecimal totalSpent, BigDecimal avgOrderValue,
                LocalDateTime lastPurchaseDate, LocalDateTime firstPurchaseDate,
                BigDecimal recentSpending) {
            this.customerId = customerId;
            this.customerName = customerName;
            this.phone = phone;
            this.transactionCount = transactionCount;
            this.totalSpent = totalSpent;
            this.avgOrderValue = avgOrderValue;
            this.lastPurchaseDate = lastPurchaseDate;
            this.firstPurchaseDate = firstPurchaseDate;
            this.recentSpending = recentSpending;
        }

        // Getters
        public Long getCustomerId() {
            return customerId;
        }

        public String getCustomerName() {
            return customerName;
        }

        public String getPhone() {
            return phone;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        public BigDecimal getTotalSpent() {
            return totalSpent;
        }

        public BigDecimal getAvgOrderValue() {
            return avgOrderValue;
        }

        public LocalDateTime getLastPurchaseDate() {
            return lastPurchaseDate;
        }

        public LocalDateTime getFirstPurchaseDate() {
            return firstPurchaseDate;
        }

        public BigDecimal getRecentSpending() {
            return recentSpending;
        }
    }

    public static class LowStockItem {

        private final Long productId;
        private final String productName;
        private final String sku;
        private final String category;
        private final int stockQuantity;
        private final BigDecimal costPrice;
        private final BigDecimal sellingPrice;
        private final String supplierName;
        private final String supplierEmail;
        private final String supplierPhone;

        public LowStockItem(Long productId, String productName, String sku, String category,
                int stockQuantity, BigDecimal costPrice, BigDecimal sellingPrice,
                String supplierName, String supplierEmail, String supplierPhone) {
            this.productId = productId;
            this.productName = productName;
            this.sku = sku;
            this.category = category;
            this.stockQuantity = stockQuantity;
            this.costPrice = costPrice;
            this.sellingPrice = sellingPrice;
            this.supplierName = supplierName;
            this.supplierEmail = supplierEmail;
            this.supplierPhone = supplierPhone;
        }

        // Getters
        public Long getProductId() {
            return productId;
        }

        public String getProductName() {
            return productName;
        }

        public String getSku() {
            return sku;
        }

        public String getCategory() {
            return category;
        }

        public int getStockQuantity() {
            return stockQuantity;
        }

        public BigDecimal getCostPrice() {
            return costPrice;
        }

        public BigDecimal getSellingPrice() {
            return sellingPrice;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public String getSupplierEmail() {
            return supplierEmail;
        }

        public String getSupplierPhone() {
            return supplierPhone;
        }
    }
}
