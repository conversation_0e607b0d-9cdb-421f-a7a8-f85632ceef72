package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing a payment made for a transaction Supports split
 * payments where multiple payment methods can be used
 */
public class Payment {

    private Long id;
    private Long transactionId;
    private PaymentMethod paymentMethod;
    private BigDecimal amount;
    private String reference; // Card last 4 digits, check number, etc.
    private String authorizationCode; // For card payments
    private PaymentStatus status;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime processedAt;

    // Constructors
    public Payment() {
        this.status = PaymentStatus.PENDING;
        this.createdAt = LocalDateTime.now();
    }

    public Payment(PaymentMethod paymentMethod, BigDecimal amount) {
        this();
        this.paymentMethod = paymentMethod;
        this.amount = amount;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    public PaymentStatus getStatus() {
        return status;
    }

    public void setStatus(PaymentStatus status) {
        this.status = status;
        if (status == PaymentStatus.COMPLETED && processedAt == null) {
            this.processedAt = LocalDateTime.now();
        }
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getProcessedAt() {
        return processedAt;
    }

    public void setProcessedAt(LocalDateTime processedAt) {
        this.processedAt = processedAt;
    }

    // Utility methods
    public boolean isCompleted() {
        return status == PaymentStatus.COMPLETED;
    }

    public boolean isPending() {
        return status == PaymentStatus.PENDING;
    }

    public boolean isFailed() {
        return status == PaymentStatus.FAILED;
    }

    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        sb.append(paymentMethod.getDisplayName());
        sb.append(": $").append(amount);

        if (reference != null && !reference.trim().isEmpty()) {
            sb.append(" (").append(reference).append(")");
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return String.format("Payment{id=%d, method=%s, amount=%s, status=%s}",
                id, paymentMethod, amount, status);
    }
}
