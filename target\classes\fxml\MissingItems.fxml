<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.clothingstore.view.MissingItemsController" spacing="10">
    
    <padding>
        <Insets top="15" right="15" bottom="15" left="15"/>
    </padding>
    
    <!-- Header Section -->
    <HBox alignment="CENTER_LEFT" spacing="10">
        <Label text="Missing Items Management" styleClass="page-title"/>
        <Region HBox.hgrow="ALWAYS"/>
        <Button fx:id="btnExportCSV" text="Export CSV" onAction="#handleExportCSV"/>
        <Button fx:id="btnExportPDF" text="Export PDF" onAction="#handleExportPDF"/>
        <Button fx:id="btnRefresh" text="Refresh" onAction="#loadMissingItems"/>
    </HBox>
    
    <Separator/>
    
    <!-- Report Missing Item Form -->
    <TitledPane text="Report Missing Item" expanded="true">
        <VBox spacing="10">
            <GridPane hgap="10" vgap="10">
                <columnConstraints>
                    <ColumnConstraints hgrow="NEVER" minWidth="100"/>
                    <ColumnConstraints hgrow="ALWAYS"/>
                    <ColumnConstraints hgrow="NEVER" minWidth="100"/>
                    <ColumnConstraints hgrow="ALWAYS"/>
                </columnConstraints>
                
                <Label text="Product:" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                <ComboBox fx:id="cmbProduct" promptText="Select Product" 
                         GridPane.columnIndex="1" GridPane.rowIndex="0" maxWidth="Infinity"/>
                
                <Label text="Quantity:" GridPane.columnIndex="2" GridPane.rowIndex="0"/>
                <TextField fx:id="txtQuantity" promptText="Enter quantity"
                          GridPane.columnIndex="3" GridPane.rowIndex="0"/>
                
                <Label text="Reason:" GridPane.columnIndex="0" GridPane.rowIndex="1"/>
                <TextArea fx:id="txtReason" promptText="Enter reason for missing items"
                         GridPane.columnIndex="1" GridPane.rowIndex="1" 
                         GridPane.columnSpan="3" prefRowCount="3"/>
            </GridPane>
            
            <HBox alignment="CENTER_RIGHT" spacing="10">
                <Button fx:id="btnReport" text="Report Missing Items" 
                        styleClass="primary-button" defaultButton="true"/>
            </HBox>
        </VBox>
    </TitledPane>
    
    <!-- Filter Section -->
    <TitledPane text="Filters" expanded="false">
        <GridPane hgap="10" vgap="10">
            <columnConstraints>
                <ColumnConstraints hgrow="NEVER" minWidth="100"/>
                <ColumnConstraints hgrow="ALWAYS"/>
                <ColumnConstraints hgrow="NEVER" minWidth="100"/>
                <ColumnConstraints hgrow="ALWAYS"/>
            </columnConstraints>
            
            <Label text="Date From:" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
            <DatePicker fx:id="dateFrom" GridPane.columnIndex="1" GridPane.rowIndex="0"/>
            
            <Label text="Date To:" GridPane.columnIndex="2" GridPane.rowIndex="0"/>
            <DatePicker fx:id="dateTo" GridPane.columnIndex="3" GridPane.rowIndex="0"/>
            
            <HBox spacing="10" alignment="CENTER_RIGHT" 
                  GridPane.columnIndex="0" GridPane.columnSpan="4" GridPane.rowIndex="1">
                <Button fx:id="btnClearFilter" text="Clear Filters"/>
                <Button fx:id="btnFilter" text="Apply Filters"/>
            </HBox>
        </GridPane>
    </TitledPane>
    
    <!-- Missing Items Table -->
    <TableView fx:id="tblMissingItems" VBox.vgrow="ALWAYS">
        <columns>
            <TableColumn fx:id="colProductName" text="Product" prefWidth="150"/>
            <TableColumn fx:id="colSKU" text="SKU" prefWidth="100"/>
            <TableColumn fx:id="colQuantity" text="Quantity" prefWidth="80"/>
            <TableColumn fx:id="colReason" text="Reason" prefWidth="200"/>
            <TableColumn fx:id="colDate" text="Report Date" prefWidth="150"/>
            <TableColumn fx:id="colStatus" text="Status" prefWidth="100"/>
            <TableColumn fx:id="colReportedBy" text="Reported By" prefWidth="120"/>
        </columns>
        <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
        </columnResizePolicy>
    </TableView>
    
    <!-- Status Bar -->
    <HBox spacing="20" alignment="CENTER_LEFT">
        <Label fx:id="lblTotalItems" text="Total Items: 0"/>
        <Label fx:id="lblPendingItems" text="Pending: 0"/>
        <Label fx:id="lblResolvedItems" text="Resolved: 0"/>
        <Label fx:id="lblWrittenOff" text="Written Off: 0"/>
    </HBox>
</VBox>