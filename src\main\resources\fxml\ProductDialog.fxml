<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.controller.ProductDialogController">
   <center>
      <ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED">
         <content>
            <VBox spacing="20.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
               
               <!-- Header -->
               <Label text="Product Information" styleClass="section-header">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               
               <!-- Basic Product Information -->
               <TitledPane collapsible="false" text="Basic Information">
                  <content>
                     <GridPane hgap="15.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                        </columnConstraints>
                        
                        <Label text="Product Name*:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="nameField" promptText="Enter product name" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="SKU*:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <TextField fx:id="skuField" promptText="Auto-generated" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                        
                        <Label text="Barcode:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="barcodeField" promptText="Enter barcode" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Category*:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <ComboBox fx:id="categoryComboBox" maxWidth="1.7976931348623157E308" promptText="Select category" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                        
                        <Label text="Brand:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextField fx:id="brandField" promptText="Enter brand" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Color:" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                        <TextField fx:id="colorField" promptText="Enter color" GridPane.columnIndex="3" GridPane.rowIndex="2" />
                        
                        <Label text="Size:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <TextField fx:id="sizeField" promptText="Enter size" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Description:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <TextArea fx:id="descriptionArea" maxHeight="80.0" prefRowCount="3" promptText="Enter product description" wrapText="true" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="4" />
                     </GridPane>
                  </content>
               </TitledPane>
               
               <!-- Supplier Integration -->
               <TitledPane collapsible="false" text="Supplier Information">
                  <content>
                     <VBox spacing="10.0">
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <Label text="Supplier:" />
                           <ComboBox fx:id="supplierComboBox" maxWidth="300.0" promptText="Select supplier" />
                           <Button fx:id="addSupplierButton" onAction="#handleAddSupplier" text="+ Add New Supplier" />
                        </HBox>
                        <Label fx:id="supplierInfoLabel" text="No supplier selected" wrapText="true" />
                     </VBox>
                  </content>
               </TitledPane>
               
               <!-- Cost Management -->
               <TitledPane collapsible="false" text="Cost Management">
                  <content>
                     <GridPane hgap="15.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" />
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" />
                        </columnConstraints>
                        
                        <Label text="Cost Price:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="costPriceField" promptText="0.00" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Selling Price:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <TextField fx:id="sellingPriceField" promptText="0.00" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                        
                        <Label text="Profit Margin:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="profitMarginLabel" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Profit Percentage:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <Label fx:id="profitPercentageLabel" text="0.00%" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                     </GridPane>
                  </content>
               </TitledPane>
               
               <!-- Stock Management -->
               <TitledPane collapsible="false" text="Stock Management">
                  <content>
                     <GridPane hgap="15.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" />
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" />
                        </columnConstraints>
                        
                        <Label text="Stock Quantity:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="stockQuantityField" promptText="0" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Min Stock Level:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <TextField fx:id="minStockLevelField" promptText="0" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                        
                        <Label text="Reorder Quantity:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="reorderQuantityField" promptText="0" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <CheckBox fx:id="lowStockAlertCheckBox" text="Enable Low Stock Alerts" GridPane.columnIndex="2" GridPane.columnSpan="2" GridPane.rowIndex="1" />
                     </GridPane>
                  </content>
               </TitledPane>
               
               <!-- Financial Tracking -->
               <TitledPane collapsible="false" text="Financial Analysis">
                  <content>
                     <GridPane hgap="15.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                           <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" />
                           <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                           <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" />
                        </columnConstraints>

                        <Label text="Total Investment:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="totalInvestmentLabel" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                        <Label text="Potential Revenue:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <Label fx:id="potentialRevenueLabel" text="$0.00" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                        <Label text="Potential Profit:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="potentialProfitLabel" text="$0.00" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="1" />

                        <Separator GridPane.columnSpan="4" GridPane.rowIndex="2" />

                        <Label text="Note: Financial calculations are based on current stock quantity and pricing."
                               wrapText="true" GridPane.columnSpan="4" GridPane.rowIndex="3" />
                     </GridPane>
                  </content>
               </TitledPane>
            </VBox>
         </content>
      </ScrollPane>
   </center>
   
   <!-- Action Buttons -->
   <bottom>
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
         <Button fx:id="cancelButton" onAction="#handleCancel" text="Cancel" />
         <Button fx:id="saveButton" onAction="#handleSave" text="Save Product" defaultButton="true" />
      </HBox>
   </bottom>
</BorderPane>
