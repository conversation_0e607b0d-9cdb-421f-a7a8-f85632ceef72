/* ===== PROFESSIONAL TRANSACTION HISTORY DIALOG STYLING ===== */

/* Root Variables for Consistent Theming */
.root {
    -fx-primary-color: #3498db;
    -fx-secondary-color: #2c3e50;
    -fx-success-color: #27ae60;
    -fx-warning-color: #f39c12;
    -fx-danger-color: #e74c3c;
    -fx-light-gray: #ecf0f1;
    -fx-dark-gray: #34495e;
    -fx-text-color: #2c3e50;
    -fx-background-color: #ffffff;
}

/* Main Window Container - Modern Professional Design */
.transaction-selection-window {
    -fx-background-color: #ffffff;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 15, 0, 0, 4);
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

/* ===== MODERN HEADER SECTION ===== */
.header-section {
    -fx-background-color: linear-gradient(to right, #f8f9fa, #e9ecef);
    -fx-background-radius: 12px 12px 0 0;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 2px 0;
    -fx-padding: 20px;
}

.header-icon {
    -fx-text-fill: #3498db;
    -fx-font-size: 20px;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 2, 0, 0, 1);
}

.header-title {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
}

.customer-info-container {
    -fx-background-color: rgba(52, 152, 219, 0.08);
    -fx-background-radius: 8px;
    -fx-padding: 16px;
    -fx-border-color: rgba(52, 152, 219, 0.2);
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.customer-info-text {
    -fx-text-fill: #2c3e50;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
}

.transaction-count-text {
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
    -fx-font-size: 12px;
}

/* ===== MODERN INSTRUCTIONS SECTION ===== */
.instructions-section {
    -fx-background-color: rgba(52, 152, 219, 0.05);
    -fx-alignment: center-left;
    -fx-padding: 12px 20px;
    -fx-border-color: rgba(52, 152, 219, 0.15);
    -fx-border-width: 0 0 1px 0;
}

.instruction-icon {
    -fx-text-fill: #3498db;
    -fx-font-size: 16px;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 2, 0, 0, 1);
}

.instruction-text {
    -fx-text-fill: #495057;
    -fx-font-weight: 500;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 13px;
}

/* ===== PROFESSIONAL TABLE CONTAINER ===== */
.table-container {
    -fx-background-color: #ffffff;
    -fx-background-radius: 0 0 12px 12px;
    -fx-padding: 0;
}

/* ===== PROFESSIONAL TABLE STYLING ===== */
.enhanced-transaction-table {
    -fx-background-color: #ffffff;
    -fx-border-color: transparent;
    -fx-table-cell-border-color: #f1f3f4;
    -fx-background-radius: 0 0 12px 12px;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 8, 0, 0, 2);
}

.enhanced-transaction-table .column-header-background {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 0;
}

.enhanced-transaction-table .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 2px 0;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-text-fill: #2d3748;
    -fx-alignment: center-left;
    -fx-padding: 12px 16px;
}

.enhanced-transaction-table .column-header .label {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-text-fill: #2d3748;
    -fx-alignment: center-left;
}

.enhanced-transaction-table .column-header:hover {
    -fx-background-color: #e9ecef;
}

.enhanced-transaction-table .table-row-cell {
    -fx-background-color: #ffffff;
    -fx-border-color: #f1f3f4;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 0;
    -fx-pref-height: 52px;
    -fx-min-height: 52px;
    -fx-max-height: 52px;
}

.enhanced-transaction-table .table-row-cell:odd {
    -fx-background-color: #fafbfc;
}

.enhanced-transaction-table .table-row-cell:hover {
    -fx-background-color: #f5f7fa !important;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(52, 152, 219, 0.15), 4, 0, 0, 1);
    -fx-border-color: #3498db;
    -fx-border-width: 0 0 2px 0;
}

.enhanced-transaction-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd !important;
    -fx-border-color: #2196f3;
    -fx-border-width: 0 0 2px 0;
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.25), 6, 0, 0, 2);
}

.enhanced-transaction-table .table-cell {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-text-fill: #2c3e50;
    -fx-alignment: center-left;
    -fx-padding: 16px;
    -fx-text-overrun: ellipsis;
    -fx-wrap-text: false;
    -fx-pref-height: 52px;
    -fx-min-height: 52px;
    -fx-max-height: 52px;
}

.enhanced-transaction-table .table-row-cell:selected .table-cell {
    -fx-text-fill: #1565c0;
    -fx-font-weight: 600;
    -fx-background-color: transparent;
}

/* Hover Effects for Table Cells */
.enhanced-transaction-table .table-row-cell:hover .table-cell {
    -fx-text-fill: #1565c0;
    -fx-font-weight: 500;
    -fx-background-color: transparent;
}

/* Column-Specific Styling */
.date-column .table-cell {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
    -fx-font-weight: 500;
}

.transaction-number-column .table-cell {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
    -fx-font-family: "Consolas", "Monaco", monospace;
    -fx-font-weight: 600;
}

.amount-column .table-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
    -fx-font-weight: 600;
    -fx-text-fill: #27ae60;
}

.status-column .table-cell {
    -fx-alignment: center;
    -fx-text-alignment: center;
    -fx-font-weight: 600;
}

.payment-column .table-cell {
    -fx-alignment: center;
    -fx-text-alignment: center;
    -fx-font-weight: 500;
}

.description-column .table-cell {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
    -fx-font-style: italic;
    -fx-text-fill: #6c757d;
}

/* Status-Specific Styling */
.status-completed {
    -fx-text-fill: #27ae60;
    -fx-font-weight: bold;
}

.status-refunded {
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

.status-pending {
    -fx-text-fill: #f39c12;
    -fx-font-weight: bold;
}

/* ===== MODERN FOOTER SECTION ===== */
.footer-separator {
    -fx-background-color: #e9ecef;
    -fx-pref-height: 2px;
}

.footer-section {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 0 0 12px 12px;
    -fx-alignment: center;
    -fx-padding: 16px 20px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0 0 0;
}

/* ===== MODERN BUTTON STYLING ===== */
.primary-button {
    -fx-background-color: #3498db;
    -fx-text-fill: #ffffff;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 12px 24px;
    -fx-cursor: hand;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 4, 0, 0, 1);
}

.primary-button:hover {
    -fx-background-color: #2980b9;
    -fx-effect: dropshadow(gaussian, rgba(52, 152, 219, 0.4), 6, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.primary-button:pressed {
    -fx-background-color: #1f618d;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.2), 2, 0, 0, 1);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

.primary-button:disabled {
    -fx-background-color: #bdc3c7;
    -fx-text-fill: #7f8c8d;
    -fx-cursor: default;
    -fx-effect: none;
    -fx-opacity: 0.6;
}

.secondary-button {
    -fx-background-color: #27ae60;
    -fx-text-fill: #ffffff;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 12px 24px;
    -fx-cursor: hand;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-effect: dropshadow(gaussian, rgba(39,174,96,0.3), 4, 0, 0, 1);
}

.secondary-button:hover {
    -fx-background-color: #229954;
    -fx-effect: dropshadow(gaussian, rgba(39, 174, 96, 0.4), 6, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.secondary-button:pressed {
    -fx-background-color: #1e8449;
    -fx-effect: dropshadow(gaussian, rgba(39,174,96,0.2), 2, 0, 0, 1);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

.close-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: #ffffff;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 12px 24px;
    -fx-cursor: hand;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-effect: dropshadow(gaussian, rgba(108,117,125,0.3), 4, 0, 0, 1);
}

.close-button:hover {
    -fx-background-color: #5a6268;
    -fx-effect: dropshadow(gaussian, rgba(108, 117, 125, 0.4), 6, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.close-button:pressed {
    -fx-background-color: #495057;
    -fx-effect: dropshadow(gaussian, rgba(108,117,125,0.2), 2, 0, 0, 1);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* ===== SMOOTH TRANSITIONS & ANIMATIONS ===== */
.primary-button, .secondary-button, .close-button {
    -fx-transition: all 0.2s ease-in-out;
}

.enhanced-transaction-table .table-row-cell {
    -fx-transition: all 0.15s ease-in-out;
}

.enhanced-transaction-table .table-row-cell:hover {
    -fx-transition: all 0.1s ease-in-out;
}

/* ===== RESPONSIVE DESIGN ADJUSTMENTS ===== */
@media screen and (max-width: 1366px) {
    .enhanced-transaction-table .table-cell {
        -fx-font-size: 13px;
        -fx-padding: 14px;
    }

    .enhanced-transaction-table .table-row-cell {
        -fx-pref-height: 48px;
        -fx-min-height: 48px;
        -fx-max-height: 48px;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.enhanced-transaction-table:focused .table-row-cell:selected {
    -fx-border-color: #2196f3;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.4), 8, 0, 0, 2);
}

/* ===== PROFESSIONAL POLISH ===== */
.transaction-selection-window .scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.transaction-selection-window .scroll-pane .viewport {
    -fx-background-color: transparent;
}

.transaction-selection-window .scroll-pane .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0 1px 1px;
}

.transaction-selection-window .scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #ced4da;
    -fx-background-radius: 4px;
}

.transaction-selection-window .scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #adb5bd;
}
