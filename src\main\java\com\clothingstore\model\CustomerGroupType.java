package com.clothingstore.model;

import java.math.BigDecimal;

/**
 * Enumeration for customer group types with default configurations
 */
public enum CustomerGroupType {
    REGULAR("Regular", "Standard retail customers", 
            BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, 0, false, false, 1),
    
    VIP("VIP", "Premium customers with special benefits", 
            new BigDecimal("10"), BigDecimal.ZERO, new BigDecimal("1000"), 15, false, true, 3),
    
    WHOLESALE("Wholesale", "Bulk buyers and resellers", 
            new BigDecimal("25"), new BigDecimal("500"), new BigDecimal("5000"), 30, true, true, 4),
    
    EMPLOYEE("Employee", "Store employees and staff", 
            new BigDecimal("20"), BigDecimal.ZERO, new BigDecimal("500"), 0, false, false, 2),
    
    CORPORATE("Corporate", "Business and corporate accounts", 
            new BigDecimal("15"), new BigDecimal("1000"), new BigDecimal("10000"), 45, true, true, 5),
    
    STUDENT("Student", "Students with valid ID", 
            new BigDecimal("5"), BigDecimal.ZERO, BigDecimal.ZERO, 0, false, false, 1),
    
    SENIOR("Senior", "Senior citizens discount", 
            new BigDecimal("8"), BigDecimal.ZERO, BigDecimal.ZERO, 0, false, false, 1),
    
    LOYALTY("Loyalty", "Long-term loyal customers", 
            new BigDecimal("12"), BigDecimal.ZERO, new BigDecimal("2000"), 15, false, true, 3);

    private final String displayName;
    private final String description;
    private final BigDecimal defaultDiscountPercentage;
    private final BigDecimal defaultMinimumOrder;
    private final BigDecimal defaultCreditLimit;
    private final int defaultPaymentTerms;
    private final boolean defaultTaxExempt;
    private final boolean defaultAllowBackorders;
    private final int defaultPriority;

    CustomerGroupType(String displayName, String description, 
                     BigDecimal defaultDiscountPercentage, BigDecimal defaultMinimumOrder,
                     BigDecimal defaultCreditLimit, int defaultPaymentTerms,
                     boolean defaultTaxExempt, boolean defaultAllowBackorders, int defaultPriority) {
        this.displayName = displayName;
        this.description = description;
        this.defaultDiscountPercentage = defaultDiscountPercentage;
        this.defaultMinimumOrder = defaultMinimumOrder;
        this.defaultCreditLimit = defaultCreditLimit;
        this.defaultPaymentTerms = defaultPaymentTerms;
        this.defaultTaxExempt = defaultTaxExempt;
        this.defaultAllowBackorders = defaultAllowBackorders;
        this.defaultPriority = defaultPriority;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public BigDecimal getDefaultDiscountPercentage() {
        return defaultDiscountPercentage;
    }

    public BigDecimal getDefaultMinimumOrder() {
        return defaultMinimumOrder;
    }

    public BigDecimal getDefaultCreditLimit() {
        return defaultCreditLimit;
    }

    public int getDefaultPaymentTerms() {
        return defaultPaymentTerms;
    }

    public boolean isDefaultTaxExempt() {
        return defaultTaxExempt;
    }

    public boolean isDefaultAllowBackorders() {
        return defaultAllowBackorders;
    }

    public int getDefaultPriority() {
        return defaultPriority;
    }

    @Override
    public String toString() {
        return displayName;
    }

    /**
     * Create a CustomerGroup with default settings for this type
     */
    public CustomerGroup createDefaultGroup() {
        CustomerGroup group = new CustomerGroup();
        group.setGroupCode(this.name());
        group.setGroupName(this.displayName);
        group.setDescription(this.description);
        group.setGroupType(this);
        group.setDiscountPercentage(this.defaultDiscountPercentage);
        group.setMinimumOrderAmount(this.defaultMinimumOrder);
        group.setCreditLimit(this.defaultCreditLimit);
        group.setPaymentTermsDays(this.defaultPaymentTerms);
        group.setTaxExempt(this.defaultTaxExempt);
        group.setAllowBackorders(this.defaultAllowBackorders);
        group.setPriority(this.defaultPriority);
        
        // Set default benefits based on type
        group.setBenefits(getDefaultBenefits());
        
        return group;
    }

    /**
     * Get default benefits description for this group type
     */
    public String getDefaultBenefits() {
        switch (this) {
            case VIP:
                return "Priority customer service\nEarly access to new arrivals\nFree shipping on all orders\nBirthday discounts\nExclusive member events";
                
            case WHOLESALE:
                return "Volume pricing discounts\nDedicated account manager\nFlexible payment terms\nBulk order processing\nCustom packaging options";
                
            case EMPLOYEE:
                return "Employee discount on all items\nAccess to employee-only sales\nFlexible payment options";
                
            case CORPORATE:
                return "Corporate billing\nVolume discounts\nDedicated support\nCustom reporting\nFlexible payment terms";
                
            case STUDENT:
                return "Student discount with valid ID\nSpecial student promotions\nBack-to-school offers";
                
            case SENIOR:
                return "Senior citizen discount\nSpecial senior hours\nPersonalized assistance";
                
            case LOYALTY:
                return "Loyalty rewards program\nPoints accumulation\nExclusive offers\nPriority support";
                
            case REGULAR:
            default:
                return "Standard pricing and service\nRegular promotions\nStandard return policy";
        }
    }

    /**
     * Check if this group type requires special approval
     */
    public boolean requiresApproval() {
        return this == WHOLESALE || this == CORPORATE || this == EMPLOYEE;
    }

    /**
     * Check if this group type is eligible for credit terms
     */
    public boolean isEligibleForCredit() {
        return this == VIP || this == WHOLESALE || this == CORPORATE || this == LOYALTY;
    }

    /**
     * Get recommended upgrade path
     */
    public CustomerGroupType getUpgradePath() {
        switch (this) {
            case REGULAR:
                return VIP;
            case VIP:
                return LOYALTY;
            case STUDENT:
                return REGULAR;
            case SENIOR:
                return VIP;
            default:
                return null; // No standard upgrade path
        }
    }
}
