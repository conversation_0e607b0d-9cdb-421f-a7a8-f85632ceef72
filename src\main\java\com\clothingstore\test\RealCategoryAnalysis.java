package com.clothingstore.test;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Real Category Analysis working with actual database structure
 */
public class RealCategoryAnalysis {

    private static final String DB_URL = "*****************************";

    public static void main(String[] args) {
        System.out.println("================================================================================");
        System.out.println("REAL CATEGORY ANALYSIS - ACTUAL DATABASE DATA");
        System.out.println("================================================================================");
        System.out.println("Analysis Date: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();

        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // 1. COMPREHENSIVE CATEGORY PERFORMANCE ANALYSIS
            System.out.println("[ANALYSIS] COMPREHENSIVE CATEGORY PERFORMANCE");
            System.out.println("================================================================");
            
            String categoryQuery = """
                SELECT 
                    COALESCE(p.category, 'Uncategorized') as category,
                    COUNT(DISTINCT t.id) as transaction_count,
                    SUM(ti.quantity) as items_sold,
                    SUM(ti.line_total) as revenue,
                    SUM(p.cost_price * ti.quantity) as cost,
                    COUNT(DISTINCT p.id) as unique_products,
                    AVG(ti.line_total / ti.quantity) as avg_price_per_item,
                    MIN(ti.line_total / ti.quantity) as min_price_per_item,
                    MAX(ti.line_total / ti.quantity) as max_price_per_item
                FROM transactions t
                INNER JOIN transaction_items ti ON t.id = ti.transaction_id
                INNER JOIN products p ON ti.product_id = p.id
                WHERE t.status = 'COMPLETED'
                    AND (t.refunded_amount IS NULL OR t.refunded_amount = 0)
                    AND p.cost_price IS NOT NULL
                GROUP BY p.category
                ORDER BY (SUM(ti.line_total) - SUM(p.cost_price * ti.quantity)) DESC
                """;
            
            List<CategoryResult> categories = new ArrayList<>();
            double totalRevenue = 0;
            double totalCost = 0;
            int totalItems = 0;
            int totalTransactions = 0;
            
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(categoryQuery)) {
                
                System.out.println("CATEGORY PERFORMANCE RANKING:");
                System.out.println("-".repeat(60));
                
                int rank = 1;
                while (rs.next()) {
                    String category = rs.getString("category");
                    int transactionCount = rs.getInt("transaction_count");
                    int itemsSold = rs.getInt("items_sold");
                    double revenue = rs.getDouble("revenue");
                    double cost = rs.getDouble("cost");
                    int uniqueProducts = rs.getInt("unique_products");
                    double avgPrice = rs.getDouble("avg_price_per_item");
                    double minPrice = rs.getDouble("min_price_per_item");
                    double maxPrice = rs.getDouble("max_price_per_item");
                    
                    double profit = revenue - cost;
                    double margin = revenue > 0 ? (profit / revenue) * 100 : 0;
                    double avgTransactionValue = transactionCount > 0 ? revenue / transactionCount : 0;
                    double revenuePerItem = itemsSold > 0 ? revenue / itemsSold : 0;
                    
                    CategoryResult result = new CategoryResult(category, revenue, cost, profit, margin, 
                        itemsSold, transactionCount, uniqueProducts, avgPrice, minPrice, maxPrice);
                    categories.add(result);
                    
                    totalRevenue += revenue;
                    totalCost += cost;
                    totalItems += itemsSold;
                    totalTransactions += transactionCount;
                    
                    String rankStr = rank == 1 ? "🥇 1st" : rank == 2 ? "🥈 2nd" : rank == 3 ? "🥉 3rd" : "#" + rank;
                    
                    System.out.println(rankStr + " PLACE: " + category.toUpperCase());
                    System.out.println("   💰 Financial Performance:");
                    System.out.println("      Revenue: $" + String.format("%.2f", revenue));
                    System.out.println("      Cost: $" + String.format("%.2f", cost));
                    System.out.println("      Profit: $" + String.format("%.2f", profit));
                    System.out.println("      Margin: " + String.format("%.2f%%", margin));
                    
                    System.out.println("   📊 Operational Metrics:");
                    System.out.println("      Items Sold: " + itemsSold);
                    System.out.println("      Transactions: " + transactionCount);
                    System.out.println("      Unique Products: " + uniqueProducts);
                    System.out.println("      Avg Transaction Value: $" + String.format("%.2f", avgTransactionValue));
                    System.out.println("      Revenue per Item: $" + String.format("%.2f", revenuePerItem));
                    
                    System.out.println("   💲 Pricing Analysis:");
                    System.out.println("      Average Price: $" + String.format("%.2f", avgPrice));
                    System.out.println("      Price Range: $" + String.format("%.2f", minPrice) + " - $" + String.format("%.2f", maxPrice));
                    
                    System.out.println("   🎯 Performance Assessment:");
                    if (margin > 40) {
                        System.out.println("      ⭐ EXCELLENT - Premium performance category");
                    } else if (margin > 25) {
                        System.out.println("      ✅ GOOD - Healthy profit margins");
                    } else if (margin > 15) {
                        System.out.println("      ⚠️ MODERATE - Room for improvement");
                    } else {
                        System.out.println("      🔴 LOW - Needs immediate attention");
                    }
                    
                    if (avgTransactionValue > 100) {
                        System.out.println("      💎 HIGH VALUE - Premium customer transactions");
                    } else if (avgTransactionValue > 50) {
                        System.out.println("      💰 GOOD VALUE - Solid transaction amounts");
                    } else {
                        System.out.println("      📈 GROWTH OPPORTUNITY - Consider upselling");
                    }
                    
                    System.out.println();
                    rank++;
                }
            }
            
            // 2. OVERALL BUSINESS SUMMARY
            double totalProfit = totalRevenue - totalCost;
            double overallMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;
            
            System.out.println("\n[SUMMARY] OVERALL BUSINESS PERFORMANCE");
            System.out.println("================================================================");
            System.out.println("📈 FINANCIAL TOTALS:");
            System.out.println("   Total Revenue: $" + String.format("%.2f", totalRevenue));
            System.out.println("   Total Cost: $" + String.format("%.2f", totalCost));
            System.out.println("   Total Profit: $" + String.format("%.2f", totalProfit));
            System.out.println("   Overall Margin: " + String.format("%.2f%%", overallMargin));
            
            System.out.println("\n📊 OPERATIONAL TOTALS:");
            System.out.println("   Total Items Sold: " + totalItems);
            System.out.println("   Total Transactions: " + totalTransactions);
            System.out.println("   Active Categories: " + categories.size());
            System.out.println("   Avg Items per Transaction: " + 
                String.format("%.1f", totalTransactions > 0 ? (double)totalItems / totalTransactions : 0));
            System.out.println("   Avg Revenue per Item: $" + 
                String.format("%.2f", totalItems > 0 ? totalRevenue / totalItems : 0));
            
            // 3. DETAILED PRODUCT BREAKDOWN BY CATEGORY
            System.out.println("\n[PRODUCTS] DETAILED PRODUCT BREAKDOWN BY CATEGORY");
            System.out.println("================================================================");
            
            for (CategoryResult category : categories) {
                System.out.println("\n📂 " + category.name.toUpperCase() + " CATEGORY PRODUCTS:");
                System.out.println("-".repeat(50));
                
                String productQuery = """
                    SELECT 
                        p.name, p.sku, p.price, p.cost_price,
                        SUM(ti.quantity) as quantity_sold,
                        SUM(ti.line_total) as revenue,
                        COUNT(DISTINCT t.id) as transactions
                    FROM transactions t
                    INNER JOIN transaction_items ti ON t.id = ti.transaction_id
                    INNER JOIN products p ON ti.product_id = p.id
                    WHERE t.status = 'COMPLETED'
                        AND (t.refunded_amount IS NULL OR t.refunded_amount = 0)
                        AND p.category = ?
                        AND p.cost_price IS NOT NULL
                    GROUP BY p.id, p.name, p.sku, p.price, p.cost_price
                    ORDER BY SUM(ti.line_total) DESC
                    """;
                
                try (PreparedStatement pstmt = conn.prepareStatement(productQuery)) {
                    pstmt.setString(1, category.name);
                    
                    try (ResultSet rs = pstmt.executeQuery()) {
                        while (rs.next()) {
                            String productName = rs.getString("name");
                            String sku = rs.getString("sku");
                            double price = rs.getDouble("price");
                            double costPrice = rs.getDouble("cost_price");
                            int quantitySold = rs.getInt("quantity_sold");
                            double productRevenue = rs.getDouble("revenue");
                            int productTransactions = rs.getInt("transactions");
                            
                            double productCost = costPrice * quantitySold;
                            double productProfit = productRevenue - productCost;
                            double productMargin = productRevenue > 0 ? (productProfit / productRevenue) * 100 : 0;
                            
                            System.out.println("   🏷️ " + productName + " (SKU: " + sku + ")");
                            System.out.println("      Selling Price: $" + String.format("%.2f", price) + 
                                " | Cost Price: $" + String.format("%.2f", costPrice));
                            System.out.println("      Revenue: $" + String.format("%.2f", productRevenue) + 
                                " | Profit: $" + String.format("%.2f", productProfit) + 
                                " | Margin: " + String.format("%.1f%%", productMargin));
                            System.out.println("      Quantity Sold: " + quantitySold + 
                                " | Transactions: " + productTransactions);
                            System.out.println();
                        }
                    }
                }
            }
            
            // 4. STRATEGIC INSIGHTS AND RECOMMENDATIONS
            System.out.println("\n[INSIGHTS] STRATEGIC BUSINESS INSIGHTS");
            System.out.println("================================================================");
            
            if (!categories.isEmpty()) {
                CategoryResult topCategory = categories.get(0);
                CategoryResult bottomCategory = categories.get(categories.size() - 1);
                
                System.out.println("🎯 PERFORMANCE ANALYSIS:");
                System.out.println("   🏆 CHAMPION: " + topCategory.name + " dominates with $" + 
                    String.format("%.2f", topCategory.profit) + " profit (" + 
                    String.format("%.1f%%", topCategory.margin) + " margin)");
                
                if (categories.size() > 1) {
                    System.out.println("   📉 CHALLENGER: " + bottomCategory.name + " trails with $" + 
                        String.format("%.2f", bottomCategory.profit) + " profit (" + 
                        String.format("%.1f%%", bottomCategory.margin) + " margin)");
                    
                    double profitGap = topCategory.profit - bottomCategory.profit;
                    double marginGap = topCategory.margin - bottomCategory.margin;
                    
                    System.out.println("   📊 PERFORMANCE GAP: $" + String.format("%.2f", profitGap) + 
                        " profit difference, " + String.format("%.1f", marginGap) + " percentage points margin gap");
                }
                
                // Market share analysis
                double topShare = (topCategory.revenue / totalRevenue) * 100;
                System.out.println("   📈 MARKET SHARE: " + topCategory.name + " represents " + 
                    String.format("%.1f%%", topShare) + " of total revenue");
                
                System.out.println("\n🚀 ACTIONABLE RECOMMENDATIONS:");
                System.out.println("   1. EXPAND SUCCESS: Increase inventory and marketing for " + topCategory.name);
                System.out.println("   2. OPTIMIZE PRICING: Review cost structure for lower-margin categories");
                System.out.println("   3. PRODUCT MIX: Focus on high-margin items within each category");
                System.out.println("   4. CROSS-SELLING: Bundle products from different categories");
                
                if (overallMargin > 40) {
                    System.out.println("   5. PREMIUM STRATEGY: Your " + String.format("%.1f%%", overallMargin) + 
                        " margin supports luxury positioning");
                } else if (overallMargin > 25) {
                    System.out.println("   5. GROWTH STRATEGY: Your " + String.format("%.1f%%", overallMargin) + 
                        " margin provides solid foundation for expansion");
                } else {
                    System.out.println("   5. EFFICIENCY FOCUS: Improve " + String.format("%.1f%%", overallMargin) + 
                        " margin through cost optimization");
                }
                
                System.out.println("   6. DATA COVERAGE: Ensure all products have cost_price for complete analysis");
            }

            System.out.println("\n================================================================================");
            System.out.println("✅ REAL CATEGORY ANALYSIS COMPLETE");
            System.out.println("📊 Categories Analyzed: " + categories.size());
            System.out.println("💰 Total Business Profit: $" + String.format("%.2f", totalProfit));
            System.out.println("📈 Overall Margin: " + String.format("%.2f%%", overallMargin));
            System.out.println("🎯 Data Source: Live database with " + totalTransactions + " completed transactions");
            System.out.println("================================================================================");

        } catch (SQLException e) {
            System.err.println("❌ Category analysis failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    static class CategoryResult {
        String name;
        double revenue, cost, profit, margin;
        int itemsSold, transactions, uniqueProducts;
        double avgPrice, minPrice, maxPrice;
        
        CategoryResult(String name, double revenue, double cost, double profit, double margin,
                      int itemsSold, int transactions, int uniqueProducts, 
                      double avgPrice, double minPrice, double maxPrice) {
            this.name = name;
            this.revenue = revenue;
            this.cost = cost;
            this.profit = profit;
            this.margin = margin;
            this.itemsSold = itemsSold;
            this.transactions = transactions;
            this.uniqueProducts = uniqueProducts;
            this.avgPrice = avgPrice;
            this.minPrice = minPrice;
            this.maxPrice = maxPrice;
        }
    }
}
