package com.clothingstore.util;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Logger;

import com.clothingstore.database.DatabaseManager;

/**
 * Database initializer to ensure all required tables and schema updates are
 * applied
 */
public class DatabaseInitializer {

    private static final Logger LOGGER = Logger.getLogger(DatabaseInitializer.class.getName());

    /**
     * Initialize all database schemas and updates
     */
    public static void initializeDatabase() {
        LOGGER.info("Starting database initialization...");

        try {
            // Run all schema updates
            ReturnExchangeSchemaUpdater.runAllUpdates();
            CashDrawerSchemaUpdater.runAllUpdates();
            InstallmentPaymentSchemaUpdater.runAllUpdates();
            PaymentHistorySchemaUpdater.runAllUpdates();
            RefundItemTrackingSchemaUpdater.updateSchemaForRefundItemTracking();

            LOGGER.info("Database initialization completed successfully");
        } catch (Exception e) {
            LOGGER.severe("Database initialization failed: " + e.getMessage());
            throw new RuntimeException("Database initialization failed", e);
        }
    }

    /**
     * Verify database connectivity and basic tables
     */
    public static boolean verifyDatabase() {
        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Test basic connectivity
            try (Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SELECT 1");
            }

            LOGGER.info("Database connectivity verified");
            return true;
        } catch (SQLException e) {
            LOGGER.severe("Database verification failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Main method for running database initialization
     */
    public static void main(String[] args) {
        System.out.println("=== Clothing Store Database Initializer ===");

        if (verifyDatabase()) {
            System.out.println("✓ Database connectivity verified");
            initializeDatabase();
            System.out.println("✓ Database initialization completed successfully!");
        } else {
            System.err.println("✗ Database connectivity failed!");
            System.exit(1);
        }
    }
}
