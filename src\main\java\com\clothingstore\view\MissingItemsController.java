package com.clothingstore.view;

import java.net.URL;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.MissingItem;
import com.clothingstore.model.Product;
import com.clothingstore.service.InventoryService;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;

public class MissingItemsController implements Initializable {

    @FXML
    private TableView<MissingItem> tblMissingItems;
    @FXML
    private TableColumn<MissingItem, String> colProductName;
    @FXML
    private TableColumn<MissingItem, String> colSKU;
    @FXML
    private TableColumn<MissingItem, Integer> colQuantity;
    @FXML
    private TableColumn<MissingItem, String> colReason;
    @FXML
    private TableColumn<MissingItem, String> colDate;
    @FXML
    private TableColumn<MissingItem, String> colStatus;
    @FXML
    private TableColumn<MissingItem, String> colReportedBy;

    @FXML
    private ComboBox<Product> cmbProduct;
    @FXML
    private TextField txtQuantity;
    @FXML
    private TextArea txtReason;
    @FXML
    private Button btnReport;
    @FXML
    private Button btnRefresh;
    @FXML
    private DatePicker dateFrom;
    @FXML
    private DatePicker dateTo;
    @FXML
    private Button btnFilter;
    @FXML
    private Button btnClearFilter;
    @FXML
    private Button btnExportCSV;
    @FXML
    private Button btnExportPDF;

    // Status labels
    @FXML
    private Label lblTotalItems;
    @FXML
    private Label lblPendingItems;
    @FXML
    private Label lblResolvedItems;
    @FXML
    private Label lblWrittenOff;

    private ObservableList<MissingItem> missingItems;
    private ProductDAO productDAO;
    private InventoryService inventoryService;
    private DateTimeFormatter dateFormatter;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        productDAO = ProductDAO.getInstance();
        inventoryService = new InventoryService();
        dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm");

        missingItems = FXCollections.observableArrayList();

        setupTableColumns();
        setupControls();
        loadProducts();
        loadMissingItems();
    }

    private void setupTableColumns() {
        colProductName.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getProduct().getName()));

        colSKU.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getProduct().getSku()));

        colQuantity.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        colReason.setCellValueFactory(new PropertyValueFactory<>("reason"));

        colDate.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getReportDate().format(dateFormatter)));

        colStatus.setCellValueFactory(new PropertyValueFactory<>("status"));
        colReportedBy.setCellValueFactory(new PropertyValueFactory<>("reportedBy"));

        tblMissingItems.setItems(missingItems);
    }

    private void setupControls() {
        // Initialize date pickers with default values
        dateFrom.setValue(LocalDateTime.now().minusMonths(1).toLocalDate());
        dateTo.setValue(LocalDateTime.now().toLocalDate());

        // Setup button handlers
        btnReport.setOnAction(e -> handleReportMissingItem());
        btnRefresh.setOnAction(e -> loadMissingItems());
        btnFilter.setOnAction(e -> applyFilters());
        btnClearFilter.setOnAction(e -> clearFilters());

        // Validation for quantity field
        txtQuantity.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*")) {
                txtQuantity.setText(newVal.replaceAll("[^\\d]", ""));
            }
        });
    }

    private void loadProducts() {
        try {
            ObservableList<Product> products = FXCollections.observableArrayList(
                    productDAO.findAll());
            cmbProduct.setItems(products);
        } catch (SQLException e) {
            AlertUtil.showError("Error", "Failed to load products: " + e.getMessage());
        }
    }

    private void loadMissingItems() {
        try {
            missingItems.setAll(inventoryService.getMissingItems(
                    dateFrom.getValue().atStartOfDay(),
                    dateTo.getValue().atTime(23, 59, 59)
            ));
            updateStatusSummary();
        } catch (SQLException e) {
            AlertUtil.showError("Error", "Failed to load missing items: " + e.getMessage());
        }
    }

    private void updateStatusSummary() {
        int totalItems = missingItems.size();
        long pendingCount = missingItems.stream().filter(item -> "REPORTED".equals(item.getStatus())).count();
        long resolvedCount = missingItems.stream().filter(item -> "RESOLVED".equals(item.getStatus())).count();
        long writtenOffCount = missingItems.stream().filter(item -> "WRITTEN_OFF".equals(item.getStatus())).count();

        lblTotalItems.setText("Total Items: " + totalItems);
        lblPendingItems.setText("Pending: " + pendingCount);
        lblResolvedItems.setText("Resolved: " + resolvedCount);
        lblWrittenOff.setText("Written Off: " + writtenOffCount);
    }

    @FXML
    private void handleReportMissingItem() {
        if (!validateInput()) {
            return;
        }

        try {
            Product selectedProduct = cmbProduct.getValue();
            int quantity = Integer.parseInt(txtQuantity.getText());
            String reason = txtReason.getText();

            MissingItem missingItem = new MissingItem(
                    selectedProduct,
                    quantity,
                    reason,
                    "REPORTED", // Initial status
                    "Current User" // TODO: Replace with actual logged-in user
            );

            inventoryService.reportMissingItem(missingItem);

            // Clear form
            cmbProduct.setValue(null);
            txtQuantity.clear();
            txtReason.clear();

            // Refresh table and summary
            loadMissingItems();

            AlertUtil.showInfo("Success", "Missing item reported successfully");

        } catch (SQLException e) {
            AlertUtil.showError("Error", "Failed to report missing item: " + e.getMessage());
        }
    }

    private boolean validateInput() {
        if (cmbProduct.getValue() == null) {
            AlertUtil.showWarning("Validation Error", "Please select a product");
            return false;
        }

        if (txtQuantity.getText().isEmpty()) {
            AlertUtil.showWarning("Validation Error", "Please enter quantity");
            return false;
        }

        int quantity;
        try {
            quantity = Integer.parseInt(txtQuantity.getText());
            if (quantity <= 0) {
                AlertUtil.showWarning("Validation Error", "Quantity must be greater than 0");
                return false;
            }
        } catch (NumberFormatException e) {
            AlertUtil.showWarning("Validation Error", "Invalid quantity");
            return false;
        }

        if (txtReason.getText().trim().isEmpty()) {
            AlertUtil.showWarning("Validation Error", "Please provide a reason");
            return false;
        }

        return true;
    }

    @FXML
    private void applyFilters() {
        loadMissingItems(); // This will use the date filters
    }

    @FXML
    private void clearFilters() {
        dateFrom.setValue(LocalDateTime.now().minusMonths(1).toLocalDate());
        dateTo.setValue(LocalDateTime.now().toLocalDate());
        loadMissingItems();
    }

    @FXML
    private void handleExportCSV() {
        try {
            javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
            fileChooser.setTitle("Export Missing Items to CSV");
            fileChooser.getExtensionFilters().add(
                    new javafx.stage.FileChooser.ExtensionFilter("CSV Files", "*.csv"));
            fileChooser.setInitialFileName("missing_items_" + java.time.LocalDate.now() + ".csv");

            java.io.File file = fileChooser.showSaveDialog(tblMissingItems.getScene().getWindow());
            if (file != null) {
                exportToCSV(file);
                AlertUtil.showSuccess("Export Complete", "Missing items exported successfully to: " + file.getName());
            }
        } catch (Exception e) {
            AlertUtil.showError("Export Error", "Failed to export to CSV: " + e.getMessage());
        }
    }

    @FXML
    private void handleExportPDF() {
        try {
            javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
            fileChooser.setTitle("Export Missing Items to PDF");
            fileChooser.getExtensionFilters().add(
                    new javafx.stage.FileChooser.ExtensionFilter("PDF Files", "*.pdf"));
            fileChooser.setInitialFileName("missing_items_" + java.time.LocalDate.now() + ".pdf");

            java.io.File file = fileChooser.showSaveDialog(tblMissingItems.getScene().getWindow());
            if (file != null) {
                exportToPDF(file);
                AlertUtil.showSuccess("Export Complete", "Missing items exported successfully to: " + file.getName());
            }
        } catch (Exception e) {
            AlertUtil.showError("Export Error", "Failed to export to PDF: " + e.getMessage());
        }
    }

    private void exportToCSV(java.io.File file) throws Exception {
        try (java.io.PrintWriter writer = new java.io.PrintWriter(file)) {
            // Write header
            writer.println("Missing Items Report");
            writer.println("Generated on: " + java.time.LocalDateTime.now().format(dateFormatter));
            writer.println("Date Range: " + dateFrom.getValue() + " to " + dateTo.getValue());
            writer.println();

            // Write CSV header
            writer.println("Product Name,SKU,Quantity,Reason,Report Date,Status,Reported By");

            // Write data
            for (MissingItem item : missingItems) {
                writer.printf("\"%s\",\"%s\",%d,\"%s\",\"%s\",\"%s\",\"%s\"%n",
                        item.getProduct().getName(),
                        item.getProduct().getSku(),
                        item.getQuantity(),
                        item.getReason().replace("\"", "\"\""), // Escape quotes
                        item.getReportDate().format(dateFormatter),
                        item.getStatus(),
                        item.getReportedBy() != null ? item.getReportedBy() : "");
            }

            // Write summary
            writer.println();
            writer.println("Summary");
            writer.println("Total Items," + missingItems.size());

            long pendingCount = missingItems.stream().filter(item -> "REPORTED".equals(item.getStatus())).count();
            long resolvedCount = missingItems.stream().filter(item -> "RESOLVED".equals(item.getStatus())).count();
            long writtenOffCount = missingItems.stream().filter(item -> "WRITTEN_OFF".equals(item.getStatus())).count();

            writer.println("Pending," + pendingCount);
            writer.println("Resolved," + resolvedCount);
            writer.println("Written Off," + writtenOffCount);
        }
    }

    private void exportToPDF(java.io.File file) throws Exception {
        // For now, create a simple text-based PDF content
        // In a real implementation, you would use a PDF library like iText
        try (java.io.PrintWriter writer = new java.io.PrintWriter(file)) {
            writer.println("MISSING ITEMS REPORT");
            writer.println("===================");
            writer.println();
            writer.println("Generated on: " + java.time.LocalDateTime.now().format(dateFormatter));
            writer.println("Date Range: " + dateFrom.getValue() + " to " + dateTo.getValue());
            writer.println();

            writer.printf("%-25s %-15s %-8s %-30s %-20s %-12s %-15s%n",
                    "Product", "SKU", "Qty", "Reason", "Date", "Status", "Reported By");
            writer.println("-".repeat(125));

            for (MissingItem item : missingItems) {
                writer.printf("%-25s %-15s %-8d %-30s %-20s %-12s %-15s%n",
                        truncate(item.getProduct().getName(), 25),
                        item.getProduct().getSku(),
                        item.getQuantity(),
                        truncate(item.getReason(), 30),
                        item.getReportDate().format(dateFormatter),
                        item.getStatus(),
                        truncate(item.getReportedBy() != null ? item.getReportedBy() : "", 15));
            }

            writer.println();
            writer.println("SUMMARY");
            writer.println("-------");
            writer.println("Total Items: " + missingItems.size());

            long pendingCount = missingItems.stream().filter(item -> "REPORTED".equals(item.getStatus())).count();
            long resolvedCount = missingItems.stream().filter(item -> "RESOLVED".equals(item.getStatus())).count();
            long writtenOffCount = missingItems.stream().filter(item -> "WRITTEN_OFF".equals(item.getStatus())).count();

            writer.println("Pending: " + pendingCount);
            writer.println("Resolved: " + resolvedCount);
            writer.println("Written Off: " + writtenOffCount);
        }
    }

    private String truncate(String text, int maxLength) {
        if (text == null) {
            return "";
        }
        return text.length() > maxLength ? text.substring(0, maxLength - 3) + "..." : text;
    }
}
