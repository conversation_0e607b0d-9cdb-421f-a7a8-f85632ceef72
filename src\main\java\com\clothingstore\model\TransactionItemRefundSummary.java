package com.clothingstore.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Summary model for transaction item refund information
 * Provides aggregated view of refund history for a specific transaction item
 */
public class TransactionItemRefundSummary {
    private Long transactionItemId;
    private Long transactionId;
    private Long productId;
    private String productName;
    private String productSku;
    private int originalQuantity;
    private int totalRefundedQuantity;
    private int remainingQuantity;
    private BigDecimal unitPrice;
    private BigDecimal totalRefundedAmount;
    private BigDecimal remainingRefundableAmount;
    private List<RefundItemTracking> refundHistory;
    private RefundStatus refundStatus;

    public enum RefundStatus {
        NOT_REFUNDED,
        PARTIALLY_REFUNDED,
        FULLY_REFUNDED
    }

    // Constructors
    public TransactionItemRefundSummary() {
        this.refundHistory = new ArrayList<>();
        this.totalRefundedQuantity = 0;
        this.totalRefundedAmount = BigDecimal.ZERO;
        this.refundStatus = RefundStatus.NOT_REFUNDED;
    }

    public TransactionItemRefundSummary(Long transactionItemId, Long transactionId, Long productId,
                                       String productName, int originalQuantity, BigDecimal unitPrice) {
        this();
        this.transactionItemId = transactionItemId;
        this.transactionId = transactionId;
        this.productId = productId;
        this.productName = productName;
        this.originalQuantity = originalQuantity;
        this.unitPrice = unitPrice;
        this.remainingQuantity = originalQuantity;
        this.remainingRefundableAmount = unitPrice.multiply(BigDecimal.valueOf(originalQuantity));
    }

    // Getters and Setters
    public Long getTransactionItemId() { return transactionItemId; }
    public void setTransactionItemId(Long transactionItemId) { this.transactionItemId = transactionItemId; }

    public Long getTransactionId() { return transactionId; }
    public void setTransactionId(Long transactionId) { this.transactionId = transactionId; }

    public Long getProductId() { return productId; }
    public void setProductId(Long productId) { this.productId = productId; }

    public String getProductName() { return productName; }
    public void setProductName(String productName) { this.productName = productName; }

    public String getProductSku() { return productSku; }
    public void setProductSku(String productSku) { this.productSku = productSku; }

    public int getOriginalQuantity() { return originalQuantity; }
    public void setOriginalQuantity(int originalQuantity) { 
        this.originalQuantity = originalQuantity;
        recalculateAmounts();
    }

    public int getTotalRefundedQuantity() { return totalRefundedQuantity; }
    public void setTotalRefundedQuantity(int totalRefundedQuantity) { 
        this.totalRefundedQuantity = totalRefundedQuantity;
        recalculateAmounts();
    }

    public int getRemainingQuantity() { return remainingQuantity; }
    public void setRemainingQuantity(int remainingQuantity) { this.remainingQuantity = remainingQuantity; }

    public BigDecimal getUnitPrice() { return unitPrice; }
    public void setUnitPrice(BigDecimal unitPrice) { 
        this.unitPrice = unitPrice;
        recalculateAmounts();
    }

    public BigDecimal getTotalRefundedAmount() { return totalRefundedAmount; }
    public void setTotalRefundedAmount(BigDecimal totalRefundedAmount) { this.totalRefundedAmount = totalRefundedAmount; }

    public BigDecimal getRemainingRefundableAmount() { return remainingRefundableAmount; }
    public void setRemainingRefundableAmount(BigDecimal remainingRefundableAmount) { this.remainingRefundableAmount = remainingRefundableAmount; }

    public List<RefundItemTracking> getRefundHistory() { return refundHistory; }
    public void setRefundHistory(List<RefundItemTracking> refundHistory) { 
        this.refundHistory = refundHistory != null ? refundHistory : new ArrayList<>();
        recalculateFromHistory();
    }

    public RefundStatus getRefundStatus() { return refundStatus; }
    public void setRefundStatus(RefundStatus refundStatus) { this.refundStatus = refundStatus; }

    // Business methods
    public void addRefundRecord(RefundItemTracking refundRecord) {
        if (refundRecord != null) {
            this.refundHistory.add(refundRecord);
            recalculateFromHistory();
        }
    }

    public boolean canRefund(int quantity) {
        return quantity > 0 && quantity <= remainingQuantity;
    }

    public boolean hasRefunds() {
        return totalRefundedQuantity > 0;
    }

    public boolean isFullyRefunded() {
        return refundStatus == RefundStatus.FULLY_REFUNDED;
    }

    public boolean isPartiallyRefunded() {
        return refundStatus == RefundStatus.PARTIALLY_REFUNDED;
    }

    public String getRefundStatusDisplay() {
        switch (refundStatus) {
            case NOT_REFUNDED:
                return "No Refunds";
            case PARTIALLY_REFUNDED:
                return String.format("Partially Refunded (%d/%d)", totalRefundedQuantity, originalQuantity);
            case FULLY_REFUNDED:
                return "Fully Refunded";
            default:
                return "Unknown";
        }
    }

    public String getQuantityDisplay() {
        if (totalRefundedQuantity == 0) {
            return String.format("Qty: %d", originalQuantity);
        } else {
            return String.format("Qty: %d | Returned: %d | Remaining: %d", 
                    originalQuantity, totalRefundedQuantity, remainingQuantity);
        }
    }

    // Private helper methods
    private void recalculateAmounts() {
        this.remainingQuantity = originalQuantity - totalRefundedQuantity;
        
        if (unitPrice != null) {
            this.totalRefundedAmount = unitPrice.multiply(BigDecimal.valueOf(totalRefundedQuantity));
            this.remainingRefundableAmount = unitPrice.multiply(BigDecimal.valueOf(remainingQuantity));
        }
        
        updateRefundStatus();
    }

    private void recalculateFromHistory() {
        this.totalRefundedQuantity = refundHistory.stream()
                .mapToInt(RefundItemTracking::getRefundedQuantity)
                .sum();
        
        this.totalRefundedAmount = refundHistory.stream()
                .map(RefundItemTracking::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        recalculateAmounts();
    }

    private void updateRefundStatus() {
        if (totalRefundedQuantity == 0) {
            this.refundStatus = RefundStatus.NOT_REFUNDED;
        } else if (totalRefundedQuantity >= originalQuantity) {
            this.refundStatus = RefundStatus.FULLY_REFUNDED;
        } else {
            this.refundStatus = RefundStatus.PARTIALLY_REFUNDED;
        }
    }

    // Utility methods
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TransactionItemRefundSummary that = (TransactionItemRefundSummary) o;
        return Objects.equals(transactionItemId, that.transactionItemId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(transactionItemId);
    }

    @Override
    public String toString() {
        return String.format("TransactionItemRefundSummary{item=%d, product='%s', status=%s, qty=%s}",
                transactionItemId, productName, refundStatus, getQuantityDisplay());
    }
}
