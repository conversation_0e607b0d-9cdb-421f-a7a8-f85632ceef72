package com.clothingstore.util;

import com.clothingstore.database.DatabaseManager;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Logger;

/**
 * Database schema updater for payment history tracking functionality
 * Creates and manages the payment_history table for detailed payment tracking
 */
public class PaymentHistorySchemaUpdater {
    
    private static final Logger LOGGER = Logger.getLogger(PaymentHistorySchemaUpdater.class.getName());
    
    /**
     * Update database schema to support detailed payment history tracking
     */
    public static void updateSchemaForPaymentHistory() throws SQLException {
        LOGGER.info("Starting payment history database schema updates...");
        
        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Create payment_history table
            createPaymentHistoryTable(conn);
            
            // Migrate existing payment data
            migrateExistingPaymentData(conn);
            
            LOGGER.info("Payment history database schema updates completed successfully");
        }
    }
    
    /**
     * Create the payment_history table for tracking individual payment records
     */
    private static void createPaymentHistoryTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS payment_history ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                + "transaction_id INTEGER NOT NULL, "
                + "payment_sequence INTEGER NOT NULL, "
                + "payment_amount DECIMAL(10,2) NOT NULL, "
                + "payment_method TEXT NOT NULL, "
                + "payment_date DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "payment_reference TEXT, "
                + "running_balance DECIMAL(10,2) NOT NULL, "
                + "remaining_balance DECIMAL(10,2) NOT NULL, "
                + "payment_type TEXT NOT NULL DEFAULT 'PAYMENT' CHECK (payment_type IN ('PAYMENT', 'REFUND')), "
                + "notes TEXT, "
                + "cashier_name TEXT, "
                + "status TEXT DEFAULT 'COMPLETED' CHECK (status IN ('PENDING', 'COMPLETED', 'CANCELLED', 'FAILED')), "
                + "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                + "FOREIGN KEY (transaction_id) REFERENCES transactions (id) ON DELETE CASCADE, "
                + "UNIQUE(transaction_id, payment_sequence)"
                + ")";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.executeUpdate();
            LOGGER.info("Payment history table created successfully");
        }
        
        // Create indexes for better performance
        createPaymentHistoryIndexes(conn);
    }
    
    /**
     * Create indexes for the payment_history table
     */
    private static void createPaymentHistoryIndexes(Connection conn) throws SQLException {
        String[] indexes = {
            "CREATE INDEX IF NOT EXISTS idx_payment_history_transaction_id ON payment_history(transaction_id)",
            "CREATE INDEX IF NOT EXISTS idx_payment_history_payment_date ON payment_history(payment_date)",
            "CREATE INDEX IF NOT EXISTS idx_payment_history_payment_type ON payment_history(payment_type)",
            "CREATE INDEX IF NOT EXISTS idx_payment_history_status ON payment_history(status)"
        };
        
        for (String indexSql : indexes) {
            try (PreparedStatement stmt = conn.prepareStatement(indexSql)) {
                stmt.executeUpdate();
            }
        }
        
        LOGGER.info("Payment history indexes created successfully");
    }
    
    /**
     * Migrate existing payment data from transactions table to payment_history table
     */
    private static void migrateExistingPaymentData(Connection conn) throws SQLException {
        // Check if migration has already been done
        String checkSql = "SELECT COUNT(*) FROM payment_history";
        try (PreparedStatement stmt = conn.prepareStatement(checkSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next() && rs.getInt(1) > 0) {
                LOGGER.info("Payment history migration already completed");
                return;
            }
        }
        
        // Migrate completed transactions
        migrateCompletedTransactions(conn);
        
        // Migrate partial payment transactions
        migratePartialPaymentTransactions(conn);
        
        LOGGER.info("Payment history migration completed successfully");
    }
    
    /**
     * Migrate completed transactions to payment history
     */
    private static void migrateCompletedTransactions(Connection conn) throws SQLException {
        String selectSql = "SELECT id, total_amount, payment_method, transaction_date, cashier_name, notes "
                + "FROM transactions WHERE status = 'COMPLETED' AND total_amount > 0";
        
        String insertSql = "INSERT INTO payment_history "
                + "(transaction_id, payment_sequence, payment_amount, payment_method, payment_date, "
                + "running_balance, remaining_balance, payment_type, notes, cashier_name, status) "
                + "VALUES (?, 1, ?, ?, ?, ?, 0.00, 'PAYMENT', ?, ?, 'COMPLETED')";
        
        try (PreparedStatement selectStmt = conn.prepareStatement(selectSql);
             PreparedStatement insertStmt = conn.prepareStatement(insertSql);
             ResultSet rs = selectStmt.executeQuery()) {
            
            int migratedCount = 0;
            while (rs.next()) {
                Long transactionId = rs.getLong("id");
                Double totalAmount = rs.getDouble("total_amount");
                String paymentMethod = rs.getString("payment_method");
                String transactionDate = rs.getString("transaction_date");
                String cashierName = rs.getString("cashier_name");
                String notes = rs.getString("notes");
                
                insertStmt.setLong(1, transactionId);
                insertStmt.setDouble(2, totalAmount);
                insertStmt.setString(3, paymentMethod);
                insertStmt.setString(4, transactionDate);
                insertStmt.setDouble(5, totalAmount);
                insertStmt.setString(6, notes);
                insertStmt.setString(7, cashierName);
                
                insertStmt.executeUpdate();
                migratedCount++;
            }
            
            LOGGER.info("Migrated " + migratedCount + " completed transactions to payment history");
        }
    }

    /**
     * Migrate partial payment transactions to payment history
     */
    private static void migratePartialPaymentTransactions(Connection conn) throws SQLException {
        String selectSql = "SELECT id, total_amount, amount_paid, payment_method, transaction_date, cashier_name, notes "
                + "FROM transactions WHERE status = 'PARTIAL_PAYMENT' AND amount_paid > 0";

        String insertSql = "INSERT INTO payment_history "
                + "(transaction_id, payment_sequence, payment_amount, payment_method, payment_date, "
                + "running_balance, remaining_balance, payment_type, notes, cashier_name, status) "
                + "VALUES (?, 1, ?, ?, ?, ?, ?, 'PAYMENT', ?, ?, 'COMPLETED')";

        try (PreparedStatement selectStmt = conn.prepareStatement(selectSql);
             PreparedStatement insertStmt = conn.prepareStatement(insertSql);
             ResultSet rs = selectStmt.executeQuery()) {

            int migratedCount = 0;
            while (rs.next()) {
                Long transactionId = rs.getLong("id");
                Double totalAmount = rs.getDouble("total_amount");
                Double amountPaid = rs.getDouble("amount_paid");
                Double remainingBalance = totalAmount - amountPaid;
                String paymentMethod = rs.getString("payment_method");
                String transactionDate = rs.getString("transaction_date");
                String cashierName = rs.getString("cashier_name");
                String notes = rs.getString("notes");

                insertStmt.setLong(1, transactionId);
                insertStmt.setDouble(2, amountPaid);
                insertStmt.setString(3, paymentMethod);
                insertStmt.setString(4, transactionDate);
                insertStmt.setDouble(5, amountPaid);
                insertStmt.setDouble(6, remainingBalance);
                insertStmt.setString(7, notes);
                insertStmt.setString(8, cashierName);

                insertStmt.executeUpdate();
                migratedCount++;
            }

            LOGGER.info("Migrated " + migratedCount + " partial payment transactions to payment history");
        }
    }

    /**
     * Run all payment history schema updates
     */
    public static void runAllUpdates() {
        try {
            updateSchemaForPaymentHistory();
            LOGGER.info("All payment history schema updates completed successfully");
        } catch (SQLException e) {
            LOGGER.severe("Failed to update payment history schema: " + e.getMessage());
            throw new RuntimeException("Payment history schema update failed", e);
        }
    }

    /**
     * Main method for running schema updates
     */
    public static void main(String[] args) {
        System.out.println("Running payment history schema updates...");
        runAllUpdates();
        System.out.println("Payment history schema updates completed successfully!");
    }
}
