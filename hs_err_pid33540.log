#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 760976 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=33540, tid=42704
#
# JRE version: Java(TM) SE Runtime Environment (17.0.12+8) (build 17.0.12+8-LTS-286)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.12+8-LTS-286, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --module-path=javafx-sdk-17.0.2\lib --add-modules=javafx.controls,javafx.fxml com.clothingstore.ClothingStoreApp

Host: Intel(R) Core(TM) i7-14700HX, 28 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Thu Jun 26 18:12:58 2025 Egypt Daylight Time elapsed time: 24.095380 seconds (0d 0h 0m 24s)

---------------  T H R E A D  ---------------

Current thread (0x000001794bdcdbf0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=42704, stack(0x00000040a9400000,0x00000040a9500000)]


Current CompileTask:
C2:  24097 6390 % !   4       org.sqlite.SQLiteConfig::apply @ 19 (790 bytes)

Stack: [0x00000040a9400000,0x00000040a9500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67a04a]
V  [jvm.dll+0x7da4ed]
V  [jvm.dll+0x7dbe33]
V  [jvm.dll+0x7dc4a3]
V  [jvm.dll+0x24508f]
V  [jvm.dll+0xab773]
V  [jvm.dll+0xabd3c]
V  [jvm.dll+0x2aa52f]
V  [jvm.dll+0x581eb9]
V  [jvm.dll+0x21f422]
V  [jvm.dll+0x21837b]
V  [jvm.dll+0x215b65]
V  [jvm.dll+0x1a2960]
V  [jvm.dll+0x22610b]
V  [jvm.dll+0x2242ab]
V  [jvm.dll+0x79075c]
V  [jvm.dll+0x78abea]
V  [jvm.dll+0x678f35]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000017992382e10, length=33, elements={
0x0000017927b81410, 0x000001794bdb1720, 0x000001794bdb22e0, 0x000001794bdc8fb0,
0x000001794bdc9b70, 0x000001794bdcc540, 0x000001794bdccf00, 0x000001794bdcdbf0,
0x000001794bdce5f0, 0x000001794bdd3010, 0x000001794bd9ad00, 0x000001794c8da8e0,
0x000001799224d1c0, 0x00000179927cfa70, 0x0000017992b5cf30, 0x0000017992ec9760,
0x0000017992eca050, 0x0000017992f8d6d0, 0x0000017992f8b870, 0x0000017992f8c290,
0x0000017992f8bd80, 0x0000017992f8c7a0, 0x0000017992f8ccb0, 0x0000017992f8a430,
0x0000017992f8dbe0, 0x0000017992f8a940, 0x0000017992f8ae50, 0x0000017992f8b360,
0x0000017999e3b2a0, 0x0000017999c9b870, 0x0000017999c9ed90, 0x0000017999c9bdc0,
0x0000017999c9d850
}

Java Threads: ( => current thread )
  0x0000017927b81410 JavaThread "main" [_thread_blocked, id=36396, stack(0x00000040a8700000,0x00000040a8800000)]
  0x000001794bdb1720 JavaThread "Reference Handler" daemon [_thread_blocked, id=40256, stack(0x00000040a8e00000,0x00000040a8f00000)]
  0x000001794bdb22e0 JavaThread "Finalizer" daemon [_thread_blocked, id=42604, stack(0x00000040a8f00000,0x00000040a9000000)]
  0x000001794bdc8fb0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=21352, stack(0x00000040a9000000,0x00000040a9100000)]
  0x000001794bdc9b70 JavaThread "Attach Listener" daemon [_thread_blocked, id=2244, stack(0x00000040a9100000,0x00000040a9200000)]
  0x000001794bdcc540 JavaThread "Service Thread" daemon [_thread_blocked, id=32472, stack(0x00000040a9200000,0x00000040a9300000)]
  0x000001794bdccf00 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=43760, stack(0x00000040a9300000,0x00000040a9400000)]
=>0x000001794bdcdbf0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=42704, stack(0x00000040a9400000,0x00000040a9500000)]
  0x000001794bdce5f0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=8948, stack(0x00000040a9500000,0x00000040a9600000)]
  0x000001794bdd3010 JavaThread "Sweeper thread" daemon [_thread_blocked, id=10768, stack(0x00000040a9600000,0x00000040a9700000)]
  0x000001794bd9ad00 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=40672, stack(0x00000040a9700000,0x00000040a9800000)]
  0x000001794c8da8e0 JavaThread "Notification Thread" daemon [_thread_blocked, id=19432, stack(0x00000040aa000000,0x00000040aa100000)]
  0x000001799224d1c0 JavaThread "QuantumRenderer-0" daemon [_thread_blocked, id=22344, stack(0x00000040a9e00000,0x00000040a9f00000)]
  0x00000179927cfa70 JavaThread "InvokeLaterDispatcher" daemon [_thread_blocked, id=7960, stack(0x00000040a9c00000,0x00000040a9d00000)]
  0x0000017992b5cf30 JavaThread "JavaFX Application Thread" [_thread_in_native, id=20964, stack(0x00000040a9d00000,0x00000040a9e00000)]
  0x0000017992ec9760 JavaThread "JavaFX-Launcher" [_thread_blocked, id=22768, stack(0x00000040ac100000,0x00000040ac200000)]
  0x0000017992eca050 JavaThread "Thread-2" daemon [_thread_in_native, id=16820, stack(0x00000040ac000000,0x00000040ac100000)]
  0x0000017992f8d6d0 JavaThread "pool-2-thread-1" [_thread_blocked, id=26812, stack(0x00000040ac800000,0x00000040ac900000)]
  0x0000017992f8b870 JavaThread "Timer-0" daemon [_thread_blocked, id=43960, stack(0x00000040aca00000,0x00000040acb00000)]
  0x0000017992f8c290 JavaThread "DashboardAutoRefresh" daemon [_thread_blocked, id=7448, stack(0x00000040a9b00000,0x00000040a9c00000)]
  0x0000017992f8bd80 JavaThread "FastDataRefresh" daemon [_thread_blocked, id=41224, stack(0x00000040ac900000,0x00000040aca00000)]
  0x0000017992f8c7a0 JavaThread "RealTimeDataService" daemon [_thread_blocked, id=33320, stack(0x00000040acb00000,0x00000040acc00000)]
  0x0000017992f8ccb0 JavaThread "Prism Font Disposer" daemon [_thread_blocked, id=42412, stack(0x00000040ada00000,0x00000040adb00000)]
  0x0000017992f8a430 JavaThread "Cleaner-0" daemon [_thread_blocked, id=34500, stack(0x00000040ad800000,0x00000040ad900000)]
  0x0000017992f8dbe0 JavaThread "pool-4-thread-1" [_thread_blocked, id=32888, stack(0x00000040add00000,0x00000040ade00000)]
  0x0000017992f8a940 JavaThread "pool-4-thread-2" [_thread_blocked, id=16396, stack(0x00000040ade00000,0x00000040adf00000)]
  0x0000017992f8ae50 JavaThread "pool-4-thread-3" [_thread_blocked, id=37456, stack(0x00000040adf00000,0x00000040ae000000)]
  0x0000017992f8b360 JavaThread "pool-4-thread-4" [_thread_blocked, id=36636, stack(0x00000040ae000000,0x00000040ae100000)]
  0x0000017999e3b2a0 JavaThread "ReportsAutoRefresh" daemon [_thread_blocked, id=25788, stack(0x00000040ae100000,0x00000040ae200000)]
  0x0000017999c9b870 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=3552, stack(0x00000040a9800000,0x00000040a9900000)]
  0x0000017999c9ed90 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=19880, stack(0x00000040a9900000,0x00000040a9a00000)]
  0x0000017999c9bdc0 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=13940, stack(0x00000040a9a00000,0x00000040a9b00000)]
  0x0000017999c9d850 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=32392, stack(0x00000040ad700000,0x00000040ad800000)]

Other Threads:
  0x000001794bdacc70 VMThread "VM Thread" [stack: 0x00000040a8d00000,0x00000040a8e00000] [id=40412]
  0x000001794bbe9800 WatcherThread [stack: 0x00000040aa100000,0x00000040aa200000] [id=29652]
  0x0000017927bf3a50 GCTaskThread "GC Thread#0" [stack: 0x00000040a8800000,0x00000040a8900000] [id=31716]
  0x0000017992fb61a0 GCTaskThread "GC Thread#1" [stack: 0x00000040ac200000,0x00000040ac300000] [id=42780]
  0x0000017992fb6460 GCTaskThread "GC Thread#2" [stack: 0x00000040ac300000,0x00000040ac400000] [id=35184]
  0x0000017992fb6720 GCTaskThread "GC Thread#3" [stack: 0x00000040ac400000,0x00000040ac500000] [id=41664]
  0x0000017992fb69e0 GCTaskThread "GC Thread#4" [stack: 0x00000040ac500000,0x00000040ac600000] [id=42868]
  0x0000017992ffd800 GCTaskThread "GC Thread#5" [stack: 0x00000040ac600000,0x00000040ac700000] [id=23532]
  0x00000179930c3040 GCTaskThread "GC Thread#6" [stack: 0x00000040ac700000,0x00000040ac800000] [id=18656]
  0x0000017998fa6990 GCTaskThread "GC Thread#7" [stack: 0x00000040acf00000,0x00000040ad000000] [id=39996]
  0x000001799849e3f0 GCTaskThread "GC Thread#8" [stack: 0x00000040ad000000,0x00000040ad100000] [id=29760]
  0x00000179986b4420 GCTaskThread "GC Thread#9" [stack: 0x00000040ad100000,0x00000040ad200000] [id=31912]
  0x0000017998612d80 GCTaskThread "GC Thread#10" [stack: 0x00000040ad200000,0x00000040ad300000] [id=39164]
  0x0000017993043b20 GCTaskThread "GC Thread#11" [stack: 0x00000040ad300000,0x00000040ad400000] [id=32988]
  0x00000179983331c0 GCTaskThread "GC Thread#12" [stack: 0x00000040ad400000,0x00000040ad500000] [id=35188]
  0x0000017998333480 GCTaskThread "GC Thread#13" [stack: 0x00000040ad500000,0x00000040ad600000] [id=22096]
  0x0000017998332400 GCTaskThread "GC Thread#14" [stack: 0x00000040ae200000,0x00000040ae300000] [id=41192]
  0x0000017998333cc0 GCTaskThread "GC Thread#15" [stack: 0x00000040ae300000,0x00000040ae400000] [id=31576]
  0x0000017998332c40 GCTaskThread "GC Thread#16" [stack: 0x00000040ae400000,0x00000040ae500000] [id=38932]
  0x0000017998333f80 GCTaskThread "GC Thread#17" [stack: 0x00000040ae500000,0x00000040ae600000] [id=26672]
  0x0000017999eba2a0 GCTaskThread "GC Thread#18" [stack: 0x00000040aed00000,0x00000040aee00000] [id=27668]
  0x0000017999eb8f60 GCTaskThread "GC Thread#19" [stack: 0x00000040ad600000,0x00000040ad700000] [id=34660]
  0x0000017927b9ddd0 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000040a8900000,0x00000040a8a00000] [id=40576]
  0x0000017927c059e0 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000040a8a00000,0x00000040a8b00000] [id=17988]
  0x00000179983326c0 ConcurrentGCThread "G1 Conc#1" [stack: 0x00000040ae600000,0x00000040ae700000] [id=32676]
  0x000001799a3d69c0 ConcurrentGCThread "G1 Conc#2" [stack: 0x00000040ae700000,0x00000040ae800000] [id=1992]
  0x000001799a3d6700 ConcurrentGCThread "G1 Conc#3" [stack: 0x00000040ae800000,0x00000040ae900000] [id=39216]
  0x000001799a3d7d00 ConcurrentGCThread "G1 Conc#4" [stack: 0x00000040ae900000,0x00000040aea00000] [id=32908]
  0x0000017927c4ebf0 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000040a8b00000,0x00000040a8c00000] [id=38812]
  0x000001794bcdfe50 ConcurrentGCThread "G1 Service" [stack: 0x00000040a8c00000,0x00000040a8d00000] [id=29592]

Threads with active compile tasks:
C2 CompilerThread0    27189 6390 % !   4       org.sqlite.SQLiteConfig::apply @ 19 (790 bytes)
C2 CompilerThread1    27189 6408       4       org.sqlite.date.FastDateParser::parse (107 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000704800000, size: 4024 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001794d000000-0x000001794dbd0000-0x000001794dbd0000), size 12386304, SharedBaseAddress: 0x000001794d000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001794e000000-0x000001798e000000, reserved size: 1073741824
Narrow klass base: 0x000001794d000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 28 total, 28 available
 Memory: 16091M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 4024M
 Pre-touch: Disabled
 Parallel Workers: 20
 Concurrent Workers: 5
 Concurrent Refinement Workers: 20
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 75776K, used 42461K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 2 survivors (4096K)
 Metaspace       used 25499K, committed 25856K, reserved 1114112K
  class space    used 3663K, committed 3840K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000704800000, 0x0000000704a00000, 0x0000000704a00000|100%| O|  |TAMS 0x0000000704a00000, 0x0000000704800000| Untracked 
|   1|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|   2|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|   3|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%|HS|  |TAMS 0x0000000705000000, 0x0000000704e00000| Complete 
|   4|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705000000, 0x0000000705000000| Untracked 
|   5|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|   6|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705400000, 0x0000000705400000| Untracked 
|   7|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|   8|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705800000, 0x0000000705800000| Untracked 
|   9|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Untracked 
|  10|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705c00000, 0x0000000705c00000| Untracked 
|  11|0x0000000705e00000, 0x0000000705f77400, 0x0000000706000000| 73%| O|  |TAMS 0x0000000705e00000, 0x0000000705e00000| Untracked 
|  12|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000, 0x0000000706000000| Untracked 
|  13|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000, 0x0000000706200000| Untracked 
|  14|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000, 0x0000000706400000| Untracked 
|  15|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000, 0x0000000706600000| Untracked 
|  16|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000, 0x0000000706800000| Untracked 
|  17|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000, 0x0000000706a00000| Untracked 
|  18|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000, 0x0000000706c00000| Untracked 
|  19|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000, 0x0000000706e00000| Untracked 
|  20|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| S|CS|TAMS 0x0000000707000000, 0x0000000707000000| Complete 
|  21|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| S|CS|TAMS 0x0000000707200000, 0x0000000707200000| Complete 
|  22|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000, 0x0000000707400000| Untracked 
|  23|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000, 0x0000000707600000| Untracked 
|  24|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000, 0x0000000707800000| Untracked 
|  25|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000, 0x0000000707a00000| Untracked 
|  26|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000, 0x0000000707c00000| Untracked 
|  27|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000, 0x0000000707e00000| Untracked 
|  28|0x0000000708000000, 0x00000007080e8240, 0x0000000708200000| 45%| E|  |TAMS 0x0000000708000000, 0x0000000708000000| Complete 
|  96|0x0000000710800000, 0x0000000710a00000, 0x0000000710a00000|100%| E|CS|TAMS 0x0000000710800000, 0x0000000710800000| Complete 
|  97|0x0000000710a00000, 0x0000000710c00000, 0x0000000710c00000|100%| E|CS|TAMS 0x0000000710a00000, 0x0000000710a00000| Complete 
|  98|0x0000000710c00000, 0x0000000710e00000, 0x0000000710e00000|100%| E|CS|TAMS 0x0000000710c00000, 0x0000000710c00000| Complete 
|  99|0x0000000710e00000, 0x0000000711000000, 0x0000000711000000|100%| E|CS|TAMS 0x0000000710e00000, 0x0000000710e00000| Complete 
| 100|0x0000000711000000, 0x0000000711200000, 0x0000000711200000|100%| E|CS|TAMS 0x0000000711000000, 0x0000000711000000| Complete 
| 101|0x0000000711200000, 0x0000000711400000, 0x0000000711400000|100%| E|CS|TAMS 0x0000000711200000, 0x0000000711200000| Complete 
| 124|0x0000000714000000, 0x0000000714200000, 0x0000000714200000|100%| E|CS|TAMS 0x0000000714000000, 0x0000000714000000| Complete 
| 125|0x0000000714200000, 0x0000000714400000, 0x0000000714400000|100%| E|CS|TAMS 0x0000000714200000, 0x0000000714200000| Complete 

Card table byte_map: [0x000001793fc10000,0x00000179403f0000] _byte_map_base: 0x000001793c3ec000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000017927bf40b0, (CMBitMap*) 0x0000017927bf4070
 Prev Bits: [0x0000017944ab0000, 0x0000017948990000)
 Next Bits: [0x0000017940bd0000, 0x0000017944ab0000)

Polling page: 0x0000017925ac0000

Metaspace:

Usage:
  Non-class:     21.32 MB used.
      Class:      3.58 MB used.
       Both:     24.90 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      21.50 MB ( 34%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.75 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      25.25 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  10.33 MB
       Class:  12.11 MB
        Both:  22.44 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.12 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 5.
num_arena_births: 340.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 404.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 5.
num_chunks_taken_from_freelist: 1184.
num_chunk_merges: 5.
num_chunk_splits: 830.
num_chunks_enlarged: 645.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=2676Kb max_used=2927Kb free=116491Kb
 bounds [0x0000017936fe0000, 0x00000179372c0000, 0x000001793e440000]
CodeHeap 'profiled nmethods': size=119104Kb used=12535Kb max_used=13749Kb free=106568Kb
 bounds [0x000001792f440000, 0x00000179301c0000, 0x0000017936890000]
CodeHeap 'non-nmethods': size=7488Kb used=1490Kb max_used=3253Kb free=5998Kb
 bounds [0x0000017936890000, 0x0000017936bd0000, 0x0000017936fe0000]
 total_blobs=6527 nmethods=5662 adapters=775
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 15.995 Thread 0x000001794bdcdbf0 6383       4       java.util.concurrent.locks.AbstractQueuedSynchronizer::signalNext (34 bytes)
Event: 15.997 Thread 0x000001794bdcdbf0 nmethod 6383 0x0000017937008410 code [0x00000179370085a0, 0x0000017937008688]
Event: 16.476 Thread 0x000001794bdce5f0 6384       3       javafx.scene.layout.BorderPane::getNodeMargin (17 bytes)
Event: 16.477 Thread 0x000001794bdce5f0 nmethod 6384 0x000001792f9e9b90 code [0x000001792f9e9dc0, 0x000001792f9ea918]
Event: 16.477 Thread 0x000001794bdce5f0 6385       3       javafx.scene.layout.BorderPane::getMargin (10 bytes)
Event: 16.477 Thread 0x000001794bdce5f0 nmethod 6385 0x000001792f554e10 code [0x000001792f555040, 0x000001792f555ab8]
Event: 16.477 Thread 0x000001794bdce5f0 6386       3       com.sun.javafx.collections.ObservableMapWrapper::get (11 bytes)
Event: 16.477 Thread 0x000001794bdce5f0 nmethod 6386 0x000001792fa05010 code [0x000001792fa051c0, 0x000001792fa053a8]
Event: 16.608 Thread 0x000001794bdce5f0 6387       3       javafx.scene.control.skin.LabeledSkinBase::layoutChildren (11 bytes)
Event: 16.609 Thread 0x000001794bdce5f0 nmethod 6387 0x000001792fa81b10 code [0x000001792fa81ca0, 0x000001792fa81e68]
Event: 17.024 Thread 0x000001794bdce5f0 6388   !   3       com.clothingstore.dao.CustomerDAO::findById (285 bytes)
Event: 17.025 Thread 0x000001794bdce5f0 nmethod 6388 0x000001792f6d9f90 code [0x000001792f6da4e0, 0x000001792f6dd5e8]
Event: 17.046 Thread 0x000001794bdcdbf0 6389       4       java.lang.Integer::numberOfLeadingZeros (79 bytes)
Event: 17.047 Thread 0x000001794bdcdbf0 nmethod 6389 0x0000017937008110 code [0x0000017937008280, 0x0000017937008338]
Event: 17.053 Thread 0x000001794bdcdbf0 6390 % !   4       org.sqlite.SQLiteConfig::apply @ 19 (790 bytes)
Event: 19.152 Thread 0x000001794bdce5f0 6392       3       java.lang.String::indexOf (64 bytes)
Event: 21.076 Thread 0x000001794bdce5f0 nmethod 6392 0x000001792f8f2310 code [0x000001792f8f2520, 0x000001792f8f2a08]
Event: 21.076 Thread 0x000001794bdce5f0 6393       3       java.time.temporal.ChronoField::isTimeBased (19 bytes)
Event: 21.076 Thread 0x000001794bdce5f0 nmethod 6393 0x000001792f6d9a10 code [0x000001792f6d9ba0, 0x000001792f6d9d38]
Event: 21.468 Thread 0x000001794bdce5f0 6395       3       javafx.scene.text.Text$3::invalidated (72 bytes)

GC Heap History (12 events):
Event: 0.814 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 258048K, used 22528K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 0 survivors (0K)
 Metaspace       used 4670K, committed 4864K, reserved 1114112K
  class space    used 522K, committed 576K, reserved 1048576K
}
Event: 0.817 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 258048K, used 3579K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 4670K, committed 4864K, reserved 1114112K
  class space    used 522K, committed 576K, reserved 1048576K
}
Event: 2.124 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 258048K, used 38395K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 19 young (38912K), 2 survivors (4096K)
 Metaspace       used 14166K, committed 14464K, reserved 1114112K
  class space    used 1822K, committed 1984K, reserved 1048576K
}
Event: 2.130 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 258048K, used 7682K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 14166K, committed 14464K, reserved 1114112K
  class space    used 1822K, committed 1984K, reserved 1048576K
}
Event: 4.483 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 258048K, used 52738K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 24 young (49152K), 3 survivors (6144K)
 Metaspace       used 21245K, committed 21504K, reserved 1114112K
  class space    used 3020K, committed 3136K, reserved 1048576K
}
Event: 4.490 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 258048K, used 15854K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 6 survivors (12288K)
 Metaspace       used 21245K, committed 21504K, reserved 1114112K
  class space    used 3020K, committed 3136K, reserved 1048576K
}
Event: 5.403 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 75776K, used 48622K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 22 young (45056K), 6 survivors (12288K)
 Metaspace       used 24232K, committed 24576K, reserved 1114112K
  class space    used 3540K, committed 3712K, reserved 1048576K
}
Event: 5.408 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 75776K, used 21262K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 24232K, committed 24576K, reserved 1114112K
  class space    used 3540K, committed 3712K, reserved 1048576K
}
Event: 5.440 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 75776K, used 23310K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 3 survivors (6144K)
 Metaspace       used 24519K, committed 24832K, reserved 1114112K
  class space    used 3581K, committed 3712K, reserved 1048576K
}
Event: 5.442 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 75776K, used 22286K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 24519K, committed 24832K, reserved 1114112K
  class space    used 3581K, committed 3712K, reserved 1048576K
}
Event: 12.879 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 75776K, used 50958K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 1 survivors (2048K)
 Metaspace       used 25482K, committed 25856K, reserved 1114112K
  class space    used 3663K, committed 3840K, reserved 1048576K
}
Event: 12.882 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 75776K, used 26077K [0x0000000704800000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 25482K, committed 25856K, reserved 1114112K
  class space    used 3663K, committed 3840K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 12.620 Thread 0x0000017992b5cf30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000017937047bb4 relative=0x00000000000004f4
Event: 12.620 Thread 0x0000017992b5cf30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000017937047bb4 method=java.lang.AbstractStringBuilder.putStringAt(ILjava/lang/String;II)V @ 8 c2
Event: 12.620 Thread 0x0000017992b5cf30 DEOPT PACKING pc=0x0000017937047bb4 sp=0x00000040a9dfe6f0
Event: 12.620 Thread 0x0000017992b5cf30 DEOPT UNPACKING pc=0x00000179368e23a3 sp=0x00000040a9dfe5e0 mode 2
Event: 12.620 Thread 0x0000017992b5cf30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000017937037554 relative=0x0000000000000134
Event: 12.620 Thread 0x0000017992b5cf30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000017937037554 method=java.lang.AbstractStringBuilder.putStringAt(ILjava/lang/String;II)V @ 8 c2
Event: 12.620 Thread 0x0000017992b5cf30 DEOPT PACKING pc=0x0000017937037554 sp=0x00000040a9dfe690
Event: 12.620 Thread 0x0000017992b5cf30 DEOPT UNPACKING pc=0x00000179368e23a3 sp=0x00000040a9dfe5d8 mode 2
Event: 12.621 Thread 0x0000017992b5cf30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001793703eb84 relative=0x0000000000000464
Event: 12.621 Thread 0x0000017992b5cf30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001793703eb84 method=java.lang.String.substring(II)Ljava/lang/String; @ 31 c2
Event: 12.621 Thread 0x0000017992b5cf30 DEOPT PACKING pc=0x000001793703eb84 sp=0x00000040a9dfe470
Event: 12.621 Thread 0x0000017992b5cf30 DEOPT UNPACKING pc=0x00000179368e23a3 sp=0x00000040a9dfe440 mode 2
Event: 17.188 Thread 0x0000017992b5cf30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000017937227ed4 relative=0x00000000000001d4
Event: 18.060 Thread 0x0000017992b5cf30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000017937227ed4 method=java.lang.String.indexOf(Ljava/lang/String;)I @ 10 c2
Event: 18.060 Thread 0x0000017992b5cf30 DEOPT PACKING pc=0x0000017937227ed4 sp=0x00000040a9dfe740
Event: 18.060 Thread 0x0000017992b5cf30 DEOPT UNPACKING pc=0x00000179368e23a3 sp=0x00000040a9dfe6d0 mode 2
Event: 18.061 Thread 0x0000017992b5cf30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000017936fee994 relative=0x0000000000000074
Event: 18.061 Thread 0x0000017992b5cf30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000017936fee994 method=java.lang.String.isLatin1()Z @ 10 c2
Event: 18.061 Thread 0x0000017992b5cf30 DEOPT PACKING pc=0x0000017936fee994 sp=0x00000040a9dfe4d0
Event: 18.061 Thread 0x0000017992b5cf30 DEOPT UNPACKING pc=0x00000179368e23a3 sp=0x00000040a9dfe3a8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 2.513 Thread 0x0000017992b5cf30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000712ecf3c8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000712ecf3c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.738 Thread 0x000001799224d1c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007128eaaa0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int)'> (0x00000007128eaaa0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.338 Thread 0x0000017992b5cf30 Exception <a 'java/lang/NullPointerException'{0x00000007125c0968}> (0x00000007125c0968) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 4.346 Thread 0x0000017992b5cf30 Exception <a 'java/io/FileNotFoundException'{0x00000007125e2f10}> (0x00000007125e2f10) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 4.355 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassNotFoundException'{0x000000071228f688}: com/sun/javafx/scene/control/skin/resources/controls-nt> (0x000000071228f688) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 535]
Event: 4.356 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassNotFoundException'{0x0000000712297ef8}: com/sun/javafx/scene/control/skin/resources/controls-nt_en> (0x0000000712297ef8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 535]
Event: 4.356 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassNotFoundException'{0x0000000712299928}: com/sun/javafx/scene/control/skin/resources/controls-nt_en_US> (0x0000000712299928) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 535]
Event: 4.380 Thread 0x0000017992b5cf30 Exception <a 'java/lang/NoSuchMethodError'{0x00000007120330a8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007120330a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.571 Thread 0x0000017992b5cf30 Exception <a 'java/lang/NoSuchMethodError'{0x00000007080bfe28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, double)'> (0x00000007080bfe28) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.693 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassCastException'{0x000000070797c198}: class javafx.scene.paint.LinearGradient cannot be cast to class javafx.scene.paint.Color (javafx.scene.paint.LinearGradient and javafx.scene.paint.Color are in module javafx.graphics of loader 'app')> (0x000000070797c198) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 444]
Event: 4.698 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassCastException'{0x00000007079d27f8}: class javafx.scene.paint.LinearGradient cannot be cast to class javafx.scene.paint.Color (javafx.scene.paint.LinearGradient and javafx.scene.paint.Color are in module javafx.graphics of loader 'app')> (0x00000007079d27f8) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 444]
Event: 4.700 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassCastException'{0x0000000707628738}: class javafx.scene.paint.LinearGradient cannot be cast to class javafx.scene.paint.Color (javafx.scene.paint.LinearGradient and javafx.scene.paint.Color are in module javafx.graphics of loader 'app')> (0x0000000707628738) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 444]
Event: 4.701 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassCastException'{0x000000070767cef0}: class javafx.scene.paint.LinearGradient cannot be cast to class javafx.scene.paint.Color (javafx.scene.paint.LinearGradient and javafx.scene.paint.Color are in module javafx.graphics of loader 'app')> (0x000000070767cef0) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 444]
Event: 4.703 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassCastException'{0x00000007076d22c0}: class javafx.scene.paint.LinearGradient cannot be cast to class javafx.scene.paint.Color (javafx.scene.paint.LinearGradient and javafx.scene.paint.Color are in module javafx.graphics of loader 'app')> (0x00000007076d22c0) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 444]
Event: 4.704 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassCastException'{0x00000007077278d0}: class javafx.scene.paint.LinearGradient cannot be cast to class javafx.scene.paint.Color (javafx.scene.paint.LinearGradient and javafx.scene.paint.Color are in module javafx.graphics of loader 'app')> (0x00000007077278d0) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 444]
Event: 4.708 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassCastException'{0x00000007077a73a0}: class javafx.scene.paint.LinearGradient cannot be cast to class javafx.scene.paint.Color (javafx.scene.paint.LinearGradient and javafx.scene.paint.Color are in module javafx.graphics of loader 'app')> (0x00000007077a73a0) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 444]
Event: 4.710 Thread 0x0000017992b5cf30 Exception <a 'java/lang/ClassCastException'{0x00000007074071f0}: class javafx.scene.paint.LinearGradient cannot be cast to class javafx.scene.paint.Color (javafx.scene.paint.LinearGradient and javafx.scene.paint.Color are in module javafx.graphics of loader 'app')> (0x00000007074071f0) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 444]
Event: 5.291 Thread 0x0000017992b5cf30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000706a8bac8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000706a8bac8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.294 Thread 0x0000017992b5cf30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000706a92140}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000706a92140) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.369 Thread 0x0000017992b5cf30 Exception <a 'java/lang/NoSuchMethodError'{0x000000070698b888}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x000000070698b888) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 7.011 Executing VM operation: Cleanup
Event: 7.012 Executing VM operation: Cleanup done
Event: 7.027 Executing VM operation: HandshakeAllThreads
Event: 7.027 Executing VM operation: HandshakeAllThreads done
Event: 8.028 Executing VM operation: Cleanup
Event: 8.028 Executing VM operation: Cleanup done
Event: 10.028 Executing VM operation: Cleanup
Event: 10.028 Executing VM operation: Cleanup done
Event: 12.029 Executing VM operation: Cleanup
Event: 12.029 Executing VM operation: Cleanup done
Event: 12.879 Executing VM operation: G1CollectForAllocation
Event: 12.882 Executing VM operation: G1CollectForAllocation done
Event: 14.881 Executing VM operation: Cleanup
Event: 14.881 Executing VM operation: Cleanup done
Event: 16.882 Executing VM operation: Cleanup
Event: 16.882 Executing VM operation: Cleanup done
Event: 18.883 Executing VM operation: Cleanup
Event: 18.883 Executing VM operation: Cleanup done
Event: 20.883 Executing VM operation: Cleanup
Event: 20.883 Executing VM operation: Cleanup done

Events (20 events):
Event: 7.032 Thread 0x000001794bdd3010 flushing nmethod 0x000001792ffd1f10
Event: 7.032 Thread 0x000001794bdd3010 flushing nmethod 0x000001792fffce10
Event: 7.119 Thread 0x0000017999c9ed90 Thread exited: 0x0000017999c9ed90
Event: 8.572 Thread 0x000001794c46c870 Thread exited: 0x000001794c46c870
Event: 8.860 Thread 0x000001794c46c350 Thread exited: 0x000001794c46c350
Event: 8.860 Thread 0x000001794c3b0b50 Thread exited: 0x000001794c3b0b50
Event: 12.086 Thread 0x0000017999c9dda0 Thread added: 0x0000017999c9dda0
Event: 12.086 Thread 0x0000017999c9cdb0 Thread added: 0x0000017999c9cdb0
Event: 12.086 Thread 0x0000017999c9e2f0 Thread added: 0x0000017999c9e2f0
Event: 12.190 Thread 0x0000017999c9b320 Thread added: 0x0000017999c9b320
Event: 12.311 Thread 0x0000017999c9b320 Thread exited: 0x0000017999c9b320
Event: 12.334 Thread 0x0000017999c9b320 Thread added: 0x0000017999c9b320
Event: 12.475 Thread 0x0000017999c9b320 Thread exited: 0x0000017999c9b320
Event: 12.478 loading class java/text/DecimalFormat$FastPathData
Event: 12.478 loading class java/text/DecimalFormat$FastPathData done
Event: 12.478 loading class java/text/DecimalFormat$DigitArrays
Event: 12.478 loading class java/text/DecimalFormat$DigitArrays done
Event: 13.461 Thread 0x0000017999c9e2f0 Thread exited: 0x0000017999c9e2f0
Event: 13.884 Thread 0x0000017999c9cdb0 Thread exited: 0x0000017999c9cdb0
Event: 14.477 Thread 0x0000017999c9dda0 Thread exited: 0x0000017999c9dda0


Dynamic libraries:
0x00007ff641a20000 - 0x00007ff641a30000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ff830020000 - 0x00007ff830285000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff82f960000 - 0x00007ff82fa29000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff82d700000 - 0x00007ff82dae8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff82d250000 - 0x00007ff82d39b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fffdeaa0000 - 0x00007fffdeabb000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007fffe0500000 - 0x00007fffe0519000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ff82e030000 - 0x00007ff82e0e3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff82df30000 - 0x00007ff82dfd9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff82f8b0000 - 0x00007ff82f956000 	C:\WINDOWS\System32\sechost.dll
0x00007ff82f100000 - 0x00007ff82f215000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff82e8a0000 - 0x00007ff82ea6a000 	C:\WINDOWS\System32\USER32.dll
0x00007ff82d220000 - 0x00007ff82d247000 	C:\WINDOWS\System32\win32u.dll
0x00007ff82e840000 - 0x00007ff82e86b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff814290000 - 0x00007ff81452a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ff82d5c0000 - 0x00007ff82d6f7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff82d170000 - 0x00007ff82d213000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8228e0000 - 0x00007ff8228eb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff82ffa0000 - 0x00007ff82ffd0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff803650000 - 0x00007ff80365c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007fffa95b0000 - 0x00007fffa963e000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007fffaace0000 - 0x00007fffab8c0000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ff82fee0000 - 0x00007ff82fee8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff827650000 - 0x00007ff827685000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff80db10000 - 0x00007ff80db1a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff82f680000 - 0x00007ff82f6f4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff82bf70000 - 0x00007ff82bf8b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff803320000 - 0x00007ff80332a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ff82aab0000 - 0x00007ff82acf1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff82fb50000 - 0x00007ff82fed5000 	C:\WINDOWS\System32\combase.dll
0x00007ff82f010000 - 0x00007ff82f0f1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff811c90000 - 0x00007ff811cc9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff82d3a0000 - 0x00007ff82d439000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fffaac70000 - 0x00007fffaac95000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007fffaab90000 - 0x00007fffaac67000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ff82e0f0000 - 0x00007ff82e832000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff82dc40000 - 0x00007ff82ddb4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff82add0000 - 0x00007ff82b628000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff82f780000 - 0x00007ff82f871000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff82f5b0000 - 0x00007ff82f61a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff82d080000 - 0x00007ff82d0af000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fffde850000 - 0x00007fffde869000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007ff822700000 - 0x00007ff82281e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff82c560000 - 0x00007ff82c5ca000 	C:\WINDOWS\system32\mswsock.dll
0x00007fffddc50000 - 0x00007fffddc66000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007fffdd930000 - 0x00007fffdd948000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x0000017925b00000 - 0x0000017925b03000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-console-l1-1-0.dll
0x0000017925b10000 - 0x0000017925b13000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-console-l1-2-0.dll
0x0000017925b20000 - 0x0000017925b23000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-datetime-l1-1-0.dll
0x0000017925b30000 - 0x0000017925b33000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-debug-l1-1-0.dll
0x0000017925b40000 - 0x0000017925b43000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-errorhandling-l1-1-0.dll
0x0000017925b50000 - 0x0000017925b54000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-file-l1-1-0.dll
0x0000017925b60000 - 0x0000017925b63000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-file-l1-2-0.dll
0x0000017925b70000 - 0x0000017925b73000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-file-l2-1-0.dll
0x0000017925b90000 - 0x0000017925b93000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-handle-l1-1-0.dll
0x0000017927a90000 - 0x0000017927a93000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-heap-l1-1-0.dll
0x0000017927aa0000 - 0x0000017927aa3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-interlocked-l1-1-0.dll
0x0000017927ab0000 - 0x0000017927ab3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-libraryloader-l1-1-0.dll
0x0000017927ac0000 - 0x0000017927ac3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-localization-l1-2-0.dll
0x0000017927ad0000 - 0x0000017927ad3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-memory-l1-1-0.dll
0x0000017927ae0000 - 0x0000017927ae3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-namedpipe-l1-1-0.dll
0x0000017927af0000 - 0x0000017927af3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-processenvironment-l1-1-0.dll
0x0000017927b00000 - 0x0000017927b03000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-processthreads-l1-1-0.dll
0x0000017927b10000 - 0x0000017927b13000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-processthreads-l1-1-1.dll
0x0000017927b20000 - 0x0000017927b23000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-profile-l1-1-0.dll
0x0000017927b30000 - 0x0000017927b33000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-rtlsupport-l1-1-0.dll
0x0000017927b40000 - 0x0000017927b43000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-string-l1-1-0.dll
0x000001794cf30000 - 0x000001794cf33000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-synch-l1-1-0.dll
0x000001794cf40000 - 0x000001794cf43000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-synch-l1-2-0.dll
0x000001794cf50000 - 0x000001794cf53000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-sysinfo-l1-1-0.dll
0x000001794cf60000 - 0x000001794cf63000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-timezone-l1-1-0.dll
0x000001794cf70000 - 0x000001794cf73000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-core-util-l1-1-0.dll
0x000001794cf80000 - 0x000001794cf83000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-conio-l1-1-0.dll
0x000001794cf90000 - 0x000001794cf94000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-convert-l1-1-0.dll
0x000001794cfa0000 - 0x000001794cfa3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-environment-l1-1-0.dll
0x000001794cfb0000 - 0x000001794cfb3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-filesystem-l1-1-0.dll
0x000001794cfc0000 - 0x000001794cfc3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-heap-l1-1-0.dll
0x000001794cfd0000 - 0x000001794cfd3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-locale-l1-1-0.dll
0x000001794cfe0000 - 0x000001794cfe5000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-math-l1-1-0.dll
0x000001794cff0000 - 0x000001794cff5000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-multibyte-l1-1-0.dll
0x00000179930d0000 - 0x00000179930e0000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-private-l1-1-0.dll
0x00000179930e0000 - 0x00000179930e3000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-process-l1-1-0.dll
0x00000179930f0000 - 0x00000179930f4000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-runtime-l1-1-0.dll
0x0000017993100000 - 0x0000017993104000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-stdio-l1-1-0.dll
0x0000017993220000 - 0x0000017993224000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-string-l1-1-0.dll
0x0000017993230000 - 0x0000017993233000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-time-l1-1-0.dll
0x0000017993240000 - 0x0000017993243000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\api-ms-win-crt-utility-l1-1-0.dll
0x00007fff8f6f0000 - 0x00007fff8f7ec000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\ucrtbase.dll
0x00007fff92580000 - 0x00007fff9259a000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\vcruntime140.dll
0x00007ff822310000 - 0x00007ff82231c000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\vcruntime140_1.dll
0x00007fff8f660000 - 0x00007fff8f6ed000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\msvcp140.dll
0x00007fff8f630000 - 0x00007fff8f65a000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\prism_d3d.dll
0x00007ffffa1f0000 - 0x00007ffffa3a7000 	C:\WINDOWS\system32\d3d9.dll
0x00007ff8278e0000 - 0x00007ff827916000 	C:\WINDOWS\SYSTEM32\dwmapi.dll
0x00007ff827980000 - 0x00007ff8279cd000 	C:\WINDOWS\SYSTEM32\dxcore.dll
0x00007ff8270b0000 - 0x00007ff82715f000 	C:\WINDOWS\system32\uxtheme.dll
0x00007fff8f390000 - 0x00007fff8f62f000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5\igdumdim64.dll
0x00007ff82f400000 - 0x00007ff82f59e000 	C:\WINDOWS\System32\ole32.dll
0x00007fff8be50000 - 0x00007fff8d7e4000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5\igd9trinity64.dll
0x00007ff827780000 - 0x00007ff8278b5000 	C:\WINDOWS\system32\dxgi.dll
0x00007ff8276b0000 - 0x00007ff827711000 	C:\WINDOWS\SYSTEM32\directxdatabasehelper.dll
0x00007ff82ce00000 - 0x00007ff82ce57000 	C:\WINDOWS\SYSTEM32\cfgmgr32.dll
0x00007ff81a8f0000 - 0x00007ff81ae83000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5\igdgmm64.dll
0x00007ff815140000 - 0x00007ff81a266000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5\igc64.dll
0x00007fff8a740000 - 0x00007fff8be4e000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5\igd9dxva64.dll
0x00007ff82ea70000 - 0x00007ff82eef6000 	C:\WINDOWS\System32\SETUPAPI.dll
0x00007ffffa5a0000 - 0x00007ffffc612000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5\media_bin_64.dll
0x00007fff8f340000 - 0x00007fff8f382000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\glass.dll
0x00007ff82ef00000 - 0x00007ff82eff6000 	C:\WINDOWS\System32\COMDLG32.dll
0x00007ff82ddc0000 - 0x00007ff82df20000 	C:\WINDOWS\System32\MSCTF.dll
0x00007fff8f190000 - 0x00007fff8f337000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5\igdml64.dll
0x00007ffffa120000 - 0x00007ffffa148000 	C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5\igdinfo64.dll
0x00007ff82c940000 - 0x00007ff82c95b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff82bf90000 - 0x00007ff82bfca000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff82c7a0000 - 0x00007ff82c7ac000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff82d050000 - 0x00007ff82d076000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff82c600000 - 0x00007ff82c62b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff82b990000 - 0x00007ff82b9c3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff82fb40000 - 0x00007ff82fb4a000 	C:\WINDOWS\System32\NSI.dll
0x00007ff8228f0000 - 0x00007ff82290f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff821ef0000 - 0x00007ff821f15000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff82ba80000 - 0x00007ff82bba7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007fff8f090000 - 0x00007fff8f18b000 	C:\Users\<USER>\AppData\Local\Temp\sqlite-********-5e350f16-6a47-48d4-927f-41bb0e9ac84f-sqlitejdbc.dll
0x00007fff8f070000 - 0x00007fff8f084000 	C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin\javafx_font.dll
0x00007ff8249b0000 - 0x00007ff824c1b000 	C:\WINDOWS\SYSTEM32\dwrite.dll
0x00007ff82fef0000 - 0x00007ff82ff98000 	C:\WINDOWS\System32\clbcatq.dll
0x00007ff824500000 - 0x00007ff824739000 	C:\WINDOWS\SYSTEM32\WindowsCodecs.dll
0x00007ff802180000 - 0x00007ff8021da000 	C:\WINDOWS\system32\dataexchange.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\OneDrive\Desktop\ddd\ddd\javafx-sdk-17.0.2\bin;C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_e0551683909fdfb5;C:\Users\<USER>\AppData\Local\Temp;C:\Program Files\Common Files\Microsoft Shared\Ink

VM Arguments:
jvm_args: --module-path=javafx-sdk-17.0.2\lib --add-modules=javafx.controls,javafx.fxml 
java_command: com.clothingstore.ClothingStoreApp
java_class_path (initial): target\classes;lib\sqlite-jdbc-********.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 5                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 20                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4219469824                                {product} {ergonomic}
   size_t MaxNewSize                               = 2531262464                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4219469824                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Incredibuild;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\composer;C:\Program Files\nodejs\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Python313\Scripts\;C:\Python313\;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\flutter\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\xampp\php;C:\Users\<USER>\AppData\Local\ComposerSetup\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Python\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\flutter\bin;
USERNAME=win 10-11
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 2 days 9:04 hours
Hyper-V role detected

CPU: total 28 (initial active 28) (14 cores per cpu, 2 threads per core) family 6 model 183 stepping 1 microcode 0x125, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv

Memory: 4k page, system-wide physical 16091M (2208M free)
TotalPageFile size 47939M (AvailPageFile size 51M)
current process WorkingSet (physical memory assigned to process): 398M, peak: 438M
current process commit charge ("private bytes"): 465M, peak: 623M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.12+8-LTS-286) for windows-amd64 JRE (17.0.12+8-LTS-286), built on Jun  5 2024 06:46:59 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
