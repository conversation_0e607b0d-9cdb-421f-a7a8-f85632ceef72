<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.SalesAnalyticsController">
   <children>
      <!-- Header Section with Controls -->
      <VBox spacing="15.0" styleClass="header-section">
         <children>
            <!-- Title and Export -->
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="page-title" text="Sales Analytics Dashboard" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="exportButton" onAction="#handleExport" styleClass="secondary-button" text="📊 Export Report" />
                  <Button fx:id="refreshButton" onAction="#refreshData" styleClass="primary-button" text="🔄 Refresh" />
               </children>
            </HBox>
            
            <!-- Date Range and Filters -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="📅 Period:" />
                  <ComboBox fx:id="periodComboBox" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="From:" />
                  <DatePicker fx:id="startDatePicker" prefWidth="130.0" styleClass="form-field" />
                  <Label styleClass="filter-label" text="To:" />
                  <DatePicker fx:id="endDatePicker" prefWidth="130.0" styleClass="form-field" />
                  <Separator orientation="VERTICAL" />
                  <Label styleClass="filter-label" text="Category:" />
                  <ComboBox fx:id="categoryFilter" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="Group:" />
                  <ComboBox fx:id="customerGroupFilter" prefWidth="100.0" styleClass="combo-box" />
                  <CheckBox fx:id="includeRefundsCheckBox" styleClass="form-checkbox" text="Include Refunds" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </VBox>

      <!-- KPI Cards Section -->
      <HBox spacing="15.0" styleClass="kpi-section">
         <children>
            <!-- Total Sales Card -->
            <VBox alignment="CENTER" spacing="8.0" styleClass="kpi-card">
               <children>
                  <Label styleClass="kpi-title" text="Total Sales" />
                  <Label fx:id="totalSalesLabel" styleClass="kpi-value" text="$0.00" />
                  <Label fx:id="salesGrowthLabel" styleClass="kpi-growth" text="0.0%" />
               </children>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </VBox>
            
            <!-- Total Transactions Card -->
            <VBox alignment="CENTER" spacing="8.0" styleClass="kpi-card">
               <children>
                  <Label styleClass="kpi-title" text="🛒 Transactions" />
                  <Label fx:id="totalTransactionsLabel" styleClass="kpi-value" text="0" />
                  <Label fx:id="transactionGrowthLabel" styleClass="kpi-growth" text="0.0%" />
               </children>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </VBox>
            
            <!-- Average Order Value Card -->
            <VBox alignment="CENTER" spacing="8.0" styleClass="kpi-card">
               <children>
                  <Label styleClass="kpi-title" text="📊 Avg Order Value" />
                  <Label fx:id="averageOrderValueLabel" styleClass="kpi-value" text="$0.00" />
                  <Label styleClass="kpi-subtitle" text="Per Transaction" />
               </children>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </VBox>
            
            <!-- Total Customers Card -->
            <VBox alignment="CENTER" spacing="8.0" styleClass="kpi-card">
               <children>
                  <Label styleClass="kpi-title" text="👥 Customers" />
                  <Label fx:id="totalCustomersLabel" styleClass="kpi-value" text="0" />
                  <Label styleClass="kpi-subtitle" text="Unique Buyers" />
               </children>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </VBox>
         </children>
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Charts Section -->
      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="20.0">
               <children>
                  <!-- Top Row Charts -->
                  <HBox spacing="15.0">
                     <children>
                        <!-- Sales Trend Chart -->
                        <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="chart-title" text="📈 Sales Trend" />
                              <LineChart fx:id="salesTrendChart" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                 <xAxis>
                                    <CategoryAxis side="BOTTOM" />
                                 </xAxis>
                                 <yAxis>
                                    <NumberAxis side="LEFT" />
                                 </yAxis>
                              </LineChart>
                           </children>
                        </VBox>
                        
                        <!-- Sales by Category Chart -->
                        <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="chart-title" text="🥧 Sales by Category" />
                              <PieChart fx:id="salesByCategoryChart" prefHeight="300.0" VBox.vgrow="ALWAYS" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Second Row Charts -->
                  <HBox spacing="15.0">
                     <children>
                        <!-- Top Products Chart -->
                        <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="chart-title" text="🏆 Top Products by Revenue" />
                              <BarChart fx:id="topProductsChart" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                 <xAxis>
                                    <CategoryAxis side="BOTTOM" />
                                 </xAxis>
                                 <yAxis>
                                    <NumberAxis side="LEFT" />
                                 </yAxis>
                              </BarChart>
                           </children>
                        </VBox>
                        
                        <!-- Daily Sales Chart -->
                        <VBox spacing="10.0" styleClass="chart-container" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="chart-title" text="📊 Cumulative Daily Sales" />
                              <AreaChart fx:id="dailySalesChart" prefHeight="300.0" VBox.vgrow="ALWAYS">
                                 <xAxis>
                                    <CategoryAxis side="BOTTOM" />
                                 </xAxis>
                                 <yAxis>
                                    <NumberAxis side="LEFT" />
                                 </yAxis>
                              </AreaChart>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Tables Section -->
                  <HBox spacing="15.0">
                     <children>
                        <!-- Top Products Table -->
                        <VBox spacing="10.0" styleClass="table-container" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="section-title" text="🏆 Top Products" />
                              <TableView fx:id="topProductsTable" prefHeight="250.0" VBox.vgrow="ALWAYS">
                                 <columns>
                                    <TableColumn fx:id="productNameColumn" prefWidth="150.0" text="Product" />
                                    <TableColumn fx:id="quantitySoldColumn" prefWidth="80.0" text="Qty Sold" />
                                    <TableColumn fx:id="revenueColumn" prefWidth="100.0" text="Revenue" />
                                 </columns>
                              </TableView>
                           </children>
                        </VBox>
                        
                        <!-- Top Customers Table -->
                        <VBox spacing="10.0" styleClass="table-container" HBox.hgrow="ALWAYS">
                           <children>
                              <Label styleClass="section-title" text="👑 Top Customers" />
                              <TableView fx:id="topCustomersTable" prefHeight="250.0" VBox.vgrow="ALWAYS">
                                 <columns>
                                    <TableColumn fx:id="customerNameColumn" prefWidth="150.0" text="Customer" />
                                    <TableColumn fx:id="totalSpentColumn" prefWidth="100.0" text="Total Spent" />
                                    <TableColumn fx:id="transactionCountColumn" prefWidth="80.0" text="Orders" />
                                 </columns>
                              </TableView>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
