package com.clothingstore.model;

/**
 * Enumeration for report formats
 */
public enum ReportFormat {
    PDF("PDF", "pdf", "Portable Document Format"),
    CSV("CSV", "csv", "Comma Separated Values"),
    EXCEL("Excel", "xlsx", "Microsoft Excel"),
    HTML("HTML", "html", "HyperText Markup Language"),
    JSON("JSON", "json", "JavaScript Object Notation");
    
    private final String displayName;
    private final String extension;
    private final String description;
    
    ReportFormat(String displayName, String extension, String description) {
        this.displayName = displayName;
        this.extension = extension;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getExtension() {
        return extension;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
