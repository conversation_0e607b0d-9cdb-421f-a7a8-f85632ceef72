package com.clothingstore.util;

import com.clothingstore.view.MainWindowController;
import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.scene.control.TextArea;
import javafx.scene.layout.VBox;

import java.io.File;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Utility for testing navigation system integrity
 */
public class NavigationTestUtil {

    private static final Logger LOGGER = Logger.getLogger(NavigationTestUtil.class.getName());

    /**
     * Test all navigation methods and FXML file availability
     */
    public static void runNavigationTest(MainWindowController controller) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Navigation System Test");
            alert.setHeaderText("Testing Navigation System Integrity");

            TextArea resultArea = new TextArea();
            resultArea.setPrefRowCount(20);
            resultArea.setPrefColumnCount(80);
            resultArea.setEditable(false);

            VBox content = new VBox(10);
            content.getChildren().add(resultArea);
            alert.getDialogPane().setContent(content);

            // Run tests
            StringBuilder results = new StringBuilder();
            results.append("=== NAVIGATION SYSTEM TEST RESULTS ===\n\n");

            // Test FXML files existence
            results.append("1. FXML FILES VERIFICATION:\n");
            Map<String, String> fxmlFiles = getFxmlFileMap();
            int fxmlPassed = 0;
            int fxmlTotal = fxmlFiles.size();

            for (Map.Entry<String, String> entry : fxmlFiles.entrySet()) {
                String fileName = entry.getKey();
                String description = entry.getValue();
                boolean exists = checkFxmlFileExists(fileName);
                
                results.append(String.format("   %s %s - %s\n",
                    exists ? "[OK]" : "[FAIL]", fileName, description));
                
                if (exists) fxmlPassed++;
            }
            
            results.append(String.format("\n   FXML Files: %d/%d passed\n\n", fxmlPassed, fxmlTotal));

            // Test navigation methods
            results.append("2. NAVIGATION METHODS VERIFICATION:\n");
            Map<String, String> navigationMethods = getNavigationMethodMap();
            int methodsPassed = 0;
            int methodsTotal = navigationMethods.size();

            for (Map.Entry<String, String> entry : navigationMethods.entrySet()) {
                String methodName = entry.getKey();
                String description = entry.getValue();
                boolean exists = checkMethodExists(controller, methodName);
                
                results.append(String.format("   %s %s() - %s\n",
                    exists ? "[OK]" : "[FAIL]", methodName, description));
                
                if (exists) methodsPassed++;
            }
            
            results.append(String.format("\n   Navigation Methods: %d/%d passed\n\n", methodsPassed, methodsTotal));

            // Test menu item mappings
            results.append("3. MENU ITEM MAPPINGS:\n");
            Map<String, String> menuMappings = getMenuItemMappings();
            int mappingsPassed = 0;
            int mappingsTotal = menuMappings.size();

            for (Map.Entry<String, String> entry : menuMappings.entrySet()) {
                String menuItem = entry.getKey();
                String handler = entry.getValue();
                boolean exists = checkMethodExists(controller, handler);
                
                results.append(String.format("   %s %s -> %s()\n",
                    exists ? "[OK]" : "[FAIL]", menuItem, handler));
                
                if (exists) mappingsPassed++;
            }
            
            results.append(String.format("\n   Menu Mappings: %d/%d passed\n\n", mappingsPassed, mappingsTotal));

            // Overall results
            int totalPassed = fxmlPassed + methodsPassed + mappingsPassed;
            int totalTests = fxmlTotal + methodsTotal + mappingsTotal;
            double passRate = (double) totalPassed / totalTests * 100;

            results.append("=== SUMMARY ===\n");
            results.append(String.format("Total Tests: %d\n", totalTests));
            results.append(String.format("Passed: %d\n", totalPassed));
            results.append(String.format("Failed: %d\n", totalTests - totalPassed));
            results.append(String.format("Pass Rate: %.1f%%\n\n", passRate));

            if (passRate >= 95) {
                results.append("EXCELLENT: Navigation system is working properly!\n");
            } else if (passRate >= 80) {
                results.append("GOOD: Navigation system is mostly working with minor issues.\n");
            } else {
                results.append("ISSUES: Navigation system has significant problems that need attention.\n");
            }

            resultArea.setText(results.toString());
            alert.showAndWait();
        });
    }

    /**
     * Get map of FXML files and their descriptions
     */
    private static Map<String, String> getFxmlFileMap() {
        Map<String, String> files = new HashMap<>();
        files.put("Dashboard.fxml", "Main dashboard");
        files.put("PointOfSaleNew.fxml", "Point of Sale system");
        files.put("ProductManagement.fxml", "Product management");
        files.put("CustomerManagement.fxml", "Customer management");
        files.put("TransactionHistory.fxml", "Transaction history");
        files.put("OutstandingBalances.fxml", "Outstanding balances");
        files.put("SalesReport.fxml", "Sales reports");
        files.put("Settings.fxml", "Application settings");
        files.put("SalesAnalytics.fxml", "Sales analytics");
        files.put("AdvancedReports.fxml", "Advanced reporting");
        files.put("ReturnExchange.fxml", "Return & exchange management");
        files.put("CashDrawer.fxml", "Cash drawer management");
        files.put("EmailReceipt.fxml", "Email receipt management");
        files.put("SupplierManagement.fxml", "Supplier management");
        files.put("CustomerGroups.fxml", "Customer groups");
        files.put("DiscountManagement.fxml", "Discount management");
        files.put("TaxManagement.fxml", "Tax management");
        files.put("AdvancedInventory.fxml", "Advanced inventory");
        files.put("CustomerReport.fxml", "Customer reports");
        files.put("DailySalesReport.fxml", "Daily sales reports");
        files.put("MonthlySalesReport.fxml", "Monthly sales reports");
        files.put("ProfitReport.fxml", "Profit analysis");
        return files;
    }

    /**
     * Get map of navigation methods and their descriptions
     */
    private static Map<String, String> getNavigationMethodMap() {
        Map<String, String> methods = new HashMap<>();
        methods.put("showDashboard", "Dashboard navigation");
        methods.put("showPointOfSale", "Point of Sale navigation");
        methods.put("showProductManagement", "Product management navigation");
        methods.put("showCustomerManagement", "Customer management navigation");
        methods.put("showTransactionHistory", "Transaction history navigation");
        methods.put("showOutstandingBalances", "Outstanding balances navigation");
        methods.put("showSalesReport", "Sales report navigation");
        methods.put("showSettings", "Settings navigation");
        methods.put("showSalesAnalytics", "Sales analytics navigation");
        methods.put("showAdvancedReports", "Advanced reports navigation");
        methods.put("showReturnExchange", "Return & exchange navigation");
        methods.put("showCashDrawer", "Cash drawer navigation");
        methods.put("showEmailReceipt", "Email receipt navigation");
        methods.put("showSupplierManagement", "Supplier management navigation");
        methods.put("showCustomerGroups", "Customer groups navigation");
        methods.put("showDiscountManagement", "Discount management navigation");
        methods.put("showTaxManagement", "Tax management navigation");
        methods.put("showAdvancedInventory", "Advanced inventory navigation");
        return methods;
    }

    /**
     * Get map of menu items to their handler methods
     */
    private static Map<String, String> getMenuItemMappings() {
        Map<String, String> mappings = new HashMap<>();
        // File menu
        mappings.put("Database Setup", "handleDatabaseSetup");
        mappings.put("Backup Database", "handleBackup");
        mappings.put("Restore Database", "handleRestore");
        mappings.put("Exit", "handleExit");
        
        // Inventory menu
        mappings.put("Manage Products", "showProductManagement");
        mappings.put("Manage Suppliers", "showSupplierManagement");
        mappings.put("Advanced Inventory", "showAdvancedInventory");
        mappings.put("Low Stock Report", "showLowStockReport");
        mappings.put("Inventory Report", "showInventoryReport");
        
        // Customers menu
        mappings.put("Manage Customers", "showCustomerManagement");
        mappings.put("Customer Groups", "showCustomerGroups");
        mappings.put("Customer Report", "showCustomerReport");
        
        // Sales menu
        mappings.put("Point of Sale", "showPointOfSale");
        mappings.put("Cash Drawer", "showCashDrawer");
        mappings.put("Returns & Exchanges", "showReturnExchange");
        mappings.put("Transaction History", "showTransactionHistory");
        mappings.put("Discount Management", "showDiscountManagement");
        mappings.put("Tax Management", "showTaxManagement");
        mappings.put("Sales Report", "showSalesReport");
        
        // Reports menu
        mappings.put("Sales Analytics", "showSalesAnalytics");
        mappings.put("Advanced Reports", "showAdvancedReports");
        mappings.put("Email Receipts", "showEmailReceipt");
        mappings.put("Daily Sales", "showDailySalesReport");
        mappings.put("Monthly Sales", "showMonthlySalesReport");
        mappings.put("Profit Analysis", "showProfitReport");
        
        // Help menu
        mappings.put("About", "showAbout");
        mappings.put("User Guide", "showHelp");
        
        return mappings;
    }

    /**
     * Check if FXML file exists
     */
    private static boolean checkFxmlFileExists(String fileName) {
        try {
            String resourcePath = "/fxml/" + fileName;
            return NavigationTestUtil.class.getResource(resourcePath) != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Check if method exists in controller
     */
    private static boolean checkMethodExists(MainWindowController controller, String methodName) {
        try {
            Method[] methods = controller.getClass().getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Quick test method for command line use
     */
    public static void quickTest() {
        System.out.println("=== QUICK NAVIGATION TEST ===");
        
        // Test FXML files
        Map<String, String> fxmlFiles = getFxmlFileMap();
        int fxmlPassed = 0;
        
        System.out.println("\nFXML Files:");
        for (String fileName : fxmlFiles.keySet()) {
            boolean exists = checkFxmlFileExists(fileName);
            System.out.printf("  %s %s\n", exists ? "[OK]" : "[FAIL]", fileName);
            if (exists) fxmlPassed++;
        }
        
        System.out.printf("\nFXML Files: %d/%d passed (%.1f%%)\n", 
            fxmlPassed, fxmlFiles.size(), (double) fxmlPassed / fxmlFiles.size() * 100);
        
        if (fxmlPassed == fxmlFiles.size()) {
            System.out.println("All FXML files are available!");
        } else {
            System.out.println("Some FXML files are missing.");
        }
    }

    /**
     * Main method for standalone testing
     */
    public static void main(String[] args) {
        quickTest();
    }
}
