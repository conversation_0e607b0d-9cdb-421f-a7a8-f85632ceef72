<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.RefundDialogController" spacing="10" padding="15">
   <children>
      <!-- Header Section -->
      <VBox>
         <children>
            <Label text="Process Refund" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
            <Separator />
         </children>
      </VBox>

      <!-- Transaction Details Section -->
      <GridPane hgap="10.0" vgap="8.0">
         <columnConstraints>
            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         <children>
            <Label text="Transaction #:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
            <Label fx:id="lblTransactionNumber" text="-" GridPane.columnIndex="1" GridPane.rowIndex="0" />

            <Label text="Date:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
            <Label fx:id="lblTransactionDate" text="-" GridPane.columnIndex="3" GridPane.rowIndex="0" />

            <Label text="Customer:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
            <Label fx:id="lblCustomer" text="-" GridPane.columnIndex="1" GridPane.rowIndex="1" />

            <Label text="Original Total:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
            <Label fx:id="lblOriginalTotal" text="-" GridPane.columnIndex="3" GridPane.rowIndex="1" />
         </children>
      </GridPane>

      <Separator />

      <!-- Items Selection Section -->
      <VBox VBox.vgrow="ALWAYS">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="Select Items to Refund:" style="-fx-font-weight: bold;" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnSelectAll" text="Select All" />
                  <Button fx:id="btnSelectNone" text="Select None" />
               </children>
            </HBox>

            <TableView fx:id="tblRefundItems" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="colSelect" prefWidth="60.0" text="Select" />
                  <TableColumn fx:id="colProduct" prefWidth="200.0" text="Product" />
                  <TableColumn fx:id="colSku" prefWidth="100.0" text="SKU" />
                  <TableColumn fx:id="colOriginalQty" prefWidth="80.0" text="Orig. Qty" />
                  <TableColumn fx:id="colRefundQty" prefWidth="80.0" text="Refund Qty" />
                  <TableColumn fx:id="colUnitPrice" prefWidth="80.0" text="Unit Price" />
                  <TableColumn fx:id="colRefundAmount" prefWidth="100.0" text="Refund Amount" />
               </columns>
            </TableView>
         </children>
      </VBox>

      <!-- Refund Summary Section -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <children>
            <Label text="Total Refund Amount:" style="-fx-font-weight: bold;" />
            <Label fx:id="lblTotalRefundAmount" text="$0.00" style="-fx-font-weight: bold; -fx-text-fill: green;" />
         </children>
      </HBox>

      <Separator />

      <!-- Reason Section -->
      <VBox>
         <children>
            <Label text="Refund Reason:" />
            <TextArea fx:id="txtReason" prefRowCount="3" promptText="Please provide a reason for this refund..." wrapText="true" />
         </children>
      </VBox>

      <!-- Button Section -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <children>
            <Button fx:id="btnCancel" text="Cancel" />
            <Button fx:id="btnProcessRefund" text="Process Refund" style="-fx-background-color: #007bff; -fx-text-fill: white;" />
         </children>
      </HBox>
   </children>
</VBox>
