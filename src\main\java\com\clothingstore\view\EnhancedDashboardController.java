package com.clothingstore.view;

import com.clothingstore.service.ProfitAnalysisService;
import com.clothingstore.service.ProfitAnalysisService.*;
import com.clothingstore.util.AlertUtil;
import javafx.animation.*;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.chart.*;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.FileChooser;
import javafx.util.Duration;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Enhanced Dashboard Controller with interactive charts and modern UI
 */
public class EnhancedDashboardController implements Initializable {

    @FXML
    private VBox mainContainer;
    @FXML
    private HBox headerContainer;
    @FXML
    private GridPane metricsGrid;
    @FXML
    private TabPane chartsTabPane;
    @FXML
    private DatePicker startDatePicker;
    @FXML
    private DatePicker endDatePicker;
    @FXML
    private Button generateReportButton;
    @FXML
    private ProgressIndicator progressIndicator;
    @FXML
    private Label statusLabel;
    @FXML
    private ToggleButton themeToggle;
    @FXML
    private HBox presetButtonsContainer;
    @FXML
    private Button exportCSVButton;
    @FXML
    private Button exportPDFButton;

    // Chart containers
    @FXML
    private Tab profitTrendTab;
    @FXML
    private Tab categoryBreakdownTab;
    @FXML
    private Tab comparisonTab;
    @FXML
    private VBox profitTrendContainer;
    @FXML
    private VBox categoryBreakdownContainer;
    @FXML
    private VBox comparisonContainer;

    private ProfitAnalysisService profitService;
    private boolean isDarkTheme = false;

    // Chart instances
    private LineChart<String, Number> profitTrendChart;
    private PieChart categoryPieChart;
    private BarChart<String, Number> comparisonBarChart;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        profitService = new ProfitAnalysisService();
        setupDefaultDates();
        setupUI();
        setupCharts();
        setupAnimations();
        setupResponsiveDesign();
    }

    private void setupDefaultDates() {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(30);

        startDatePicker.setValue(startDate);
        endDatePicker.setValue(endDate);
    }

    private void setupUI() {
        progressIndicator.setVisible(false);
        statusLabel.setText("Select date range and click Generate Report");

        generateReportButton.setOnAction(e -> generateDashboardReport());
        themeToggle.setOnAction(e -> toggleTheme());

        setupPresetButtons();
        setupExportButtons();
        setupTabListeners();
    }

    private void setupPresetButtons() {
        if (presetButtonsContainer != null) {
            Button todayBtn = createPresetButton("Today", () -> setDateRange(LocalDate.now(), LocalDate.now()));
            Button weekBtn = createPresetButton("This Week", () -> setDateRange(LocalDate.now().minusDays(6), LocalDate.now()));
            Button monthBtn = createPresetButton("This Month", () -> setDateRange(LocalDate.now().withDayOfMonth(1), LocalDate.now()));
            Button last30Btn = createPresetButton("Last 30 Days", () -> setDateRange(LocalDate.now().minusDays(30), LocalDate.now()));
            Button last90Btn = createPresetButton("Last 90 Days", () -> setDateRange(LocalDate.now().minusDays(90), LocalDate.now()));

            presetButtonsContainer.getChildren().addAll(todayBtn, weekBtn, monthBtn, last30Btn, last90Btn);
        }
    }

    private Button createPresetButton(String text, Runnable action) {
        Button button = new Button(text);
        button.getStyleClass().add("preset-button");
        button.setOnAction(e -> {
            action.run();
            generateDashboardReport();
            animateButtonClick(button);
        });
        return button;
    }

    private void setDateRange(LocalDate start, LocalDate end) {
        startDatePicker.setValue(start);
        endDatePicker.setValue(end);
    }

    private void setupExportButtons() {
        if (exportCSVButton != null) {
            exportCSVButton.setOnAction(e -> exportToCSV());
            exportCSVButton.getStyleClass().add("export-button");
        }
        if (exportPDFButton != null) {
            exportPDFButton.setOnAction(e -> exportToPDF());
            exportPDFButton.getStyleClass().add("export-button");
        }
    }

    private void setupTabListeners() {
        if (chartsTabPane != null) {
            chartsTabPane.getSelectionModel().selectedItemProperty().addListener((obs, oldTab, newTab) -> {
                if (newTab == categoryBreakdownTab) {
                    generateCategoryChart();
                } else if (newTab == comparisonTab) {
                    generateComparisonChart();
                }
            });
        }
    }

    private void setupCharts() {
        setupProfitTrendChart();
        setupCategoryPieChart();
        setupComparisonBarChart();
    }

    private void setupProfitTrendChart() {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        xAxis.setLabel("Period");
        yAxis.setLabel("Profit ($)");

        profitTrendChart = new LineChart<>(xAxis, yAxis);
        profitTrendChart.setTitle("Profit Trend Analysis");
        profitTrendChart.setCreateSymbols(true);
        profitTrendChart.setLegendVisible(true);
        profitTrendChart.getStyleClass().add("profit-trend-chart");

        if (profitTrendContainer != null) {
            profitTrendContainer.getChildren().clear();
            profitTrendContainer.getChildren().add(profitTrendChart);
            VBox.setVgrow(profitTrendChart, Priority.ALWAYS);
        }
    }

    private void setupCategoryPieChart() {
        categoryPieChart = new PieChart();
        categoryPieChart.setTitle("Profit by Category");
        categoryPieChart.setLegendVisible(true);
        categoryPieChart.setLabelsVisible(true);
        categoryPieChart.getStyleClass().add("category-pie-chart");

        if (categoryBreakdownContainer != null) {
            categoryBreakdownContainer.getChildren().clear();
            categoryBreakdownContainer.getChildren().add(categoryPieChart);
            VBox.setVgrow(categoryPieChart, Priority.ALWAYS);
        }
    }

    private void setupComparisonBarChart() {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        xAxis.setLabel("Metrics");
        yAxis.setLabel("Amount ($)");

        comparisonBarChart = new BarChart<>(xAxis, yAxis);
        comparisonBarChart.setTitle("Current vs Previous Period");
        comparisonBarChart.setLegendVisible(true);
        comparisonBarChart.getStyleClass().add("comparison-bar-chart");

        if (comparisonContainer != null) {
            comparisonContainer.getChildren().clear();
            comparisonContainer.getChildren().add(comparisonBarChart);
            VBox.setVgrow(comparisonBarChart, Priority.ALWAYS);
        }
    }

    private void setupAnimations() {
        // Add fade-in animation for main container
        if (mainContainer != null) {
            FadeTransition fadeIn = new FadeTransition(Duration.millis(800), mainContainer);
            fadeIn.setFromValue(0.0);
            fadeIn.setToValue(1.0);
            fadeIn.play();
        }
    }

    private void setupResponsiveDesign() {
        // Add responsive behavior for different screen sizes
        if (mainContainer != null) {
            mainContainer.widthProperty().addListener((obs, oldWidth, newWidth) -> {
                adjustLayoutForWidth(newWidth.doubleValue());
            });

            mainContainer.heightProperty().addListener((obs, oldHeight, newHeight) -> {
                adjustLayoutForHeight(newHeight.doubleValue());
            });
        }
    }

    private void adjustLayoutForWidth(double width) {
        if (width < 1280) {
            // Compact layout for smaller screens
            if (metricsGrid != null) {
                metricsGrid.getColumnConstraints().clear();
                ColumnConstraints col1 = new ColumnConstraints();
                col1.setPercentWidth(50);
                ColumnConstraints col2 = new ColumnConstraints();
                col2.setPercentWidth(50);
                metricsGrid.getColumnConstraints().addAll(col1, col2);
            }
        } else {
            // Full layout for larger screens
            if (metricsGrid != null) {
                metricsGrid.getColumnConstraints().clear();
                for (int i = 0; i < 4; i++) {
                    ColumnConstraints col = new ColumnConstraints();
                    col.setPercentWidth(25);
                    metricsGrid.getColumnConstraints().add(col);
                }
            }
        }
    }

    private void adjustLayoutForHeight(double height) {
        // Adjust chart heights based on available space
        double chartHeight = Math.max(300, height * 0.4);

        if (profitTrendChart != null) {
            profitTrendChart.setPrefHeight(chartHeight);
        }
        if (categoryPieChart != null) {
            categoryPieChart.setPrefHeight(chartHeight);
        }
        if (comparisonBarChart != null) {
            comparisonBarChart.setPrefHeight(chartHeight);
        }
    }

    private void toggleTheme() {
        isDarkTheme = !isDarkTheme;
        applyTheme();

        // Animate theme transition
        if (mainContainer != null) {
            FadeTransition fadeOut = new FadeTransition(Duration.millis(200), mainContainer);
            fadeOut.setFromValue(1.0);
            fadeOut.setToValue(0.8);

            FadeTransition fadeIn = new FadeTransition(Duration.millis(200), mainContainer);
            fadeIn.setFromValue(0.8);
            fadeIn.setToValue(1.0);

            SequentialTransition transition = new SequentialTransition(fadeOut, fadeIn);
            transition.play();
        }
    }

    private void applyTheme() {
        String themeClass = isDarkTheme ? "dark-theme" : "light-theme";

        if (mainContainer != null) {
            mainContainer.getStyleClass().removeAll("dark-theme", "light-theme");
            mainContainer.getStyleClass().add(themeClass);
        }

        // Update theme toggle button text
        if (themeToggle != null) {
            themeToggle.setText(isDarkTheme ? "Light Theme" : "Dark Theme");
        }
    }

    private void animateButtonClick(Button button) {
        ScaleTransition scale = new ScaleTransition(Duration.millis(100), button);
        scale.setFromX(1.0);
        scale.setFromY(1.0);
        scale.setToX(0.95);
        scale.setToY(0.95);
        scale.setAutoReverse(true);
        scale.setCycleCount(2);
        scale.play();
    }

    @FXML
    private void generateDashboardReport() {
        if (startDatePicker.getValue() == null || endDatePicker.getValue() == null) {
            AlertUtil.showWarning("Invalid Date Range", "Please select both start and end dates.");
            return;
        }

        if (startDatePicker.getValue().isAfter(endDatePicker.getValue())) {
            AlertUtil.showWarning("Invalid Date Range", "Start date must be before end date.");
            return;
        }

        progressIndicator.setVisible(true);
        generateReportButton.setDisable(true);
        statusLabel.setText("Generating comprehensive dashboard report...");

        Task<DashboardData> task = new Task<DashboardData>() {
            @Override
            protected DashboardData call() throws Exception {
                LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
                LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

                // Generate all dashboard data
                ProfitMetrics metrics = profitService.calculateProfitMetrics(startDateTime, endDateTime);
                List<CategoryProfitData> categories = profitService.calculateCategoryProfitBreakdown(startDateTime, endDateTime);

                // Calculate comparison data
                long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDateTime.toLocalDate(), endDateTime.toLocalDate());
                LocalDateTime previousStart = startDateTime.minusDays(daysBetween + 1);
                LocalDateTime previousEnd = endDateTime.minusDays(daysBetween + 1);
                ComparisonMetrics comparison = profitService.calculateComparisonMetrics(startDateTime, endDateTime, previousStart, previousEnd);

                return new DashboardData(metrics, categories, comparison);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    DashboardData data = getValue();
                    displayDashboardData(data);
                    progressIndicator.setVisible(false);
                    generateReportButton.setDisable(false);
                    statusLabel.setText("Dashboard report generated successfully");
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    AlertUtil.showError("Report Generation Failed", "Failed to generate dashboard report: " + getException().getMessage());
                    progressIndicator.setVisible(false);
                    generateReportButton.setDisable(false);
                    statusLabel.setText("Dashboard report generation failed");
                });
            }
        };

        new Thread(task).start();
    }

    private void displayDashboardData(DashboardData data) {
        displayMetricsCards(data.metrics);
        updateProfitTrendChart(data);
        updateCategoryPieChart(data.categories);
        updateComparisonBarChart(data.comparison);
    }

    private void displayMetricsCards(ProfitMetrics metrics) {
        if (metricsGrid == null) {
            return;
        }

        metricsGrid.getChildren().clear();

        // Create animated metric cards
        VBox revenueCard = createAnimatedMetricCard("Total Revenue", metrics.getFormattedRevenue(), "#27ae60");
        VBox costCard = createAnimatedMetricCard("Total Cost", metrics.getFormattedCost(), "#e74c3c");
        VBox profitCard = createAnimatedMetricCard("Gross Profit", metrics.getFormattedProfit(), "#3498db");
        VBox marginCard = createAnimatedMetricCard("Profit Margin", metrics.getFormattedProfitMargin(), "#9b59b6");

        metricsGrid.add(revenueCard, 0, 0);
        metricsGrid.add(costCard, 1, 0);
        metricsGrid.add(profitCard, 2, 0);
        metricsGrid.add(marginCard, 3, 0);
    }

    private VBox createAnimatedMetricCard(String title, String value, String color) {
        VBox card = new VBox(8);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(20));
        card.getStyleClass().add("metric-card");
        card.setStyle(String.format("-fx-background-color: white; -fx-border-color: %s; -fx-border-width: 2; -fx-border-radius: 8; -fx-background-radius: 8; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);", color));

        Label titleLabel = new Label(title);
        titleLabel.setFont(Font.font("System", FontWeight.NORMAL, 12));
        titleLabel.setStyle("-fx-text-fill: #6c757d;");

        Label valueLabel = new Label(value);
        valueLabel.setFont(Font.font("System", FontWeight.BOLD, 18));
        valueLabel.setStyle(String.format("-fx-text-fill: %s;", color));

        card.getChildren().addAll(titleLabel, valueLabel);

        // Add entrance animation
        card.setOpacity(0);
        card.setTranslateY(20);

        FadeTransition fadeIn = new FadeTransition(Duration.millis(600), card);
        fadeIn.setFromValue(0);
        fadeIn.setToValue(1);

        TranslateTransition slideUp = new TranslateTransition(Duration.millis(600), card);
        slideUp.setFromY(20);
        slideUp.setToY(0);

        ParallelTransition entrance = new ParallelTransition(fadeIn, slideUp);
        entrance.setDelay(Duration.millis(Math.random() * 200)); // Stagger animations
        entrance.play();

        return card;
    }

    private void updateProfitTrendChart(DashboardData data) {
        if (profitTrendChart == null) {
            return;
        }

        profitTrendChart.getData().clear();

        // Create sample trend data (in real implementation, this would come from database)
        XYChart.Series<String, Number> profitSeries = new XYChart.Series<>();
        profitSeries.setName("Profit Trend");

        // Add sample data points
        LocalDate startDate = startDatePicker.getValue();
        LocalDate endDate = endDatePicker.getValue();
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate);

        if (daysBetween <= 7) {
            // Daily data for week or less
            for (int i = 0; i <= daysBetween; i++) {
                LocalDate date = startDate.plusDays(i);
                double profit = data.metrics.getTotalProfit() * (0.8 + Math.random() * 0.4) / (daysBetween + 1);
                profitSeries.getData().add(new XYChart.Data<>(date.format(DateTimeFormatter.ofPattern("MM/dd")), profit));
            }
        } else {
            // Weekly data for longer periods
            int weeks = (int) Math.ceil(daysBetween / 7.0);
            for (int i = 0; i < weeks; i++) {
                LocalDate weekStart = startDate.plusWeeks(i);
                double profit = data.metrics.getTotalProfit() * (0.8 + Math.random() * 0.4) / weeks;
                profitSeries.getData().add(new XYChart.Data<>("Week " + (i + 1), profit));
            }
        }

        profitTrendChart.getData().add(profitSeries);

        // Animate chart appearance
        Timeline timeline = new Timeline();
        for (int i = 0; i < profitSeries.getData().size(); i++) {
            XYChart.Data<String, Number> data1 = profitSeries.getData().get(i);
            timeline.getKeyFrames().add(new KeyFrame(Duration.millis(100 * i), e -> {
                if (data1.getNode() != null) {
                    data1.getNode().setOpacity(0);
                    FadeTransition fade = new FadeTransition(Duration.millis(500), data1.getNode());
                    fade.setFromValue(0);
                    fade.setToValue(1);
                    fade.play();
                }
            }));
        }
        timeline.play();
    }

    private void updateCategoryPieChart(List<CategoryProfitData> categories) {
        if (categoryPieChart == null) {
            return;
        }

        categoryPieChart.getData().clear();

        // Define colors for categories
        String[] colors = {"#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#98D8C8"};

        for (int i = 0; i < categories.size(); i++) {
            CategoryProfitData category = categories.get(i);
            PieChart.Data slice = new PieChart.Data(category.getCategoryName(), category.getProfit());
            categoryPieChart.getData().add(slice);

            // Apply custom colors
            final String color = colors[i % colors.length];
            Platform.runLater(() -> {
                if (slice.getNode() != null) {
                    slice.getNode().setStyle("-fx-pie-color: " + color + ";");

                    // Add hover effects
                    slice.getNode().setOnMouseEntered(e -> {
                        slice.getNode().setStyle("-fx-pie-color: derive(" + color + ", 20%);");
                        ScaleTransition scale = new ScaleTransition(Duration.millis(200), slice.getNode());
                        scale.setToX(1.1);
                        scale.setToY(1.1);
                        scale.play();
                    });

                    slice.getNode().setOnMouseExited(e -> {
                        slice.getNode().setStyle("-fx-pie-color: " + color + ";");
                        ScaleTransition scale = new ScaleTransition(Duration.millis(200), slice.getNode());
                        scale.setToX(1.0);
                        scale.setToY(1.0);
                        scale.play();
                    });
                }
            });
        }

        // Animate pie chart appearance
        categoryPieChart.setOpacity(0);
        FadeTransition fadeIn = new FadeTransition(Duration.millis(800), categoryPieChart);
        fadeIn.setFromValue(0);
        fadeIn.setToValue(1);
        fadeIn.play();
    }

    private void updateComparisonBarChart(ComparisonMetrics comparison) {
        if (comparisonBarChart == null) {
            return;
        }

        comparisonBarChart.getData().clear();

        XYChart.Series<String, Number> currentSeries = new XYChart.Series<>();
        currentSeries.setName("Current Period");

        XYChart.Series<String, Number> previousSeries = new XYChart.Series<>();
        previousSeries.setName("Previous Period");

        // Add data points
        currentSeries.getData().add(new XYChart.Data<>("Revenue", comparison.getCurrentPeriod().getTotalRevenue()));
        currentSeries.getData().add(new XYChart.Data<>("Cost", comparison.getCurrentPeriod().getTotalCost()));
        currentSeries.getData().add(new XYChart.Data<>("Profit", comparison.getCurrentPeriod().getTotalProfit()));

        previousSeries.getData().add(new XYChart.Data<>("Revenue", comparison.getPreviousPeriod().getTotalRevenue()));
        previousSeries.getData().add(new XYChart.Data<>("Cost", comparison.getPreviousPeriod().getTotalCost()));
        previousSeries.getData().add(new XYChart.Data<>("Profit", comparison.getPreviousPeriod().getTotalProfit()));

        comparisonBarChart.getData().addAll(currentSeries, previousSeries);

        // Style the bars
        Platform.runLater(() -> {
            for (XYChart.Data<String, Number> data : currentSeries.getData()) {
                if (data.getNode() != null) {
                    data.getNode().setStyle("-fx-bar-fill: #3498db;");
                }
            }
            for (XYChart.Data<String, Number> data : previousSeries.getData()) {
                if (data.getNode() != null) {
                    data.getNode().setStyle("-fx-bar-fill: #95a5a6;");
                }
            }
        });

        // Animate bars
        Timeline timeline = new Timeline();
        for (int i = 0; i < currentSeries.getData().size(); i++) {
            XYChart.Data<String, Number> currentData = currentSeries.getData().get(i);
            XYChart.Data<String, Number> previousData = previousSeries.getData().get(i);

            timeline.getKeyFrames().add(new KeyFrame(Duration.millis(200 * i), e -> {
                if (currentData.getNode() != null) {
                    currentData.getNode().setScaleY(0);
                    ScaleTransition currentScale = new ScaleTransition(Duration.millis(600), currentData.getNode());
                    currentScale.setFromY(0);
                    currentScale.setToY(1);
                    currentScale.play();
                }
                if (previousData.getNode() != null) {
                    previousData.getNode().setScaleY(0);
                    ScaleTransition previousScale = new ScaleTransition(Duration.millis(600), previousData.getNode());
                    previousScale.setFromY(0);
                    previousScale.setToY(1);
                    previousScale.setDelay(Duration.millis(100));
                    previousScale.play();
                }
            }));
        }
        timeline.play();
    }

    private void generateCategoryChart() {
        if (startDatePicker.getValue() == null || endDatePicker.getValue() == null) {
            return;
        }

        Task<List<CategoryProfitData>> task = new Task<List<CategoryProfitData>>() {
            @Override
            protected List<CategoryProfitData> call() throws Exception {
                LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
                LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);
                return profitService.calculateCategoryProfitBreakdown(startDateTime, endDateTime);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    List<CategoryProfitData> categories = getValue();
                    updateCategoryPieChart(categories);
                });
            }
        };

        new Thread(task).start();
    }

    private void generateComparisonChart() {
        if (startDatePicker.getValue() == null || endDatePicker.getValue() == null) {
            return;
        }

        Task<ComparisonMetrics> task = new Task<ComparisonMetrics>() {
            @Override
            protected ComparisonMetrics call() throws Exception {
                LocalDateTime currentStart = startDatePicker.getValue().atStartOfDay();
                LocalDateTime currentEnd = endDatePicker.getValue().atTime(23, 59, 59);

                long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(currentStart.toLocalDate(), currentEnd.toLocalDate());
                LocalDateTime previousStart = currentStart.minusDays(daysBetween + 1);
                LocalDateTime previousEnd = currentEnd.minusDays(daysBetween + 1);

                return profitService.calculateComparisonMetrics(currentStart, currentEnd, previousStart, previousEnd);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    ComparisonMetrics comparison = getValue();
                    updateComparisonBarChart(comparison);
                });
            }
        };

        new Thread(task).start();
    }

    private void exportToCSV() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Export Dashboard Data to CSV");
        fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("CSV Files", "*.csv"));
        fileChooser.setInitialFileName("dashboard_report_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".csv");

        File file = fileChooser.showSaveDialog(generateReportButton.getScene().getWindow());
        if (file != null) {
            exportDashboardDataToCSV(file);
        }
    }

    private void exportDashboardDataToCSV(File file) {
        try (FileWriter writer = new FileWriter(file)) {
            LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
            LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

            ProfitMetrics metrics = profitService.calculateProfitMetrics(startDateTime, endDateTime);
            List<CategoryProfitData> categories = profitService.calculateCategoryProfitBreakdown(startDateTime, endDateTime);

            // Write CSV header
            writer.write("Enhanced Dashboard Report\n");
            writer.write("Generated: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
            writer.write("Period: " + startDatePicker.getValue() + " to " + endDatePicker.getValue() + "\n\n");

            // Write overall metrics
            writer.write("Overall Metrics\n");
            writer.write("Metric,Value\n");
            writer.write("Total Revenue," + metrics.getTotalRevenue() + "\n");
            writer.write("Total Cost," + metrics.getTotalCost() + "\n");
            writer.write("Gross Profit," + metrics.getTotalProfit() + "\n");
            writer.write("Profit Margin," + metrics.getProfitMargin() + "%\n");
            writer.write("Items Sold," + metrics.getTotalItemsSold() + "\n");
            writer.write("Transactions," + metrics.getTransactionCount() + "\n\n");

            // Write category breakdown
            writer.write("Category Breakdown\n");
            writer.write("Category,Revenue,Cost,Profit,Margin,Items Sold\n");
            for (CategoryProfitData category : categories) {
                writer.write(String.format("%s,%.2f,%.2f,%.2f,%.2f,%d\n",
                        category.getCategoryName(),
                        category.getRevenue(),
                        category.getCost(),
                        category.getProfit(),
                        category.getProfitMargin(),
                        category.getItemsSold()));
            }

            AlertUtil.showInfo("Export Successful", "Dashboard data exported to CSV successfully!");

        } catch (IOException e) {
            AlertUtil.showError("Export Failed", "Failed to export CSV: " + e.getMessage());
        }
    }

    private void exportToPDF() {
        AlertUtil.showInfo("PDF Export", "Enhanced PDF export with charts and professional formatting will be implemented in the next update.");
    }

    // Dashboard data container
    private static class DashboardData {

        final ProfitMetrics metrics;
        final List<CategoryProfitData> categories;
        final ComparisonMetrics comparison;

        DashboardData(ProfitMetrics metrics, List<CategoryProfitData> categories, ComparisonMetrics comparison) {
            this.metrics = metrics;
            this.categories = categories;
            this.comparison = comparison;
        }
    }
}
