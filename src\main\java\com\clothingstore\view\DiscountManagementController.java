package com.clothingstore.view;

import com.clothingstore.model.Discount;
import com.clothingstore.model.DiscountType;
import com.clothingstore.service.DiscountService;
import com.clothingstore.util.AlertUtil;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Controller for Discount Management interface
 */
public class DiscountManagementController implements Initializable {

    // FXML Components - Table and Selection
    @FXML
    private TableView<Discount> discountTable;
    @FXML
    private TableColumn<Discount, String> nameColumn;
    @FXML
    private TableColumn<Discount, String> typeColumn;
    @FXML
    private TableColumn<Discount, String> valueColumn;
    @FXML
    private TableColumn<Discount, String> statusColumn;
    @FXML
    private TableColumn<Discount, String> validityColumn;
    @FXML
    private TableColumn<Discount, String> usageColumn;

    // FXML Components - Form Fields
    @FXML
    private TextField nameField;
    @FXML
    private TextArea descriptionField;
    @FXML
    private ComboBox<DiscountType> typeComboBox;
    @FXML
    private TextField valueField;
    @FXML
    private TextField minimumAmountField;
    @FXML
    private TextField maximumDiscountField;
    @FXML
    private TextField minimumQuantityField;
    @FXML
    private TextField buyQuantityField;
    @FXML
    private TextField getQuantityField;
    @FXML
    private TextField promoCodeField;
    @FXML
    private DatePicker startDatePicker;
    @FXML
    private DatePicker endDatePicker;
    @FXML
    private TextField usageLimitField;
    @FXML
    private CheckBox activeCheckBox;
    @FXML
    private CheckBox stackableCheckBox;

    // FXML Components - Buttons
    @FXML
    private Button addDiscountButton;
    @FXML
    private Button editDiscountButton;
    @FXML
    private Button deleteDiscountButton;
    @FXML
    private Button saveDiscountButton;
    @FXML
    private Button cancelDiscountButton;
    @FXML
    private Button testDiscountButton;

    // FXML Components - Search and Filter
    @FXML
    private TextField searchField;
    @FXML
    private ComboBox<String> statusFilter;
    @FXML
    private ComboBox<DiscountType> typeFilter;

    // FXML Components - Info Labels
    @FXML
    private Label discountCountLabel;
    @FXML
    private Label selectionInfoLabel;

    // Data and Services
    private DiscountService discountService;
    private ObservableList<Discount> allDiscounts;
    private ObservableList<Discount> filteredDiscounts;
    private Discount selectedDiscount;
    private boolean isEditMode = false;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateFormatter;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeServices();
        initializeFormatters();
        initializeTableColumns();
        initializeComboBoxes();
        initializeEventHandlers();
        loadDiscounts();
        setFormEditMode(false);
    }

    private void initializeServices() {
        discountService = DiscountService.getInstance();
        allDiscounts = FXCollections.observableArrayList();
        filteredDiscounts = FXCollections.observableArrayList();
        discountTable.setItems(filteredDiscounts);
    }

    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy");
    }

    private void initializeTableColumns() {
        nameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));

        typeColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getType().getDisplayName()));

        valueColumn.setCellValueFactory(cellData -> {
            Discount discount = cellData.getValue();
            String value;
            if (discount.getType() == DiscountType.PERCENTAGE) {
                value = discount.getValue() + "%";
            } else if (discount.getType() == DiscountType.FIXED_AMOUNT) {
                value = currencyFormat.format(discount.getValue());
            } else {
                value = discount.getValue().toString();
            }
            return new SimpleStringProperty(value);
        });

        statusColumn.setCellValueFactory(cellData -> {
            Discount discount = cellData.getValue();
            String status;
            if (!discount.isActive()) {
                status = "Inactive";
            } else if (discount.isExpired()) {
                status = "Expired";
            } else if (discount.isUsageLimitReached()) {
                status = "Limit Reached";
            } else {
                status = "Active";
            }
            return new SimpleStringProperty(status);
        });

        validityColumn.setCellValueFactory(cellData -> {
            Discount discount = cellData.getValue();
            String validity = "No Expiry";
            if (discount.getStartDate() != null && discount.getEndDate() != null) {
                validity = discount.getStartDate().format(dateFormatter) + " - "
                        + discount.getEndDate().format(dateFormatter);
            } else if (discount.getEndDate() != null) {
                validity = "Until " + discount.getEndDate().format(dateFormatter);
            }
            return new SimpleStringProperty(validity);
        });

        usageColumn.setCellValueFactory(cellData -> {
            Discount discount = cellData.getValue();
            String usage;
            if (discount.getUsageLimit() > 0) {
                usage = discount.getUsageCount() + "/" + discount.getUsageLimit();
            } else {
                usage = String.valueOf(discount.getUsageCount());
            }
            return new SimpleStringProperty(usage);
        });

        // Table selection handler
        discountTable.getSelectionModel().selectedItemProperty().addListener(
                (obs, oldSelection, newSelection) -> {
                    selectedDiscount = newSelection;
                    updateSelectionInfo();
                    updateFormFromSelection();
                    updateButtonStates();
                });
    }

    private void initializeComboBoxes() {
        // Type ComboBox
        typeComboBox.setItems(FXCollections.observableArrayList(DiscountType.values()));
        typeComboBox.getSelectionModel().selectedItemProperty().addListener(
                (obs, oldType, newType) -> updateFormFieldsVisibility(newType));

        // Status Filter
        statusFilter.setItems(FXCollections.observableArrayList(
                "All", "Active", "Inactive", "Expired", "Limit Reached"));
        statusFilter.setValue("All");

        // Type Filter
        typeFilter.setItems(FXCollections.observableArrayList(DiscountType.values()));
        typeFilter.setPromptText("All Types");
    }

    private void initializeEventHandlers() {
        // Search field
        searchField.textProperty().addListener((obs, oldText, newText) -> applyFilters());

        // Filter ComboBoxes
        statusFilter.setOnAction(e -> applyFilters());
        typeFilter.setOnAction(e -> applyFilters());
    }

    private void loadDiscounts() {
        try {
            List<Discount> discounts = discountService.getAvailableDiscounts();
            allDiscounts.setAll(discounts);
            applyFilters();
            updateDiscountCount();
        } catch (Exception e) {
            AlertUtil.showError("Load Error", "Failed to load discounts: " + e.getMessage());
        }
    }

    private void applyFilters() {
        String searchText = searchField.getText().toLowerCase().trim();
        String statusFilterValue = statusFilter.getValue();
        DiscountType typeFilterValue = typeFilter.getValue();

        filteredDiscounts.setAll(allDiscounts.stream()
                .filter(discount -> {
                    // Search filter
                    if (!searchText.isEmpty()) {
                        return discount.getName().toLowerCase().contains(searchText)
                                || discount.getDescription().toLowerCase().contains(searchText)
                                || (discount.getPromoCode() != null
                                && discount.getPromoCode().toLowerCase().contains(searchText));
                    }
                    return true;
                })
                .filter(discount -> {
                    // Status filter
                    if (!"All".equals(statusFilterValue)) {
                        switch (statusFilterValue) {
                            case "Active":
                                return discount.isActive() && !discount.isExpired() && !discount.isUsageLimitReached();
                            case "Inactive":
                                return !discount.isActive();
                            case "Expired":
                                return discount.isExpired();
                            case "Limit Reached":
                                return discount.isUsageLimitReached();
                        }
                    }
                    return true;
                })
                .filter(discount -> {
                    // Type filter
                    return typeFilterValue == null || discount.getType() == typeFilterValue;
                })
                .toList());

        updateDiscountCount();
    }

    private void updateDiscountCount() {
        int total = allDiscounts.size();
        int filtered = filteredDiscounts.size();

        if (filtered == total) {
            discountCountLabel.setText("Showing: " + total + " discounts");
        } else {
            discountCountLabel.setText("Showing: " + filtered + " of " + total + " discounts");
        }
    }

    private void updateSelectionInfo() {
        if (selectedDiscount == null) {
            selectionInfoLabel.setText("Select a discount for details");
        } else {
            String info = String.format("Selected: %s (%s)",
                    selectedDiscount.getName(),
                    selectedDiscount.getType().getDisplayName());
            selectionInfoLabel.setText(info);
        }
    }

    private void updateFormFromSelection() {
        if (selectedDiscount == null) {
            clearForm();
            return;
        }

        nameField.setText(selectedDiscount.getName());
        descriptionField.setText(selectedDiscount.getDescription());
        typeComboBox.setValue(selectedDiscount.getType());
        valueField.setText(selectedDiscount.getValue().toString());

        if (selectedDiscount.getMinimumAmount() != null) {
            minimumAmountField.setText(selectedDiscount.getMinimumAmount().toString());
        }
        if (selectedDiscount.getMaximumDiscount() != null) {
            maximumDiscountField.setText(selectedDiscount.getMaximumDiscount().toString());
        }

        minimumQuantityField.setText(String.valueOf(selectedDiscount.getMinimumQuantity()));
        buyQuantityField.setText(String.valueOf(selectedDiscount.getBuyQuantity()));
        getQuantityField.setText(String.valueOf(selectedDiscount.getGetQuantity()));
        promoCodeField.setText(selectedDiscount.getPromoCode());

        if (selectedDiscount.getStartDate() != null) {
            startDatePicker.setValue(selectedDiscount.getStartDate().toLocalDate());
        }
        if (selectedDiscount.getEndDate() != null) {
            endDatePicker.setValue(selectedDiscount.getEndDate().toLocalDate());
        }

        if (selectedDiscount.getUsageLimit() > 0) {
            usageLimitField.setText(String.valueOf(selectedDiscount.getUsageLimit()));
        }

        activeCheckBox.setSelected(selectedDiscount.isActive());
        stackableCheckBox.setSelected(selectedDiscount.isStackable());

        updateFormFieldsVisibility(selectedDiscount.getType());
    }

    private void updateFormFieldsVisibility(DiscountType type) {
        if (type == null) {
            return;
        }

        // Show/hide fields based on discount type
        boolean showMinAmount = type == DiscountType.FIXED_AMOUNT || type == DiscountType.PERCENTAGE;
        boolean showMaxDiscount = type == DiscountType.PERCENTAGE;
        boolean showQuantityFields = type.isItemLevel();
        boolean showBuyGetFields = type == DiscountType.BUY_X_GET_Y || type == DiscountType.BUY_ONE_GET_ONE;

        minimumAmountField.setVisible(showMinAmount);
        maximumDiscountField.setVisible(showMaxDiscount);
        minimumQuantityField.setVisible(showQuantityFields);
        buyQuantityField.setVisible(showBuyGetFields);
        getQuantityField.setVisible(showBuyGetFields);
    }

    private void clearForm() {
        nameField.clear();
        descriptionField.clear();
        typeComboBox.setValue(null);
        valueField.clear();
        minimumAmountField.clear();
        maximumDiscountField.clear();
        minimumQuantityField.clear();
        buyQuantityField.clear();
        getQuantityField.clear();
        promoCodeField.clear();
        startDatePicker.setValue(null);
        endDatePicker.setValue(null);
        usageLimitField.clear();
        activeCheckBox.setSelected(true);
        stackableCheckBox.setSelected(false);
    }

    private void updateButtonStates() {
        boolean hasSelection = selectedDiscount != null;
        editDiscountButton.setDisable(!hasSelection || isEditMode);
        deleteDiscountButton.setDisable(!hasSelection || isEditMode);
        testDiscountButton.setDisable(!hasSelection);
    }

    private void setFormEditMode(boolean editMode) {
        isEditMode = editMode;

        // Form fields
        nameField.setDisable(!editMode);
        descriptionField.setDisable(!editMode);
        typeComboBox.setDisable(!editMode);
        valueField.setDisable(!editMode);
        minimumAmountField.setDisable(!editMode);
        maximumDiscountField.setDisable(!editMode);
        minimumQuantityField.setDisable(!editMode);
        buyQuantityField.setDisable(!editMode);
        getQuantityField.setDisable(!editMode);
        promoCodeField.setDisable(!editMode);
        startDatePicker.setDisable(!editMode);
        endDatePicker.setDisable(!editMode);
        usageLimitField.setDisable(!editMode);
        activeCheckBox.setDisable(!editMode);
        stackableCheckBox.setDisable(!editMode);

        // Buttons
        saveDiscountButton.setVisible(editMode);
        cancelDiscountButton.setVisible(editMode);
        addDiscountButton.setDisable(editMode);

        updateButtonStates();
    }

    // Event Handlers
    @FXML
    private void handleAddDiscount() {
        selectedDiscount = null;
        clearForm();
        setFormEditMode(true);
        nameField.requestFocus();
    }

    @FXML
    private void handleEditDiscount() {
        if (selectedDiscount != null) {
            setFormEditMode(true);
            nameField.requestFocus();
        }
    }

    @FXML
    private void handleDeleteDiscount() {
        if (selectedDiscount == null) {
            return;
        }

        if (AlertUtil.showConfirmation("Delete Discount",
                "Are you sure you want to delete the discount '" + selectedDiscount.getName() + "'?")) {
            try {
                discountService.removeDiscount(selectedDiscount.getId());
                loadDiscounts();
                AlertUtil.showInfo("Success", "Discount deleted successfully.");
            } catch (Exception e) {
                AlertUtil.showError("Delete Error", "Failed to delete discount: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleSaveDiscount() {
        try {
            if (!validateForm()) {
                return;
            }

            Discount discount = selectedDiscount != null ? selectedDiscount : new Discount();
            populateDiscountFromForm(discount);

            discountService.addDiscount(discount);
            loadDiscounts();
            setFormEditMode(false);

            // Select the saved discount
            discountTable.getSelectionModel().select(discount);

            AlertUtil.showInfo("Success", "Discount saved successfully.");

        } catch (Exception e) {
            AlertUtil.showError("Save Error", "Failed to save discount: " + e.getMessage());
        }
    }

    @FXML
    private void handleCancelDiscount() {
        setFormEditMode(false);
        updateFormFromSelection();
    }

    @FXML
    private void handleTestDiscount() {
        if (selectedDiscount == null) {
            return;
        }

        // Show discount test dialog
        String testInfo = String.format(
                "Discount Test Information:\n\n"
                + "Name: %s\n"
                + "Type: %s\n"
                + "Value: %s\n"
                + "Status: %s\n"
                + "Valid: %s\n"
                + "Usage: %d/%s\n"
                + "Stackable: %s",
                selectedDiscount.getName(),
                selectedDiscount.getType().getDisplayName(),
                selectedDiscount.getType() == DiscountType.PERCENTAGE
                ? selectedDiscount.getValue() + "%"
                : currencyFormat.format(selectedDiscount.getValue()),
                selectedDiscount.isValid() ? "Valid" : "Invalid",
                selectedDiscount.isValid() ? "Yes" : "No",
                selectedDiscount.getUsageCount(),
                selectedDiscount.getUsageLimit() > 0 ? String.valueOf(selectedDiscount.getUsageLimit()) : "Unlimited",
                selectedDiscount.isStackable() ? "Yes" : "No"
        );

        AlertUtil.showInfo("Discount Test", testInfo);
    }

    @FXML
    private void handleClearFilters() {
        searchField.clear();
        statusFilter.setValue("All");
        typeFilter.setValue(null);
        applyFilters();
    }

    private boolean validateForm() {
        if (nameField.getText().trim().isEmpty()) {
            AlertUtil.showWarning("Validation Error", "Discount name is required.");
            nameField.requestFocus();
            return false;
        }

        if (typeComboBox.getValue() == null) {
            AlertUtil.showWarning("Validation Error", "Discount type is required.");
            typeComboBox.requestFocus();
            return false;
        }

        try {
            BigDecimal value = new BigDecimal(valueField.getText().trim());
            if (value.compareTo(BigDecimal.ZERO) <= 0) {
                AlertUtil.showWarning("Validation Error", "Discount value must be greater than 0.");
                valueField.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            AlertUtil.showWarning("Validation Error", "Invalid discount value.");
            valueField.requestFocus();
            return false;
        }

        return true;
    }

    private void populateDiscountFromForm(Discount discount) {
        discount.setName(nameField.getText().trim());
        discount.setDescription(descriptionField.getText().trim());
        discount.setType(typeComboBox.getValue());
        discount.setValue(new BigDecimal(valueField.getText().trim()));

        if (!minimumAmountField.getText().trim().isEmpty()) {
            discount.setMinimumAmount(new BigDecimal(minimumAmountField.getText().trim()));
        }
        if (!maximumDiscountField.getText().trim().isEmpty()) {
            discount.setMaximumDiscount(new BigDecimal(maximumDiscountField.getText().trim()));
        }

        if (!minimumQuantityField.getText().trim().isEmpty()) {
            discount.setMinimumQuantity(Integer.parseInt(minimumQuantityField.getText().trim()));
        }
        if (!buyQuantityField.getText().trim().isEmpty()) {
            discount.setBuyQuantity(Integer.parseInt(buyQuantityField.getText().trim()));
        }
        if (!getQuantityField.getText().trim().isEmpty()) {
            discount.setGetQuantity(Integer.parseInt(getQuantityField.getText().trim()));
        }

        discount.setPromoCode(promoCodeField.getText().trim().isEmpty() ? null : promoCodeField.getText().trim());

        if (startDatePicker.getValue() != null) {
            discount.setStartDate(startDatePicker.getValue().atStartOfDay());
        }
        if (endDatePicker.getValue() != null) {
            discount.setEndDate(endDatePicker.getValue().atTime(23, 59, 59));
        }

        if (!usageLimitField.getText().trim().isEmpty()) {
            discount.setUsageLimit(Integer.parseInt(usageLimitField.getText().trim()));
        }

        discount.setActive(activeCheckBox.isSelected());
        discount.setStackable(stackableCheckBox.isSelected());
        discount.setUpdatedAt(LocalDateTime.now());
    }
}
