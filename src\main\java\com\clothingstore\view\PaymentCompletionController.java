package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.ReceiptPrintingService;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressIndicator;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Controller for the enhanced payment completion dialog with WhatsApp receipt
 * option
 */
public class PaymentCompletionController implements Initializable {

    // Header Controls
    @FXML
    private Label lblTransactionNumber;

    // Transaction Summary Controls
    @FXML
    private Label lblDateTime;
    @FXML
    private Label lblCustomer;
    @FXML
    private Label lblPaymentMethod;
    @FXML
    private Label lblTotalAmount;
    @FXML
    private Label lblAmountPaid;
    @FXML
    private Label lblChangeLabel;
    @FXML
    private Label lblChange;

    // Items Table
    @FXML
    private TableView<TransactionItem> tblItems;
    @FXML
    private TableColumn<TransactionItem, String> colItemName;
    @FXML
    private TableColumn<TransactionItem, String> colQuantity;
    @FXML
    private TableColumn<TransactionItem, String> colUnitPrice;
    @FXML
    private TableColumn<TransactionItem, String> colTotal;

    @FXML
    private VBox vboxProgress;
    @FXML
    private ProgressIndicator progressIndicator;
    @FXML
    private Label lblProgressStatus;
    @FXML
    private VBox vboxResult;
    @FXML
    private Label lblResultIcon;
    @FXML
    private Label lblResultMessage;

    // Traditional Receipt Controls
    @FXML
    private Button btnPrintReceipt;
    @FXML
    private Button btnEmailReceipt;
    @FXML
    private Button btnViewReceipt;

    // Action Controls
    @FXML
    private Button btnNewTransaction;
    @FXML
    private Button btnClose;

    // Data
    private Transaction transaction;
    private Customer customer;
    private BigDecimal amountPaid;
    private BigDecimal changeAmount;
    private boolean newTransactionRequested = false;

    // Services
    private ReceiptPrintingService receiptService;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateTimeFormatter;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Initialize services
        receiptService = ReceiptPrintingService.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateTimeFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");

        // Setup table columns
        setupItemsTable();

    }

    /**
     * Setup the items table columns
     */
    private void setupItemsTable() {
        colItemName.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getProductName()));

        colQuantity.setCellValueFactory(cellData
                -> new SimpleStringProperty(String.valueOf(cellData.getValue().getQuantity())));

        colUnitPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice())));

        colTotal.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getLineTotal())));
    }

    /**
     * Set the transaction data for display
     */
    public void setTransactionData(Transaction transaction, Customer customer, BigDecimal amountPaid, BigDecimal changeAmount) {
        this.transaction = transaction;
        this.customer = customer;
        this.amountPaid = amountPaid;
        this.changeAmount = changeAmount;

        populateTransactionDetails();
        populateItemsTable();
    }

    /**
     * Populate transaction details in the UI
     */
    private void populateTransactionDetails() {
        lblTransactionNumber.setText("Transaction: " + transaction.getTransactionNumber());
        lblDateTime.setText(transaction.getTransactionDate().format(dateTimeFormatter));
        lblCustomer.setText(customer != null ? customer.getFullName() : "Walk-in Customer");
        lblPaymentMethod.setText(transaction.getPaymentMethod());
        lblTotalAmount.setText(currencyFormat.format(transaction.getTotalAmount()));
        lblAmountPaid.setText(currencyFormat.format(amountPaid));

        // Handle change display
        if (changeAmount != null && changeAmount.compareTo(BigDecimal.ZERO) > 0) {
            lblChange.setText(currencyFormat.format(changeAmount));
            lblChangeLabel.setVisible(true);
            lblChange.setVisible(true);
        } else {
            lblChangeLabel.setVisible(false);
            lblChange.setVisible(false);
        }
    }

    /**
     * Populate the items table
     */
    private void populateItemsTable() {
        ObservableList<TransactionItem> items = FXCollections.observableArrayList(transaction.getItems());
        tblItems.setItems(items);
    }

    /**
     * Show progress indicator
     */
    private void showProgress(String message) {
        vboxProgress.setVisible(true);
        vboxProgress.setManaged(true);
        lblProgressStatus.setText(message);
        progressIndicator.setProgress(-1); // Indeterminate

    }

    /**
     * Hide progress indicator
     */
    private void hideProgress() {
        vboxProgress.setVisible(false);
        vboxProgress.setManaged(false);
    }

    /**
     * Show result message
     */
    private void showResult(String icon, String message, String color) {
        vboxResult.setVisible(true);
        vboxResult.setManaged(true);
        lblResultIcon.setText(icon);
        lblResultMessage.setText(message);
        lblResultMessage.setStyle("-fx-text-fill: " + color + "; -fx-font-weight: bold;");
    }

    /**
     * Handle print receipt
     */
    @FXML
    private void handlePrintReceipt() {
        receiptService.showReceiptPreview(transaction, amountPaid, changeAmount, customer);
    }

    /**
     * Handle email receipt (placeholder)
     */
    @FXML
    private void handleEmailReceipt() {
        AlertUtil.showInfo("Email Receipt", "Email receipt functionality will be implemented in a future update.");
    }

    /**
     * Handle view receipt
     */
    @FXML
    private void handleViewReceipt() {
        receiptService.showReceiptPreview(transaction, amountPaid, changeAmount, customer);
    }

    /**
     * Handle new transaction request
     */
    @FXML
    private void handleNewTransaction() {
        newTransactionRequested = true;
        closeDialog();
    }

    /**
     * Handle close dialog
     */
    @FXML
    private void handleClose() {
        closeDialog();
    }

    /**
     * Close the dialog
     */
    private void closeDialog() {
        Stage stage = (Stage) btnClose.getScene().getWindow();
        stage.close();
    }

    /**
     * Check if new transaction was requested
     */
    public boolean isNewTransactionRequested() {
        return newTransactionRequested;
    }
}
