package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

import com.clothingstore.dao.CashDrawerDAO;
import com.clothingstore.dao.CashDropDAO;
import com.clothingstore.model.CashDrawer;
import com.clothingstore.model.CashDrop;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Dialog;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;

/**
 * Controller for Cash Drawer Management
 */
public class CashDrawerController implements Initializable {

    // FXML Components - Current Drawer Status
    @FXML
    private Label currentDrawerLabel;
    @FXML
    private Label currentCashierLabel;
    @FXML
    private Label drawerStatusLabel;
    @FXML
    private Label openedAtLabel;
    @FXML
    private Label openingAmountLabel;
    @FXML
    private Label currentExpectedLabel;
    @FXML
    private Label totalSalesLabel;
    @FXML
    private Label totalCashDropsLabel;
    @FXML
    private Label transactionCountLabel;

    // FXML Components - Drawer Actions
    @FXML
    private Button openDrawerButton;
    @FXML
    private Button closeDrawerButton;
    @FXML
    private Button cashDropButton;
    @FXML
    private Button payoutButton;
    @FXML
    private Button reconcileButton;

    // FXML Components - Drawer History
    @FXML
    private TableView<CashDrawer> drawerHistoryTable;
    @FXML
    private TableColumn<CashDrawer, String> drawerNumberColumn;
    @FXML
    private TableColumn<CashDrawer, String> cashierColumn;
    @FXML
    private TableColumn<CashDrawer, String> openedColumn;
    @FXML
    private TableColumn<CashDrawer, String> closedColumn;
    @FXML
    private TableColumn<CashDrawer, String> statusColumn;
    @FXML
    private TableColumn<CashDrawer, String> varianceColumn;

    // FXML Components - Cash Drops
    @FXML
    private TableView<CashDrop> cashDropsTable;
    @FXML
    private TableColumn<CashDrop, String> dropTimeColumn;
    @FXML
    private TableColumn<CashDrop, String> dropAmountColumn;
    @FXML
    private TableColumn<CashDrop, String> dropReasonColumn;
    @FXML
    private TableColumn<CashDrop, String> dropStatusColumn;

    // FXML Components - Filters
    @FXML
    private DatePicker startDatePicker;
    @FXML
    private DatePicker endDatePicker;
    @FXML
    private ComboBox<String> statusFilter;
    @FXML
    private TextField cashierFilter;

    // Data and Services
    private ObservableList<CashDrawer> drawerHistory;
    private ObservableList<CashDrop> cashDropHistory;
    private CashDrawer currentDrawer;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateTimeFormatter;
    private CashDrawerDAO cashDrawerDAO;
    private CashDropDAO cashDropDAO;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeServices();
        initializeFormatters();
        initializeTableColumns();
        initializeComponents();
        loadCurrentDrawer();
        loadDrawerHistory();
        loadCashDropHistory();
    }

    private void initializeServices() {
        drawerHistory = FXCollections.observableArrayList();
        cashDropHistory = FXCollections.observableArrayList();

        drawerHistoryTable.setItems(drawerHistory);
        cashDropsTable.setItems(cashDropHistory);

        // Initialize DAOs
        cashDrawerDAO = CashDrawerDAO.getInstance();
        cashDropDAO = CashDropDAO.getInstance();
    }

    private void initializeFormatters() {
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");
    }

    private void initializeTableColumns() {
        // Drawer History Table
        drawerNumberColumn.setCellValueFactory(new PropertyValueFactory<>("drawerNumber"));
        cashierColumn.setCellValueFactory(new PropertyValueFactory<>("cashierName"));
        openedColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getOpenedAt() != null
                        ? cellData.getValue().getOpenedAt().format(dateTimeFormatter) : ""));
        closedColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getClosedAt() != null
                        ? cellData.getValue().getClosedAt().format(dateTimeFormatter) : "Open"));
        statusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        varianceColumn.setCellValueFactory(cellData -> {
            CashDrawer drawer = cellData.getValue();
            String variance = currencyFormat.format(drawer.getVariance());
            if (drawer.isOverage()) {
                variance = "+" + variance;
            }
            return new SimpleStringProperty(variance);
        });

        // Cash Drops Table
        dropTimeColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getFormattedDropTime()));
        dropAmountColumn.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getAmount())));
        dropReasonColumn.setCellValueFactory(new PropertyValueFactory<>("reason"));
        dropStatusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));

        // Add row factory for variance color coding
        drawerHistoryTable.setRowFactory(tv -> new TableRow<CashDrawer>() {
            @Override
            protected void updateItem(CashDrawer drawer, boolean empty) {
                super.updateItem(drawer, empty);
                if (empty || drawer == null) {
                    setStyle("");
                } else if (drawer.isShortage()) {
                    setStyle("-fx-background-color: #ffebee;"); // Light red for shortage
                } else if (drawer.isOverage()) {
                    setStyle("-fx-background-color: #fff3e0;"); // Light orange for overage
                } else if (drawer.isReconciled()) {
                    setStyle("-fx-background-color: #e8f5e8;"); // Light green for reconciled
                }
            }
        });
    }

    private void initializeComponents() {
        // Status filter
        statusFilter.setItems(FXCollections.observableArrayList(
                "All", "OPEN", "CLOSED", "RECONCILED"));
        statusFilter.setValue("All");

        // Date filters - default to last 30 days
        endDatePicker.setValue(java.time.LocalDate.now());
        startDatePicker.setValue(java.time.LocalDate.now().minusDays(30));

        // Event handlers
        statusFilter.setOnAction(e -> loadDrawerHistory());
        startDatePicker.setOnAction(e -> loadDrawerHistory());
        endDatePicker.setOnAction(e -> loadDrawerHistory());
        cashierFilter.textProperty().addListener((obs, oldText, newText) -> loadDrawerHistory());
    }

    private void loadCurrentDrawer() {
        try {
            // Try to find an open drawer for the current user
            currentDrawer = cashDrawerDAO.findOpenDrawerByCashier("Current User");
            updateCurrentDrawerDisplay();
        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load current drawer: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void updateCurrentDrawerDisplay() {
        if (currentDrawer == null) {
            currentDrawerLabel.setText("No drawer open");
            currentCashierLabel.setText("--");
            drawerStatusLabel.setText("CLOSED");
            openedAtLabel.setText("--");
            openingAmountLabel.setText("--");
            currentExpectedLabel.setText("--");
            totalSalesLabel.setText("--");
            totalCashDropsLabel.setText("--");
            transactionCountLabel.setText("--");

            // Update button states
            openDrawerButton.setDisable(false);
            closeDrawerButton.setDisable(true);
            cashDropButton.setDisable(true);
            payoutButton.setDisable(true);
            reconcileButton.setDisable(true);
        } else {
            currentDrawerLabel.setText(currentDrawer.getDrawerNumber());
            currentCashierLabel.setText(currentDrawer.getCashierName());
            drawerStatusLabel.setText(currentDrawer.getStatus());
            openedAtLabel.setText(currentDrawer.getOpenedAt().format(dateTimeFormatter));
            openingAmountLabel.setText(currencyFormat.format(currentDrawer.getOpeningAmount()));
            currentExpectedLabel.setText(currencyFormat.format(currentDrawer.getExpectedAmount()));
            totalSalesLabel.setText(currencyFormat.format(currentDrawer.getTotalCashSales()));
            totalCashDropsLabel.setText(currencyFormat.format(currentDrawer.getTotalCashDrops()));
            transactionCountLabel.setText(String.valueOf(currentDrawer.getTransactionCount()));

            // Update button states
            boolean isOpen = currentDrawer.isOpen();
            boolean isClosed = currentDrawer.isClosed();

            openDrawerButton.setDisable(isOpen);
            closeDrawerButton.setDisable(!isOpen);
            cashDropButton.setDisable(!isOpen);
            payoutButton.setDisable(!isOpen);
            reconcileButton.setDisable(!isClosed);
        }
    }

    private void loadDrawerHistory() {
        try {
            String status = statusFilter.getValue();
            String cashierName = cashierFilter.getText();
            LocalDateTime startDate = startDatePicker.getValue() != null ?
                startDatePicker.getValue().atStartOfDay() : null;
            LocalDateTime endDate = endDatePicker.getValue() != null ?
                endDatePicker.getValue().atTime(23, 59, 59) : null;

            List<CashDrawer> history = cashDrawerDAO.findAll(status, cashierName, startDate, endDate);
            drawerHistory.setAll(history);
        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load drawer history: " + e.getMessage());
            e.printStackTrace();
        }
    }



    private void loadCashDropHistory() {
        try {
            if (currentDrawer != null) {
                List<CashDrop> drops = cashDropDAO.findByDrawerId(currentDrawer.getId());
                cashDropHistory.setAll(drops);
            } else {
                // Load all recent cash drops if no current drawer
                LocalDateTime startDate = LocalDateTime.now().minusDays(7);
                LocalDateTime endDate = LocalDateTime.now();
                List<CashDrop> drops = cashDropDAO.findAll("All", startDate, endDate);
                cashDropHistory.setAll(drops);
            }
        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load cash drop history: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void handleOpenDrawer() {
        Dialog<BigDecimal> dialog = createOpenDrawerDialog();
        Optional<BigDecimal> result = dialog.showAndWait();

        result.ifPresent(openingAmount -> {
            try {
                // Generate drawer number
                String drawerNumber = "DRAWER-" + String.format("%02d", System.currentTimeMillis() % 100);

                currentDrawer = new CashDrawer(drawerNumber, "Current User", openingAmount);
                currentDrawer = cashDrawerDAO.create(currentDrawer);

                updateCurrentDrawerDisplay();
                loadDrawerHistory();

                AlertUtil.showInfo("Drawer Opened", "Cash drawer " + drawerNumber +
                    " opened successfully with " + currencyFormat.format(openingAmount));
            } catch (Exception e) {
                AlertUtil.showError("Database Error", "Failed to open drawer: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private Dialog<BigDecimal> createOpenDrawerDialog() {
        Dialog<BigDecimal> dialog = new Dialog<>();
        dialog.setTitle("Open Cash Drawer");
        dialog.setHeaderText("Enter opening cash amount");

        ButtonType openButtonType = new ButtonType("Open Drawer", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(openButtonType, ButtonType.CANCEL);

        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextField amountField = new TextField("100.00");
        amountField.setPromptText("Opening amount");

        grid.add(new Label("Opening Amount:"), 0, 0);
        grid.add(amountField, 1, 0);

        dialog.getDialogPane().setContent(grid);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == openButtonType) {
                try {
                    return new BigDecimal(amountField.getText());
                } catch (NumberFormatException e) {
                    AlertUtil.showWarning("Invalid Amount", "Please enter a valid amount.");
                    return null;
                }
            }
            return null;
        });

        return dialog;
    }

    @FXML
    private void handleCloseDrawer() {
        if (currentDrawer == null || !currentDrawer.isOpen()) {
            AlertUtil.showWarning("No Open Drawer", "No drawer is currently open.");
            return;
        }

        Dialog<BigDecimal> dialog = createCloseDrawerDialog();
        Optional<BigDecimal> result = dialog.showAndWait();

        result.ifPresent(actualAmount -> {
            try {
                cashDrawerDAO.closeDrawer(currentDrawer.getId(), actualAmount);
                currentDrawer.closeDrawer(actualAmount);
                updateCurrentDrawerDisplay();

                String message = String.format("Drawer closed.\nExpected: %s\nActual: %s\nVariance: %s (%s)",
                        currencyFormat.format(currentDrawer.getExpectedAmount()),
                        currencyFormat.format(currentDrawer.getActualAmount()),
                        currencyFormat.format(currentDrawer.getVariance()),
                        currentDrawer.getVarianceType());

                AlertUtil.showInfo("Drawer Closed", message);
                loadDrawerHistory();
            } catch (Exception e) {
                AlertUtil.showError("Database Error", "Failed to close drawer: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private Dialog<BigDecimal> createCloseDrawerDialog() {
        Dialog<BigDecimal> dialog = new Dialog<>();
        dialog.setTitle("Close Cash Drawer");
        dialog.setHeaderText("Count actual cash in drawer");

        ButtonType closeButtonType = new ButtonType("Close Drawer", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(closeButtonType, ButtonType.CANCEL);

        VBox content = new VBox(15);
        content.setPadding(new Insets(20));

        // Expected amount display
        Label expectedLabel = new Label("Expected Amount: "
                + currencyFormat.format(currentDrawer.getExpectedAmount()));
        expectedLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        // Actual amount input
        HBox amountBox = new HBox(10);
        amountBox.setAlignment(Pos.CENTER_LEFT);
        TextField actualAmountField = new TextField();
        actualAmountField.setPromptText("Actual cash count");
        amountBox.getChildren().addAll(new Label("Actual Amount:"), actualAmountField);

        content.getChildren().addAll(expectedLabel, amountBox);
        dialog.getDialogPane().setContent(content);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == closeButtonType) {
                try {
                    return new BigDecimal(actualAmountField.getText());
                } catch (NumberFormatException e) {
                    AlertUtil.showWarning("Invalid Amount", "Please enter a valid amount.");
                    return null;
                }
            }
            return null;
        });

        return dialog;
    }

    @FXML
    private void handleCashDrop() {
        if (currentDrawer == null || !currentDrawer.isOpen()) {
            AlertUtil.showWarning("No Open Drawer", "No drawer is currently open.");
            return;
        }

        Dialog<CashDrop> dialog = createCashDropDialog();
        Optional<CashDrop> result = dialog.showAndWait();

        result.ifPresent(cashDrop -> {
            try {
                // Create cash drop in database
                CashDrop createdDrop = cashDropDAO.create(cashDrop);

                // Update drawer totals
                currentDrawer.addCashDrop(cashDrop.getAmount());
                cashDrawerDAO.updateTotals(currentDrawer.getId(),
                    currentDrawer.getTotalSales(),
                    currentDrawer.getTotalCashSales(),
                    currentDrawer.getTotalRefunds(),
                    currentDrawer.getTotalCashDrops(),
                    currentDrawer.getTotalPayouts(),
                    currentDrawer.getTransactionCount());

                updateCurrentDrawerDisplay();
                loadCashDropHistory();

                AlertUtil.showInfo("Cash Drop", "Cash drop of "
                        + currencyFormat.format(cashDrop.getAmount()) + " recorded successfully.");
            } catch (Exception e) {
                AlertUtil.showError("Database Error", "Failed to record cash drop: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private Dialog<CashDrop> createCashDropDialog() {
        Dialog<CashDrop> dialog = new Dialog<>();
        dialog.setTitle("Cash Drop");
        dialog.setHeaderText("Record cash removal from drawer");

        ButtonType dropButtonType = new ButtonType("Record Drop", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(dropButtonType, ButtonType.CANCEL);

        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextField amountField = new TextField();
        amountField.setPromptText("Amount to drop");

        ComboBox<String> reasonCombo = new ComboBox<>();
        reasonCombo.setItems(FXCollections.observableArrayList(
                "Bank Deposit", "Safe Drop", "Change Fund", "Other"));
        reasonCombo.setValue("Bank Deposit");

        TextArea notesArea = new TextArea();
        notesArea.setPrefRowCount(2);
        notesArea.setPromptText("Optional notes");

        grid.add(new Label("Amount:"), 0, 0);
        grid.add(amountField, 1, 0);
        grid.add(new Label("Reason:"), 0, 1);
        grid.add(reasonCombo, 1, 1);
        grid.add(new Label("Notes:"), 0, 2);
        grid.add(notesArea, 1, 2);

        dialog.getDialogPane().setContent(grid);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == dropButtonType) {
                try {
                    BigDecimal amount = new BigDecimal(amountField.getText());
                    CashDrop drop = new CashDrop(currentDrawer.getId(),
                            currentDrawer.getDrawerNumber(),
                            currentDrawer.getCashierName(),
                            amount, reasonCombo.getValue());
                    drop.setNotes(notesArea.getText());
                    return drop;
                } catch (NumberFormatException e) {
                    AlertUtil.showWarning("Invalid Amount", "Please enter a valid amount.");
                    return null;
                }
            }
            return null;
        });

        return dialog;
    }

    @FXML
    private void handlePayout() {
        // TODO: Implement payout functionality
        AlertUtil.showInfo("Payout", "Payout functionality will be implemented in a future update.");
    }

    @FXML
    private void handleReconcile() {
        if (currentDrawer == null || !currentDrawer.isClosed()) {
            AlertUtil.showWarning("Cannot Reconcile", "Drawer must be closed before reconciliation.");
            return;
        }

        if (AlertUtil.showConfirmation("Reconcile Drawer",
                "Are you sure you want to reconcile this drawer? This action cannot be undone.")) {
            try {
                cashDrawerDAO.reconcileDrawer(currentDrawer.getId());
                currentDrawer.reconcile();
                updateCurrentDrawerDisplay();
                loadDrawerHistory();

                AlertUtil.showInfo("Reconciled", "Drawer has been reconciled successfully.");
            } catch (Exception e) {
                AlertUtil.showError("Database Error", "Failed to reconcile drawer: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    @FXML
    private void handleGenerateShiftReport() {
        if (currentDrawer == null) {
            AlertUtil.showWarning("No Drawer", "No drawer data available for report generation.");
            return;
        }

        // TODO: Generate comprehensive shift report
        String report = String.format(
                "SHIFT REPORT\n\n"
                + "Drawer: %s\n"
                + "Cashier: %s\n"
                + "Opened: %s\n"
                + "Duration: %s\n"
                + "Status: %s\n\n"
                + "FINANCIAL SUMMARY\n"
                + "Opening Amount: %s\n"
                + "Total Sales: %s\n"
                + "Cash Drops: %s\n"
                + "Expected: %s\n"
                + "Actual: %s\n"
                + "Variance: %s (%s)\n\n"
                + "TRANSACTION COUNT: %d",
                currentDrawer.getDrawerNumber(),
                currentDrawer.getCashierName(),
                currentDrawer.getOpenedAt().format(dateTimeFormatter),
                currentDrawer.getShiftDuration(),
                currentDrawer.getStatus(),
                currencyFormat.format(currentDrawer.getOpeningAmount()),
                currencyFormat.format(currentDrawer.getTotalCashSales()),
                currencyFormat.format(currentDrawer.getTotalCashDrops()),
                currencyFormat.format(currentDrawer.getExpectedAmount()),
                currencyFormat.format(currentDrawer.getActualAmount()),
                currencyFormat.format(currentDrawer.getVariance()),
                currentDrawer.getVarianceType(),
                currentDrawer.getTransactionCount()
        );

        AlertUtil.showInfo("Shift Report", report);
    }

    @FXML
    private void handleRefreshData() {
        loadCurrentDrawer();
        loadDrawerHistory();
        loadCashDropHistory();
    }
}
