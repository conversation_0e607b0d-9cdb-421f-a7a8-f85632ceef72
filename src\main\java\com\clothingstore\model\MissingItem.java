package com.clothingstore.model;

import java.time.LocalDateTime;

/**
 * Model class representing a missing inventory item
 */
public class MissingItem {

    private Long id;
    private Product product;
    private int quantity;
    private String reason;
    private LocalDateTime reportDate;
    private String status; // REPORTED, INVESTIGATING, RESOLVED, WRITTEN_OFF
    private String reportedBy;
    private String notes;
    private LocalDateTime resolvedDate;
    private String resolvedBy;

    // Constructors
    public MissingItem() {
        this.reportDate = LocalDateTime.now();
        this.status = "REPORTED";
    }

    public MissingItem(Product product, int quantity, String reason, String status, String reportedBy) {
        this();
        this.product = product;
        this.quantity = quantity;
        this.reason = reason;
        this.status = status;
        this.reportedBy = reportedBy;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReportedBy() {
        return reportedBy;
    }

    public void setReportedBy(String reportedBy) {
        this.reportedBy = reportedBy;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDateTime getResolvedDate() {
        return resolvedDate;
    }

    public void setResolvedDate(LocalDateTime resolvedDate) {
        this.resolvedDate = resolvedDate;
    }

    public String getResolvedBy() {
        return resolvedBy;
    }

    public void setResolvedBy(String resolvedBy) {
        this.resolvedBy = resolvedBy;
    }

    // Business methods
    public boolean isResolved() {
        return "RESOLVED".equals(status) || "WRITTEN_OFF".equals(status);
    }

    public void resolve(String resolvedBy, String notes) {
        this.status = "RESOLVED";
        this.resolvedBy = resolvedBy;
        this.resolvedDate = LocalDateTime.now();
        this.notes = notes;
    }

    public void writeOff(String resolvedBy, String notes) {
        this.status = "WRITTEN_OFF";
        this.resolvedBy = resolvedBy;
        this.resolvedDate = LocalDateTime.now();
        this.notes = notes;
    }

    @Override
    public String toString() {
        return String.format("MissingItem{id=%d, product='%s', quantity=%d, status='%s'}",
                id, product != null ? product.getName() : "Unknown", quantity, status);
    }
}
