<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.AdvancedInventoryController">
   <children>
      <!-- Header Section -->
      <VBox spacing="15.0" styleClass="header-section">
         <children>
            <!-- Title and Actions -->
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label styleClass="page-title" text="Advanced Inventory Management" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="generateReorderReportButton" onAction="#handleGenerateReorderReport" styleClass="secondary-button" text="📋 Reorder Report" />
                  <Button fx:id="exportDataButton" onAction="#handleExportData" styleClass="secondary-button" text="📊 Export Data" />
                  <Button fx:id="refreshDataButton" onAction="#refreshStockData" styleClass="primary-button" text="🔄 Refresh" />
               </children>
            </HBox>
            
            <!-- Summary Cards -->
            <HBox spacing="15.0" styleClass="summary-section">
               <children>
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card">
                     <children>
                        <Label styleClass="summary-title" text="📦 Total Products" />
                        <Label fx:id="totalProductsLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card warning">
                     <children>
                        <Label styleClass="summary-title" text="⚠️ Low Stock" />
                        <Label fx:id="lowStockCountLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card danger">
                     <children>
                        <Label styleClass="summary-title" text="❌ Out of Stock" />
                        <Label fx:id="outOfStockCountLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card success">
                     <children>
                        <Label styleClass="summary-title" text="💰 Total Value" />
                        <Label fx:id="totalInventoryValueLabel" styleClass="summary-value" text="$0.00" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
                  
                  <VBox alignment="CENTER" spacing="5.0" styleClass="summary-card info">
                     <children>
                        <Label styleClass="summary-title" text="🔄 Reorder Required" />
                        <Label fx:id="reorderRequiredLabel" styleClass="summary-value" text="0" />
                     </children>
                     <HBox.hgrow>ALWAYS</HBox.hgrow>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Filter Controls -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
               <children>
                  <Label styleClass="filter-label" text="🔍 Search:" />
                  <TextField fx:id="searchField" prefWidth="200.0" promptText="Search products..." styleClass="search-box" />
                  <Label styleClass="filter-label" text="Category:" />
                  <ComboBox fx:id="categoryFilter" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="Status:" />
                  <ComboBox fx:id="stockStatusFilter" prefWidth="120.0" styleClass="combo-box" />
                  <Label styleClass="filter-label" text="Supplier:" />
                  <ComboBox fx:id="supplierFilter" prefWidth="150.0" styleClass="combo-box" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </VBox>

      <!-- Main Content Tabs -->
      <TabPane fx:id="mainTabPane" VBox.vgrow="ALWAYS">
         <tabs>
            <!-- Stock Levels Tab -->
            <Tab fx:id="stockLevelsTab" text="📊 Stock Levels" closable="false">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <TableView fx:id="stockLevelsTable" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="productNameColumn" prefWidth="200.0" text="Product Name" />
                              <TableColumn fx:id="skuColumn" prefWidth="120.0" text="SKU" />
                              <TableColumn fx:id="currentStockColumn" prefWidth="100.0" text="Current Stock" />
                              <TableColumn fx:id="minStockColumn" prefWidth="100.0" text="Min Stock" />
                              <TableColumn fx:id="reorderQtyColumn" prefWidth="100.0" text="Reorder Qty" />
                              <TableColumn fx:id="stockStatusColumn" prefWidth="120.0" text="Status" />
                              <TableColumn fx:id="stockValueColumn" prefWidth="120.0" text="Stock Value" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>

            <!-- Reorder Management Tab -->
            <Tab fx:id="reorderTab" text="🔄 Reorder Management" closable="false">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="section-title" text="Products Requiring Reorder" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button fx:id="bulkAdjustmentButton" onAction="#handleBulkAdjustment" styleClass="secondary-button" text="📦 Bulk Order" />
                           </children>
                        </HBox>
                        
                        <TableView fx:id="reorderTable" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="reorderProductColumn" prefWidth="200.0" text="Product" />
                              <TableColumn fx:id="reorderSupplierColumn" prefWidth="150.0" text="Supplier" />
                              <TableColumn fx:id="reorderCurrentStockColumn" prefWidth="100.0" text="Current" />
                              <TableColumn fx:id="reorderMinStockColumn" prefWidth="100.0" text="Min Stock" />
                              <TableColumn fx:id="reorderSuggestedQtyColumn" prefWidth="120.0" text="Suggested Qty" />
                              <TableColumn fx:id="reorderActionsColumn" prefWidth="150.0" text="Actions" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>

            <!-- Stock Adjustments Tab -->
            <Tab fx:id="adjustmentsTab" text="⚖️ Stock Adjustments" closable="false">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <!-- Adjustment Form -->
                        <VBox spacing="10.0" styleClass="form-section">
                           <children>
                              <Label styleClass="section-title" text="Make Stock Adjustment" />
                              
                              <GridPane hgap="15.0" vgap="10.0">
                                 <columnConstraints>
                                    <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                                    <ColumnConstraints hgrow="ALWAYS" />
                                    <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                                    <ColumnConstraints hgrow="ALWAYS" />
                                 </columnConstraints>
                                 <rowConstraints>
                                    <RowConstraints />
                                    <RowConstraints />
                                    <RowConstraints />
                                 </rowConstraints>
                                 
                                 <Label styleClass="form-label" text="Product:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                 <ComboBox fx:id="adjustmentProductCombo" prefWidth="200.0" promptText="Select product" styleClass="form-field" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                                 
                                 <Label styleClass="form-label" text="Quantity:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                                 <TextField fx:id="adjustmentQuantityField" prefWidth="100.0" promptText="0" styleClass="form-field" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                                 
                                 <Label styleClass="form-label" text="Type:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                 <ComboBox fx:id="adjustmentTypeCombo" prefWidth="150.0" promptText="Select type" styleClass="form-field" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                 
                                 <Label styleClass="form-label" text="Reason:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                 <TextArea fx:id="adjustmentReasonField" prefRowCount="2" promptText="Enter reason for adjustment" styleClass="form-field" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="2" />
                              </GridPane>
                              
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Button fx:id="submitAdjustmentButton" onAction="#handleSubmitAdjustment" styleClass="primary-button" text="✅ Submit Adjustment" />
                                 </children>
                              </HBox>
                           </children>
                        </VBox>
                        
                        <!-- Adjustment History -->
                        <VBox spacing="10.0" VBox.vgrow="ALWAYS">
                           <children>
                              <Label styleClass="section-title" text="Recent Adjustments" />
                              <TableView fx:id="adjustmentsHistoryTable" VBox.vgrow="ALWAYS">
                                 <columns>
                                    <TableColumn prefWidth="120.0" text="Date" />
                                    <TableColumn prefWidth="200.0" text="Product" />
                                    <TableColumn prefWidth="100.0" text="Type" />
                                    <TableColumn prefWidth="80.0" text="Quantity" />
                                    <TableColumn prefWidth="200.0" text="Reason" />
                                 </columns>
                              </TableView>
                           </children>
                        </VBox>
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>

            <!-- Stock Movements Tab -->
            <Tab fx:id="movementsTab" text="📈 Stock Movements" closable="false">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <!-- Movement Filters -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0">
                           <children>
                              <Label styleClass="form-label" text="From:" />
                              <DatePicker fx:id="movementStartDate" prefWidth="130.0" styleClass="form-field" />
                              <Label styleClass="form-label" text="To:" />
                              <DatePicker fx:id="movementEndDate" prefWidth="130.0" styleClass="form-field" />
                              <Label styleClass="form-label" text="Type:" />
                              <ComboBox fx:id="movementTypeFilter" prefWidth="120.0" styleClass="combo-box" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Button styleClass="primary-button" text="🔍 Filter" />
                           </children>
                        </HBox>
                        
                        <!-- Movements Table -->
                        <TableView fx:id="movementsTable" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn prefWidth="120.0" text="Date" />
                              <TableColumn prefWidth="200.0" text="Product" />
                              <TableColumn prefWidth="100.0" text="Type" />
                              <TableColumn prefWidth="80.0" text="Quantity" />
                              <TableColumn prefWidth="150.0" text="Reference" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </children>
</VBox>
