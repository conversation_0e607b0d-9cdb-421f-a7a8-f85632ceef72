
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

public class TestTransactionItemLoading {

    public static void main(String[] args) {
        try {
            System.out.println("=== TESTING TRANSACTION ITEM LOADING ===");

            // Test transaction item ID 168
            Long transactionItemId = 168L;

            // 1. Direct database query to get transaction_id
            System.out.println("1. Direct database query for item " + transactionItemId + ":");
            String sql = "SELECT transaction_id FROM transaction_items WHERE id = ?";
            try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setLong(1, transactionItemId);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        Long transactionId = rs.getLong("transaction_id");
                        System.out.println("  Found transaction ID: " + transactionId);

                        // 2. Load the transaction using TransactionDAO
                        System.out.println("\n2. Loading transaction " + transactionId + " using TransactionDAO:");
                        TransactionDAO transactionDAO = TransactionDAO.getInstance();
                        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);

                        if (transactionOpt.isPresent()) {
                            Transaction transaction = transactionOpt.get();
                            System.out.println("  Transaction loaded successfully:");
                            System.out.println("    Transaction Number: " + transaction.getTransactionNumber());
                            System.out.println("    Items Count: " + transaction.getItems().size());

                            // 3. Check each item's ID
                            System.out.println("\n3. Transaction items and their IDs:");
                            for (int i = 0; i < transaction.getItems().size(); i++) {
                                TransactionItem item = transaction.getItems().get(i);
                                System.out.printf("    Item %d: ID=%s, Product=%s, Qty=%d%n",
                                        i + 1,
                                        item.getId(),
                                        item.getProduct() != null ? item.getProduct().getName() : "Unknown",
                                        item.getQuantity()
                                );
                            }

                            // 4. Try to find the specific item
                            System.out.println("\n4. Looking for item with ID " + transactionItemId + ":");
                            final Long searchItemId = transactionItemId;
                            TransactionItem foundItem = transaction.getItems().stream()
                                    .filter(item -> item.getId() != null && item.getId().equals(searchItemId))
                                    .findFirst()
                                    .orElse(null);

                            if (foundItem != null) {
                                System.out.println("  SUCCESS: Found item: " + foundItem.getProduct().getName());
                            } else {
                                System.out.println("  ERROR: Item not found in transaction items list");
                                System.out.println("  Available item IDs:");
                                for (TransactionItem item : transaction.getItems()) {
                                    System.out.println("    - " + item.getId());
                                }
                            }

                        } else {
                            System.out.println("  ERROR: Transaction not found!");
                        }

                    } else {
                        System.out.println("  ERROR: Transaction item not found in database!");
                    }
                }
            }

            // Test with item 22 as well
            System.out.println("\n" + "=".repeat(60));
            System.out.println("Testing transaction item ID 22:");

            transactionItemId = 22L;
            try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setLong(1, transactionItemId);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        Long transactionId = rs.getLong("transaction_id");
                        System.out.println("  Found transaction ID: " + transactionId);

                        TransactionDAO transactionDAO = TransactionDAO.getInstance();
                        Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);

                        if (transactionOpt.isPresent()) {
                            Transaction transaction = transactionOpt.get();
                            System.out.println("  Transaction: " + transaction.getTransactionNumber());
                            System.out.println("  Items: " + transaction.getItems().size());

                            for (TransactionItem item : transaction.getItems()) {
                                System.out.printf("    Item ID: %s, Product: %s%n",
                                        item.getId(),
                                        item.getProduct() != null ? item.getProduct().getName() : "Unknown"
                                );
                            }
                        }
                    }
                }
            }

        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
