package com.clothingstore.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.TaxRate;
import com.clothingstore.model.TaxType;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

/**
 * Service for handling tax calculations and management
 */
public class TaxService {

    private static final Logger LOGGER = Logger.getLogger(TaxService.class.getName());
    private static TaxService instance;

    private final List<TaxRate> taxRates;
    private final Set<String> taxExemptCustomers;

    private TaxService() {
        this.taxRates = new ArrayList<>();
        this.taxExemptCustomers = new HashSet<>();
        initializeDefaultTaxRates();
    }

    public static synchronized TaxService getInstance() {
        if (instance == null) {
            instance = new TaxService();
        }
        return instance;
    }

    /**
     * Initialize default tax rates
     */
    private void initializeDefaultTaxRates() {
        // State sales tax
        TaxRate stateTax = new TaxRate("State Sales Tax", new BigDecimal("6.0"), TaxType.SALES_TAX);
        stateTax.setDescription("State of California sales tax");
        stateTax.setJurisdiction("California");
        taxRates.add(stateTax);

        // Local sales tax
        TaxRate localTax = new TaxRate("Local Sales Tax", new BigDecimal("2.5"), TaxType.SALES_TAX);
        localTax.setDescription("Local city sales tax");
        localTax.setJurisdiction("San Francisco");
        taxRates.add(localTax);

        // Luxury tax for expensive items
        TaxRate luxuryTax = new TaxRate("Luxury Tax", new BigDecimal("5.0"), TaxType.LUXURY_TAX);
        luxuryTax.setDescription("Luxury tax for items over $500");
        luxuryTax.setMinimumAmount(new BigDecimal("500"));
        luxuryTax.setApplicableCategories("Jewelry,Designer");
        taxRates.add(luxuryTax);

        // Environmental tax for certain categories
        TaxRate envTax = new TaxRate("Environmental Tax", new BigDecimal("1.0"), TaxType.ENVIRONMENTAL_TAX);
        envTax.setDescription("Environmental impact tax");
        envTax.setApplicableCategories("Leather,Synthetic");
        taxRates.add(envTax);
    }

    /**
     * Calculate total tax for a transaction
     */
    public TaxCalculationResult calculateTax(Transaction transaction, Customer customer) {
        try {
            // Check if customer is tax exempt
            if (isCustomerTaxExempt(customer)) {
                return new TaxCalculationResult(true, "Customer is tax exempt",
                        BigDecimal.ZERO, new ArrayList<>());
            }

            List<AppliedTax> appliedTaxes = new ArrayList<>();
            BigDecimal totalTax = BigDecimal.ZERO;

            // Calculate tax for each item
            for (TransactionItem item : transaction.getItems()) {
                List<TaxRate> applicableTaxRates = findApplicableTaxRates(item);

                for (TaxRate taxRate : applicableTaxRates) {
                    BigDecimal itemTotal = item.getLineTotal();
                    BigDecimal taxAmount = calculateTaxAmount(itemTotal, taxRate);

                    if (taxAmount.compareTo(BigDecimal.ZERO) > 0) {
                        AppliedTax appliedTax = new AppliedTax(taxRate, taxAmount, item);
                        appliedTaxes.add(appliedTax);
                        totalTax = totalTax.add(taxAmount);
                    }
                }
            }

            // Consolidate taxes by rate
            Map<String, AppliedTax> consolidatedTaxes = consolidateTaxes(appliedTaxes);

            return new TaxCalculationResult(true, "Tax calculated successfully",
                    totalTax, new ArrayList<>(consolidatedTaxes.values()));

        } catch (Exception e) {
            LOGGER.severe("Error calculating tax: " + e.getMessage());
            return new TaxCalculationResult(false, "Error calculating tax: " + e.getMessage(),
                    BigDecimal.ZERO, new ArrayList<>());
        }
    }

    /**
     * Find applicable tax rates for a transaction item
     */
    private List<TaxRate> findApplicableTaxRates(TransactionItem item) {
        return taxRates.stream()
                .filter(taxRate -> isTaxRateApplicable(taxRate, item))
                .collect(Collectors.toList());
    }

    /**
     * Check if a tax rate is applicable to a transaction item
     */
    private boolean isTaxRateApplicable(TaxRate taxRate, TransactionItem item) {
        if (!taxRate.isValid()) {
            return false;
        }

        // Check if tax rate applies to the item's category
        Product product = item.getProduct();
        if (product != null && !taxRate.isApplicableToCategory(product.getCategory())) {
            return false;
        }

        // Check if tax rate applies to the item's amount
        if (!taxRate.isApplicableToAmount(item.getLineTotal())) {
            return false;
        }

        return true;
    }

    /**
     * Calculate tax amount for a given amount and tax rate
     */
    private BigDecimal calculateTaxAmount(BigDecimal amount, TaxRate taxRate) {
        if (amount == null || taxRate == null || taxRate.getRate() == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal taxAmount = amount.multiply(taxRate.getRate())
                .divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

        // Apply maximum tax amount if specified
        if (taxRate.getMaximumAmount() != null) {
            taxAmount = taxAmount.min(taxRate.getMaximumAmount());
        }

        return taxAmount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Consolidate multiple tax applications by tax rate
     */
    private Map<String, AppliedTax> consolidateTaxes(List<AppliedTax> appliedTaxes) {
        Map<String, AppliedTax> consolidated = new HashMap<>();

        for (AppliedTax appliedTax : appliedTaxes) {
            String key = appliedTax.getTaxRate().getName();

            if (consolidated.containsKey(key)) {
                AppliedTax existing = consolidated.get(key);
                BigDecimal newAmount = existing.getAmount().add(appliedTax.getAmount());
                consolidated.put(key, new AppliedTax(existing.getTaxRate(), newAmount, null));
            } else {
                consolidated.put(key, appliedTax);
            }
        }

        return consolidated;
    }

    /**
     * Check if customer is tax exempt
     */
    private boolean isCustomerTaxExempt(Customer customer) {
        if (customer == null) {
            return false;
        }

        // Check by customer ID
        if (customer.getId() != null && taxExemptCustomers.contains(customer.getId().toString())) {
            return true;
        }

        // Check by phone
        if (customer.getPhone() != null && taxExemptCustomers.contains(customer.getPhone())) {
            return true;
        }

        return false;
    }

    /**
     * Add customer to tax exempt list
     */
    public void addTaxExemptCustomer(Customer customer) {
        if (customer != null) {
            if (customer.getId() != null) {
                taxExemptCustomers.add(customer.getId().toString());
            }
            if (customer.getPhone() != null) {
                taxExemptCustomers.add(customer.getPhone());
            }
        }
    }

    /**
     * Remove customer from tax exempt list
     */
    public void removeTaxExemptCustomer(Customer customer) {
        if (customer != null) {
            if (customer.getId() != null) {
                taxExemptCustomers.remove(customer.getId().toString());
            }
            if (customer.getPhone() != null) {
                taxExemptCustomers.remove(customer.getPhone());
            }
        }
    }

    /**
     * Get all tax rates
     */
    public List<TaxRate> getAllTaxRates() {
        return new ArrayList<>(taxRates);
    }

    /**
     * Get active tax rates
     */
    public List<TaxRate> getActiveTaxRates() {
        return taxRates.stream()
                .filter(TaxRate::isValid)
                .collect(Collectors.toList());
    }

    /**
     * Add a new tax rate
     */
    public void addTaxRate(TaxRate taxRate) {
        if (taxRate != null) {
            taxRates.add(taxRate);
        }
    }

    /**
     * Remove a tax rate
     */
    public boolean removeTaxRate(Long taxRateId) {
        return taxRates.removeIf(tr -> tr.getId() != null && tr.getId().equals(taxRateId));
    }

    /**
     * Calculate effective tax rate for a transaction
     */
    public BigDecimal getEffectiveTaxRate(Transaction transaction, Customer customer) {
        TaxCalculationResult result = calculateTax(transaction, customer);

        if (!result.isSuccess() || transaction.getSubtotal().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return result.getTotalTax()
                .divide(transaction.getSubtotal(), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Generate tax report for a list of transactions
     */
    public TaxReport generateTaxReport(List<Transaction> transactions, LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal totalTaxCollected = BigDecimal.ZERO;
        BigDecimal totalTaxableAmount = BigDecimal.ZERO;
        Map<String, BigDecimal> taxByJurisdiction = new HashMap<>();
        Map<String, BigDecimal> taxByType = new HashMap<>();

        for (Transaction transaction : transactions) {
            if (isTransactionInDateRange(transaction, startDate, endDate)) {
                TaxCalculationResult taxResult = calculateTax(transaction, transaction.getCustomer());

                if (taxResult.isSuccess()) {
                    totalTaxCollected = totalTaxCollected.add(taxResult.getTotalTax());
                    totalTaxableAmount = totalTaxableAmount.add(transaction.getSubtotal());

                    // Group by jurisdiction and type
                    for (AppliedTax appliedTax : taxResult.getAppliedTaxes()) {
                        TaxRate taxRate = appliedTax.getTaxRate();

                        // By jurisdiction
                        String jurisdiction = taxRate.getJurisdiction() != null
                                ? taxRate.getJurisdiction() : "Unknown";
                        taxByJurisdiction.merge(jurisdiction, appliedTax.getAmount(), BigDecimal::add);

                        // By type
                        String type = taxRate.getType().getDisplayName();
                        taxByType.merge(type, appliedTax.getAmount(), BigDecimal::add);
                    }
                }
            }
        }

        return new TaxReport(totalTaxCollected, totalTaxableAmount, taxByJurisdiction, taxByType,
                startDate, endDate);
    }

    /**
     * Check if transaction is within date range
     */
    private boolean isTransactionInDateRange(Transaction transaction, LocalDateTime startDate, LocalDateTime endDate) {
        LocalDateTime transactionDate = transaction.getTransactionDate();

        if (startDate != null && transactionDate.isBefore(startDate)) {
            return false;
        }

        if (endDate != null && transactionDate.isAfter(endDate)) {
            return false;
        }

        return true;
    }
}

/**
 * Result class for tax calculations
 */
class TaxCalculationResult {

    private final boolean success;
    private final String message;
    private final BigDecimal totalTax;
    private final List<AppliedTax> appliedTaxes;

    public TaxCalculationResult(boolean success, String message, BigDecimal totalTax,
            List<AppliedTax> appliedTaxes) {
        this.success = success;
        this.message = message;
        this.totalTax = totalTax != null ? totalTax : BigDecimal.ZERO;
        this.appliedTaxes = appliedTaxes != null ? appliedTaxes : new ArrayList<>();
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public List<AppliedTax> getAppliedTaxes() {
        return appliedTaxes;
    }

    public boolean hasTax() {
        return totalTax.compareTo(BigDecimal.ZERO) > 0;
    }
}

/**
 * Class representing a tax that has been applied
 */
class AppliedTax {

    private final TaxRate taxRate;
    private final BigDecimal amount;
    private final TransactionItem item; // Optional, for item-level taxes

    public AppliedTax(TaxRate taxRate, BigDecimal amount, TransactionItem item) {
        this.taxRate = taxRate;
        this.amount = amount != null ? amount : BigDecimal.ZERO;
        this.item = item;
    }

    public TaxRate getTaxRate() {
        return taxRate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public TransactionItem getItem() {
        return item;
    }

    public String getDisplayText() {
        return String.format("%s: $%.2f", taxRate.getName(), amount);
    }
}

/**
 * Tax report class
 */
class TaxReport {

    private final BigDecimal totalTaxCollected;
    private final BigDecimal totalTaxableAmount;
    private final Map<String, BigDecimal> taxByJurisdiction;
    private final Map<String, BigDecimal> taxByType;
    private final LocalDateTime startDate;
    private final LocalDateTime endDate;

    public TaxReport(BigDecimal totalTaxCollected, BigDecimal totalTaxableAmount,
            Map<String, BigDecimal> taxByJurisdiction, Map<String, BigDecimal> taxByType,
            LocalDateTime startDate, LocalDateTime endDate) {
        this.totalTaxCollected = totalTaxCollected;
        this.totalTaxableAmount = totalTaxableAmount;
        this.taxByJurisdiction = taxByJurisdiction;
        this.taxByType = taxByType;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public BigDecimal getTotalTaxCollected() {
        return totalTaxCollected;
    }

    public BigDecimal getTotalTaxableAmount() {
        return totalTaxableAmount;
    }

    public Map<String, BigDecimal> getTaxByJurisdiction() {
        return taxByJurisdiction;
    }

    public Map<String, BigDecimal> getTaxByType() {
        return taxByType;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public BigDecimal getEffectiveTaxRate() {
        if (totalTaxableAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return totalTaxCollected.divide(totalTaxableAmount, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }
}
