package com.clothingstore.view;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.service.PaymentHistoryService;
import com.clothingstore.util.AlertUtil;
import com.clothingstore.view.PaymentDialogController;

import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.MenuBar;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.MenuItem;
import javafx.scene.control.ToolBar;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

/**
 * Main Window Controller for the Clothing Store Management System
 */
public class MainWindowController implements Initializable {

    // Menu items
    @FXML
    private MenuBar menuBar;
    @FXML
    private MenuItem menuBackup;
    @FXML
    private MenuItem menuRestore;
    @FXML
    private MenuItem menuExit;
    @FXML
    private MenuItem menuProducts;
    @FXML
    private MenuItem menuSuppliers;
    @FXML
    private MenuItem menuLowStock;
    @FXML
    private MenuItem menuInventoryReport;
    @FXML
    private MenuItem menuCustomers;
    @FXML
    private MenuItem menuCustomerGroups;
    @FXML
    private MenuItem menuCustomerReport;
    @FXML
    private MenuItem menuPOS;
    @FXML
    private MenuItem menuTransactions;
    @FXML
    private MenuItem menuSalesReport;
    @FXML
    private MenuItem menuDailySales;
    @FXML
    private MenuItem menuMonthlySales;
    @FXML
    private MenuItem menuProfitReport;
    @FXML
    private MenuItem menuAbout;
    @FXML
    private MenuItem menuHelp;

    // Toolbar
    @FXML
    private ToolBar toolBar;
    @FXML
    private Button btnPOS;
    @FXML
    private Button btnProducts;
    @FXML
    private Button btnCustomers;
    @FXML
    private Button btnReports;
    @FXML
    private Label lblCurrentUser;
    @FXML
    private Label lblCurrentTime;

    // Navigation
    @FXML
    private VBox navigationPanel;
    @FXML
    private Button navBtnDashboard;
    @FXML
    private Button navBtnPOS;
    @FXML
    private Button navBtnProducts;
    @FXML
    private Button navBtnCustomers;
    @FXML
    private Button navBtnTransactions;
    @FXML
    private Button navBtnOutstandingBalances;
    @FXML
    private Button navBtnReports;
    @FXML
    private Button navBtnSettings;

    // Content area
    @FXML
    private StackPane contentArea;

    // Status bar
    @FXML
    private HBox statusBar;
    @FXML
    private Label lblStatus;
    @FXML
    private Label lblDatabaseStatus;
    @FXML
    private Label lblVersion;

    private Timer clockTimer;
    private Button currentSelectedNavButton;

    // private PointOfSaleController pointOfSaleController; // Removed - controller not available
    private TransactionHistoryController transactionHistoryController;
    private boolean isRefundMode = false;
    private com.clothingstore.model.Transaction originalTransaction;
    private javafx.scene.control.Button processPaymentButton;
    private javafx.scene.control.Button installmentPaymentButton;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupClock();
        updateDatabaseStatus();
        setStatus("Application started successfully");

        // Set default selected navigation button and load Dashboard
        selectNavButton(navBtnDashboard);

        // Load Customer Management page directly to bypass FXML issues
        try {
            loadContent("CustomerManagementSimple.fxml", "Customer Management");
            selectNavButton(navBtnCustomers);
            setStatus("Application started - Customer Management loaded");
            System.out.println("DEBUG: Customer Management loaded successfully as startup page");
        } catch (Exception e) {
            System.err.println("ERROR: Failed to load Customer Management during initialization: " + e.getMessage());
            e.printStackTrace();
            setStatus("Customer Management loading failed during startup");
        }

        // Initialize and start real-time data service
        initializeRealTimeDataService();
    }

    private void setupClock() {
        clockTimer = new Timer(true);
        clockTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                Platform.runLater(() -> {
                    LocalDateTime now = LocalDateTime.now();
                    lblCurrentTime.setText("Time: " + now.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                });
            }
        }, 0, 1000);
    }

    /**
     * Initialize and start the real-time data service
     */
    private void initializeRealTimeDataService() {
        try {
            com.clothingstore.service.RealTimeDataService realTimeService
                    = com.clothingstore.service.RealTimeDataService.getInstance();
            realTimeService.startRealTimeUpdates();
            setStatus("Real-time data service started - Automatic updates enabled");
        } catch (Exception e) {
            System.err.println("Failed to start real-time data service: " + e.getMessage());
            setStatus("Real-time data service failed to start");
        }
    }

    private void selectNavButton(Button selectedButton) {
        // Remove selection from previous button
        if (currentSelectedNavButton != null) {
            currentSelectedNavButton.getStyleClass().remove("selected");
        }

        // Add selection to new button
        selectedButton.getStyleClass().add("selected");
        currentSelectedNavButton = selectedButton;
    }

    private void updateDatabaseStatus() {
        try {
            DatabaseManager.getInstance().getConnection();
            lblDatabaseStatus.setText("Database: Connected");
            lblDatabaseStatus.setStyle("-fx-text-fill: green;");
        } catch (Exception e) {
            lblDatabaseStatus.setText("Database: Error");
            lblDatabaseStatus.setStyle("-fx-text-fill: red;");
        }
    }

    private void setStatus(String message) {
        lblStatus.setText(message);
    }

    private <T> T loadContent(String fxmlFile, String title) {
        try {
            System.out.println("Loading FXML: " + fxmlFile);
            java.net.URL fxmlUrl = getClass().getResource("/fxml/" + fxmlFile);
            System.out.println("FXML URL: " + fxmlUrl);

            if (fxmlUrl == null) {
                throw new RuntimeException("FXML file not found: /fxml/" + fxmlFile);
            }

            FXMLLoader loader = new FXMLLoader(fxmlUrl);
            System.out.println("FXMLLoader created successfully");

            Parent content = loader.load();
            System.out.println("FXML loaded successfully");

            T controller = loader.getController();
            System.out.println("Controller retrieved: " + (controller != null ? controller.getClass().getSimpleName() : "null"));

            if (controller instanceof TransactionHistoryController) {
                ((TransactionHistoryController) controller).setMainWindowController(this);
            }

            contentArea.getChildren().clear();

            // Add scrolling support for Reports page
            if ("Reports.fxml".equals(fxmlFile)) {
                ScrollPane scrollPane = new ScrollPane(content);
                scrollPane.setFitToWidth(true);
                scrollPane.setFitToHeight(false);
                scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
                scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
                scrollPane.getStyleClass().add("main-scroll-pane");
                contentArea.getChildren().add(scrollPane);
                System.out.println("Reports content wrapped in ScrollPane for smooth scrolling");
            } else {
                contentArea.getChildren().add(content);
            }

            System.out.println("Content added to contentArea");

            setStatus(title + " loaded");
            return controller;
        } catch (IOException e) {
            System.err.println("IOException loading " + fxmlFile + ": " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Loading Error", "Failed to load " + title + ": " + e.getMessage());
            setStatus("Error loading " + title);
            return null;
        } catch (Exception e) {
            System.err.println("Exception loading " + fxmlFile + ": " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Loading Error", "Failed to load " + title + ": " + e.getMessage());
            setStatus("Error loading " + title);
            return null;
        }
    }

    // Menu Actions
    @FXML
    private void handleDatabaseSetup() {
        com.clothingstore.util.DatabaseSetupUtil.showDatabaseSetupDialog();
    }

    @FXML
    private void handleBackup() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Save Database Backup");
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Database Files", "*.db"));

        Stage stage = (Stage) menuBar.getScene().getWindow();
        File file = fileChooser.showSaveDialog(stage);

        if (file != null) {
            try {
                DatabaseManager.getInstance().backupDatabase(file.getAbsolutePath());
                AlertUtil.showSuccess("Backup Complete", "Database backed up successfully to: " + file.getName());
                setStatus("Database backup completed");
            } catch (Exception e) {
                AlertUtil.showError("Backup Failed", "Failed to backup database: " + e.getMessage());
                setStatus("Database backup failed");
            }
        }
    }

    @FXML
    private void handleRestore() {
        if (AlertUtil.showConfirmation("Restore Database",
                "This will replace the current database. Are you sure you want to continue?")) {

            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Select Database Backup");
            fileChooser.getExtensionFilters().add(
                    new FileChooser.ExtensionFilter("Database Files", "*.db"));

            Stage stage = (Stage) menuBar.getScene().getWindow();
            File file = fileChooser.showOpenDialog(stage);

            if (file != null) {
                try {
                    DatabaseManager.getInstance().restoreDatabase(file.getAbsolutePath());
                    AlertUtil.showSuccess("Restore Complete", "Database restored successfully from: " + file.getName());
                    setStatus("Database restore completed");
                    updateDatabaseStatus();
                } catch (Exception e) {
                    AlertUtil.showError("Restore Failed", "Failed to restore database: " + e.getMessage());
                    setStatus("Database restore failed");
                }
            }
        }
    }

    @FXML
    private void handleExit() {
        if (AlertUtil.showConfirmation("Exit Application", "Are you sure you want to exit?")) {
            if (clockTimer != null) {
                clockTimer.cancel();
            }
            Platform.exit();
        }
    }

    // Navigation Actions
    @FXML
    private void showDashboard() {
        showDashboardPage();
    }

    public void showDashboardPage() {
        try {
            // Try to load the main dashboard first
            try {
                loadContent("Dashboard.fxml", "Dashboard");
                selectNavButton(navBtnDashboard);
                setStatus("Dashboard view - Analytics Dashboard");
                System.out.println("DEBUG: Main Dashboard loaded successfully");
            } catch (Exception mainError) {
                System.err.println("Main Dashboard failed, trying fallback: " + mainError.getMessage());

                // Try fallback dashboards in order of preference
                try {
                    loadContent("Dashboard_Working.fxml", "Dashboard");
                    selectNavButton(navBtnDashboard);
                    setStatus("Dashboard view - Working version");
                    System.out.println("DEBUG: Working Dashboard loaded successfully");
                } catch (Exception workingError) {
                    System.err.println("Working Dashboard failed, trying basic: " + workingError.getMessage());

                    try {
                        loadContent("Dashboard_Basic.fxml", "Dashboard");
                        selectNavButton(navBtnDashboard);
                        setStatus("Dashboard view - Basic version");
                        System.out.println("DEBUG: Basic Dashboard loaded successfully");
                    } catch (Exception basicError) {
                        System.err.println("Basic Dashboard failed, trying minimal: " + basicError.getMessage());

                        loadContent("Dashboard_Minimal.fxml", "Dashboard");
                        selectNavButton(navBtnDashboard);
                        setStatus("Dashboard view - Minimal version");
                        System.out.println("DEBUG: Minimal Dashboard loaded successfully");
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("ERROR: All dashboard loading attempts failed: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Dashboard Loading Error", "Failed to load any Dashboard version: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Dashboard - All versions failed");
        }
    }

    @FXML
    private void showPointOfSale() {
        showPointOfSalePage();
    }

    public void showPointOfSalePage() {
        try {
            createSimplePOSInterface();
            selectNavButton(navBtnPOS);
            setStatus("Point of Sale loaded - Ready for transactions");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Point of Sale: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Point of Sale");
        }
    }

    @FXML
    private void showProductManagement() {
        showProductManagementPage();
    }

    public void showProductManagementPage() {
        try {
            loadContent("ProductManagement.fxml", "Product Management");
            selectNavButton(navBtnProducts);
            setStatus("Product Management loaded - Manage inventory and products");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Product Management: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Product Management");
        }
    }

    @FXML
    private void showSupplierManagement() {
        showSupplierManagementPage();
    }

    public void showSupplierManagementPage() {
        try {
            loadContent("SupplierManagement.fxml", "Supplier Management");
            setStatus("Supplier Management loaded - Manage supplier relationships");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Supplier Management: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Supplier Management");
        }
    }

    @FXML
    private void showCustomerManagement() {
        showCustomerManagementPage();
    }

    public void showCustomerManagementPage() {
        try {
            // Try to load the enhanced FXML first, fallback to simple interface if it fails
            try {
                loadContent("CustomerManagementSimple.fxml", "Customer Management");
                selectNavButton(navBtnCustomers);
                setStatus("Customer Management loaded - Enhanced analytics dashboard");
            } catch (Exception fxmlError) {
                System.err.println("FXML loading failed, falling back to simple interface: " + fxmlError.getMessage());
                // Fallback to simple interface
                createSimpleCustomerManagement();
                selectNavButton(navBtnCustomers);
                setStatus("Customer Management loaded - Simple interface");
            }
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Customer Management: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Customer Management");
        }
    }

    @FXML
    private void showCustomerGroups() {
        showCustomerGroupsPage();
    }

    public void showCustomerGroupsPage() {
        try {
            loadContent("CustomerGroups.fxml", "Customer Groups");
            setStatus("Customer Groups loaded - Manage customer categories");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Customer Groups: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Customer Groups");
        }
    }

    @FXML
    private void showDiscountManagement() {
        showDiscountManagementPage();
    }

    public void showDiscountManagementPage() {
        try {
            loadContent("DiscountManagement.fxml", "Discount Management");
            setStatus("Discount Management loaded - Configure discounts and promotions");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Discount Management: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Discount Management");
        }
    }

    @FXML
    private void showTaxManagement() {
        showTaxManagementPage();
    }

    public void showTaxManagementPage() {
        try {
            loadContent("TaxManagement.fxml", "Tax Management");
            setStatus("Tax Management loaded - Configure tax rates and settings");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Tax Management: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Tax Management");
        }
    }

    @FXML
    private void showSalesAnalytics() {
        showSalesAnalyticsPage();
    }

    public void showSalesAnalyticsPage() {
        try {
            loadContent("SalesAnalytics.fxml", "Sales Analytics");
            setStatus("Sales Analytics loaded");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to load Sales Analytics: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void showAdvancedInventory() {
        showAdvancedInventoryPage();
    }

    public void showAdvancedInventoryPage() {
        try {
            loadContent("AdvancedInventory.fxml", "Advanced Inventory Management");
            setStatus("Advanced Inventory Management loaded - Advanced inventory features");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Advanced Inventory: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Advanced Inventory");
        }
    }

    @FXML
    private void showAdvancedReports() {
        showAdvancedReportsPage();
    }

    public void showAdvancedReportsPage() {
        try {
            loadContent("AdvancedReports.fxml", "Advanced Reporting Suite");
            setStatus("Advanced Reporting Suite loaded");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to load Advanced Reports: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void showCashDrawer() {
        showCashDrawerPage();
    }

    public void showCashDrawerPage() {
        try {
            loadContent("CashDrawer.fxml", "Cash Drawer Management");
            setStatus("Cash Drawer Management loaded - Manage cash operations");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Cash Drawer Management: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Cash Drawer Management");
        }
    }

    @FXML
    private void showReturnExchange() {
        showReturnExchangePage();
    }

    public void showReturnExchangePage() {
        try {
            loadContent("ReturnExchange.fxml", "Return & Exchange Management");
            setStatus("Return & Exchange Management loaded - Process returns and exchanges");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Return & Exchange Management: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Return & Exchange Management");
        }
    }

    @FXML
    private void showEmailReceipt() {
        showEmailReceiptPage();
    }

    public void showEmailReceiptPage() {
        try {
            loadContent("EmailReceipt.fxml", "Email Receipt Management");
            setStatus("Email Receipt Management loaded - Configure email receipts");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Email Receipt Management: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Email Receipt Management");
        }
    }

    @FXML
    private void showTransactionHistory() {
        if (transactionHistoryController == null) {
            transactionHistoryController = loadContent("TransactionHistory.fxml", "Transaction History");
        } else {
            loadContent("TransactionHistory.fxml", "Transaction History");
        }
        selectNavButton(navBtnTransactions);
    }

    @FXML
    private void showOutstandingBalances() {
        try {
            loadContent("OutstandingBalances.fxml", "Outstanding Balances");
            selectNavButton(navBtnOutstandingBalances);
            setStatus("Outstanding Balances loaded - Track customer balances");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Outstanding Balances: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Outstanding Balances");
        }
    }

    @FXML
    private void showSalesReport() {
        showSalesReportPage();
    }

    public void showSalesReportPage() {
        showReports();
    }

    @FXML
    private void showReports() {
        try {
            loadContent("Reports.fxml", "Profit Analysis Reports");
            selectNavButton(navBtnReports);
            setStatus("Profit Analysis Reports loaded - Business profitability analysis");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Profit Analysis Reports: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Profit Analysis Reports");
        }
    }

    @FXML
    private void showSettings() {
        showSettingsPage();
    }

    public void showSettingsPage() {
        try {
            loadContent("Settings.fxml", "Settings");
            selectNavButton(navBtnSettings);
            setStatus("Settings page loaded");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to load settings page: " + e.getMessage());
        }
    }

    // Report Actions
    @FXML
    private void showCustomerReport() {
        try {
            loadContent("CustomerReport.fxml", "Customer Report");
            setStatus("Customer Report loaded - Analyze customer data");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Customer Report: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Customer Report");
        }
    }

    @FXML
    private void showDailySalesReport() {
        try {
            loadContent("DailySalesReport.fxml", "Daily Sales Report");
            setStatus("Daily Sales Report loaded - View daily sales performance");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Daily Sales Report: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Daily Sales Report");
        }
    }

    @FXML
    private void showMonthlySalesReport() {
        try {
            loadContent("MonthlySalesReport.fxml", "Monthly Sales Report");
            setStatus("Monthly Sales Report loaded - View monthly sales trends");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Monthly Sales Report: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Monthly Sales Report");
        }
    }

    @FXML
    private void showProfitReport() {
        try {
            loadContent("ProfitReport.fxml", "Profit Report");
            setStatus("Profit Report loaded - Analyze profit margins");
        } catch (Exception e) {
            AlertUtil.showError("Loading Error", "Failed to load Profit Report: " + e.getMessage());
            e.printStackTrace();
            setStatus("Error loading Profit Report");
        }
    }

    // Help Actions
    @FXML
    private void runNavigationTest() {
        com.clothingstore.util.NavigationTestUtil.runNavigationTest(this);
    }

    @FXML
    private void showAbout() {
        AlertUtil.showInfo("About",
                "Clothing Store Management System v1.0.0\n\n"
                + "A comprehensive point-of-sale and inventory management solution\n"
                + "for clothing retail businesses.\n\n"
                + "Built with JavaFX and SQLite\n"
                + "© 2024 Clothing Store Management System");
    }

    @FXML
    private void showHelp() {
        AlertUtil.showInfo("User Guide",
                "Quick Start Guide:\n\n"
                + "1. Use the navigation panel on the left to access different modules\n"
                + "2. Start with Product Management to add your inventory\n"
                + "3. Add customers in Customer Management\n"
                + "4. Use Point of Sale for transactions\n"
                + "5. View reports for business insights\n\n"
                + "For detailed help, refer to the user manual.");
    }

    private void createSimpleCustomerManagement() {
        try {
            // Create customer table
            javafx.scene.control.TableView<com.clothingstore.model.Customer> customerTable = new javafx.scene.control.TableView<>();
            javafx.scene.control.TableColumn<com.clothingstore.model.Customer, String> nameCol = new javafx.scene.control.TableColumn<>("Name");
            javafx.scene.control.TableColumn<com.clothingstore.model.Customer, String> phoneCol = new javafx.scene.control.TableColumn<>("Phone");
            javafx.scene.control.TableColumn<com.clothingstore.model.Customer, String> pointsCol = new javafx.scene.control.TableColumn<>("Points");

            // Set up columns
            nameCol.setCellValueFactory(cellData
                    -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getFullName()));
            phoneCol.setCellValueFactory(cellData
                    -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getPhone()));
            pointsCol.setCellValueFactory(cellData
                    -> new javafx.beans.property.SimpleStringProperty(String.valueOf(cellData.getValue().getLoyaltyPoints())));

            customerTable.getColumns().addAll(nameCol, phoneCol, pointsCol);

            // Load customer data
            com.clothingstore.dao.CustomerDAO customerDAO = com.clothingstore.dao.CustomerDAO.getInstance();
            java.util.List<com.clothingstore.model.Customer> customers = customerDAO.findAll();
            customerTable.getItems().addAll(customers);

            // Create header
            javafx.scene.layout.HBox header = new javafx.scene.layout.HBox(10);
            header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
            header.setPadding(new javafx.geometry.Insets(10));

            javafx.scene.control.Label title = new javafx.scene.control.Label("Customer Management");
            title.setFont(javafx.scene.text.Font.font("System", javafx.scene.text.FontWeight.BOLD, 18));

            javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
            javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

            javafx.scene.control.Button addBtn = new javafx.scene.control.Button("+ Add Customer");
            addBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-weight: bold;");
            addBtn.setOnAction(e -> showAddCustomerDialog());

            javafx.scene.control.Button refreshBtn = new javafx.scene.control.Button("Refresh");
            refreshBtn.setOnAction(e -> {
                try {
                    customerTable.getItems().clear();
                    customerTable.getItems().addAll(customerDAO.findAll());
                    setStatus("Customer data refreshed");
                } catch (Exception ex) {
                    AlertUtil.showError("Database Error", "Failed to refresh customers: " + ex.getMessage());
                }
            });

            header.getChildren().addAll(title, spacer, addBtn, refreshBtn);

            // Create stats
            javafx.scene.layout.HBox stats = new javafx.scene.layout.HBox(20);
            stats.setAlignment(javafx.geometry.Pos.CENTER);
            stats.setPadding(new javafx.geometry.Insets(10));

            javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("Total Customers: " + customers.size());
            totalLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold;");

            int activeCount = (int) customers.stream().filter(com.clothingstore.model.Customer::isActive).count();
            javafx.scene.control.Label activeLabel = new javafx.scene.control.Label("Active: " + activeCount);
            activeLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: green;");

            int totalPoints = customers.stream().mapToInt(com.clothingstore.model.Customer::getLoyaltyPoints).sum();
            javafx.scene.control.Label pointsLabel = new javafx.scene.control.Label("Total Points: " + totalPoints);
            pointsLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: orange;");

            stats.getChildren().addAll(totalLabel, activeLabel, pointsLabel);

            // Create main layout
            javafx.scene.layout.VBox layout = new javafx.scene.layout.VBox(10);
            layout.getChildren().addAll(header, new javafx.scene.control.Separator(), stats, customerTable);

            // Add to content area
            contentArea.getChildren().clear();
            contentArea.getChildren().add(layout);

        } catch (Exception e) {
            AlertUtil.showError("Customer Management Error", "Failed to create customer interface: " + e.getMessage());
        }
    }

    private void showAddCustomerDialog() {
        try {
            javafx.scene.control.Dialog<com.clothingstore.model.Customer> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Add New Customer");
            dialog.setHeaderText("Enter customer details");

            // Create form
            javafx.scene.layout.GridPane grid = new javafx.scene.layout.GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));

            javafx.scene.control.TextField firstNameField = new javafx.scene.control.TextField();
            javafx.scene.control.TextField lastNameField = new javafx.scene.control.TextField();
            javafx.scene.control.TextField phoneField = new javafx.scene.control.TextField();

            grid.add(new javafx.scene.control.Label("First Name:"), 0, 0);
            grid.add(firstNameField, 1, 0);
            grid.add(new javafx.scene.control.Label("Last Name:"), 0, 1);
            grid.add(lastNameField, 1, 1);
            grid.add(new javafx.scene.control.Label("Phone:"), 0, 2);
            grid.add(phoneField, 1, 2);

            dialog.getDialogPane().setContent(grid);

            javafx.scene.control.ButtonType saveButtonType = new javafx.scene.control.ButtonType("Save", javafx.scene.control.ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, javafx.scene.control.ButtonType.CANCEL);

            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == saveButtonType) {
                    com.clothingstore.model.Customer customer = new com.clothingstore.model.Customer();
                    customer.setFirstName(firstNameField.getText());
                    customer.setLastName(lastNameField.getText());
                    customer.setPhone(phoneField.getText());
                    return customer;
                }
                return null;
            });

            java.util.Optional<com.clothingstore.model.Customer> result = dialog.showAndWait();
            result.ifPresent(customer -> {
                try {
                    com.clothingstore.dao.CustomerDAO.getInstance().save(customer);
                    AlertUtil.showSuccess("Success", "Customer added successfully!");
                    showCustomerManagementPage(); // Refresh the page
                } catch (Exception e) {
                    AlertUtil.showError("Database Error", "Failed to save customer: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open add customer dialog: " + e.getMessage());
        }
    }

    private void createProgrammaticPOSInterface() {
        try {
            // Clear content area
            contentArea.getChildren().clear();

            // Create main layout
            javafx.scene.layout.VBox mainLayout = new javafx.scene.layout.VBox(15);
            mainLayout.setPadding(new javafx.geometry.Insets(20));

            // Header
            javafx.scene.layout.HBox header = new javafx.scene.layout.HBox(10);
            header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
            header.setStyle("-fx-background-color: #2c3e50; -fx-padding: 15;");

            javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Point of Sale - Multiple Payment Demo");
            titleLabel.setStyle("-fx-text-fill: white; -fx-font-size: 18px; -fx-font-weight: bold;");

            javafx.scene.layout.Region spacer1 = new javafx.scene.layout.Region();
            javafx.scene.layout.HBox.setHgrow(spacer1, javafx.scene.layout.Priority.ALWAYS);

            javafx.scene.control.Label transactionLabel = new javafx.scene.control.Label("Transaction: TXN000001");
            transactionLabel.setStyle("-fx-text-fill: white;");

            header.getChildren().addAll(titleLabel, spacer1, transactionLabel);

            // Main content area
            javafx.scene.layout.HBox contentArea = new javafx.scene.layout.HBox(20);
            javafx.scene.layout.VBox.setVgrow(contentArea, javafx.scene.layout.Priority.ALWAYS);

            // Left side - Products
            javafx.scene.layout.VBox leftPanel = createProductPanel();
            javafx.scene.layout.HBox.setHgrow(leftPanel, javafx.scene.layout.Priority.ALWAYS);

            // Right side - Cart and Payment
            javafx.scene.layout.VBox rightPanel = createCartAndPaymentPanel();
            javafx.scene.layout.HBox.setHgrow(rightPanel, javafx.scene.layout.Priority.ALWAYS);

            contentArea.getChildren().addAll(leftPanel, rightPanel);

            // Status bar
            javafx.scene.layout.HBox statusBar = new javafx.scene.layout.HBox(10);
            statusBar.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
            statusBar.setStyle("-fx-background-color: #34495e; -fx-padding: 10;");

            javafx.scene.control.Label statusLabel = new javafx.scene.control.Label("Ready for new transaction");
            statusLabel.setStyle("-fx-text-fill: white;");

            javafx.scene.layout.Region spacer2 = new javafx.scene.layout.Region();
            javafx.scene.layout.HBox.setHgrow(spacer2, javafx.scene.layout.Priority.ALWAYS);

            javafx.scene.control.Label itemCountLabel = new javafx.scene.control.Label("Items: 0");
            itemCountLabel.setStyle("-fx-text-fill: white;");

            statusBar.getChildren().addAll(statusLabel, spacer2, itemCountLabel);

            // Add all to main layout
            mainLayout.getChildren().addAll(header, contentArea, statusBar);

            // Add to content area
            this.contentArea.getChildren().add(mainLayout);

        } catch (Exception e) {
            AlertUtil.showError("POS Interface Error", "Failed to create POS interface: " + e.getMessage());
        }
    }

    private javafx.scene.layout.VBox createProductPanel() {
        javafx.scene.layout.VBox panel = new javafx.scene.layout.VBox(15);
        panel.setPadding(new javafx.geometry.Insets(20));

        // Title
        javafx.scene.control.Label title = new javafx.scene.control.Label("Products");
        title.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // Product table
        javafx.scene.control.TableView<com.clothingstore.model.Product> productTable = new javafx.scene.control.TableView<>();
        productTable.setPrefHeight(300);

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> nameCol = new javafx.scene.control.TableColumn<>("Product");
        nameCol.setPrefWidth(200);
        nameCol.setCellValueFactory(new javafx.scene.control.cell.PropertyValueFactory<>("name"));

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> priceCol = new javafx.scene.control.TableColumn<>("Price");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(cellData -> {
            java.math.BigDecimal price = cellData.getValue().getPrice();
            return new javafx.beans.property.SimpleStringProperty("$" + price.toString());
        });

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> stockCol = new javafx.scene.control.TableColumn<>("Stock");
        stockCol.setPrefWidth(80);
        stockCol.setCellValueFactory(new javafx.scene.control.cell.PropertyValueFactory<>("stockQuantity"));

        productTable.getColumns().addAll(nameCol, priceCol, stockCol);

        // Load products
        try {
            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            java.util.List<com.clothingstore.model.Product> products = productDAO.findAll();
            productTable.getItems().addAll(products);
        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load products: " + e.getMessage());
        }

        // Add to cart button
        javafx.scene.control.Button addToCartBtn = new javafx.scene.control.Button("Add Selected to Cart");
        addToCartBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;");
        addToCartBtn.setOnAction(e -> {
            com.clothingstore.model.Product selected = productTable.getSelectionModel().getSelectedItem();
            if (selected != null) {
                AlertUtil.showInfo("Product Added", "Added " + selected.getName() + " to cart!");
            } else {
                AlertUtil.showWarning("No Selection", "Please select a product to add to cart.");
            }
        });

        panel.getChildren().addAll(title, productTable, addToCartBtn);
        return panel;
    }

    private javafx.scene.layout.VBox createCartAndPaymentPanel() {
        javafx.scene.layout.VBox panel = new javafx.scene.layout.VBox(15);
        panel.setPadding(new javafx.geometry.Insets(20));
        panel.setStyle("-fx-background-color: #ecf0f1;");

        // Title
        javafx.scene.control.Label title = new javafx.scene.control.Label("Shopping Cart");
        title.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // Cart table
        javafx.scene.control.TableView<com.clothingstore.model.TransactionItem> cartTable = new javafx.scene.control.TableView<>();
        cartTable.setPrefHeight(200);

        javafx.scene.control.TableColumn<com.clothingstore.model.TransactionItem, String> productCol = new javafx.scene.control.TableColumn<>("Product");
        productCol.setPrefWidth(180);

        javafx.scene.control.TableColumn<com.clothingstore.model.TransactionItem, String> qtyCol = new javafx.scene.control.TableColumn<>("Qty");
        qtyCol.setPrefWidth(60);

        javafx.scene.control.TableColumn<com.clothingstore.model.TransactionItem, String> totalCol = new javafx.scene.control.TableColumn<>("Total");
        totalCol.setPrefWidth(80);

        cartTable.getColumns().addAll(productCol, qtyCol, totalCol);

        // Summary section
        javafx.scene.layout.VBox summaryBox = new javafx.scene.layout.VBox(10);
        summaryBox.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #bdc3c7; -fx-border-width: 1;");

        javafx.scene.layout.HBox subtotalRow = new javafx.scene.layout.HBox();
        javafx.scene.control.Label subtotalLabel = new javafx.scene.control.Label("Subtotal:");
        javafx.scene.layout.Region spacer1 = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer1, javafx.scene.layout.Priority.ALWAYS);
        javafx.scene.control.Label subtotalValue = new javafx.scene.control.Label("$0.00");
        subtotalValue.setStyle("-fx-font-weight: bold;");
        subtotalRow.getChildren().addAll(subtotalLabel, spacer1, subtotalValue);

        javafx.scene.control.Separator separator = new javafx.scene.control.Separator();

        javafx.scene.layout.HBox totalRow = new javafx.scene.layout.HBox();
        javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("Total:");
        totalLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");
        javafx.scene.layout.Region spacer2 = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer2, javafx.scene.layout.Priority.ALWAYS);
        javafx.scene.control.Label totalValue = new javafx.scene.control.Label("$0.00");
        totalValue.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");
        totalRow.getChildren().addAll(totalLabel, spacer2, totalValue);

        summaryBox.getChildren().addAll(subtotalRow, separator, totalRow);

        // Payment buttons
        javafx.scene.layout.VBox buttonBox = new javafx.scene.layout.VBox(10);

        javafx.scene.control.Button singlePaymentBtn = new javafx.scene.control.Button("Single Payment Method");
        singlePaymentBtn.setPrefHeight(50);
        singlePaymentBtn.setMaxWidth(Double.MAX_VALUE);
        singlePaymentBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;");
        singlePaymentBtn.setOnAction(e -> AlertUtil.showInfo("Single Payment", "Single payment method selected!"));

        javafx.scene.control.Button multiplePaymentBtn = new javafx.scene.control.Button("Multiple Payment Methods");
        multiplePaymentBtn.setPrefHeight(50);
        multiplePaymentBtn.setMaxWidth(Double.MAX_VALUE);
        multiplePaymentBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;");
        multiplePaymentBtn.setOnAction(e -> showMultiplePaymentDemo());

        javafx.scene.control.Button newTransactionBtn = new javafx.scene.control.Button("New Transaction");
        newTransactionBtn.setMaxWidth(Double.MAX_VALUE);
        newTransactionBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white;");
        newTransactionBtn.setOnAction(e -> AlertUtil.showInfo("New Transaction", "Starting new transaction..."));

        buttonBox.getChildren().addAll(singlePaymentBtn, multiplePaymentBtn, newTransactionBtn);

        panel.getChildren().addAll(title, cartTable, summaryBox, buttonBox);
        return panel;
    }

    private void showMultiplePaymentDemo() {
        try {
            // Create a sample transaction for demonstration
            com.clothingstore.model.Transaction transaction = new com.clothingstore.model.Transaction();
            transaction.setTransactionNumber("TXN" + System.currentTimeMillis());
            transaction.setSubtotal(new java.math.BigDecimal("150.00"));
            transaction.setTaxAmount(new java.math.BigDecimal("12.00"));
            transaction.setTotalAmount(new java.math.BigDecimal("162.00"));
            transaction.setTransactionDate(java.time.LocalDateTime.now());

            // Load Multiple Payment Dialog
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/MultiplePaymentDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            com.clothingstore.view.MultiplePaymentDialogController controller = loader.getController();
            controller.setTransaction(transaction);

            // Show dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Multiple Payment Methods - Demo");
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(contentArea.getScene().getWindow());
            stage.setResizable(true);
            stage.setMinWidth(800);
            stage.setMinHeight(600);

            stage.showAndWait();

            AlertUtil.showSuccess("Demo Complete", "Multiple payment demonstration completed!");

        } catch (Exception e) {
            AlertUtil.showError("Demo Error", "Failed to show multiple payment demo: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // POS System State Variables
    private java.util.List<com.clothingstore.model.TransactionItem> currentCartItems = new java.util.ArrayList<>();
    private com.clothingstore.model.Customer selectedCustomer = null;
    private String currentTransactionNumber = null;
    private javafx.scene.control.ListView<com.clothingstore.model.TransactionItem> cartListView;
    private javafx.scene.control.Label subtotalValueLabel;
    private javafx.scene.control.Label taxValueLabel;
    private javafx.scene.control.Label totalValueLabel;
    private javafx.scene.control.Label itemsCountLabel;
    private javafx.scene.control.Label transactionNumberLabel;
    private javafx.scene.control.Label customerLabel;
    private javafx.scene.control.TableView<com.clothingstore.model.Product> productTableView;
    private javafx.scene.control.Button multiplePaymentButton;

    private void createSimplePOSInterface() {
        try {
            // Initialize complete POS state
            initializeCompletePOSState();

            // Create responsive main layout with screen size detection
            javafx.scene.layout.VBox layout = createResponsiveMainLayout();

            // Enhanced header with responsive design
            javafx.scene.layout.VBox headerSection = createResponsivePOSHeader();

            // Customer selection section with enhanced visual design
            javafx.scene.layout.HBox customerSection = createModernCustomerSection();

            // Main content area with optimized proportions (60/40 split)
            javafx.scene.layout.HBox mainContent = createResponsiveMainContent();

            // Enhanced status bar with modern styling
            javafx.scene.layout.HBox statusBar = createModernStatusBar();

            // Create main content area (everything except status bar)
            javafx.scene.layout.VBox mainContentArea = new javafx.scene.layout.VBox();
            mainContentArea.setSpacing(getResponsiveSpacing());
            mainContentArea.getChildren().addAll(
                    headerSection,
                    createStyledSeparator(),
                    customerSection,
                    createStyledSeparator(),
                    mainContent
            );

            // Set main content to grow and fill available space
            javafx.scene.layout.VBox.setVgrow(mainContentArea, javafx.scene.layout.Priority.ALWAYS);

            // Add minimal padding to top of main content for compact header
            mainContentArea.setPadding(new javafx.geometry.Insets(5, 0, 0, 0)); // 5px top padding for status bar separation

            // Add status bar at top and main content below
            layout.getChildren().addAll(statusBar, mainContentArea);

            // Add to content area with responsive scrollable container
            contentArea.getChildren().clear();
            javafx.scene.control.ScrollPane scrollContainer = createResponsiveContainer(layout);

            // Check if we have an overlay container with scroll indicator
            javafx.scene.Node containerToAdd = scrollContainer;
            if (scrollContainer.getUserData() instanceof javafx.scene.layout.StackPane) {
                containerToAdd = (javafx.scene.layout.StackPane) scrollContainer.getUserData();
            }

            // Ensure the container fills the entire content area
            javafx.scene.layout.VBox.setVgrow(containerToAdd, javafx.scene.layout.Priority.ALWAYS);
            contentArea.getChildren().add(containerToAdd);

            // Load initial data and setup responsive event handlers
            initializeResponsivePOSSystem();

        } catch (Exception e) {
            AlertUtil.showError("POS Interface Error", "Failed to create POS interface: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // ## Responsive Layout Foundation
    private javafx.scene.layout.VBox createResponsiveMainLayout() {
        javafx.scene.layout.VBox layout = new javafx.scene.layout.VBox();
        layout.setSpacing(getResponsiveSpacing());
        layout.setPadding(getResponsivePadding());
        layout.setStyle(getMainLayoutStyle());

        // Set responsive constraints
        layout.setMinWidth(1024);
        layout.setMaxWidth(Double.MAX_VALUE);
        javafx.scene.layout.VBox.setVgrow(layout, javafx.scene.layout.Priority.ALWAYS);

        return layout;
    }

    private javafx.scene.control.ScrollPane createResponsiveContainer(javafx.scene.layout.VBox content) {
        // Use ScrollPane to allow scrolling when content exceeds viewport
        javafx.scene.control.ScrollPane scrollPane = new javafx.scene.control.ScrollPane();
        scrollPane.setContent(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(false); // Allow vertical scrolling
        scrollPane.setHbarPolicy(javafx.scene.control.ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setVbarPolicy(javafx.scene.control.ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setStyle("-fx-background-color: transparent; -fx-background: transparent;");

        // Set responsive sizing with proper minimum dimensions
        scrollPane.setMinViewportWidth(1024);
        scrollPane.setMinViewportHeight(700); // Increased minimum height
        scrollPane.setPrefViewportHeight(800); // Preferred height

        // Smooth scrolling
        scrollPane.setPannable(true);
        scrollPane.setVvalue(0.0); // Start at top

        // Add scroll indicator when content extends beyond viewport
        addScrollIndicator(scrollPane);

        return scrollPane;
    }

    // ## Scroll Indicator for Better UX
    private void addScrollIndicator(javafx.scene.control.ScrollPane scrollPane) {
        // Create scroll indicator overlay
        javafx.scene.layout.StackPane scrollIndicator = new javafx.scene.layout.StackPane();
        scrollIndicator.setStyle("-fx-background-color: rgba(0, 123, 255, 0.8); -fx-background-radius: 20; "
                + "-fx-padding: 8 12; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 4, 0, 0, 2);");
        scrollIndicator.setMaxWidth(200);
        scrollIndicator.setMaxHeight(40);

        javafx.scene.control.Label scrollText = new javafx.scene.control.Label("⬇ Scroll down for more content ⬇");
        scrollText.setStyle("-fx-text-fill: white; -fx-font-size: 12px; -fx-font-weight: bold; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");
        scrollIndicator.getChildren().add(scrollText);

        // Position indicator at bottom of viewport
        javafx.scene.layout.StackPane.setAlignment(scrollIndicator, javafx.geometry.Pos.BOTTOM_CENTER);
        javafx.scene.layout.StackPane.setMargin(scrollIndicator, new javafx.geometry.Insets(0, 0, 70, 0)); // Above footer

        // Add indicator to a parent container that overlays the scroll pane
        javafx.scene.layout.StackPane overlayContainer = new javafx.scene.layout.StackPane();
        overlayContainer.getChildren().addAll(scrollPane, scrollIndicator);

        // Show/hide indicator based on scroll position and content height
        scrollPane.vvalueProperty().addListener((obs, oldVal, newVal) -> {
            double scrollPosition = newVal.doubleValue();
            boolean hasMoreContent = scrollPosition < 0.9; // Hide when near bottom
            scrollIndicator.setVisible(hasMoreContent);
            scrollIndicator.setManaged(hasMoreContent);
        });

        // Initially check if content needs scrolling
        scrollPane.heightProperty().addListener((obs, oldVal, newVal) -> {
            javafx.application.Platform.runLater(() -> {
                boolean needsScrolling = scrollPane.getContent().getBoundsInLocal().getHeight()
                        > scrollPane.getViewportBounds().getHeight();
                scrollIndicator.setVisible(needsScrolling && scrollPane.getVvalue() < 0.9);
                scrollIndicator.setManaged(needsScrolling && scrollPane.getVvalue() < 0.9);
            });
        });

        // Replace the original scroll pane with the overlay container
        // This will be handled in the calling method
        scrollPane.setUserData(overlayContainer);
    }

    private javafx.scene.layout.HBox createResponsiveMainContent() {
        javafx.scene.layout.HBox mainContent = new javafx.scene.layout.HBox();
        mainContent.setSpacing(getResponsiveSpacing() * 1.5); // Increased spacing for better separation
        mainContent.setAlignment(javafx.geometry.Pos.TOP_CENTER);

        // Set responsive height based on screen size with better proportions
        double screenHeight = getScreenHeight();
        mainContent.setPrefHeight(Math.max(550, screenHeight * 0.65)); // Increased height utilization
        mainContent.setMinHeight(500); // Increased minimum height

        // Optimized product search section with responsive width
        javafx.scene.layout.VBox productSection = createModernProductSection();
        javafx.scene.layout.HBox.setHgrow(productSection, javafx.scene.layout.Priority.ALWAYS);

        // Dynamic width allocation based on screen size for optimal cart visibility
        double screenWidth = getScreenWidth();
        double productWidth, cartWidth;

        if (screenWidth <= 1366) {
            // Smaller screens: give more space to cart for better usability
            productWidth = 0.52; // 52%
            cartWidth = 0.48;    // 48%
        } else if (screenWidth <= 1920) {
            // Standard screens: balanced approach
            productWidth = 0.54; // 54%
            cartWidth = 0.46;    // 46%
        } else {
            // Large screens: can afford more product space
            productWidth = 0.56; // 56%
            cartWidth = 0.44;    // 44%
        }

        productSection.setPrefWidth(screenWidth * productWidth);
        productSection.setMinWidth(Math.min(520, screenWidth * 0.4)); // Responsive minimum
        productSection.setMaxWidth(Double.MAX_VALUE);

        // Enhanced shopping cart section with responsive width allocation
        javafx.scene.layout.VBox cartSection = createImprovedCartSection();
        cartSection.setPrefWidth(screenWidth * cartWidth);
        cartSection.setMinWidth(Math.min(420, screenWidth * 0.35)); // Responsive minimum
        cartSection.setMaxWidth(Math.min(800, screenWidth * 0.55)); // Responsive maximum

        mainContent.getChildren().addAll(productSection, cartSection);
        return mainContent;
    }

    // ## Responsive Design Helper Methods
    private double getScreenWidth() {
        try {
            return javafx.stage.Screen.getPrimary().getVisualBounds().getWidth();
        } catch (Exception e) {
            return 1920; // Default fallback
        }
    }

    private double getScreenHeight() {
        try {
            return javafx.stage.Screen.getPrimary().getVisualBounds().getHeight();
        } catch (Exception e) {
            return 1080; // Default fallback
        }
    }

    private double getResponsiveSpacing() {
        double screenWidth = getScreenWidth();
        if (screenWidth <= 1366) {
            return 12;
        } else if (screenWidth <= 1920) {
            return 15;
        } else {
            return 20;
        }
    }

    private javafx.geometry.Insets getResponsivePadding() {
        double screenWidth = getScreenWidth();
        if (screenWidth <= 1366) {
            return new javafx.geometry.Insets(15);
        } else if (screenWidth <= 1920) {
            return new javafx.geometry.Insets(20);
        } else {
            return new javafx.geometry.Insets(25);
        }
    }

    // Enhanced padding for improved POS interface
    private javafx.geometry.Insets getImprovedPadding() {
        double screenWidth = getScreenWidth();
        if (screenWidth <= 1366) {
            return new javafx.geometry.Insets(18, 18, 18, 18); // Increased padding for better spacing
        } else if (screenWidth <= 1920) {
            return new javafx.geometry.Insets(24, 24, 24, 24); // Enhanced padding for 1920x1080
        } else {
            return new javafx.geometry.Insets(30, 30, 30, 30); // Premium padding for larger screens
        }
    }

    // Enhanced separator for better visual hierarchy
    private javafx.scene.control.Separator createImprovedSeparator() {
        javafx.scene.control.Separator separator = new javafx.scene.control.Separator();
        separator.setStyle("-fx-background-color: linear-gradient(to right, transparent 0%, #ced4da 15%, #ced4da 85%, transparent 100%); "
                + "-fx-pref-height: 3; -fx-max-height: 3; -fx-opacity: 0.8;"); // Enhanced separator styling
        return separator;
    }

    // ## Responsive Cart Helper Methods for Optimal Sizing
    // Get responsive spacing for cart elements based on screen size
    private double getResponsiveCartSpacing() {
        double screenWidth = getScreenWidth();
        double screenHeight = getScreenHeight();

        if (screenWidth <= 1366) {
            return getResponsiveSpacing() * 1.1; // Compact spacing for smaller screens
        } else if (screenWidth <= 1920) {
            return getResponsiveSpacing() * 1.3; // Standard spacing
        } else {
            return getResponsiveSpacing() * 1.5; // Generous spacing for large screens
        }
    }

    // Get responsive padding for cart section based on screen size
    private javafx.geometry.Insets getResponsiveCartPadding() {
        double screenWidth = getScreenWidth();

        if (screenWidth <= 1366) {
            return new javafx.geometry.Insets(16, 16, 16, 16); // Compact padding
        } else if (screenWidth <= 1920) {
            return new javafx.geometry.Insets(20, 20, 20, 20); // Standard padding
        } else {
            return new javafx.geometry.Insets(24, 24, 24, 24); // Generous padding
        }
    }

    // Calculate optimal cart list height based on screen dimensions - optimized for compact items
    private double getOptimalCartListHeight() {
        double screenHeight = getScreenHeight();
        double screenWidth = getScreenWidth();

        // With compact items (approximately 30px each), we can show more items in the same space
        // Base height calculation: 35-40% of screen height for better cart visibility
        double baseHeight = screenHeight * 0.36;

        // Adjust based on screen size for optimal visibility with compact items
        if (screenWidth <= 1366) {
            // Smaller screens: maximize cart space since items are now compact
            return Math.max(250, Math.min(baseHeight, screenHeight * 0.42));
        } else if (screenWidth <= 1920) {
            // Standard screens: generous space for compact items
            return Math.max(300, Math.min(baseHeight, screenHeight * 0.38));
        } else {
            // Large screens: ample space for many compact items
            return Math.max(350, Math.min(baseHeight, screenHeight * 0.36));
        }
    }

    // ## Compact Cart Sizing Test Method for Multiple Screen Resolutions
    public void testCompactCartSizingAcrossResolutions() {
        System.out.println("=== Compact Cart Sizing Test Results ===");

        // Test common screen resolutions
        double[][] testResolutions = {
            {1366, 768}, // Common laptop resolution
            {1920, 1080}, // Full HD
            {2560, 1440}, // 2K
            {3840, 2160} // 4K
        };

        for (double[] resolution : testResolutions) {
            double width = resolution[0];
            double height = resolution[1];

            System.out.printf("\n--- Testing Resolution: %.0fx%.0f ---\n", width, height);

            // Simulate screen dimensions for testing
            double productWidth, cartWidth;

            if (width <= 1366) {
                productWidth = 0.52; // 52%
                cartWidth = 0.48;    // 48%
            } else if (width <= 1920) {
                productWidth = 0.54; // 54%
                cartWidth = 0.46;    // 46%
            } else {
                productWidth = 0.56; // 56%
                cartWidth = 0.44;    // 44%
            }

            double productPixelWidth = width * productWidth;
            double cartPixelWidth = width * cartWidth;
            // Updated calculation for compact cart items (32px height each)
            double cartListHeight = height * 0.36; // Increased base for compact items

            if (width <= 1366) {
                cartListHeight = Math.max(250, Math.min(cartListHeight, height * 0.42));
            } else if (width <= 1920) {
                cartListHeight = Math.max(300, Math.min(cartListHeight, height * 0.38));
            } else {
                cartListHeight = Math.max(350, Math.min(cartListHeight, height * 0.36));
            }

            // Calculate how many compact items can fit (32px per item)
            int maxVisibleItems = (int) (cartListHeight / 32);

            System.out.printf("Product Section: %.0f pixels (%.1f%%)\n", productPixelWidth, productWidth * 100);
            System.out.printf("Cart Section: %.0f pixels (%.1f%%)\n", cartPixelWidth, cartWidth * 100);
            System.out.printf("Cart List Height: %.0f pixels (%.1f%% of screen height)\n",
                    cartListHeight, (cartListHeight / height) * 100);
            System.out.printf("Max Visible Cart Items: %d (with 32px compact cells)\n", maxVisibleItems);

            // Check if dimensions are reasonable for compact design
            boolean isOptimal = cartPixelWidth >= 420 && cartPixelWidth <= 800
                    && cartListHeight >= 250 && maxVisibleItems >= 8;
            System.out.printf("Compact Layout Assessment: %s\n", isOptimal ? "OPTIMAL" : "NEEDS ADJUSTMENT");
        }

        System.out.println("\n=== Compact Cart Test Complete ===");
    }

    private String getMainLayoutStyle() {
        return "-fx-background-color: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%); "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);";
    }

    private javafx.scene.control.Separator createStyledSeparator() {
        javafx.scene.control.Separator separator = new javafx.scene.control.Separator();
        separator.setStyle("-fx-background-color: linear-gradient(to right, transparent 0%, #dee2e6 20%, #dee2e6 80%, transparent 100%); "
                + "-fx-pref-height: 2; -fx-max-height: 2;");
        return separator;
    }

    // ## Enhanced Visual Styling Constants for Improved POS Interface
    private static final String IMPROVED_CARD_STYLE
            = "-fx-background-color: white; "
            + "-fx-background-radius: 15; " // Increased radius for modern look
            + "-fx-border-radius: 15; "
            + "-fx-border-color: #dee2e6; "
            + "-fx-border-width: 1; "
            + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 12, 0, 0, 3);"; // Enhanced shadow

    private static final String MODERN_CARD_STYLE
            = "-fx-background-color: white; "
            + "-fx-background-radius: 12; "
            + "-fx-border-radius: 12; "
            + "-fx-border-color: #e1e5e9; "
            + "-fx-border-width: 1; "
            + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);";

    private static final String MODERN_BUTTON_PRIMARY
            = "-fx-background-color: linear-gradient(to bottom, #007bff 0%, #0056b3 100%); "
            + "-fx-text-fill: white; "
            + "-fx-font-weight: bold; "
            + "-fx-background-radius: 10; " // Increased radius for touch-friendly design
            + "-fx-min-height: 44; " // Touch-friendly minimum height
            + "-fx-padding: 12 20; " // Enhanced padding for better touch targets
            + "-fx-border-radius: 10; "
            + "-fx-cursor: hand; "
            + "-fx-effect: dropshadow(gaussian, rgba(0,123,255,0.3), 4, 0, 0, 2);";

    private static final String MODERN_BUTTON_SUCCESS
            = "-fx-background-color: linear-gradient(to bottom, #28a745 0%, #1e7e34 100%); "
            + "-fx-text-fill: white; "
            + "-fx-font-weight: bold; "
            + "-fx-background-radius: 8; "
            + "-fx-border-radius: 8; "
            + "-fx-cursor: hand; "
            + "-fx-effect: dropshadow(gaussian, rgba(40,167,69,0.3), 4, 0, 0, 2);";

    private static final String MODERN_BUTTON_DANGER
            = "-fx-background-color: linear-gradient(to bottom, #dc3545 0%, #bd2130 100%); "
            + "-fx-text-fill: white; "
            + "-fx-font-weight: bold; "
            + "-fx-background-radius: 8; "
            + "-fx-border-radius: 8; "
            + "-fx-cursor: hand; "
            + "-fx-effect: dropshadow(gaussian, rgba(220,53,69,0.3), 4, 0, 0, 2);";

    private static final String MODERN_BUTTON_SECONDARY
            = "-fx-background-color: linear-gradient(to bottom, #6c757d 0%, #545b62 100%); "
            + "-fx-text-fill: white; "
            + "-fx-font-weight: bold; "
            + "-fx-background-radius: 8; "
            + "-fx-border-radius: 8; "
            + "-fx-cursor: hand; "
            + "-fx-effect: dropshadow(gaussian, rgba(108,117,125,0.3), 4, 0, 0, 2);";

    private static final String MODERN_INPUT_STYLE
            = "-fx-background-color: white; "
            + "-fx-background-radius: 6; "
            + "-fx-border-radius: 6; "
            + "-fx-border-color: #ced4da; "
            + "-fx-border-width: 1; "
            + "-fx-padding: 8 12; "
            + "-fx-font-size: 14px;";

    private static final String MODERN_HEADER_STYLE
            = "-fx-background-color: linear-gradient(to bottom, #2c3e50 0%, #34495e 100%); "
            + "-fx-background-radius: 12 12 0 0; "
            + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 6, 0, 0, 2);";

    // ## Enhanced Modern Components
    private javafx.scene.layout.VBox createResponsivePOSHeader() {
        javafx.scene.layout.VBox headerSection = new javafx.scene.layout.VBox();
        headerSection.setSpacing(getResponsiveSpacing());
        headerSection.setPadding(getResponsivePadding());
        headerSection.setStyle(MODERN_HEADER_STYLE);

        // Title row with responsive typography
        javafx.scene.layout.HBox titleRow = createHeaderTitleRow();

        // Transaction info row with modern styling
        javafx.scene.layout.HBox transactionRow = createHeaderTransactionRow();

        headerSection.getChildren().addAll(titleRow, transactionRow);
        return headerSection;
    }

    private javafx.scene.layout.HBox createHeaderTitleRow() {
        javafx.scene.layout.HBox titleRow = new javafx.scene.layout.HBox();
        titleRow.setSpacing(getResponsiveSpacing());
        titleRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Modern POS title with responsive font size
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Point of Sale System");
        double fontSize = getScreenWidth() <= 1366 ? 18 : 22;
        titleLabel.setStyle(String.format("-fx-font-size: %.0fpx; -fx-font-weight: bold; -fx-text-fill: white; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 2, 0, 1, 1);", fontSize));

        javafx.scene.layout.Region titleSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(titleSpacer, javafx.scene.layout.Priority.ALWAYS);

        // Real-time clock with modern styling
        javafx.scene.control.Label timeLabel = new javafx.scene.control.Label();
        timeLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #ecf0f1; -fx-font-family: 'Segoe UI', sans-serif;");
        updateTimeDisplay(timeLabel);

        titleRow.getChildren().addAll(titleLabel, titleSpacer, timeLabel);
        return titleRow;
    }

    private javafx.scene.layout.HBox createHeaderTransactionRow() {
        javafx.scene.layout.HBox transactionRow = new javafx.scene.layout.HBox();
        transactionRow.setSpacing(getResponsiveSpacing() * 1.5);
        transactionRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Transaction number with modern badge styling
        transactionNumberLabel = new javafx.scene.control.Label("Transaction: " + generateTransactionNumber());
        transactionNumberLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #3498db; "
                + "-fx-background-color: rgba(52, 152, 219, 0.1); -fx-background-radius: 15; "
                + "-fx-padding: 4 12; -fx-border-color: #3498db; -fx-border-radius: 15; -fx-border-width: 1;");

        // Cashier info with subtle styling
        javafx.scene.control.Label cashierLabel = new javafx.scene.control.Label("Cashier: System User");
        cashierLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #bdc3c7; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.layout.Region transactionSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(transactionSpacer, javafx.scene.layout.Priority.ALWAYS);

        // Modern action buttons with responsive sizing
        javafx.scene.layout.HBox buttonGroup = createHeaderButtonGroup();

        transactionRow.getChildren().addAll(transactionNumberLabel, cashierLabel, transactionSpacer, buttonGroup);
        return transactionRow;
    }

    private javafx.scene.layout.HBox createHeaderButtonGroup() {
        javafx.scene.layout.HBox buttonGroup = new javafx.scene.layout.HBox();
        buttonGroup.setSpacing(getResponsiveSpacing());
        buttonGroup.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        // Responsive button sizing
        double buttonHeight = getScreenWidth() <= 1366 ? 32 : 36;
        double buttonPadding = getScreenWidth() <= 1366 ? 8 : 12;

        javafx.scene.control.Button newTransactionBtn = new javafx.scene.control.Button("New Transaction");
        newTransactionBtn.setStyle(MODERN_BUTTON_SUCCESS + String.format("-fx-pref-height: %.0f; -fx-padding: %.0f 16;", buttonHeight, buttonPadding));
        newTransactionBtn.setOnAction(e -> startNewTransaction());
        addButtonHoverEffect(newTransactionBtn, MODERN_BUTTON_SUCCESS);

        javafx.scene.control.Button holdBtn = new javafx.scene.control.Button("Hold");
        holdBtn.setStyle(MODERN_BUTTON_SECONDARY + String.format("-fx-pref-height: %.0f; -fx-padding: %.0f 16;", buttonHeight, buttonPadding));
        holdBtn.setOnAction(e -> holdTransaction());
        addButtonHoverEffect(holdBtn, MODERN_BUTTON_SECONDARY);

        buttonGroup.getChildren().addAll(newTransactionBtn, holdBtn);
        return buttonGroup;
    }

    // ## Modern Customer Section
    private javafx.scene.layout.HBox createModernCustomerSection() {
        javafx.scene.layout.HBox customerSection = new javafx.scene.layout.HBox();
        customerSection.setSpacing(getResponsiveSpacing());
        customerSection.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        customerSection.setPadding(getResponsivePadding());
        customerSection.setStyle(MODERN_CARD_STYLE);

        // Customer info section
        javafx.scene.layout.VBox customerInfo = createCustomerInfoSection();

        javafx.scene.layout.Region customerSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(customerSpacer, javafx.scene.layout.Priority.ALWAYS);

        // Modern customer action buttons
        javafx.scene.layout.HBox customerActions = createCustomerActionButtons();

        customerSection.getChildren().addAll(customerInfo, customerSpacer, customerActions);
        return customerSection;
    }

    private javafx.scene.layout.VBox createCustomerInfoSection() {
        javafx.scene.layout.VBox customerInfo = new javafx.scene.layout.VBox();
        customerInfo.setSpacing(4);

        javafx.scene.control.Label customerTitleLabel = new javafx.scene.control.Label("Customer");
        customerTitleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #6c757d; "
                + "-fx-font-family: 'Segoe UI', sans-serif; -fx-text-transform: uppercase;");

        customerLabel = new javafx.scene.control.Label("Walk-in Customer");
        customerLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #495057; -fx-font-weight: 500; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");

        customerInfo.getChildren().addAll(customerTitleLabel, customerLabel);
        return customerInfo;
    }

    private javafx.scene.layout.HBox createCustomerActionButtons() {
        javafx.scene.layout.HBox customerActions = new javafx.scene.layout.HBox();
        customerActions.setSpacing(getResponsiveSpacing());
        customerActions.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        // Responsive button sizing
        double buttonHeight = getScreenWidth() <= 1366 ? 36 : 40;
        String buttonPadding = getScreenWidth() <= 1366 ? "8 16" : "10 20";

        javafx.scene.control.Button selectCustomerBtn = new javafx.scene.control.Button("Select Customer");
        selectCustomerBtn.setStyle(MODERN_BUTTON_PRIMARY + String.format("-fx-pref-height: %.0f; -fx-padding: %s;", buttonHeight, buttonPadding));
        selectCustomerBtn.setOnAction(e -> showAdvancedCustomerSelectionDialog());
        addButtonHoverEffect(selectCustomerBtn, MODERN_BUTTON_PRIMARY);

        javafx.scene.control.Button newCustomerBtn = new javafx.scene.control.Button("New Customer");
        newCustomerBtn.setStyle(MODERN_BUTTON_SUCCESS + String.format("-fx-pref-height: %.0f; -fx-padding: %s;", buttonHeight, buttonPadding));
        newCustomerBtn.setOnAction(e -> showQuickCustomerCreationDialog());
        addButtonHoverEffect(newCustomerBtn, MODERN_BUTTON_SUCCESS);

        javafx.scene.control.Button clearCustomerBtn = new javafx.scene.control.Button("Clear");
        clearCustomerBtn.setStyle(MODERN_BUTTON_DANGER + String.format("-fx-pref-height: %.0f; -fx-padding: %s;", buttonHeight, buttonPadding));
        clearCustomerBtn.setOnAction(e -> clearSelectedCustomer());
        addButtonHoverEffect(clearCustomerBtn, MODERN_BUTTON_DANGER);

        customerActions.getChildren().addAll(selectCustomerBtn, newCustomerBtn, clearCustomerBtn);
        return customerActions;
    }

    // ## Modern Product Section (55% width with better layout)
    private javafx.scene.layout.VBox createModernProductSection() {
        javafx.scene.layout.VBox productSection = new javafx.scene.layout.VBox();
        productSection.setSpacing(getResponsiveSpacing() * 1.2); // Increased spacing
        productSection.setPadding(getImprovedPadding()); // Enhanced padding
        productSection.setStyle(IMPROVED_CARD_STYLE); // Enhanced card styling

        // Improved product section header with better visual hierarchy
        javafx.scene.layout.HBox productHeader = createModernProductHeader();

        // Enhanced search controls with better touch-friendly design
        javafx.scene.layout.VBox searchSection = createModernProductSearch();

        // Optimized product table with better column sizing
        productTableView = createModernProductTable();

        // Touch-friendly quick action buttons with better placement
        javafx.scene.layout.HBox quickActions = createModernProductActions();

        productSection.getChildren().addAll(
                productHeader,
                createImprovedSeparator(),
                searchSection,
                productTableView,
                quickActions
        );

        return productSection;
    }

    private javafx.scene.layout.HBox createModernProductHeader() {
        javafx.scene.layout.HBox productHeader = new javafx.scene.layout.HBox();
        productHeader.setSpacing(getResponsiveSpacing());
        productHeader.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Modern section title
        javafx.scene.control.Label productLabel = new javafx.scene.control.Label("Product Search & Selection");
        double fontSize = getScreenWidth() <= 1366 ? 16 : 18;
        productLabel.setStyle(String.format("-fx-font-size: %.0fpx; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;", fontSize));

        javafx.scene.layout.Region productHeaderSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(productHeaderSpacer, javafx.scene.layout.Priority.ALWAYS);

        // Product count badge
        javafx.scene.control.Label productCountLabel = new javafx.scene.control.Label("0 products");
        productCountLabel.setStyle("-fx-background-color: #e9ecef; -fx-text-fill: #6c757d; -fx-font-size: 12px; "
                + "-fx-font-weight: bold; -fx-background-radius: 12; -fx-padding: 4 8; "
                + "-fx-border-color: #dee2e6; -fx-border-radius: 12; -fx-border-width: 1;");

        productHeader.getChildren().addAll(productLabel, productHeaderSpacer, productCountLabel);
        return productHeader;
    }

    private javafx.scene.layout.VBox createModernProductSearch() {
        javafx.scene.layout.VBox searchSection = new javafx.scene.layout.VBox();
        searchSection.setSpacing(getResponsiveSpacing());

        // Main search row with modern input styling
        javafx.scene.layout.HBox searchRow = createModernSearchRow();

        // Filter row with enhanced controls
        javafx.scene.layout.HBox filterRow = createModernFilterRow();

        searchSection.getChildren().addAll(searchRow, filterRow);
        return searchSection;
    }

    private javafx.scene.layout.HBox createModernSearchRow() {
        javafx.scene.layout.HBox searchRow = new javafx.scene.layout.HBox();
        searchRow.setSpacing(getResponsiveSpacing());
        searchRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Modern search field with enhanced styling
        javafx.scene.control.TextField searchField = new javafx.scene.control.TextField();
        searchField.setPromptText("Search by name, SKU, or barcode...");
        searchField.setStyle(MODERN_INPUT_STYLE + "-fx-pref-width: 300; -fx-font-family: 'Segoe UI', sans-serif;");
        searchField.setOnKeyReleased(e -> performAdvancedProductSearch(searchField.getText()));
        addInputFocusEffect(searchField);

        // Modern scan button
        javafx.scene.control.Button scanBtn = new javafx.scene.control.Button("Scan Barcode");
        double buttonHeight = getScreenWidth() <= 1366 ? 36 : 40;
        scanBtn.setStyle(MODERN_BUTTON_PRIMARY + String.format("-fx-pref-height: %.0f; -fx-padding: 8 16;", buttonHeight));
        scanBtn.setOnAction(e -> showAdvancedBarcodeDialog());
        addButtonHoverEffect(scanBtn, MODERN_BUTTON_PRIMARY);

        // Clear search button
        javafx.scene.control.Button clearSearchBtn = new javafx.scene.control.Button("Clear");
        clearSearchBtn.setStyle(MODERN_BUTTON_SECONDARY + String.format("-fx-pref-height: %.0f; -fx-padding: 8 12;", buttonHeight));
        clearSearchBtn.setOnAction(e -> {
            searchField.clear();
            loadAllProductsForPOS();
        });
        addButtonHoverEffect(clearSearchBtn, MODERN_BUTTON_SECONDARY);

        searchRow.getChildren().addAll(searchField, scanBtn, clearSearchBtn);
        return searchRow;
    }

    private javafx.scene.layout.HBox createModernFilterRow() {
        javafx.scene.layout.HBox filterRow = new javafx.scene.layout.HBox();
        filterRow.setSpacing(getResponsiveSpacing());
        filterRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Category filter with modern styling
        javafx.scene.control.Label categoryLabel = new javafx.scene.control.Label("Category:");
        categoryLabel.setStyle("-fx-font-weight: 500; -fx-text-fill: #495057; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.ComboBox<String> categoryFilter = new javafx.scene.control.ComboBox<>();
        loadCategoriesForPOSFilter(categoryFilter);
        categoryFilter.setValue("All Categories");
        categoryFilter.setStyle(MODERN_INPUT_STYLE + "-fx-pref-width: 150;");
        categoryFilter.setOnAction(e -> applyProductFilters(categoryFilter.getValue(), null));

        // Stock filter with modern styling
        javafx.scene.control.Label stockLabel = new javafx.scene.control.Label("Stock:");
        stockLabel.setStyle("-fx-font-weight: 500; -fx-text-fill: #495057; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.ComboBox<String> stockFilter = new javafx.scene.control.ComboBox<>();
        stockFilter.getItems().addAll("All Stock", "In Stock", "Low Stock", "Out of Stock");
        stockFilter.setValue("All Stock");
        stockFilter.setStyle(MODERN_INPUT_STYLE + "-fx-pref-width: 120;");
        stockFilter.setOnAction(e -> applyProductFilters(null, stockFilter.getValue()));

        // Refresh button
        javafx.scene.control.Button refreshBtn = new javafx.scene.control.Button("Refresh");
        double buttonHeight = getScreenWidth() <= 1366 ? 32 : 36;
        refreshBtn.setStyle(MODERN_BUTTON_PRIMARY + String.format("-fx-pref-height: %.0f; -fx-padding: 6 12;", buttonHeight));
        refreshBtn.setOnAction(e -> loadAllProductsForPOS());
        addButtonHoverEffect(refreshBtn, MODERN_BUTTON_PRIMARY);

        filterRow.getChildren().addAll(categoryLabel, categoryFilter, stockLabel, stockFilter, refreshBtn);
        return filterRow;
    }

    // ## Helper Methods for Modern UI Effects
    private void addButtonHoverEffect(javafx.scene.control.Button button, String baseStyle) {
        button.setOnMouseEntered(e -> {
            button.setStyle(baseStyle + "-fx-scale-x: 1.05; -fx-scale-y: 1.05;");
        });
        button.setOnMouseExited(e -> {
            button.setStyle(baseStyle);
        });
    }

    private void addInputFocusEffect(javafx.scene.control.TextField textField) {
        textField.setOnMouseEntered(e -> {
            if (!textField.isFocused()) {
                textField.setStyle(MODERN_INPUT_STYLE + "-fx-border-color: #80bdff; -fx-border-width: 2;");
            }
        });
        textField.setOnMouseExited(e -> {
            if (!textField.isFocused()) {
                textField.setStyle(MODERN_INPUT_STYLE);
            }
        });
        textField.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                textField.setStyle(MODERN_INPUT_STYLE + "-fx-border-color: #007bff; -fx-border-width: 2; "
                        + "-fx-effect: dropshadow(gaussian, rgba(0,123,255,0.25), 4, 0, 0, 0);");
            } else {
                textField.setStyle(MODERN_INPUT_STYLE);
            }
        });
    }

    // ## Modern Product Table and Actions
    private javafx.scene.control.TableView<com.clothingstore.model.Product> createModernProductTable() {
        javafx.scene.control.TableView<com.clothingstore.model.Product> table = new javafx.scene.control.TableView<>();

        // Responsive table height
        double screenHeight = getScreenHeight();
        table.setPrefHeight(Math.max(300, screenHeight * 0.4));
        table.setMinHeight(250);

        table.setStyle("-fx-background-color: white; -fx-border-color: #e1e5e9; -fx-border-width: 1; "
                + "-fx-border-radius: 8; -fx-background-radius: 8;");

        // Enhanced columns with responsive widths
        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> nameCol = new javafx.scene.control.TableColumn<>("Product Name");
        nameCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getName()));
        nameCol.setPrefWidth(getScreenWidth() <= 1366 ? 160 : 200);
        nameCol.setStyle("-fx-alignment: CENTER-LEFT; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> skuCol = new javafx.scene.control.TableColumn<>("SKU");
        skuCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getSku()));
        skuCol.setPrefWidth(getScreenWidth() <= 1366 ? 80 : 100);
        skuCol.setStyle("-fx-alignment: CENTER; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> priceCol = new javafx.scene.control.TableColumn<>("Price");
        priceCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty("$" + cellData.getValue().getPrice().toString()));
        priceCol.setPrefWidth(getScreenWidth() <= 1366 ? 80 : 100);
        priceCol.setStyle("-fx-alignment: CENTER-RIGHT; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> stockCol = new javafx.scene.control.TableColumn<>("Stock");
        stockCol.setCellValueFactory(cellData -> {
            int stock = cellData.getValue().getStockQuantity();
            return new javafx.beans.property.SimpleStringProperty(String.valueOf(stock));
        });
        stockCol.setPrefWidth(getScreenWidth() <= 1366 ? 60 : 80);
        stockCol.setStyle("-fx-alignment: CENTER; -fx-font-family: 'Segoe UI', sans-serif;");

        // Enhanced action column with modern buttons
        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> actionCol = new javafx.scene.control.TableColumn<>("Actions");
        actionCol.setCellFactory(col -> new ModernProductActionCell());
        actionCol.setPrefWidth(getScreenWidth() <= 1366 ? 120 : 140);
        actionCol.setSortable(false);

        table.getColumns().addAll(nameCol, skuCol, priceCol, stockCol, actionCol);

        // Enhanced row styling with modern design
        table.setRowFactory(tv -> {
            javafx.scene.control.TableRow<com.clothingstore.model.Product> row = new javafx.scene.control.TableRow<>();

            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    addProductToCartWithQuantityDialog(row.getItem());
                }
            });

            // Modern row styling based on stock
            row.itemProperty().addListener((obs, oldItem, newItem) -> {
                if (newItem == null) {
                    row.setStyle("");
                } else if (newItem.getStockQuantity() == 0) {
                    row.setStyle("-fx-background-color: #fff5f5; -fx-border-color: #fed7d7; -fx-border-width: 0 0 1 0;");
                } else if (newItem.getStockQuantity() <= 5) {
                    row.setStyle("-fx-background-color: #fffbf0; -fx-border-color: #feebc8; -fx-border-width: 0 0 1 0;");
                } else {
                    row.setStyle("-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;");
                }
            });

            // Hover effect
            row.setOnMouseEntered(e -> {
                if (!row.isEmpty()) {
                    row.setStyle(row.getStyle() + "-fx-background-color: #f7fafc;");
                }
            });
            row.setOnMouseExited(e -> {
                // Restore original styling based on stock
                if (!row.isEmpty()) {
                    com.clothingstore.model.Product item = row.getItem();
                    if (item.getStockQuantity() == 0) {
                        row.setStyle("-fx-background-color: #fff5f5; -fx-border-color: #fed7d7; -fx-border-width: 0 0 1 0;");
                    } else if (item.getStockQuantity() <= 5) {
                        row.setStyle("-fx-background-color: #fffbf0; -fx-border-color: #feebc8; -fx-border-width: 0 0 1 0;");
                    } else {
                        row.setStyle("-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;");
                    }
                }
            });

            return row;
        });

        table.setPlaceholder(new javafx.scene.control.Label("No products found. Try adjusting your search criteria."));
        return table;
    }

    private javafx.scene.layout.HBox createModernProductActions() {
        javafx.scene.layout.HBox quickActions = new javafx.scene.layout.HBox();
        quickActions.setSpacing(getResponsiveSpacing());
        quickActions.setAlignment(javafx.geometry.Pos.CENTER);

        double buttonHeight = getScreenWidth() <= 1366 ? 32 : 36;

        javafx.scene.control.Button refreshBtn = new javafx.scene.control.Button("Refresh Products");
        refreshBtn.setStyle(MODERN_BUTTON_PRIMARY + String.format("-fx-pref-height: %.0f; -fx-padding: 6 12;", buttonHeight));
        refreshBtn.setOnAction(e -> loadAllProductsForPOS());
        addButtonHoverEffect(refreshBtn, MODERN_BUTTON_PRIMARY);

        javafx.scene.control.Button lowStockBtn = new javafx.scene.control.Button("Low Stock");
        lowStockBtn.setStyle(MODERN_BUTTON_SECONDARY + String.format("-fx-pref-height: %.0f; -fx-padding: 6 12;", buttonHeight));
        lowStockBtn.setOnAction(e -> showLowStockProducts());
        addButtonHoverEffect(lowStockBtn, MODERN_BUTTON_SECONDARY);

        javafx.scene.control.Button outOfStockBtn = new javafx.scene.control.Button("Out of Stock");
        outOfStockBtn.setStyle(MODERN_BUTTON_DANGER + String.format("-fx-pref-height: %.0f; -fx-padding: 6 12;", buttonHeight));
        outOfStockBtn.setOnAction(e -> showOutOfStockProducts());
        addButtonHoverEffect(outOfStockBtn, MODERN_BUTTON_DANGER);

        quickActions.getChildren().addAll(refreshBtn, lowStockBtn, outOfStockBtn);
        return quickActions;
    }

    // ## Improved Cart Section with Responsive Sizing and Optimal Proportions
    private javafx.scene.layout.VBox createImprovedCartSection() {
        javafx.scene.layout.VBox cartSection = new javafx.scene.layout.VBox();

        // Responsive spacing based on screen size
        double screenWidth = getScreenWidth();
        double screenHeight = getScreenHeight();
        double spacing = getResponsiveCartSpacing();

        cartSection.setSpacing(spacing);
        cartSection.setPadding(getResponsiveCartPadding());
        cartSection.setStyle(IMPROVED_CARD_STYLE);

        // Responsive cart header with screen-aware sizing
        javafx.scene.layout.HBox cartHeader = createResponsiveCartHeader();

        // Enhanced shopping cart list with optimal height allocation
        cartListView = createResponsiveCartListView();

        // Responsive cart summary with appropriate sizing
        javafx.scene.layout.VBox cartSummary = createResponsiveCartSummary();

        // Responsive payment section with screen-aware layout
        javafx.scene.layout.VBox paymentSection = createResponsivePaymentSection();

        // Responsive cart action buttons with optimal sizing
        javafx.scene.layout.HBox cartActions = createResponsiveCartActions();

        cartSection.getChildren().addAll(
                cartHeader,
                createImprovedSeparator(),
                cartListView,
                createImprovedSeparator(),
                cartSummary,
                createImprovedSeparator(),
                paymentSection,
                cartActions
        );

        // Wrap the cart section in a ScrollPane to ensure payment buttons are always accessible
        javafx.scene.control.ScrollPane cartScrollPane = new javafx.scene.control.ScrollPane(cartSection);
        cartScrollPane.setFitToWidth(true);
        cartScrollPane.setFitToHeight(false);
        cartScrollPane.setVbarPolicy(javafx.scene.control.ScrollPane.ScrollBarPolicy.AS_NEEDED);
        cartScrollPane.setHbarPolicy(javafx.scene.control.ScrollPane.ScrollBarPolicy.NEVER);
        cartScrollPane.setStyle("-fx-background-color: transparent; -fx-background: transparent;");

        // Create a container for the scrollable cart
        javafx.scene.layout.VBox cartContainer = new javafx.scene.layout.VBox();
        cartContainer.getChildren().add(cartScrollPane);

        return cartContainer;
    }

    // ## Responsive Cart Header with Screen-Aware Sizing
    private javafx.scene.layout.HBox createResponsiveCartHeader() {
        javafx.scene.layout.HBox cartHeader = new javafx.scene.layout.HBox();
        cartHeader.setSpacing(getResponsiveCartSpacing());
        cartHeader.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Responsive cart title with screen-appropriate font size
        javafx.scene.control.Label cartLabel = new javafx.scene.control.Label("Shopping Cart");
        double screenWidth = getScreenWidth();
        double fontSize;

        if (screenWidth <= 1366) {
            fontSize = 15; // Compact for smaller screens
        } else if (screenWidth <= 1920) {
            fontSize = 17; // Standard for medium screens
        } else {
            fontSize = 19; // Larger for big screens
        }

        cartLabel.setStyle(String.format("-fx-font-size: %.0fpx; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;", fontSize));

        javafx.scene.layout.Region cartHeaderSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(cartHeaderSpacer, javafx.scene.layout.Priority.ALWAYS);

        // Items count badge
        itemsCountLabel = new javafx.scene.control.Label("0 items");
        itemsCountLabel.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-size: 12px; "
                + "-fx-font-weight: bold; -fx-background-radius: 12; -fx-padding: 4 8; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,123,255,0.3), 4, 0, 0, 2);");

        cartHeader.getChildren().addAll(cartLabel, cartHeaderSpacer, itemsCountLabel);
        return cartHeader;
    }

    // ## Responsive Cart List View with Optimal Height Allocation
    private javafx.scene.control.ListView<com.clothingstore.model.TransactionItem> createResponsiveCartListView() {
        javafx.scene.control.ListView<com.clothingstore.model.TransactionItem> listView = new javafx.scene.control.ListView<>();

        // Use optimal height calculation for better cart item visibility
        double optimalHeight = getOptimalCartListHeight();
        listView.setPrefHeight(optimalHeight);

        // Set responsive minimum height based on screen size
        double screenWidth = getScreenWidth();
        double minHeight = screenWidth <= 1366 ? 200 : 220;
        listView.setMinHeight(minHeight);

        // Optimized styling for compact cart items
        listView.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #e1e5e9; -fx-border-width: 1; "
                + "-fx-border-radius: 8; -fx-background-radius: 8; -fx-cell-size: 32;"); // Fixed cell height for compact items
        listView.setCellFactory(listView1 -> new CompactCartItemCell());

        // Modern placeholder
        javafx.scene.layout.VBox placeholder = new javafx.scene.layout.VBox(10);
        placeholder.setAlignment(javafx.geometry.Pos.CENTER);
        placeholder.setStyle("-fx-padding: 20;");

        javafx.scene.control.Label placeholderText = new javafx.scene.control.Label("Cart is empty");
        placeholderText.setStyle("-fx-font-size: 16px; -fx-text-fill: #6c757d; -fx-font-weight: 500;");

        javafx.scene.control.Label placeholderSubtext = new javafx.scene.control.Label("Add products to get started");
        placeholderSubtext.setStyle("-fx-font-size: 12px; -fx-text-fill: #adb5bd;");

        placeholder.getChildren().addAll(placeholderText, placeholderSubtext);
        listView.setPlaceholder(placeholder);

        return listView;
    }

    // ## Responsive Cart Summary with Screen-Appropriate Sizing
    private javafx.scene.layout.VBox createResponsiveCartSummary() {
        javafx.scene.layout.VBox summaryBox = new javafx.scene.layout.VBox();
        summaryBox.setSpacing(getResponsiveCartSpacing() * 0.8); // Slightly tighter spacing for summary
        summaryBox.setPadding(getResponsiveCartPadding());
        summaryBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #e1e5e9; -fx-border-width: 1; "
                + "-fx-border-radius: 8; -fx-background-radius: 8;");

        // Subtotal row
        javafx.scene.layout.HBox subtotalRow = createSummaryRow("Subtotal:", "subtotalValue");
        subtotalValueLabel = (javafx.scene.control.Label) subtotalRow.getChildren().get(2);

        // Tax row
        javafx.scene.layout.HBox taxRow = createSummaryRow("Tax (8.5%):", "taxValue");
        taxValueLabel = (javafx.scene.control.Label) taxRow.getChildren().get(2);

        // Discount row
        javafx.scene.layout.HBox discountRow = createSummaryRow("Discount:", "discountValue");
        javafx.scene.control.Label discountValueLabel = (javafx.scene.control.Label) discountRow.getChildren().get(2);
        discountValueLabel.setText("$0.00");
        discountValueLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #28a745;");

        // Separator
        javafx.scene.control.Separator separator = createStyledSeparator();

        // Total row with emphasis
        javafx.scene.layout.HBox totalRow = new javafx.scene.layout.HBox();
        totalRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("TOTAL:");
        totalLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.layout.Region totalSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(totalSpacer, javafx.scene.layout.Priority.ALWAYS);

        totalValueLabel = new javafx.scene.control.Label("$0.00");
        totalValueLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #28a745; "
                + "-fx-font-family: 'Segoe UI', sans-serif; "
                + "-fx-effect: dropshadow(gaussian, rgba(40,167,69,0.3), 4, 0, 0, 2);");

        totalRow.getChildren().addAll(totalLabel, totalSpacer, totalValueLabel);

        summaryBox.getChildren().addAll(subtotalRow, taxRow, discountRow, separator, totalRow);
        return summaryBox;
    }

    private javafx.scene.layout.HBox createSummaryRow(String labelText, String valueId) {
        javafx.scene.layout.HBox row = new javafx.scene.layout.HBox();
        row.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label label = new javafx.scene.control.Label(labelText);
        label.setStyle("-fx-font-size: 14px; -fx-text-fill: #495057; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        javafx.scene.control.Label value = new javafx.scene.control.Label("$0.00");
        value.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");
        value.setId(valueId);

        row.getChildren().addAll(label, spacer, value);
        return row;
    }

    // ## Enhanced Payment Section with Prominent Visible Buttons
    private javafx.scene.layout.VBox createResponsivePaymentSection() {
        javafx.scene.layout.VBox paymentSection = new javafx.scene.layout.VBox();
        paymentSection.setSpacing(12); // Fixed spacing for consistency
        paymentSection.setPadding(new javafx.geometry.Insets(15, 10, 15, 10)); // Extra padding for visibility

        // Add a prominent header to make the section more visible
        javafx.scene.control.Label paymentHeader = new javafx.scene.control.Label("PAYMENT OPTIONS");
        paymentHeader.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-background-color: #f8f9fa; -fx-padding: 8 12; -fx-background-radius: 6; "
                + "-fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 6;");
        paymentHeader.setMaxWidth(Double.MAX_VALUE);
        paymentHeader.setAlignment(javafx.geometry.Pos.CENTER);

        // Payment buttons with enhanced styling for better visibility
        javafx.scene.layout.VBox paymentButtons = new javafx.scene.layout.VBox();
        paymentButtons.setSpacing(10); // Fixed spacing

        double buttonHeight = 50; // Larger fixed height for better visibility

        // Enhanced Single payment button
        processPaymentButton = new javafx.scene.control.Button("Single Payment Method");
        processPaymentButton.setStyle("-fx-background-color: linear-gradient(to bottom, #28a745 0%, #1e7e34 100%); "
                + "-fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 15px; "
                + String.format("-fx-pref-height: %.0f; ", buttonHeight)
                + "-fx-padding: 15 20; -fx-background-radius: 10; -fx-border-radius: 10; "
                + "-fx-effect: dropshadow(gaussian, rgba(40,167,69,0.4), 6, 0, 0, 3); "
                + "-fx-cursor: hand;");
        processPaymentButton.setMaxWidth(Double.MAX_VALUE);
        processPaymentButton.setOnAction(e -> processPayment());

        // Enhanced Multiple payment button
        multiplePaymentButton = new javafx.scene.control.Button("Multiple Payment Methods");
        multiplePaymentButton.setStyle("-fx-background-color: linear-gradient(to bottom, #dc3545 0%, #bd2130 100%); "
                + "-fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 15px; "
                + String.format("-fx-pref-height: %.0f; ", buttonHeight)
                + "-fx-padding: 15 20; -fx-background-radius: 10; -fx-border-radius: 10; "
                + "-fx-effect: dropshadow(gaussian, rgba(220,53,69,0.4), 6, 0, 0, 3); "
                + "-fx-cursor: hand;");
        multiplePaymentButton.setMaxWidth(Double.MAX_VALUE);
        multiplePaymentButton.setOnAction(e -> processMultiplePayments());

        // Enhanced Installment payment button
        installmentPaymentButton = new javafx.scene.control.Button("Installment Payment");
        installmentPaymentButton.setStyle("-fx-background-color: linear-gradient(to bottom, #fd7e14 0%, #e55a00 100%); "
                + "-fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 15px; "
                + String.format("-fx-pref-height: %.0f; ", buttonHeight)
                + "-fx-padding: 15 20; -fx-background-radius: 10; -fx-border-radius: 10; "
                + "-fx-effect: dropshadow(gaussian, rgba(253,126,20,0.4), 6, 0, 0, 3); "
                + "-fx-cursor: hand;");
        installmentPaymentButton.setMaxWidth(Double.MAX_VALUE);
        installmentPaymentButton.setOnAction(e -> processInstallmentPayment());

        // Add hover effects
        addEnhancedButtonHoverEffect(processPaymentButton);
        addEnhancedButtonHoverEffect(multiplePaymentButton);
        addEnhancedButtonHoverEffect(installmentPaymentButton);

        paymentButtons.getChildren().addAll(processPaymentButton, multiplePaymentButton, installmentPaymentButton);
        paymentSection.getChildren().addAll(paymentHeader, paymentButtons);

        // Make the payment section more prominent with background styling
        paymentSection.setStyle("-fx-background-color: #ffffff; -fx-border-color: #28a745; "
                + "-fx-border-width: 2; -fx-border-radius: 12; -fx-background-radius: 12; "
                + "-fx-effect: dropshadow(gaussian, rgba(40,167,69,0.2), 8, 0, 0, 4);");

        return paymentSection;
    }

    // Enhanced button hover effect for payment buttons
    private void addEnhancedButtonHoverEffect(javafx.scene.control.Button button) {
        String originalStyle = button.getStyle();

        button.setOnMouseEntered(e -> {
            if (button.getText().contains("Single")) {
                button.setStyle(originalStyle + "-fx-scale-x: 1.05; -fx-scale-y: 1.05; "
                        + "-fx-effect: dropshadow(gaussian, rgba(40,167,69,0.6), 10, 0, 0, 5);");
            } else {
                button.setStyle(originalStyle + "-fx-scale-x: 1.05; -fx-scale-y: 1.05; "
                        + "-fx-effect: dropshadow(gaussian, rgba(220,53,69,0.6), 10, 0, 0, 5);");
            }
        });

        button.setOnMouseExited(e -> button.setStyle(originalStyle));
    }

    // ## Responsive Cart Actions with Screen-Aware Button Sizing
    private javafx.scene.layout.HBox createResponsiveCartActions() {
        javafx.scene.layout.HBox actionButtons = new javafx.scene.layout.HBox();
        actionButtons.setSpacing(getResponsiveCartSpacing());
        actionButtons.setAlignment(javafx.geometry.Pos.CENTER);

        // Responsive button sizing based on screen dimensions
        double screenWidth = getScreenWidth();
        double buttonHeight;

        if (screenWidth <= 1366) {
            buttonHeight = 34; // Compact for smaller screens
        } else if (screenWidth <= 1920) {
            buttonHeight = 38; // Standard for medium screens
        } else {
            buttonHeight = 42; // Larger for big screens
        }

        javafx.scene.control.Button clearCartBtn = new javafx.scene.control.Button("Clear Cart");
        clearCartBtn.setStyle(MODERN_BUTTON_SECONDARY + String.format("-fx-pref-height: %.0f; -fx-padding: 8 16;", buttonHeight));
        clearCartBtn.setOnAction(e -> clearCart());
        addButtonHoverEffect(clearCartBtn, MODERN_BUTTON_SECONDARY);

        javafx.scene.control.Button holdTransactionBtn = new javafx.scene.control.Button("Hold Transaction");
        holdTransactionBtn.setStyle(MODERN_BUTTON_SECONDARY + String.format("-fx-pref-height: %.0f; -fx-padding: 8 16;", buttonHeight));
        holdTransactionBtn.setOnAction(e -> holdTransaction());
        addButtonHoverEffect(holdTransactionBtn, MODERN_BUTTON_SECONDARY);

        javafx.scene.control.Button voidTransactionBtn = new javafx.scene.control.Button("Void Transaction");
        voidTransactionBtn.setStyle(MODERN_BUTTON_DANGER + String.format("-fx-pref-height: %.0f; -fx-padding: 8 16;", buttonHeight));
        voidTransactionBtn.setOnAction(e -> voidTransaction());
        addButtonHoverEffect(voidTransactionBtn, MODERN_BUTTON_DANGER);

        actionButtons.getChildren().addAll(clearCartBtn, holdTransactionBtn, voidTransactionBtn);
        return actionButtons;
    }

    // ## Modern Header Status Bar (moved to top)
    private javafx.scene.layout.HBox createModernStatusBar() {
        javafx.scene.layout.HBox statusBar = new javafx.scene.layout.HBox();
        statusBar.setSpacing(getResponsiveSpacing() * 0.8); // Reduced spacing for compact footer
        statusBar.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        statusBar.setPadding(new javafx.geometry.Insets(8, 15, 8, 15)); // Reduced padding for compact footer
        statusBar.setStyle("-fx-background-color: linear-gradient(to bottom, #34495e 0%, #2c3e50 100%); "
                + "-fx-border-color: #1a252f; -fx-border-width: 0 0 1 0; "
                + "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 8, 0, 0, 4);"); // Shadow below for header

        // Set smaller fixed height for footer to give more space to main content
        statusBar.setPrefHeight(35);
        statusBar.setMinHeight(35);
        statusBar.setMaxHeight(35);

        // Status message - compact design
        javafx.scene.control.Label statusLabel = new javafx.scene.control.Label("Ready for new transaction");
        statusLabel.setStyle("-fx-text-fill: #ecf0f1; -fx-font-size: 11px; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.layout.Region statusSpacer1 = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(statusSpacer1, javafx.scene.layout.Priority.ALWAYS);

        // Database status with compact badge
        javafx.scene.control.Label dbStatusLabel = new javafx.scene.control.Label("Database: Connected");
        dbStatusLabel.setStyle("-fx-background-color: rgba(40, 167, 69, 0.2); -fx-text-fill: #27ae60; "
                + "-fx-font-size: 9px; -fx-font-weight: bold; -fx-background-radius: 8; "
                + "-fx-padding: 2 6; -fx-border-color: #27ae60; -fx-border-radius: 8; -fx-border-width: 1;");

        javafx.scene.layout.Region statusSpacer2 = new javafx.scene.layout.Region();
        statusSpacer2.setPrefWidth(15);

        // User info with compact styling
        javafx.scene.control.Label userLabel = new javafx.scene.control.Label("Cashier: System User");
        userLabel.setStyle("-fx-text-fill: #bdc3c7; -fx-font-size: 9px; -fx-font-family: 'Segoe UI', sans-serif;");

        statusBar.getChildren().addAll(statusLabel, statusSpacer1,
                dbStatusLabel, statusSpacer2, userLabel);

        return statusBar;
    }

    // ## Modern Custom Cell Classes
    private class ModernProductActionCell extends javafx.scene.control.TableCell<com.clothingstore.model.Product, String> {

        private final javafx.scene.layout.HBox buttonBox;
        private final javafx.scene.control.Button addBtn;
        private final javafx.scene.control.Button quickAddBtn;

        public ModernProductActionCell() {
            buttonBox = new javafx.scene.layout.HBox(5);
            buttonBox.setAlignment(javafx.geometry.Pos.CENTER);

            addBtn = new javafx.scene.control.Button("Add");
            addBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 10px; "
                    + "-fx-padding: 4 8; -fx-background-radius: 4; -fx-font-weight: bold;");

            quickAddBtn = new javafx.scene.control.Button("⚡");
            quickAddBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-size: 10px; "
                    + "-fx-padding: 4 6; -fx-background-radius: 4; -fx-font-weight: bold;");
            quickAddBtn.setTooltip(new javafx.scene.control.Tooltip("Quick add with quantity"));

            buttonBox.getChildren().addAll(addBtn, quickAddBtn);
        }

        @Override
        protected void updateItem(String item, boolean empty) {
            super.updateItem(item, empty);

            if (empty || getTableRow() == null || getTableRow().getItem() == null) {
                setGraphic(null);
            } else {
                com.clothingstore.model.Product product = getTableRow().getItem();

                boolean hasStock = product.getStockQuantity() > 0;
                addBtn.setDisable(!hasStock);
                quickAddBtn.setDisable(!hasStock);

                if (!hasStock) {
                    addBtn.setText("Out");
                    addBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 10px; "
                            + "-fx-padding: 4 8; -fx-background-radius: 4;");
                } else {
                    addBtn.setText("Add");
                    addBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 10px; "
                            + "-fx-padding: 4 8; -fx-background-radius: 4; -fx-font-weight: bold;");
                }

                addBtn.setOnAction(e -> {
                    if (hasStock) {
                        addProductToCart(product);
                    }
                });

                quickAddBtn.setOnAction(e -> {
                    if (hasStock) {
                        addProductToCartWithQuantityDialog(product);
                    }
                });

                setGraphic(buttonBox);
            }
        }
    }

    // ## Compact Cart Item Cell - Streamlined Design Similar to Product Table Rows
    private class CompactCartItemCell extends javafx.scene.control.ListCell<com.clothingstore.model.TransactionItem> {

        @Override
        protected void updateItem(com.clothingstore.model.TransactionItem item, boolean empty) {
            super.updateItem(item, empty);

            if (empty || item == null) {
                setGraphic(null);
                setText(null);
                setStyle("");
            } else {
                // Single row layout similar to table rows - compact and efficient
                javafx.scene.layout.HBox container = new javafx.scene.layout.HBox();
                container.setSpacing(8);
                container.setPadding(new javafx.geometry.Insets(6, 8, 6, 8)); // Minimal padding
                container.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

                // Clean, minimal styling similar to table rows
                container.setStyle("-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;");

                // Product name - truncated if too long, similar to table cell
                javafx.scene.control.Label nameLabel = new javafx.scene.control.Label(item.getProductName());
                nameLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #2d3748; -fx-font-family: 'Segoe UI', sans-serif;");
                nameLabel.setPrefWidth(120);
                nameLabel.setMaxWidth(120);
                nameLabel.setTextOverrun(javafx.scene.control.OverrunStyle.ELLIPSIS);

                // Compact quantity controls - smaller buttons
                javafx.scene.control.Button decreaseBtn = new javafx.scene.control.Button("-");
                decreaseBtn.setStyle("-fx-background-color: #e2e8f0; -fx-text-fill: #4a5568; -fx-font-size: 10px; "
                        + "-fx-padding: 2 6; -fx-background-radius: 3; -fx-border-color: #cbd5e0; -fx-border-width: 1;");
                decreaseBtn.setPrefSize(20, 20);
                decreaseBtn.setOnAction(e -> updateItemQuantity(item, item.getQuantity() - 1));

                javafx.scene.control.Label quantityLabel = new javafx.scene.control.Label(String.valueOf(item.getQuantity()));
                quantityLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #2d3748; -fx-min-width: 25; -fx-alignment: center;");

                javafx.scene.control.Button increaseBtn = new javafx.scene.control.Button("+");
                increaseBtn.setStyle("-fx-background-color: #e2e8f0; -fx-text-fill: #4a5568; -fx-font-size: 10px; "
                        + "-fx-padding: 2 6; -fx-background-radius: 3; -fx-border-color: #cbd5e0; -fx-border-width: 1;");
                increaseBtn.setPrefSize(20, 20);
                increaseBtn.setOnAction(e -> updateItemQuantity(item, item.getQuantity() + 1));

                // Unit price - compact display
                javafx.scene.control.Label priceLabel = new javafx.scene.control.Label("$" + item.getUnitPrice().setScale(2, java.math.RoundingMode.HALF_UP));
                priceLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #718096; -fx-min-width: 50; -fx-alignment: center-right;");

                // Discount display (if applicable)
                javafx.scene.control.Label discountLabel = null;
                if (item.getDiscountAmount() != null && item.getDiscountAmount().compareTo(java.math.BigDecimal.ZERO) > 0) {
                    discountLabel = new javafx.scene.control.Label("-$" + item.getDiscountAmount().setScale(2, java.math.RoundingMode.HALF_UP));
                    discountLabel.setStyle("-fx-font-size: 10px; -fx-text-fill: #e53e3e; -fx-min-width: 45; -fx-alignment: center-right;");
                }

                // Line total - prominent but compact
                javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("$" + item.getLineTotal().setScale(2, java.math.RoundingMode.HALF_UP));
                totalLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #2d3748; "
                        + "-fx-min-width: 60; -fx-alignment: center-right;");

                // Spacer to push action buttons to the right
                javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
                javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

                // Compact discount button
                javafx.scene.control.Button discountBtn = new javafx.scene.control.Button("💰 Discount");
                discountBtn.setStyle("-fx-background-color: #f6e05e; -fx-text-fill: #744210; -fx-font-size: 9px; "
                        + "-fx-padding: 2 6; -fx-background-radius: 3; -fx-border-color: #ecc94b; -fx-border-width: 1;");
                discountBtn.setPrefSize(60, 22);
                discountBtn.setOnAction(e -> editCartItemDiscount(item));

                // Compact remove button
                javafx.scene.control.Button removeBtn = new javafx.scene.control.Button("Remove");
                removeBtn.setStyle("-fx-background-color: #fed7d7; -fx-text-fill: #c53030; -fx-font-size: 10px; "
                        + "-fx-padding: 2 6; -fx-background-radius: 3; -fx-border-color: #feb2b2; -fx-border-width: 1;");
                removeBtn.setPrefSize(50, 22);
                removeBtn.setOnAction(e -> removeItemFromCart(item));

                // Add components to container, including discount label if present
                if (discountLabel != null) {
                    container.getChildren().addAll(nameLabel, decreaseBtn, quantityLabel, increaseBtn, priceLabel, discountLabel, totalLabel, spacer, discountBtn, removeBtn);
                } else {
                    container.getChildren().addAll(nameLabel, decreaseBtn, quantityLabel, increaseBtn, priceLabel, totalLabel, spacer, discountBtn, removeBtn);
                }

                setGraphic(container);
                setText(null);

                // Hover effect similar to table rows
                setOnMouseEntered(e -> container.setStyle("-fx-background-color: #f7fafc; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;"));
                setOnMouseExited(e -> container.setStyle("-fx-background-color: white; -fx-border-color: #e2e8f0; -fx-border-width: 0 0 1 0;"));
            }
        }
    }

    // ## Responsive Initialization Method
    private void initializeResponsivePOSSystem() {
        try {
            // Load products with responsive handling
            loadAllProductsForPOS();

            // Update cart display with modern styling
            updateCartDisplayWithButtonStates();

            // Set initial status with modern formatting
            setStatus("Modern POS system ready - Transaction: " + currentTransactionNumber);

            // Start responsive time update timer
            startResponsiveTimeUpdateTimer();

            // Setup responsive window listeners
            setupResponsiveWindowListeners();

        } catch (Exception e) {
            AlertUtil.showError("Initialization Error", "Failed to initialize responsive POS system: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void startResponsiveTimeUpdateTimer() {
        javafx.animation.Timeline timeline = new javafx.animation.Timeline(
                new javafx.animation.KeyFrame(javafx.util.Duration.seconds(1), e -> {
                    // Update time displays if they exist
                    updateAllTimeDisplays();
                })
        );
        timeline.setCycleCount(javafx.animation.Timeline.INDEFINITE);
        timeline.play();
    }

    private void updateAllTimeDisplays() {
        // This method would update all time labels in the interface
        // Implementation would depend on storing references to time labels
    }

    private void setupResponsiveWindowListeners() {
        // Setup listeners for window resize events to adjust layout
        if (contentArea.getScene() != null && contentArea.getScene().getWindow() != null) {
            contentArea.getScene().getWindow().widthProperty().addListener((obs, oldVal, newVal) -> {
                // Trigger responsive layout updates when window width changes
                adjustResponsiveLayout();
            });

            contentArea.getScene().getWindow().heightProperty().addListener((obs, oldVal, newVal) -> {
                // Trigger responsive layout updates when window height changes
                adjustResponsiveLayout();
            });
        }
    }

    private void adjustResponsiveLayout() {
        // Method to adjust layout based on current window size
        // This would be called when window is resized
        javafx.application.Platform.runLater(() -> {
            // Refresh the POS interface with new responsive calculations
            // Implementation would depend on specific responsive requirements
        });
    }

    // ## 1. Complete POS Header with Transaction Info
    private javafx.scene.layout.VBox createCompletePOSHeader() {
        javafx.scene.layout.VBox headerSection = new javafx.scene.layout.VBox(10);
        headerSection.setStyle("-fx-background-color: #2c3e50; -fx-padding: 15; -fx-border-radius: 5;");

        // Title row
        javafx.scene.layout.HBox titleRow = new javafx.scene.layout.HBox(10);
        titleRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Point of Sale System");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: white;");

        javafx.scene.layout.Region titleSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(titleSpacer, javafx.scene.layout.Priority.ALWAYS);

        // Current time display
        javafx.scene.control.Label timeLabel = new javafx.scene.control.Label();
        timeLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #ecf0f1;");
        updateTimeDisplay(timeLabel);

        titleRow.getChildren().addAll(titleLabel, titleSpacer, timeLabel);

        // Transaction info row
        javafx.scene.layout.HBox transactionRow = new javafx.scene.layout.HBox(20);
        transactionRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        transactionNumberLabel = new javafx.scene.control.Label("Transaction: " + generateTransactionNumber());
        transactionNumberLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #3498db;");

        javafx.scene.control.Label cashierLabel = new javafx.scene.control.Label("Cashier: System User");
        cashierLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #95a5a6;");

        javafx.scene.layout.Region transactionSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(transactionSpacer, javafx.scene.layout.Priority.ALWAYS);

        // Quick action buttons
        javafx.scene.control.Button newTransactionBtn = new javafx.scene.control.Button("New");
        newTransactionBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        newTransactionBtn.setOnAction(e -> startNewTransaction());

        javafx.scene.control.Button holdBtn = new javafx.scene.control.Button("|| Hold");
        holdBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        holdBtn.setOnAction(e -> holdTransaction());

        transactionRow.getChildren().addAll(transactionNumberLabel, cashierLabel, transactionSpacer, newTransactionBtn, holdBtn);

        headerSection.getChildren().addAll(titleRow, transactionRow);
        return headerSection;
    }

    // ## 2. Advanced Customer Selection Interface
    private javafx.scene.layout.HBox createAdvancedCustomerSection() {
        javafx.scene.layout.HBox customerSection = new javafx.scene.layout.HBox(15);
        customerSection.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        customerSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #bdc3c7; -fx-border-width: 1; -fx-border-radius: 5;");

        // Customer label
        javafx.scene.control.Label customerTitleLabel = new javafx.scene.control.Label("Customer:");
        customerTitleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        // Customer display area
        customerLabel = new javafx.scene.control.Label("Walk-in Customer");
        customerLabel.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 14px; -fx-min-width: 200;");

        // Customer action buttons
        javafx.scene.control.Button selectCustomerBtn = new javafx.scene.control.Button("Search Select Customer");
        selectCustomerBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 16;");
        selectCustomerBtn.setOnAction(e -> showAdvancedCustomerSelectionDialog());

        javafx.scene.control.Button clearCustomerBtn = new javafx.scene.control.Button("X Clear");
        clearCustomerBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 16;");
        clearCustomerBtn.setOnAction(e -> clearSelectedCustomer());

        javafx.scene.control.Button newCustomerBtn = new javafx.scene.control.Button("+ New Customer");
        newCustomerBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 16;");
        newCustomerBtn.setOnAction(e -> showQuickCustomerCreationDialog());

        javafx.scene.layout.Region customerSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(customerSpacer, javafx.scene.layout.Priority.ALWAYS);

        // Customer loyalty info
        javafx.scene.control.Label loyaltyLabel = new javafx.scene.control.Label("Points: 0");
        loyaltyLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #f39c12;");

        customerSection.getChildren().addAll(customerTitleLabel, customerLabel, customerSpacer,
                selectCustomerBtn, clearCustomerBtn, newCustomerBtn, loyaltyLabel);

        return customerSection;
    }

    // ## 3. Enhanced Product Management Interface
    private javafx.scene.layout.VBox createEnhancedProductSection() {
        javafx.scene.layout.VBox productSection = new javafx.scene.layout.VBox(10);
        productSection.setPrefWidth(500);
        productSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #bdc3c7; -fx-border-width: 1; -fx-border-radius: 5;");

        // Product section header
        javafx.scene.layout.HBox productHeader = new javafx.scene.layout.HBox(10);
        productHeader.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label productLabel = new javafx.scene.control.Label("Product Search & Selection");
        productLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        javafx.scene.layout.Region productHeaderSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(productHeaderSpacer, javafx.scene.layout.Priority.ALWAYS);

        javafx.scene.control.Label productCountLabel = new javafx.scene.control.Label("0 products");
        productCountLabel.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 12px;");

        productHeader.getChildren().addAll(productLabel, productHeaderSpacer, productCountLabel);

        // Advanced search controls
        javafx.scene.layout.VBox searchSection = createAdvancedProductSearch();

        // Product table with enhanced features
        productTableView = createEnhancedProductTable();

        // Quick action buttons
        javafx.scene.layout.HBox quickActions = createProductQuickActions();

        productSection.getChildren().addAll(productHeader, new javafx.scene.control.Separator(),
                searchSection, productTableView, quickActions);

        return productSection;
    }

    private javafx.scene.layout.VBox createAdvancedProductSearch() {
        javafx.scene.layout.VBox searchSection = new javafx.scene.layout.VBox(10);

        // Main search row
        javafx.scene.layout.HBox searchRow = new javafx.scene.layout.HBox(10);
        searchRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.TextField searchField = new javafx.scene.control.TextField();
        searchField.setPromptText("Search by name, SKU, or barcode...");
        searchField.setPrefWidth(300);
        searchField.setStyle("-fx-font-size: 14px; -fx-padding: 8;");
        searchField.setOnKeyReleased(e -> performAdvancedProductSearch(searchField.getText()));

        javafx.scene.control.Button scanBtn = new javafx.scene.control.Button("Scan");
        scanBtn.setStyle("-fx-background-color: #9b59b6; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 16;");
        scanBtn.setOnAction(e -> showAdvancedBarcodeDialog());

        javafx.scene.control.Button clearSearchBtn = new javafx.scene.control.Button("X");
        clearSearchBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-background-radius: 50%; -fx-padding: 8;");
        clearSearchBtn.setOnAction(e -> {
            searchField.clear();
            loadAllProductsForPOS();
        });

        searchRow.getChildren().addAll(searchField, scanBtn, clearSearchBtn);

        // Filter row
        javafx.scene.layout.HBox filterRow = new javafx.scene.layout.HBox(10);
        filterRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label categoryLabel = new javafx.scene.control.Label("Category:");
        javafx.scene.control.ComboBox<String> categoryFilter = new javafx.scene.control.ComboBox<>();
        loadCategoriesForPOSFilter(categoryFilter);
        categoryFilter.setValue("All Categories");
        categoryFilter.setOnAction(e -> applyProductFilters(categoryFilter.getValue(), null));

        javafx.scene.control.Label stockLabel = new javafx.scene.control.Label("Stock:");
        javafx.scene.control.ComboBox<String> stockFilter = new javafx.scene.control.ComboBox<>();
        stockFilter.getItems().addAll("All Stock", "In Stock", "Low Stock", "Out of Stock");
        stockFilter.setValue("All Stock");
        stockFilter.setOnAction(e -> applyProductFilters(null, stockFilter.getValue()));

        javafx.scene.control.Button refreshBtn = new javafx.scene.control.Button("Refresh");
        refreshBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-background-radius: 4;");
        refreshBtn.setOnAction(e -> loadAllProductsForPOS());

        filterRow.getChildren().addAll(categoryLabel, categoryFilter, stockLabel, stockFilter, refreshBtn);

        searchSection.getChildren().addAll(searchRow, filterRow);
        return searchSection;
    }

    private javafx.scene.control.TableView<com.clothingstore.model.Product> createEnhancedProductTable() {
        javafx.scene.control.TableView<com.clothingstore.model.Product> table = new javafx.scene.control.TableView<>();
        table.setPrefHeight(350);
        table.setStyle("-fx-background-color: white;");

        // Enhanced columns with better formatting
        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> nameCol = new javafx.scene.control.TableColumn<>("Product Name");
        nameCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getName()));
        nameCol.setPrefWidth(180);
        nameCol.setStyle("-fx-alignment: CENTER-LEFT;");

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> skuCol = new javafx.scene.control.TableColumn<>("SKU");
        skuCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getSku()));
        skuCol.setPrefWidth(80);
        skuCol.setStyle("-fx-alignment: CENTER;");

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> priceCol = new javafx.scene.control.TableColumn<>("Price");
        priceCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty("$" + cellData.getValue().getPrice().toString()));
        priceCol.setPrefWidth(80);
        priceCol.setStyle("-fx-alignment: CENTER-RIGHT;");

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> stockCol = new javafx.scene.control.TableColumn<>("Stock");
        stockCol.setCellValueFactory(cellData -> {
            int stock = cellData.getValue().getStockQuantity();
            String stockText = String.valueOf(stock);
            return new javafx.beans.property.SimpleStringProperty(stockText);
        });
        stockCol.setPrefWidth(60);
        stockCol.setStyle("-fx-alignment: CENTER;");

        // Enhanced action column with multiple buttons
        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> actionCol = new javafx.scene.control.TableColumn<>("Actions");
        actionCol.setCellFactory(col -> new EnhancedProductActionCell());
        actionCol.setPrefWidth(120);
        actionCol.setSortable(false);

        table.getColumns().addAll(nameCol, skuCol, priceCol, stockCol, actionCol);

        // Enhanced row styling and double-click handling
        table.setRowFactory(tv -> {
            javafx.scene.control.TableRow<com.clothingstore.model.Product> row = new javafx.scene.control.TableRow<>();
            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    addProductToCartWithQuantityDialog(row.getItem());
                }
            });

            // Dynamic row styling based on stock
            row.itemProperty().addListener((obs, oldItem, newItem) -> {
                if (newItem == null) {
                    row.setStyle("");
                } else if (newItem.getStockQuantity() == 0) {
                    row.setStyle("-fx-background-color: #ffebee;");
                } else if (newItem.getStockQuantity() <= 5) {
                    row.setStyle("-fx-background-color: #fff3e0;");
                } else {
                    row.setStyle("");
                }
            });

            return row;
        });

        table.setPlaceholder(new javafx.scene.control.Label("No products found. Try adjusting your search criteria."));
        return table;
    }

    // ## 4. Comprehensive Shopping Cart Interface
    private javafx.scene.layout.VBox createComprehensiveCartSection() {
        javafx.scene.layout.VBox cartSection = new javafx.scene.layout.VBox(10);
        cartSection.setPrefWidth(450);
        cartSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #bdc3c7; -fx-border-width: 1; -fx-border-radius: 5;");

        // Cart header with item count
        javafx.scene.layout.HBox cartHeader = createCartHeader();

        // Shopping cart list with enhanced display
        cartListView = createEnhancedCartListView();

        // Cart summary with detailed calculations
        javafx.scene.layout.VBox cartSummary = createDetailedCartSummary();

        // Payment processing section
        javafx.scene.layout.VBox paymentSection = createComprehensivePaymentSection();

        // Cart action buttons
        javafx.scene.layout.HBox cartActions = createCartActionButtons();

        cartSection.getChildren().addAll(cartHeader, new javafx.scene.control.Separator(),
                cartListView, new javafx.scene.control.Separator(),
                cartSummary, new javafx.scene.control.Separator(),
                paymentSection, cartActions);

        return cartSection;
    }

    private javafx.scene.layout.HBox createCartHeader() {
        javafx.scene.layout.HBox cartHeader = new javafx.scene.layout.HBox(10);
        cartHeader.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label cartLabel = new javafx.scene.control.Label("Shopping Cart");
        cartLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        javafx.scene.layout.Region cartHeaderSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(cartHeaderSpacer, javafx.scene.layout.Priority.ALWAYS);

        itemsCountLabel = new javafx.scene.control.Label("0 items");
        itemsCountLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #3498db; -fx-font-size: 14px;");

        cartHeader.getChildren().addAll(cartLabel, cartHeaderSpacer, itemsCountLabel);
        return cartHeader;
    }

    private javafx.scene.control.ListView<com.clothingstore.model.TransactionItem> createEnhancedCartListView() {
        javafx.scene.control.ListView<com.clothingstore.model.TransactionItem> listView = new javafx.scene.control.ListView<>();
        listView.setPrefHeight(250);
        listView.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 3;");
        listView.setCellFactory(listView1 -> new EnhancedCartItemCell());
        listView.setPlaceholder(new javafx.scene.control.Label("Cart is empty\nAdd products to get started"));
        return listView;
    }

    private javafx.scene.layout.VBox createDetailedCartSummary() {
        javafx.scene.layout.VBox summaryBox = new javafx.scene.layout.VBox(8);
        summaryBox.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        // Subtotal row
        javafx.scene.layout.HBox subtotalRow = new javafx.scene.layout.HBox();
        javafx.scene.control.Label subtotalLabel = new javafx.scene.control.Label("Subtotal:");
        subtotalLabel.setStyle("-fx-font-size: 14px;");
        javafx.scene.layout.Region subtotalSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(subtotalSpacer, javafx.scene.layout.Priority.ALWAYS);
        subtotalValueLabel = new javafx.scene.control.Label("$0.00");
        subtotalValueLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold;");
        subtotalRow.getChildren().addAll(subtotalLabel, subtotalSpacer, subtotalValueLabel);

        // Tax row with rate display
        javafx.scene.layout.HBox taxRow = new javafx.scene.layout.HBox();
        javafx.scene.control.Label taxLabel = new javafx.scene.control.Label("Tax (8.5%):");
        taxLabel.setStyle("-fx-font-size: 14px;");
        javafx.scene.layout.Region taxSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(taxSpacer, javafx.scene.layout.Priority.ALWAYS);
        taxValueLabel = new javafx.scene.control.Label("$0.00");
        taxValueLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold;");
        taxRow.getChildren().addAll(taxLabel, taxSpacer, taxValueLabel);

        // Discount row (for future use)
        javafx.scene.layout.HBox discountRow = new javafx.scene.layout.HBox();
        javafx.scene.control.Label discountLabel = new javafx.scene.control.Label("Discount:");
        discountLabel.setStyle("-fx-font-size: 14px;");
        javafx.scene.layout.Region discountSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(discountSpacer, javafx.scene.layout.Priority.ALWAYS);
        javafx.scene.control.Label discountValueLabel = new javafx.scene.control.Label("$0.00");
        discountValueLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");
        discountRow.getChildren().addAll(discountLabel, discountSpacer, discountValueLabel);

        // Separator
        javafx.scene.control.Separator separator = new javafx.scene.control.Separator();

        // Total row with emphasis
        javafx.scene.layout.HBox totalRow = new javafx.scene.layout.HBox();
        javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("TOTAL:");
        totalLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");
        javafx.scene.layout.Region totalSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(totalSpacer, javafx.scene.layout.Priority.ALWAYS);
        totalValueLabel = new javafx.scene.control.Label("$0.00");
        totalValueLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");
        totalRow.getChildren().addAll(totalLabel, totalSpacer, totalValueLabel);

        summaryBox.getChildren().addAll(subtotalRow, taxRow, discountRow, separator, totalRow);
        return summaryBox;
    }

    private javafx.scene.layout.VBox createComprehensivePaymentSection() {
        javafx.scene.layout.VBox paymentSection = new javafx.scene.layout.VBox(10);

        // Payment method buttons
        javafx.scene.layout.VBox paymentButtons = new javafx.scene.layout.VBox(8);

        // Single payment button
        processPaymentButton = new javafx.scene.control.Button("Single Payment Method");
        processPaymentButton.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 14px; -fx-padding: 12; -fx-background-radius: 5;");
        processPaymentButton.setMaxWidth(Double.MAX_VALUE);
        processPaymentButton.setOnAction(e -> processPayment());

        // Multiple payment button
        multiplePaymentButton = new javafx.scene.control.Button("Multiple Payment Methods");
        multiplePaymentButton.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 14px; -fx-padding: 12; -fx-background-radius: 5;");
        multiplePaymentButton.setMaxWidth(Double.MAX_VALUE);
        multiplePaymentButton.setOnAction(e -> processMultiplePayments());

        paymentButtons.getChildren().addAll(processPaymentButton, multiplePaymentButton);

        paymentSection.getChildren().add(paymentButtons);
        return paymentSection;
    }

    private javafx.scene.layout.HBox createCartActionButtons() {
        javafx.scene.layout.HBox actionButtons = new javafx.scene.layout.HBox(10);
        actionButtons.setAlignment(javafx.geometry.Pos.CENTER);

        javafx.scene.control.Button clearCartBtn = new javafx.scene.control.Button("Clear Cart");
        clearCartBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 16;");
        clearCartBtn.setOnAction(e -> clearCart());

        javafx.scene.control.Button holdTransactionBtn = new javafx.scene.control.Button("|| Hold Transaction");
        holdTransactionBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 16;");
        holdTransactionBtn.setOnAction(e -> holdTransaction());

        javafx.scene.control.Button voidTransactionBtn = new javafx.scene.control.Button("X Void Transaction");
        voidTransactionBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 16;");
        voidTransactionBtn.setOnAction(e -> voidTransaction());

        actionButtons.getChildren().addAll(clearCartBtn, holdTransactionBtn, voidTransactionBtn);
        return actionButtons;
    }

    // ## 5. Enhanced Status Bar with Real-time Updates
    private javafx.scene.layout.HBox createEnhancedStatusBar() {
        javafx.scene.layout.HBox statusBar = new javafx.scene.layout.HBox(15);
        statusBar.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        statusBar.setStyle("-fx-background-color: #34495e; -fx-padding: 10; -fx-border-radius: 5;");

        // Status message
        javafx.scene.control.Label statusIcon = new javafx.scene.control.Label("ℹ");
        statusIcon.setStyle("-fx-font-size: 16px; -fx-text-fill: #3498db;");

        javafx.scene.control.Label statusLabel = new javafx.scene.control.Label("Ready for new transaction");
        statusLabel.setStyle("-fx-text-fill: #ecf0f1; -fx-font-size: 14px;");

        javafx.scene.layout.Region statusSpacer1 = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(statusSpacer1, javafx.scene.layout.Priority.ALWAYS);

        // Database status
        javafx.scene.control.Label dbIcon = new javafx.scene.control.Label("🗄");
        dbIcon.setStyle("-fx-font-size: 14px;");

        javafx.scene.control.Label dbStatusLabel = new javafx.scene.control.Label("Database: Connected");
        dbStatusLabel.setStyle("-fx-text-fill: #27ae60; -fx-font-size: 12px; -fx-font-weight: bold;");

        javafx.scene.layout.Region statusSpacer2 = new javafx.scene.layout.Region();
        statusSpacer2.setPrefWidth(20);

        // User info
        javafx.scene.control.Label userIcon = new javafx.scene.control.Label("👤");
        userIcon.setStyle("-fx-font-size: 14px;");

        javafx.scene.control.Label userLabel = new javafx.scene.control.Label("Cashier: System User");
        userLabel.setStyle("-fx-text-fill: #bdc3c7; -fx-font-size: 12px;");

        statusBar.getChildren().addAll(statusIcon, statusLabel, statusSpacer1,
                dbIcon, dbStatusLabel, statusSpacer2, userIcon, userLabel);

        return statusBar;
    }

    // ## 6. Complete POS System Initialization
    private void initializeCompletePOSState() {
        // Initialize transaction state
        currentCartItems = new java.util.ArrayList<>();
        selectedCustomer = null;
        currentTransactionNumber = generateTransactionNumber();

        // Initialize UI state
        if (transactionNumberLabel != null) {
            transactionNumberLabel.setText("Transaction: " + currentTransactionNumber);
        }
        if (customerLabel != null) {
            customerLabel.setText("Walk-in Customer");
            customerLabel.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 14px;");
        }
    }

    private void initializeCompletePOSSystem() {
        try {
            // Load products
            loadAllProductsForPOS();

            // Update cart display
            updateCartDisplayWithButtonStates();

            // Set initial status
            setStatus("POS system ready - Transaction: " + currentTransactionNumber);

            // Start time update timer
            startTimeUpdateTimer();

        } catch (Exception e) {
            AlertUtil.showError("Initialization Error", "Failed to initialize POS system: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // ## 7. Enhanced Helper Methods
    private void updateTimeDisplay(javafx.scene.control.Label timeLabel) {
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy - HH:mm:ss");
        timeLabel.setText(java.time.LocalDateTime.now().format(formatter));
    }

    private void startTimeUpdateTimer() {
        // Update time every second
        javafx.animation.Timeline timeline = new javafx.animation.Timeline(
                new javafx.animation.KeyFrame(javafx.util.Duration.seconds(1), e -> {
                    // Find and update time labels if they exist
                    // This is a simplified approach - in production, you'd store references
                })
        );
        timeline.setCycleCount(javafx.animation.Timeline.INDEFINITE);
        timeline.play();
    }

    private void loadAllProductsForPOS() {
        try {
            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            java.util.List<com.clothingstore.model.Product> products = productDAO.findAll();

            if (productTableView != null) {
                productTableView.getItems().clear();
                productTableView.getItems().addAll(products);
            }

            setStatus("Loaded " + products.size() + " products");
        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load products: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void performAdvancedProductSearch(String searchTerm) {
        try {
            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            java.util.List<com.clothingstore.model.Product> products;

            if (searchTerm == null || searchTerm.trim().isEmpty()) {
                products = productDAO.findAll();
            } else {
                products = productDAO.searchProducts(searchTerm.trim());
            }

            if (productTableView != null) {
                productTableView.getItems().clear();
                productTableView.getItems().addAll(products);
            }

            setStatus("Found " + products.size() + " products matching '" + searchTerm + "'");
        } catch (Exception e) {
            AlertUtil.showError("Search Error", "Failed to search products: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void applyProductFilters(String category, String stockFilter) {
        // Implementation for filtering products by category and stock status
        try {
            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            java.util.List<com.clothingstore.model.Product> products = productDAO.findAll();

            // Apply filters
            java.util.stream.Stream<com.clothingstore.model.Product> filteredStream = products.stream();

            if (category != null && !"All Categories".equals(category)) {
                filteredStream = filteredStream.filter(p -> category.equals(p.getCategory()));
            }

            if (stockFilter != null && !"All Stock".equals(stockFilter)) {
                switch (stockFilter) {
                    case "In Stock":
                        filteredStream = filteredStream.filter(p -> p.getStockQuantity() > 5);
                        break;
                    case "Low Stock":
                        filteredStream = filteredStream.filter(p -> p.getStockQuantity() > 0 && p.getStockQuantity() <= 5);
                        break;
                    case "Out of Stock":
                        filteredStream = filteredStream.filter(p -> p.getStockQuantity() == 0);
                        break;
                }
            }

            java.util.List<com.clothingstore.model.Product> filteredProducts = filteredStream.collect(java.util.stream.Collectors.toList());

            if (productTableView != null) {
                productTableView.getItems().clear();
                productTableView.getItems().addAll(filteredProducts);
            }

            setStatus("Applied filters - " + filteredProducts.size() + " products shown");
        } catch (Exception e) {
            AlertUtil.showError("Filter Error", "Failed to apply filters: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // ## 8. Custom Cell Classes for Enhanced UI
    private class EnhancedProductActionCell extends javafx.scene.control.TableCell<com.clothingstore.model.Product, String> {

        private final javafx.scene.layout.HBox buttonBox;
        private final javafx.scene.control.Button addBtn;
        private final javafx.scene.control.Button quickAddBtn;

        public EnhancedProductActionCell() {
            buttonBox = new javafx.scene.layout.HBox(5);
            buttonBox.setAlignment(javafx.geometry.Pos.CENTER);

            addBtn = new javafx.scene.control.Button("+ Add");
            addBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8; -fx-background-radius: 3;");

            quickAddBtn = new javafx.scene.control.Button("⚡");
            quickAddBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 6; -fx-background-radius: 3;");
            quickAddBtn.setTooltip(new javafx.scene.control.Tooltip("Quick add with quantity"));

            buttonBox.getChildren().addAll(addBtn, quickAddBtn);
        }

        @Override
        protected void updateItem(String item, boolean empty) {
            super.updateItem(item, empty);

            if (empty || getTableRow() == null || getTableRow().getItem() == null) {
                setGraphic(null);
            } else {
                com.clothingstore.model.Product product = getTableRow().getItem();

                // Update button states based on stock
                boolean hasStock = product.getStockQuantity() > 0;
                addBtn.setDisable(!hasStock);
                quickAddBtn.setDisable(!hasStock);

                if (!hasStock) {
                    addBtn.setText("Out of Stock");
                    addBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8; -fx-background-radius: 3;");
                } else {
                    addBtn.setText("+ Add");
                    addBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8; -fx-background-radius: 3;");
                }

                // Set button actions
                addBtn.setOnAction(e -> {
                    if (hasStock) {
                        addProductToCart(product);
                    }
                });

                quickAddBtn.setOnAction(e -> {
                    if (hasStock) {
                        addProductToCartWithQuantityDialog(product);
                    }
                });

                setGraphic(buttonBox);
            }
        }
    }

    private class EnhancedCartItemCell extends javafx.scene.control.ListCell<com.clothingstore.model.TransactionItem> {

        @Override
        protected void updateItem(com.clothingstore.model.TransactionItem item, boolean empty) {
            super.updateItem(item, empty);

            if (empty || item == null) {
                setGraphic(null);
                setText(null);
            } else {
                javafx.scene.layout.VBox container = new javafx.scene.layout.VBox(5);
                container.setStyle("-fx-padding: 8; -fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 3;");

                // Product info row
                javafx.scene.layout.HBox productRow = new javafx.scene.layout.HBox(10);
                productRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

                javafx.scene.control.Label nameLabel = new javafx.scene.control.Label(item.getProductName());
                nameLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

                javafx.scene.layout.Region productSpacer = new javafx.scene.layout.Region();
                javafx.scene.layout.HBox.setHgrow(productSpacer, javafx.scene.layout.Priority.ALWAYS);

                // Action buttons
                javafx.scene.control.Button discountBtn = new javafx.scene.control.Button("💰 Discount");
                discountBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 4 6; -fx-background-radius: 3;");
                discountBtn.setOnAction(e -> editCartItemDiscount(item));

                javafx.scene.control.Button removeBtn = new javafx.scene.control.Button("Remove");
                removeBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 4 6; -fx-background-radius: 3;");
                removeBtn.setOnAction(e -> removeItemFromCart(item));

                productRow.getChildren().addAll(nameLabel, productSpacer, discountBtn, removeBtn);

                // Details row
                javafx.scene.layout.HBox detailsRow = new javafx.scene.layout.HBox(10);
                detailsRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

                javafx.scene.control.Label skuLabel = new javafx.scene.control.Label("SKU: " + item.getProductSku());
                skuLabel.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 11px;");

                javafx.scene.control.Label priceLabel = new javafx.scene.control.Label("$" + item.getUnitPrice().toString() + " each");
                priceLabel.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 11px;");

                detailsRow.getChildren().addAll(skuLabel, priceLabel);

                // Quantity and total row
                javafx.scene.layout.HBox quantityRow = new javafx.scene.layout.HBox(10);
                quantityRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

                // Quantity controls
                javafx.scene.control.Button decreaseBtn = new javafx.scene.control.Button("-");
                decreaseBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-font-size: 12px; -fx-padding: 4 8; -fx-background-radius: 3;");
                decreaseBtn.setOnAction(e -> updateItemQuantity(item, item.getQuantity() - 1));

                javafx.scene.control.Label quantityLabel = new javafx.scene.control.Label(String.valueOf(item.getQuantity()));
                quantityLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px; -fx-min-width: 30; -fx-alignment: center;");

                javafx.scene.control.Button increaseBtn = new javafx.scene.control.Button("+");
                increaseBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 12px; -fx-padding: 4 8; -fx-background-radius: 3;");
                increaseBtn.setOnAction(e -> updateItemQuantity(item, item.getQuantity() + 1));

                javafx.scene.layout.Region quantitySpacer = new javafx.scene.layout.Region();
                javafx.scene.layout.HBox.setHgrow(quantitySpacer, javafx.scene.layout.Priority.ALWAYS);

                // Line total
                javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("$" + item.getLineTotal().setScale(2, java.math.RoundingMode.HALF_UP).toString());
                totalLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 16px; -fx-text-fill: #27ae60;");

                quantityRow.getChildren().addAll(decreaseBtn, quantityLabel, increaseBtn, quantitySpacer, totalLabel);

                // Add discount info row if discount is applied
                if (item.getDiscountAmount() != null && item.getDiscountAmount().compareTo(java.math.BigDecimal.ZERO) > 0) {
                    javafx.scene.layout.HBox discountRow = new javafx.scene.layout.HBox(10);
                    discountRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
                    discountRow.setPadding(new javafx.geometry.Insets(0, 0, 0, 20));

                    javafx.scene.control.Label discountLabel = new javafx.scene.control.Label(
                            String.format("💰 Discount Applied: -$%.2f", item.getDiscountAmount().doubleValue()));
                    discountLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #e74c3c; -fx-font-style: italic;");

                    discountRow.getChildren().add(discountLabel);
                    container.getChildren().addAll(productRow, detailsRow, quantityRow, discountRow);
                } else {
                    container.getChildren().addAll(productRow, detailsRow, quantityRow);
                }

                setGraphic(container);
                setText(null);
            }
        }
    }

    // ## 8.5. Individual Product Discount Management
    private void editCartItemDiscount(com.clothingstore.model.TransactionItem item) {
        // Create custom discount dialog
        javafx.stage.Stage discountDialog = new javafx.stage.Stage();
        discountDialog.setTitle("Apply Product Discount");
        discountDialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
        discountDialog.setResizable(false);

        javafx.scene.layout.VBox dialogContent = new javafx.scene.layout.VBox(15);
        dialogContent.setPadding(new javafx.geometry.Insets(20));
        dialogContent.setStyle("-fx-background-color: white;");

        // Header
        javafx.scene.control.Label headerLabel = new javafx.scene.control.Label("Apply Discount to " + item.getProductName());
        headerLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Product info
        javafx.scene.layout.VBox productInfo = new javafx.scene.layout.VBox(5);
        productInfo.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        javafx.scene.control.Label productLabel = new javafx.scene.control.Label("Product: " + item.getProductName());
        javafx.scene.control.Label quantityLabel = new javafx.scene.control.Label("Quantity: " + item.getQuantity());
        javafx.scene.control.Label unitPriceLabel = new javafx.scene.control.Label("Unit Price: $" + item.getUnitPrice().toString());

        java.math.BigDecimal subtotal = item.getUnitPrice().multiply(java.math.BigDecimal.valueOf(item.getQuantity()));
        javafx.scene.control.Label subtotalLabel = new javafx.scene.control.Label("Subtotal: $" + subtotal.toString());

        java.math.BigDecimal currentDiscount = item.getDiscountAmount() != null ? item.getDiscountAmount() : java.math.BigDecimal.ZERO;
        javafx.scene.control.Label currentDiscountLabel = new javafx.scene.control.Label("Current Discount: $" + currentDiscount.toString());
        currentDiscountLabel.setStyle("-fx-text-fill: #e74c3c;");

        productInfo.getChildren().addAll(productLabel, quantityLabel, unitPriceLabel, subtotalLabel, currentDiscountLabel);

        // Discount input section
        javafx.scene.layout.VBox discountSection = new javafx.scene.layout.VBox(10);

        javafx.scene.control.Label discountTypeLabel = new javafx.scene.control.Label("Discount Type:");
        discountTypeLabel.setStyle("-fx-font-weight: bold;");

        javafx.scene.control.ToggleGroup discountTypeGroup = new javafx.scene.control.ToggleGroup();
        javafx.scene.control.RadioButton amountRadio = new javafx.scene.control.RadioButton("Fixed Amount ($)");
        javafx.scene.control.RadioButton percentRadio = new javafx.scene.control.RadioButton("Percentage (%)");
        amountRadio.setToggleGroup(discountTypeGroup);
        percentRadio.setToggleGroup(discountTypeGroup);
        amountRadio.setSelected(true);

        javafx.scene.layout.HBox radioBox = new javafx.scene.layout.HBox(20);
        radioBox.getChildren().addAll(amountRadio, percentRadio);

        javafx.scene.control.Label discountValueLabel = new javafx.scene.control.Label("Discount Value:");
        discountValueLabel.setStyle("-fx-font-weight: bold;");

        javafx.scene.control.TextField discountField = new javafx.scene.control.TextField();
        discountField.setPromptText("Enter discount amount or percentage");
        discountField.setText(currentDiscount.toString());

        // Live calculation display
        javafx.scene.control.Label calculationLabel = new javafx.scene.control.Label("New Total: $" + item.getLineTotal().toString());
        calculationLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");

        discountSection.getChildren().addAll(discountTypeLabel, radioBox, discountValueLabel, discountField, calculationLabel);

        // Update calculation when input changes
        discountField.textProperty().addListener((obs, oldVal, newVal) -> {
            updateDiscountCalculation(item, discountTypeGroup, discountField, calculationLabel, subtotal);
        });

        discountTypeGroup.selectedToggleProperty().addListener((obs, oldToggle, newToggle) -> {
            updateDiscountCalculation(item, discountTypeGroup, discountField, calculationLabel, subtotal);
        });

        // Buttons
        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(10);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        javafx.scene.control.Button applyBtn = new javafx.scene.control.Button("Apply Discount");
        applyBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 16;");

        javafx.scene.control.Button removeBtn = new javafx.scene.control.Button("Remove Discount");
        removeBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 16;");

        javafx.scene.control.Button cancelBtn = new javafx.scene.control.Button("Cancel");
        cancelBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 16;");

        buttonBox.getChildren().addAll(removeBtn, cancelBtn, applyBtn);

        // Button actions
        applyBtn.setOnAction(e -> {
            try {
                java.math.BigDecimal discountAmount = calculateDiscountAmount(discountTypeGroup, discountField, subtotal);
                if (discountAmount.compareTo(java.math.BigDecimal.ZERO) >= 0 && discountAmount.compareTo(subtotal) <= 0) {
                    item.applyDiscount(discountAmount);
                    updateCartDisplayWithButtonStates();
                    discountDialog.close();
                } else {
                    showDiscountError("Discount amount must be between $0 and $" + subtotal.toString());
                }
            } catch (NumberFormatException ex) {
                showDiscountError("Please enter a valid number");
            }
        });

        removeBtn.setOnAction(e -> {
            item.applyDiscount(java.math.BigDecimal.ZERO);
            updateCartDisplayWithButtonStates();
            discountDialog.close();
        });

        cancelBtn.setOnAction(e -> discountDialog.close());

        dialogContent.getChildren().addAll(headerLabel, productInfo, discountSection, buttonBox);

        javafx.scene.Scene scene = new javafx.scene.Scene(dialogContent, 450, 500);
        discountDialog.setScene(scene);
        discountDialog.showAndWait();
    }

    private void updateDiscountCalculation(com.clothingstore.model.TransactionItem item,
            javafx.scene.control.ToggleGroup discountTypeGroup,
            javafx.scene.control.TextField discountField,
            javafx.scene.control.Label calculationLabel,
            java.math.BigDecimal subtotal) {
        try {
            java.math.BigDecimal discountAmount = calculateDiscountAmount(discountTypeGroup, discountField, subtotal);
            java.math.BigDecimal newTotal = subtotal.subtract(discountAmount);
            calculationLabel.setText("New Total: $" + newTotal.setScale(2, java.math.RoundingMode.HALF_UP).toString());
            calculationLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");
        } catch (NumberFormatException e) {
            calculationLabel.setText("Invalid input");
            calculationLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;");
        }
    }

    private java.math.BigDecimal calculateDiscountAmount(javafx.scene.control.ToggleGroup discountTypeGroup,
            javafx.scene.control.TextField discountField,
            java.math.BigDecimal subtotal) {
        String inputText = discountField.getText().trim();
        if (inputText.isEmpty()) {
            return java.math.BigDecimal.ZERO;
        }

        java.math.BigDecimal inputValue = new java.math.BigDecimal(inputText);

        if (((javafx.scene.control.RadioButton) discountTypeGroup.getSelectedToggle()).getText().contains("Percentage")) {
            // Percentage discount
            return subtotal.multiply(inputValue).divide(java.math.BigDecimal.valueOf(100), 2, java.math.RoundingMode.HALF_UP);
        } else {
            // Fixed amount discount
            return inputValue;
        }
    }

    private void showDiscountError(String message) {
        javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
        alert.setTitle("Discount Error");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // ## 9. Enhanced Product and Customer Dialog Methods
    private void addProductToCartWithQuantityDialog(com.clothingstore.model.Product product) {
        if (product.getStockQuantity() <= 0) {
            AlertUtil.showWarning("Out of Stock", "Product " + product.getName() + " is out of stock!");
            return;
        }

        javafx.scene.control.TextInputDialog dialog = new javafx.scene.control.TextInputDialog("1");
        dialog.setTitle("Add Product to Cart");
        dialog.setHeaderText("Add " + product.getName() + " to cart");
        dialog.setContentText("Quantity (Max: " + product.getStockQuantity() + "):");

        java.util.Optional<String> result = dialog.showAndWait();
        result.ifPresent(quantityStr -> {
            try {
                int quantity = Integer.parseInt(quantityStr);
                if (quantity <= 0) {
                    AlertUtil.showWarning("Invalid Quantity", "Please enter a positive quantity.");
                    return;
                }
                if (quantity > product.getStockQuantity()) {
                    AlertUtil.showWarning("Insufficient Stock", "Only " + product.getStockQuantity() + " items available.");
                    return;
                }

                // Add to cart with specified quantity
                addProductToCartWithQuantity(product, quantity);
            } catch (NumberFormatException e) {
                AlertUtil.showWarning("Invalid Input", "Please enter a valid number.");
            }
        });
    }

    private void addProductToCartWithQuantity(com.clothingstore.model.Product product, int quantity) {
        // Check if product already in cart
        for (com.clothingstore.model.TransactionItem item : currentCartItems) {
            if (item.getProductId().equals(product.getId())) {
                int newQuantity = item.getQuantity() + quantity;
                if (newQuantity <= product.getStockQuantity()) {
                    item.setQuantity(newQuantity);
                    updateCartDisplayWithButtonStates();
                    setStatus("Updated " + product.getName() + " quantity to " + newQuantity);
                    return;
                } else {
                    AlertUtil.showWarning("Stock Limit", "Cannot add " + quantity + " more items. Stock limit reached!");
                    return;
                }
            }
        }

        // Add new item to cart
        com.clothingstore.model.TransactionItem newItem = new com.clothingstore.model.TransactionItem(product, quantity);
        currentCartItems.add(newItem);
        updateCartDisplayWithButtonStates();
        setStatus("Added " + quantity + " x " + product.getName() + " to cart");
    }

    // ## 10. Advanced Customer Selection and Management
    private void showAdvancedCustomerSelectionDialog() {
        try {
            com.clothingstore.dao.CustomerDAO customerDAO = com.clothingstore.dao.CustomerDAO.getInstance();
            java.util.List<com.clothingstore.model.Customer> allCustomers = customerDAO.findAll();

            if (allCustomers.isEmpty()) {
                boolean createDemo = AlertUtil.showConfirmation("No Customers Found",
                        "No customers found in the database.\nWould you like to create some demo customers?");
                if (createDemo) {
                    createDemoCustomersWithoutMembership();
                    allCustomers = customerDAO.findAll();
                }
            }

            // Create enhanced customer selection dialog
            javafx.stage.Stage dialog = new javafx.stage.Stage();
            dialog.setTitle("Select Customer - Advanced Search");
            dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            dialog.initOwner(contentArea.getScene().getWindow());

            javafx.scene.layout.VBox root = createAdvancedCustomerSelectionDialog(allCustomers, dialog);
            javafx.scene.Scene scene = new javafx.scene.Scene(root, 700, 600);

            dialog.setScene(scene);
            dialog.showAndWait();

        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load customers: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private javafx.scene.layout.VBox createAdvancedCustomerSelectionDialog(
            java.util.List<com.clothingstore.model.Customer> allCustomers,
            javafx.stage.Stage dialog) {

        javafx.scene.layout.VBox root = new javafx.scene.layout.VBox(15);
        root.setPadding(new javafx.geometry.Insets(20));
        root.setStyle("-fx-background-color: #f8f9fa;");

        // Header
        javafx.scene.control.Label headerLabel = new javafx.scene.control.Label("Advanced Customer Search");
        headerLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Search and filter section
        javafx.scene.layout.VBox searchSection = createAdvancedCustomerSearchSection(allCustomers);

        // Customer table
        javafx.scene.control.TableView<com.clothingstore.model.Customer> customerTable = createAdvancedCustomerTable();
        javafx.collections.ObservableList<com.clothingstore.model.Customer> filteredCustomers = javafx.collections.FXCollections.observableArrayList();
        customerTable.setItems(filteredCustomers);
        filteredCustomers.setAll(allCustomers);

        // Action buttons
        javafx.scene.layout.HBox buttonBox = createAdvancedCustomerDialogButtons(dialog, customerTable);

        root.getChildren().addAll(headerLabel, new javafx.scene.control.Separator(),
                searchSection, customerTable, buttonBox);

        // Setup filtering logic
        setupAdvancedCustomerFiltering(searchSection, filteredCustomers, allCustomers);

        return root;
    }

    private void showQuickCustomerCreationDialog() {
        javafx.stage.Stage dialog = new javafx.stage.Stage();
        dialog.setTitle("Quick Customer Creation");
        dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
        dialog.initOwner(contentArea.getScene().getWindow());

        javafx.scene.layout.VBox root = new javafx.scene.layout.VBox(15);
        root.setPadding(new javafx.geometry.Insets(20));
        root.setStyle("-fx-background-color: #f8f9fa;");

        // Header
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("➕ Create New Customer");
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        // Form fields
        javafx.scene.layout.GridPane form = new javafx.scene.layout.GridPane();
        form.setHgap(10);
        form.setVgap(10);

        javafx.scene.control.TextField firstNameField = new javafx.scene.control.TextField();
        javafx.scene.control.TextField lastNameField = new javafx.scene.control.TextField();
        javafx.scene.control.TextField phoneField = new javafx.scene.control.TextField();

        form.add(new javafx.scene.control.Label("First Name:"), 0, 0);
        form.add(firstNameField, 1, 0);
        form.add(new javafx.scene.control.Label("Last Name:"), 0, 1);
        form.add(lastNameField, 1, 1);
        form.add(new javafx.scene.control.Label("Phone:"), 0, 2);
        form.add(phoneField, 1, 2);

        // Buttons
        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(10);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        javafx.scene.control.Button createBtn = new javafx.scene.control.Button("Create Customer");
        createBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold;");

        javafx.scene.control.Button cancelBtn = new javafx.scene.control.Button("Cancel");
        cancelBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white;");

        createBtn.setOnAction(e -> {
            if (firstNameField.getText().trim().isEmpty() || lastNameField.getText().trim().isEmpty()) {
                AlertUtil.showWarning("Missing Information", "Please enter at least first and last name.");
                return;
            }

            try {
                com.clothingstore.model.Customer newCustomer = new com.clothingstore.model.Customer();
                newCustomer.setFirstName(firstNameField.getText().trim());
                newCustomer.setLastName(lastNameField.getText().trim());
                newCustomer.setPhone(phoneField.getText().trim());
                newCustomer.setActive(true);
                newCustomer.setLoyaltyPoints(0);
                newCustomer.setRegistrationDate(java.time.LocalDateTime.now());

                com.clothingstore.dao.CustomerDAO customerDAO = com.clothingstore.dao.CustomerDAO.getInstance();
                com.clothingstore.model.Customer savedCustomer = customerDAO.save(newCustomer);

                if (savedCustomer != null) {
                    selectedCustomer = savedCustomer;
                    updateCustomerDisplay();
                    AlertUtil.showSuccess("Customer Created", "Customer created and selected successfully!");
                    dialog.close();
                }
            } catch (Exception ex) {
                AlertUtil.showError("Creation Error", "Failed to create customer: " + ex.getMessage());
            }
        });

        cancelBtn.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(cancelBtn, createBtn);

        root.getChildren().addAll(titleLabel, form, buttonBox);

        dialog.setScene(new javafx.scene.Scene(root, 400, 300));
        dialog.showAndWait();
    }

    private void showAdvancedBarcodeDialog() {
        javafx.scene.control.TextInputDialog dialog = new javafx.scene.control.TextInputDialog();
        dialog.setTitle("Advanced Barcode Scanner");
        dialog.setHeaderText("Scan or Enter Product Code");
        dialog.setContentText("Barcode/SKU/Product Code:");

        // Add some styling
        dialog.getDialogPane().setStyle("-fx-background-color: #f8f9fa;");

        java.util.Optional<String> result = dialog.showAndWait();
        result.ifPresent(code -> {
            try {
                com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();

                // Try multiple search methods
                com.clothingstore.model.Product product = null;

                // First try by SKU
                java.util.Optional<com.clothingstore.model.Product> productOpt = productDAO.findBySku(code);
                if (productOpt.isPresent()) {
                    product = productOpt.get();
                } else {
                    // Try by barcode if different from SKU
                    java.util.Optional<com.clothingstore.model.Product> barcodeOpt = productDAO.findByBarcode(code);
                    if (barcodeOpt.isPresent()) {
                        product = barcodeOpt.get();
                    } else {
                        // Try general search
                        java.util.List<com.clothingstore.model.Product> searchResults = productDAO.searchProducts(code);
                        if (!searchResults.isEmpty()) {
                            product = searchResults.get(0); // Take first match
                        }
                    }
                }

                if (product != null) {
                    addProductToCartWithQuantityDialog(product);
                    setStatus("Found product: " + product.getName());
                } else {
                    AlertUtil.showWarning("Product Not Found", "No product found with code: " + code);
                }
            } catch (Exception e) {
                AlertUtil.showError("Search Error", "Failed to search for product: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private void updateCustomerDisplay() {
        if (selectedCustomer != null) {
            customerLabel.setText(selectedCustomer.getFullName() + " (Points: " + selectedCustomer.getLoyaltyPoints() + ")");
            customerLabel.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold; -fx-font-size: 14px;");
            setStatus("Customer selected: " + selectedCustomer.getFullName());
        } else {
            customerLabel.setText("Walk-in Customer");
            customerLabel.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 14px;");
            setStatus("Customer cleared");
        }
    }

    // ## 11. Missing Helper Methods Implementation
    private javafx.scene.layout.VBox createAdvancedCustomerSearchSection(java.util.List<com.clothingstore.model.Customer> allCustomers) {
        javafx.scene.layout.VBox searchSection = new javafx.scene.layout.VBox(10);
        searchSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        // Search field
        javafx.scene.layout.HBox searchRow = new javafx.scene.layout.HBox(10);
        javafx.scene.control.TextField searchField = new javafx.scene.control.TextField();
        searchField.setPromptText("Search by name, phone, email...");
        searchField.setPrefWidth(300);
        searchField.setId("advancedCustomerSearch");

        javafx.scene.control.Button clearBtn = new javafx.scene.control.Button("Clear");
        clearBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-background-radius: 3;");
        clearBtn.setOnAction(e -> searchField.clear());

        searchRow.getChildren().addAll(searchField, clearBtn);

        // Filter row
        javafx.scene.layout.HBox filterRow = new javafx.scene.layout.HBox(15);
        javafx.scene.control.ComboBox<String> statusFilter = new javafx.scene.control.ComboBox<>();
        statusFilter.getItems().addAll("All Status", "Active", "Inactive");
        statusFilter.setValue("All Status");
        statusFilter.setId("advancedStatusFilter");

        javafx.scene.control.TextField minPointsField = new javafx.scene.control.TextField();
        minPointsField.setPromptText("Min Points");
        minPointsField.setPrefWidth(100);
        minPointsField.setId("advancedPointsFilter");

        javafx.scene.control.Label resultLabel = new javafx.scene.control.Label("Showing: " + allCustomers.size() + " customers");
        resultLabel.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");
        resultLabel.setId("advancedResultLabel");

        filterRow.getChildren().addAll(new javafx.scene.control.Label("Status:"), statusFilter,
                new javafx.scene.control.Label("Min Points:"), minPointsField, resultLabel);

        searchSection.getChildren().addAll(searchRow, filterRow);
        return searchSection;
    }

    private javafx.scene.control.TableView<com.clothingstore.model.Customer> createAdvancedCustomerTable() {
        javafx.scene.control.TableView<com.clothingstore.model.Customer> table = new javafx.scene.control.TableView<>();
        table.setPrefHeight(300);

        // Enhanced columns
        javafx.scene.control.TableColumn<com.clothingstore.model.Customer, String> nameCol = new javafx.scene.control.TableColumn<>("Name");
        nameCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getFullName()));
        nameCol.setPrefWidth(150);

        javafx.scene.control.TableColumn<com.clothingstore.model.Customer, String> phoneCol = new javafx.scene.control.TableColumn<>("Phone");
        phoneCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getPhone()));
        phoneCol.setPrefWidth(120);

        javafx.scene.control.TableColumn<com.clothingstore.model.Customer, Integer> pointsCol = new javafx.scene.control.TableColumn<>("Points");
        pointsCol.setCellValueFactory(new javafx.scene.control.cell.PropertyValueFactory<>("loyaltyPoints"));
        pointsCol.setPrefWidth(80);

        javafx.scene.control.TableColumn<com.clothingstore.model.Customer, String> statusCol = new javafx.scene.control.TableColumn<>("Status");
        statusCol.setCellValueFactory(cellData -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().isActive() ? "Active" : "Inactive"));
        statusCol.setPrefWidth(80);

        table.getColumns().addAll(nameCol, phoneCol, pointsCol, statusCol);
        return table;
    }

    private javafx.scene.layout.HBox createAdvancedCustomerDialogButtons(javafx.stage.Stage dialog, javafx.scene.control.TableView<com.clothingstore.model.Customer> customerTable) {
        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(10);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        javafx.scene.control.Button selectBtn = new javafx.scene.control.Button("Select Customer");
        selectBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 16;");
        selectBtn.setDisable(true);

        javafx.scene.control.Button cancelBtn = new javafx.scene.control.Button("Cancel");
        cancelBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-padding: 8 16;");

        // Enable select button when customer is selected
        customerTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            selectBtn.setDisable(newSelection == null);
        });

        selectBtn.setOnAction(e -> {
            com.clothingstore.model.Customer selected = customerTable.getSelectionModel().getSelectedItem();
            if (selected != null) {
                selectedCustomer = selected;
                updateCustomerDisplay();
                dialog.close();
            }
        });

        cancelBtn.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(cancelBtn, selectBtn);
        return buttonBox;
    }

    private void setupAdvancedCustomerFiltering(javafx.scene.layout.VBox searchSection,
            javafx.collections.ObservableList<com.clothingstore.model.Customer> filteredCustomers,
            java.util.List<com.clothingstore.model.Customer> allCustomers) {

        javafx.scene.control.TextField searchField = (javafx.scene.control.TextField) searchSection.lookup("#advancedCustomerSearch");
        javafx.scene.control.ComboBox<String> statusFilter = (javafx.scene.control.ComboBox<String>) searchSection.lookup("#advancedStatusFilter");
        javafx.scene.control.TextField pointsField = (javafx.scene.control.TextField) searchSection.lookup("#advancedPointsFilter");
        javafx.scene.control.Label resultLabel = (javafx.scene.control.Label) searchSection.lookup("#advancedResultLabel");

        Runnable applyFilters = () -> {
            String searchTerm = searchField != null ? searchField.getText().toLowerCase().trim() : "";
            String selectedStatus = statusFilter != null ? statusFilter.getValue() : "All Status";
            String pointsText = pointsField != null ? pointsField.getText().trim() : "";

            java.util.List<com.clothingstore.model.Customer> filtered = allCustomers.stream()
                    .filter(customer -> {
                        // Search filter
                        if (!searchTerm.isEmpty()) {
                            boolean matches = (customer.getFullName() != null && customer.getFullName().toLowerCase().contains(searchTerm))
                                    || (customer.getPhone() != null && customer.getPhone().toLowerCase().contains(searchTerm));
                            if (!matches) {
                                return false;
                            }
                        }

                        // Status filter
                        if (!"All Status".equals(selectedStatus)) {
                            boolean isActive = customer.isActive();
                            if ("Active".equals(selectedStatus) && !isActive) {
                                return false;
                            }
                            if ("Inactive".equals(selectedStatus) && isActive) {
                                return false;
                            }
                        }

                        // Points filter
                        if (!pointsText.isEmpty()) {
                            try {
                                int minPoints = Integer.parseInt(pointsText);
                                if (customer.getLoyaltyPoints() < minPoints) {
                                    return false;
                                }
                            } catch (NumberFormatException e) {
                                // Invalid number, ignore filter
                            }
                        }

                        return true;
                    })
                    .collect(java.util.stream.Collectors.toList());

            filteredCustomers.setAll(filtered);
            if (resultLabel != null) {
                resultLabel.setText("Showing: " + filtered.size() + " of " + allCustomers.size() + " customers");
            }
        };

        // Add listeners
        if (searchField != null) {
            searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters.run());
        }
        if (statusFilter != null) {
            statusFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters.run());
        }
        if (pointsField != null) {
            pointsField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters.run());
        }

        applyFilters.run();
    }

    private javafx.scene.layout.HBox createProductQuickActions() {
        javafx.scene.layout.HBox quickActions = new javafx.scene.layout.HBox(10);
        quickActions.setAlignment(javafx.geometry.Pos.CENTER);

        javafx.scene.control.Button refreshBtn = new javafx.scene.control.Button("Refresh Products");
        refreshBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        refreshBtn.setOnAction(e -> loadAllProductsForPOS());

        javafx.scene.control.Button lowStockBtn = new javafx.scene.control.Button("Low Stock");
        lowStockBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        lowStockBtn.setOnAction(e -> showLowStockProducts());

        javafx.scene.control.Button outOfStockBtn = new javafx.scene.control.Button("X Out of Stock");
        outOfStockBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4;");
        outOfStockBtn.setOnAction(e -> showOutOfStockProducts());

        quickActions.getChildren().addAll(refreshBtn, lowStockBtn, outOfStockBtn);
        return quickActions;
    }

    private void showLowStockProducts() {
        applyProductFilters(null, "Low Stock");
        setStatus("Showing low stock products (≤5 items)");
    }

    private void showOutOfStockProducts() {
        applyProductFilters(null, "Out of Stock");
        setStatus("Showing out of stock products");
    }

    private void initializePOSState() {
        currentCartItems = new java.util.ArrayList<>();
        selectedCustomer = null;
        currentTransactionNumber = generateTransactionNumber();
    }

    private String generateTransactionNumber() {
        return "TXN" + String.format("%06d", System.currentTimeMillis() % 1000000);
    }

    private java.math.BigDecimal getTaxRate() {
        try {
            com.clothingstore.dao.SettingsDAO settingsDAO = com.clothingstore.dao.SettingsDAO.getInstance();
            double taxRatePercent = settingsDAO.getDoubleValue("tax_rate", 8.5);
            return new java.math.BigDecimal(taxRatePercent / 100.0); // Convert percentage to decimal
        } catch (Exception e) {
            // Fallback to default 8.5% if settings can't be loaded
            return new java.math.BigDecimal("0.085");
        }
    }

    private String getTaxRateDisplayText() {
        try {
            com.clothingstore.dao.SettingsDAO settingsDAO = com.clothingstore.dao.SettingsDAO.getInstance();
            double taxRatePercent = settingsDAO.getDoubleValue("tax_rate", 8.5);
            return String.format("Tax (%.1f%%):", taxRatePercent);
        } catch (Exception e) {
            return "Tax (8.5%):";
        }
    }

    private javafx.scene.layout.HBox createPOSHeader() {
        javafx.scene.layout.HBox header = new javafx.scene.layout.HBox(10);
        header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label title = new javafx.scene.control.Label("Point of Sale System");
        title.setFont(javafx.scene.text.Font.font("System", javafx.scene.text.FontWeight.BOLD, 18));

        // Quick access buttons for new features
        javafx.scene.control.Button suppliersBtn = new javafx.scene.control.Button("Suppliers");
        suppliersBtn.setStyle("-fx-font-size: 11px; -fx-padding: 5 8; -fx-background-color: #17a2b8; -fx-text-fill: white;");
        suppliersBtn.setOnAction(e -> showSupplierManagementPage());

        javafx.scene.control.Button groupsBtn = new javafx.scene.control.Button("Groups");
        groupsBtn.setStyle("-fx-font-size: 11px; -fx-padding: 5 8; -fx-background-color: #fd7e14; -fx-text-fill: white;");
        groupsBtn.setOnAction(e -> showCustomerGroupsPage());

        javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        transactionNumberLabel = new javafx.scene.control.Label("Transaction: " + currentTransactionNumber);
        transactionNumberLabel.setStyle("-fx-font-weight: bold;");

        javafx.scene.control.Button newTransactionBtn = new javafx.scene.control.Button("New Transaction");
        newTransactionBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-weight: bold;");
        newTransactionBtn.setOnAction(e -> startNewTransaction());

        header.getChildren().addAll(title, suppliersBtn, groupsBtn, spacer, transactionNumberLabel, newTransactionBtn);
        return header;
    }

    private javafx.scene.layout.HBox createCustomerSection() {
        javafx.scene.layout.HBox customerSection = new javafx.scene.layout.HBox(10);
        customerSection.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        customerSection.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 1;");

        javafx.scene.control.Label customerTitleLabel = new javafx.scene.control.Label("Customer:");
        customerTitleLabel.setStyle("-fx-font-weight: bold;");

        customerLabel = new javafx.scene.control.Label("Walk-in Customer");
        customerLabel.setStyle("-fx-text-fill: #6c757d;");

        javafx.scene.control.Button selectCustomerBtn = new javafx.scene.control.Button("Select Customer");
        selectCustomerBtn.setOnAction(e -> showCustomerSelectionDialog());

        javafx.scene.control.Button clearCustomerBtn = new javafx.scene.control.Button("Clear");
        clearCustomerBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 6 12 6 12;");
        clearCustomerBtn.setOnAction(e -> clearSelectedCustomer());

        javafx.scene.layout.Region customerSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(customerSpacer, javafx.scene.layout.Priority.ALWAYS);

        customerSection.getChildren().addAll(customerTitleLabel, customerLabel, customerSpacer,
                selectCustomerBtn, clearCustomerBtn);
        return customerSection;
    }

    private javafx.scene.layout.VBox createProductSection() {
        javafx.scene.layout.VBox productSection = new javafx.scene.layout.VBox(10);
        productSection.setPrefWidth(450);

        javafx.scene.control.Label productLabel = new javafx.scene.control.Label("Product Search");
        productLabel.setFont(javafx.scene.text.Font.font("System", javafx.scene.text.FontWeight.BOLD, 14));

        // Search controls
        javafx.scene.layout.HBox searchBox = new javafx.scene.layout.HBox(10);
        javafx.scene.control.TextField searchField = new javafx.scene.control.TextField();
        searchField.setPromptText("Search products by name or SKU...");
        searchField.setPrefWidth(300);
        searchField.setOnKeyReleased(e -> searchProducts(searchField.getText()));

        javafx.scene.control.Button scanBtn = new javafx.scene.control.Button("Scan");
        scanBtn.setOnAction(e -> showBarcodeDialog());

        searchBox.getChildren().addAll(searchField, scanBtn);

        // Product table
        productTableView = new javafx.scene.control.TableView<>();
        productTableView.setPrefHeight(350);

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> nameCol
                = new javafx.scene.control.TableColumn<>("Product");
        nameCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getName()));
        nameCol.setPrefWidth(200);

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> skuCol
                = new javafx.scene.control.TableColumn<>("SKU");
        skuCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getSku()));
        skuCol.setPrefWidth(80);

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> priceCol
                = new javafx.scene.control.TableColumn<>("Price");
        priceCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty("$" + cellData.getValue().getPrice().toString()));
        priceCol.setPrefWidth(80);

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> stockCol
                = new javafx.scene.control.TableColumn<>("Stock");
        stockCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(String.valueOf(cellData.getValue().getStockQuantity())));
        stockCol.setPrefWidth(60);

        javafx.scene.control.TableColumn<com.clothingstore.model.Product, String> actionCol
                = new javafx.scene.control.TableColumn<>("Action");
        actionCol.setCellFactory(col -> new javafx.scene.control.TableCell<com.clothingstore.model.Product, String>() {
            private final javafx.scene.control.Button addBtn = new javafx.scene.control.Button("+ Add");

            {
                addBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 2 6;");
                addBtn.setOnAction(e -> {
                    com.clothingstore.model.Product product = getTableView().getItems().get(getIndex());
                    addProductToCart(product);
                });
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(addBtn);
                }
            }
        });
        actionCol.setPrefWidth(70);

        productTableView.getColumns().addAll(nameCol, skuCol, priceCol, stockCol, actionCol);

        // Double-click to add product
        productTableView.setRowFactory(tv -> {
            javafx.scene.control.TableRow<com.clothingstore.model.Product> row = new javafx.scene.control.TableRow<>();
            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    addProductToCart(row.getItem());
                }
            });
            return row;
        });

        productSection.getChildren().addAll(productLabel, searchBox, productTableView);
        return productSection;
    }

    private javafx.scene.layout.VBox createCartSection() {
        javafx.scene.layout.VBox cartSection = new javafx.scene.layout.VBox(10);
        javafx.scene.layout.HBox.setHgrow(cartSection, javafx.scene.layout.Priority.ALWAYS);

        javafx.scene.control.Label cartLabel = new javafx.scene.control.Label("Shopping Cart");
        cartLabel.setFont(javafx.scene.text.Font.font("System", javafx.scene.text.FontWeight.BOLD, 14));

        // Cart list view
        cartListView = new javafx.scene.control.ListView<>();
        cartListView.setPlaceholder(new javafx.scene.control.Label("Cart is empty"));
        cartListView.setPrefHeight(250);
        cartListView.setCellFactory(listView -> new CartItemCell());

        // Cart totals
        javafx.scene.layout.VBox totalsBox = new javafx.scene.layout.VBox(5);
        totalsBox.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        javafx.scene.layout.HBox subtotalBox = new javafx.scene.layout.HBox();
        javafx.scene.control.Label subtotalLabel = new javafx.scene.control.Label("Subtotal:");
        javafx.scene.layout.Region subtotalSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(subtotalSpacer, javafx.scene.layout.Priority.ALWAYS);
        subtotalValueLabel = new javafx.scene.control.Label("$0.00");
        subtotalBox.getChildren().addAll(subtotalLabel, subtotalSpacer, subtotalValueLabel);

        javafx.scene.layout.HBox taxBox = new javafx.scene.layout.HBox();
        javafx.scene.control.Label taxLabel = new javafx.scene.control.Label(getTaxRateDisplayText());
        javafx.scene.layout.Region taxSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(taxSpacer, javafx.scene.layout.Priority.ALWAYS);
        taxValueLabel = new javafx.scene.control.Label("$0.00");
        taxBox.getChildren().addAll(taxLabel, taxSpacer, taxValueLabel);

        javafx.scene.layout.HBox totalBox = new javafx.scene.layout.HBox();
        javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("Total:");
        totalLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 16px;");
        javafx.scene.layout.Region totalSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(totalSpacer, javafx.scene.layout.Priority.ALWAYS);
        totalValueLabel = new javafx.scene.control.Label("$0.00");
        totalValueLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 16px;");
        totalBox.getChildren().addAll(totalLabel, totalSpacer, totalValueLabel);

        totalsBox.getChildren().addAll(subtotalBox, taxBox, new javafx.scene.control.Separator(), totalBox);

        // Payment buttons
        javafx.scene.layout.VBox paymentButtonsContainer = new javafx.scene.layout.VBox(10);

        processPaymentButton = new javafx.scene.control.Button("Single Payment Method");
        processPaymentButton.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 14px;");
        processPaymentButton.setPrefHeight(40);
        processPaymentButton.setMaxWidth(Double.MAX_VALUE);
        processPaymentButton.setOnAction(e -> processPayment());

        javafx.scene.control.Button multiplePaymentBtn = new javafx.scene.control.Button("Multiple Payment Methods");
        multiplePaymentBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 14px;");
        multiplePaymentBtn.setPrefHeight(40);
        multiplePaymentBtn.setMaxWidth(Double.MAX_VALUE);
        multiplePaymentBtn.setOnAction(e -> processMultiplePayments());

        javafx.scene.layout.HBox actionButtons = new javafx.scene.layout.HBox(10);
        javafx.scene.control.Button clearCartBtn = new javafx.scene.control.Button("Clear Cart");
        clearCartBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold;");
        clearCartBtn.setOnAction(e -> clearCart());

        javafx.scene.control.Button newTransactionBtn = new javafx.scene.control.Button("New Transaction");
        newTransactionBtn.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold;");
        newTransactionBtn.setOnAction(e -> startNewTransaction());

        actionButtons.getChildren().addAll(clearCartBtn, newTransactionBtn);

        paymentButtonsContainer.getChildren().addAll(processPaymentButton, multiplePaymentBtn, actionButtons);

        cartSection.getChildren().addAll(cartLabel, cartListView, totalsBox, paymentButtonsContainer);
        return cartSection;
    }

    private javafx.scene.layout.HBox createStatusBar() {
        javafx.scene.layout.HBox statusBar = new javafx.scene.layout.HBox(10);
        statusBar.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        statusBar.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 1 0 0 0;");

        javafx.scene.control.Label statusLabel = new javafx.scene.control.Label("Ready for new transaction");
        javafx.scene.layout.Region statusSpacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(statusSpacer, javafx.scene.layout.Priority.ALWAYS);

        itemsCountLabel = new javafx.scene.control.Label("Items: 0");
        itemsCountLabel.setStyle("-fx-font-weight: bold;");

        statusBar.getChildren().addAll(statusLabel, statusSpacer, itemsCountLabel);
        return statusBar;
    }

    // POS Functionality Methods
    private void loadProductsForPOS() {
        try {
            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            java.util.List<com.clothingstore.model.Product> products = productDAO.findAll();
            productTableView.getItems().clear();
            productTableView.getItems().addAll(products);
        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load products: " + e.getMessage());
        }
    }

    /**
     * Load categories dynamically from database for POS filter
     */
    private void loadCategoriesForPOSFilter(javafx.scene.control.ComboBox<String> categoryFilter) {
        try {
            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            java.util.List<String> categories = productDAO.getAllCategories();

            categoryFilter.getItems().clear();
            categoryFilter.getItems().add("All Categories");
            categoryFilter.getItems().addAll(categories);

        } catch (Exception e) {
            // Fallback to basic categories if database fails
            categoryFilter.getItems().clear();
            categoryFilter.getItems().addAll("All Categories", "Clothing", "Accessories", "Footwear");
            System.err.println("Failed to load categories from database, using fallback: " + e.getMessage());
        }
    }

    private void searchProducts(String searchTerm) {
        try {
            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            java.util.List<com.clothingstore.model.Product> products;

            if (searchTerm == null || searchTerm.trim().isEmpty()) {
                products = productDAO.findAll();
            } else {
                products = productDAO.searchProducts(searchTerm.trim());
            }

            productTableView.getItems().clear();
            productTableView.getItems().addAll(products);
        } catch (Exception e) {
            AlertUtil.showError("Search Error", "Failed to search products: " + e.getMessage());
        }
    }

    private void addProductToCart(com.clothingstore.model.Product product) {
        if (product.getStockQuantity() <= 0) {
            AlertUtil.showWarning("Out of Stock", "Product " + product.getName() + " is out of stock!");
            return;
        }

        // Check if product already in cart
        for (com.clothingstore.model.TransactionItem item : currentCartItems) {
            if (item.getProductId().equals(product.getId())) {
                // Increase quantity
                if (item.getQuantity() < product.getStockQuantity()) {
                    item.setQuantity(item.getQuantity() + 1);
                    updateCartDisplayWithButtonStates();
                    return;
                } else {
                    AlertUtil.showWarning("Stock Limit", "Cannot add more items. Stock limit reached!");
                    return;
                }
            }
        }

        // Add new item to cart
        com.clothingstore.model.TransactionItem newItem = new com.clothingstore.model.TransactionItem(product, 1);
        currentCartItems.add(newItem);
        updateCartDisplayWithButtonStates();

        setStatus("Added " + product.getName() + " to cart");
    }

    private void updateCartDisplay() {
        cartListView.getItems().clear();
        cartListView.getItems().addAll(currentCartItems);

        // Calculate totals
        java.math.BigDecimal subtotal = java.math.BigDecimal.ZERO;
        for (com.clothingstore.model.TransactionItem item : currentCartItems) {
            subtotal = subtotal.add(item.getLineTotal());
        }

        java.math.BigDecimal taxRate = getTaxRate();
        java.math.BigDecimal taxAmount = subtotal.multiply(taxRate);
        java.math.BigDecimal total = subtotal.add(taxAmount);

        subtotalValueLabel.setText("$" + subtotal.toString());
        taxValueLabel.setText("$" + taxAmount.setScale(2, java.math.RoundingMode.HALF_UP).toString());
        totalValueLabel.setText("$" + total.setScale(2, java.math.RoundingMode.HALF_UP).toString());

        itemsCountLabel.setText("Items: " + currentCartItems.size());
    }

    private void removeItemFromCart(com.clothingstore.model.TransactionItem item) {
        currentCartItems.remove(item);
        updateCartDisplayWithButtonStates();
        setStatus("Removed " + item.getProductName() + " from cart");
    }

    private void updateItemQuantity(com.clothingstore.model.TransactionItem item, int newQuantity) {
        if (newQuantity <= 0) {
            removeItemFromCart(item);
            return;
        }

        if (newQuantity > item.getProduct().getStockQuantity()) {
            AlertUtil.showWarning("Stock Limit", "Cannot set quantity to " + newQuantity + ". Stock limit: " + item.getProduct().getStockQuantity());
            return;
        }

        item.setQuantity(newQuantity);
        updateCartDisplayWithButtonStates();
    }

    private void clearCart() {
        if (currentCartItems.isEmpty()) {
            return;
        }

        if (AlertUtil.showConfirmation("Clear Cart", "Are you sure you want to clear the cart?")) {
            currentCartItems.clear();
            updateCartDisplayWithButtonStates();
            setStatus("Cart cleared");
        }
    }

    private void startNewTransaction() {
        if (!currentCartItems.isEmpty()) {
            if (!AlertUtil.showConfirmation("New Transaction", "Current cart will be cleared. Continue?")) {
                return;
            }
        }

        currentCartItems.clear();
        selectedCustomer = null;
        currentTransactionNumber = generateTransactionNumber();

        transactionNumberLabel.setText("Transaction: " + currentTransactionNumber);
        customerLabel.setText("Walk-in Customer");
        customerLabel.setStyle("-fx-text-fill: #6c757d;");

        updateCartDisplayWithButtonStates();
        setStatus("New transaction started: " + currentTransactionNumber);
    }

    private void showCustomerSelectionDialog() {
        try {
            com.clothingstore.dao.CustomerDAO customerDAO = com.clothingstore.dao.CustomerDAO.getInstance();
            java.util.List<com.clothingstore.model.Customer> allCustomers = customerDAO.findAll();

            // Debug information
            System.out.println("DEBUG: Loaded " + allCustomers.size() + " customers from database");
            if (!allCustomers.isEmpty()) {
                System.out.println("DEBUG: First customer: " + allCustomers.get(0).getFullName());
                System.out.println("DEBUG: First customer phone: " + allCustomers.get(0).getPhone());

                System.out.println("DEBUG: First customer points: " + allCustomers.get(0).getLoyaltyPoints());
            } else {
                // If no customers exist, offer to create demo data
                boolean createDemo = AlertUtil.showConfirmation("No Customers Found",
                        "No customers found in the database.\nWould you like to create some demo customers?");
                if (createDemo) {
                    createDemoCustomersWithoutMembership();
                    allCustomers = customerDAO.findAll(); // Reload after creating demo data
                    System.out.println("DEBUG: After demo creation, loaded " + allCustomers.size() + " customers");
                }
            }

            // Create enhanced customer selection dialog
            javafx.stage.Stage dialog = new javafx.stage.Stage();
            dialog.setTitle("Select Customer");
            dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            // dialog.initOwner(primaryStage); // Remove this line as primaryStage is not available

            // Create the enhanced customer filter interface
            javafx.scene.layout.VBox root = createEnhancedCustomerFilterDialog(allCustomers, dialog);

            javafx.scene.Scene scene = new javafx.scene.Scene(root, 600, 500);

            // Try to load CSS, but don't fail if it's not found
            try {
                java.net.URL cssUrl = getClass().getResource("/css/customer-filter.css");
                if (cssUrl != null) {
                    scene.getStylesheets().add(cssUrl.toExternalForm());
                }
            } catch (Exception cssException) {
                System.out.println("Warning: Could not load customer-filter.css: " + cssException.getMessage());
            }

            dialog.setScene(scene);
            dialog.showAndWait();

        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to load customers: " + e.getMessage());
        }
    }

    private javafx.scene.layout.VBox createEnhancedCustomerFilterDialog(
            java.util.List<com.clothingstore.model.Customer> allCustomers,
            javafx.stage.Stage dialog) {

        javafx.scene.layout.VBox root = new javafx.scene.layout.VBox(15);
        root.setPadding(new javafx.geometry.Insets(20));
        root.setStyle("-fx-background-color: #f8f9fa;");

        // Header
        javafx.scene.control.Label headerLabel = new javafx.scene.control.Label("Select Customer");
        headerLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        javafx.scene.control.Label subHeaderLabel = new javafx.scene.control.Label("Choose a customer for this transaction");
        subHeaderLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6c757d;");

        // Filter Section
        javafx.scene.layout.VBox filterSection = createCustomerFilterSection(allCustomers);

        // Customer List
        javafx.scene.control.TableView<com.clothingstore.model.Customer> customerTable = createCustomerTable();
        javafx.collections.ObservableList<com.clothingstore.model.Customer> filteredCustomers
                = javafx.collections.FXCollections.observableArrayList();
        customerTable.setItems(filteredCustomers);

        // Initially populate the table with all customers - like CustomerManagementController
        filteredCustomers.setAll(allCustomers);

        // Buttons
        javafx.scene.layout.HBox buttonBox = createDialogButtons(dialog, customerTable);

        root.getChildren().addAll(headerLabel, subHeaderLabel, filterSection, customerTable, buttonBox);

        // Setup filtering logic
        setupCustomerFiltering(filterSection, filteredCustomers, allCustomers);

        return root;
    }

    private javafx.scene.layout.VBox createCustomerFilterSection(java.util.List<com.clothingstore.model.Customer> allCustomers) {
        javafx.scene.layout.VBox filterSection = new javafx.scene.layout.VBox(10);
        filterSection.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        // Filter Title
        javafx.scene.control.Label filterTitle = new javafx.scene.control.Label("Filter Customers");
        filterTitle.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #495057;");

        // Search Row
        javafx.scene.layout.HBox searchRow = new javafx.scene.layout.HBox(10);
        searchRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label searchLabel = new javafx.scene.control.Label("Search:");
        searchLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #6c757d;");

        javafx.scene.control.TextField searchField = new javafx.scene.control.TextField();
        searchField.setPromptText("Name, phone, address...");
        searchField.setPrefWidth(250);
        searchField.setId("customerSearchField");

        javafx.scene.control.Button clearSearchBtn = new javafx.scene.control.Button("✕");
        clearSearchBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 50%; -fx-border-radius: 50%; -fx-padding: 4 8 4 8;");
        clearSearchBtn.setOnAction(e -> searchField.clear());

        searchRow.getChildren().addAll(searchLabel, searchField, clearSearchBtn);

        // Filter Row
        javafx.scene.layout.HBox filterRow = new javafx.scene.layout.HBox(15);
        filterRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Remove membership filter completely - no replacement needed
        // Status Filter
        javafx.scene.control.Label statusLabel = new javafx.scene.control.Label("Status:");
        statusLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #6c757d;");

        javafx.scene.control.ComboBox<String> statusFilter = new javafx.scene.control.ComboBox<>();
        statusFilter.getItems().addAll("All", "Active", "Inactive");
        statusFilter.setValue("All");
        statusFilter.setPrefWidth(100);
        statusFilter.setId("statusFilter");

        // Points Filter
        javafx.scene.control.Label pointsLabel = new javafx.scene.control.Label("Min Points:");
        pointsLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #6c757d;");

        javafx.scene.control.TextField pointsField = new javafx.scene.control.TextField();
        pointsField.setPromptText("0");
        pointsField.setPrefWidth(80);
        pointsField.setId("pointsFilter");

        filterRow.getChildren().addAll(statusLabel, statusFilter, pointsLabel, pointsField);

        // Action Row
        javafx.scene.layout.HBox actionRow = new javafx.scene.layout.HBox(10);
        actionRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Button clearFiltersBtn = new javafx.scene.control.Button("Clear All Filters");
        clearFiltersBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 6 12 6 12;");
        clearFiltersBtn.setOnAction(e -> {
            searchField.clear();
            statusFilter.setValue("All");
            pointsField.clear();
        });

        javafx.scene.control.Label resultCountLabel = new javafx.scene.control.Label("Showing: " + allCustomers.size() + " customers");
        resultCountLabel.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");
        resultCountLabel.setId("resultCountLabel");

        javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        actionRow.getChildren().addAll(clearFiltersBtn, spacer, resultCountLabel);

        filterSection.getChildren().addAll(filterTitle, searchRow, filterRow, actionRow);
        return filterSection;
    }

    private javafx.scene.control.TableView<com.clothingstore.model.Customer> createCustomerTable() {
        javafx.scene.control.TableView<com.clothingstore.model.Customer> table = new javafx.scene.control.TableView<>();
        table.setPrefHeight(250);

        // Name Column - using the same simple approach as CustomerManagementController
        javafx.scene.control.TableColumn<com.clothingstore.model.Customer, String> nameCol
                = new javafx.scene.control.TableColumn<>("Name");
        nameCol.setPrefWidth(150);
        nameCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getFullName()));

        // Phone Column - using the same simple approach as CustomerManagementController
        javafx.scene.control.TableColumn<com.clothingstore.model.Customer, String> phoneCol
                = new javafx.scene.control.TableColumn<>("Phone");
        phoneCol.setPrefWidth(120);
        phoneCol.setCellValueFactory(cellData
                -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getPhone()));

        // Points Column - using PropertyValueFactory like CustomerManagementController
        javafx.scene.control.TableColumn<com.clothingstore.model.Customer, Integer> pointsCol
                = new javafx.scene.control.TableColumn<>("Points");
        pointsCol.setPrefWidth(70);
        pointsCol.setCellValueFactory(new javafx.scene.control.cell.PropertyValueFactory<>("loyaltyPoints"));

        table.getColumns().addAll(nameCol, phoneCol, pointsCol);

        // Simple table styling - minimal like CustomerManagementController
        table.setStyle("-fx-background-color: white;");

        // Set placeholder for empty table
        javafx.scene.control.Label placeholder = new javafx.scene.control.Label("No customers found matching the current filters");
        table.setPlaceholder(placeholder);

        System.out.println("DEBUG: Simple customer table created with " + table.getColumns().size() + " columns");
        return table;
    }

    private javafx.scene.layout.HBox createDialogButtons(javafx.stage.Stage dialog,
            javafx.scene.control.TableView<com.clothingstore.model.Customer> customerTable) {

        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(10);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);
        buttonBox.setPadding(new javafx.geometry.Insets(10, 0, 0, 0));

        javafx.scene.control.Button selectBtn = new javafx.scene.control.Button("✓ Select Customer");
        selectBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 16 8 16;");
        selectBtn.setDisable(true);

        javafx.scene.control.Button cancelBtn = new javafx.scene.control.Button("✕ Cancel");
        cancelBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 4; -fx-padding: 8 16 8 16;");

        // Enable/disable select button based on table selection
        customerTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            selectBtn.setDisable(newSelection == null);
        });

        // Button actions
        selectBtn.setOnAction(e -> {
            com.clothingstore.model.Customer selected = customerTable.getSelectionModel().getSelectedItem();
            if (selected != null) {
                selectedCustomer = selected;
                customerLabel.setText(selected.getFullName() + " (Points: " + selected.getLoyaltyPoints() + ")");
                customerLabel.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");
                setStatus("Customer selected: " + selected.getFullName());
                dialog.close();
            }
        });

        cancelBtn.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(cancelBtn, selectBtn);
        return buttonBox;
    }

    private void setupCustomerFiltering(javafx.scene.layout.VBox filterSection,
            javafx.collections.ObservableList<com.clothingstore.model.Customer> filteredCustomers,
            java.util.List<com.clothingstore.model.Customer> allCustomers) {

        // Get filter controls by ID with null checks
        javafx.scene.control.TextField searchField = (javafx.scene.control.TextField) filterSection.lookup("#customerSearchField");
        javafx.scene.control.ComboBox<String> statusFilter = (javafx.scene.control.ComboBox<String>) filterSection.lookup("#statusFilter");
        javafx.scene.control.TextField pointsField = (javafx.scene.control.TextField) filterSection.lookup("#pointsFilter");
        javafx.scene.control.Label resultCountLabel = (javafx.scene.control.Label) filterSection.lookup("#resultCountLabel");

        // Debug: Check if controls were found
        if (searchField == null) {
            System.out.println("DEBUG: searchField not found!");
        }
        if (statusFilter == null) {
            System.out.println("DEBUG: statusFilter not found!");
        }
        if (pointsField == null) {
            System.out.println("DEBUG: pointsField not found!");
        }
        if (resultCountLabel == null) {
            System.out.println("DEBUG: resultCountLabel not found!");
        }

        // Create filtering logic with null checks
        Runnable applyFilters = () -> {
            // Safely get values with null checks
            String searchTerm = (searchField != null && searchField.getText() != null)
                    ? searchField.getText().toLowerCase().trim() : "";
            String selectedStatus = (statusFilter != null && statusFilter.getValue() != null)
                    ? statusFilter.getValue() : "All";
            String pointsText = (pointsField != null && pointsField.getText() != null)
                    ? pointsField.getText().trim() : "";

            java.util.List<com.clothingstore.model.Customer> filtered = allCustomers.stream()
                    .filter(customer -> {
                        // Search filter
                        if (!searchTerm.isEmpty()) {
                            boolean matchesSearch
                                    = (customer.getFullName() != null && customer.getFullName().toLowerCase().contains(searchTerm))
                                    || (customer.getPhone() != null && customer.getPhone().toLowerCase().contains(searchTerm))
                                    || (customer.getAddress() != null && customer.getAddress().toLowerCase().contains(searchTerm))
                                    || (customer.getCity() != null && customer.getCity().toLowerCase().contains(searchTerm));

                            if (!matchesSearch) {
                                return false;
                            }
                        }

                        // Membership filter removed - no longer needed
                        // Status filter
                        if (!"All".equals(selectedStatus)) {
                            String customerStatus = customer.getStatus();
                            if (customerStatus == null) {
                                customerStatus = "Active";
                            }
                            if (!selectedStatus.equals(customerStatus)) {
                                return false;
                            }
                        }

                        // Points filter
                        if (!pointsText.isEmpty()) {
                            try {
                                int minPoints = Integer.parseInt(pointsText);
                                if (customer.getLoyaltyPoints() < minPoints) {
                                    return false;
                                }
                            } catch (NumberFormatException e) {
                                // Invalid number, ignore filter
                            }
                        }

                        return true;
                    })
                    .collect(java.util.stream.Collectors.toList());

            // Direct update like CustomerManagementController - no Platform.runLater needed
            filteredCustomers.setAll(filtered);
            if (resultCountLabel != null) {
                resultCountLabel.setText("Showing: " + filtered.size() + " of " + allCustomers.size() + " customers");
            }
        };

        // Add listeners for real-time filtering with null checks
        if (searchField != null) {
            searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters.run());
        }
        if (statusFilter != null) {
            statusFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters.run());
        }
        if (pointsField != null) {
            pointsField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters.run());
        }

        // Initial filter application
        applyFilters.run();
    }

    private void createDemoCustomersWithoutMembership() {
        try {
            com.clothingstore.dao.CustomerDAO customerDAO = com.clothingstore.dao.CustomerDAO.getInstance();

            // Create sample customers without membership levels
            com.clothingstore.model.Customer[] customers = {
                createSimpleCustomer("John", "Smith", "555-0101", 1250),
                createSimpleCustomer("Sarah", "Johnson", "555-0102", 3200),
                createSimpleCustomer("Michael", "Brown", "555-0103", 750),
                createSimpleCustomer("Emily", "Davis", "555-0104", 5500),
                createSimpleCustomer("David", "Wilson", "555-0105", 2100),
                createSimpleCustomer("Lisa", "Anderson", "555-0106", 890),
                createSimpleCustomer("Robert", "Taylor", "555-0107", 4200),
                createSimpleCustomer("Jennifer", "Martinez", "555-0108", 1680),
                createSimpleCustomer("Christopher", "Garcia", "555-0109", 3750),
                createSimpleCustomer("Amanda", "Rodriguez", "555-0110", 920)
            };

            // Save customers to database
            for (com.clothingstore.model.Customer customer : customers) {
                try {
                    // Check if customer already exists by phone
                    if (customerDAO.findByPhone(customer.getPhone()).isEmpty()) {
                        customerDAO.save(customer);
                        System.out.println("Created demo customer: " + customer.getFullName());
                    }
                } catch (Exception e) {
                    System.err.println("Error saving customer " + customer.getFullName() + ": " + e.getMessage());
                }
            }

            AlertUtil.showSuccess("Demo Data Created", "Successfully created " + customers.length + " demo customers!");

        } catch (Exception e) {
            AlertUtil.showError("Demo Data Error", "Failed to create demo customers: " + e.getMessage());
        }
    }

    private com.clothingstore.model.Customer createSimpleCustomer(String firstName, String lastName, String phone, int loyaltyPoints) {
        com.clothingstore.model.Customer customer = new com.clothingstore.model.Customer();
        customer.setFirstName(firstName);
        customer.setLastName(lastName);
        customer.setPhone(phone);
        customer.setAddress("123 Main St");
        customer.setCity("Springfield");
        customer.setState("IL");
        customer.setZipCode("62701");
        customer.setActive(true);
        customer.setLoyaltyPoints(loyaltyPoints);
        // No membership level - system will default to "Standard"
        customer.setRegistrationDate(java.time.LocalDateTime.now().minusDays((int) (Math.random() * 365)));
        customer.setTotalSpent((double) loyaltyPoints);
        customer.setTotalPurchases(loyaltyPoints / 100);
        customer.setLastPurchaseDate(java.time.LocalDateTime.now().minusDays((int) (Math.random() * 30)));
        return customer;
    }

    private void clearSelectedCustomer() {
        selectedCustomer = null;
        customerLabel.setText("Walk-in Customer");
        customerLabel.setStyle("-fx-text-fill: #6c757d;");
        setStatus("Customer cleared");
    }

    private void showBarcodeDialog() {
        javafx.scene.control.TextInputDialog dialog = new javafx.scene.control.TextInputDialog();
        dialog.setTitle("Barcode Scanner");
        dialog.setHeaderText("Enter or scan product barcode");
        dialog.setContentText("Barcode/SKU:");

        java.util.Optional<String> result = dialog.showAndWait();
        result.ifPresent(barcode -> {
            try {
                com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
                java.util.Optional<com.clothingstore.model.Product> product = productDAO.findBySku(barcode);

                if (product.isPresent()) {
                    addProductToCart(product.get());
                } else {
                    AlertUtil.showWarning("Product Not Found", "No product found with SKU: " + barcode);
                }
            } catch (Exception e) {
                AlertUtil.showError("Database Error", "Failed to find product: " + e.getMessage());
            }
        });
    }

    private void showReceipt(com.clothingstore.model.Transaction transaction, java.math.BigDecimal amountReceived, java.math.BigDecimal change) {
        javafx.scene.control.Alert receiptDialog = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
        receiptDialog.setTitle("Transaction Receipt");
        receiptDialog.setHeaderText("Transaction: " + transaction.getTransactionNumber());

        StringBuilder receipt = new StringBuilder();
        receipt.append("Date: ").append(transaction.getTransactionDate().format(java.time.format.DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm"))).append("\n");
        receipt.append("Customer: ").append(selectedCustomer != null ? selectedCustomer.getFullName() : "Walk-in Customer").append("\n");
        receipt.append("Cashier: ").append(transaction.getCashierName()).append("\n\n");

        receipt.append("ITEMS:\n");
        receipt.append("----------------------------------------\n");
        for (com.clothingstore.model.TransactionItem item : transaction.getItems()) {
            receipt.append(String.format("%-20s %2d x $%6.2f = $%7.2f\n",
                    item.getProductName(), item.getQuantity(),
                    item.getUnitPrice().doubleValue(), item.getLineTotal().doubleValue()));
        }
        receipt.append("----------------------------------------\n");
        receipt.append(String.format("Subtotal: %26s $%7.2f\n", "", transaction.getSubtotal().doubleValue()));
        receipt.append(String.format("Tax: %30s $%7.2f\n", "", transaction.getTaxAmount().doubleValue()));
        receipt.append(String.format("Total: %28s $%7.2f\n", "", transaction.getTotalAmount().doubleValue()));
        receipt.append(String.format("Payment (%s): %15s $%7.2f\n", transaction.getPaymentMethod(), "", amountReceived.doubleValue()));
        receipt.append(String.format("Change: %27s $%7.2f\n", "", change.doubleValue()));

        receiptDialog.setContentText(receipt.toString());
        receiptDialog.showAndWait();
    }

    // Custom ListCell for cart items
    private class CartItemCell extends javafx.scene.control.ListCell<com.clothingstore.model.TransactionItem> {

        @Override
        protected void updateItem(com.clothingstore.model.TransactionItem item, boolean empty) {
            super.updateItem(item, empty);

            if (empty || item == null) {
                setGraphic(null);
                setText(null);
            } else {
                javafx.scene.layout.HBox container = new javafx.scene.layout.HBox(10);
                container.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

                // Product info
                javafx.scene.layout.VBox productInfo = new javafx.scene.layout.VBox(2);
                javafx.scene.control.Label nameLabel = new javafx.scene.control.Label(item.getProductName());
                nameLabel.setStyle("-fx-font-weight: bold;");
                javafx.scene.control.Label detailLabel = new javafx.scene.control.Label(
                        item.getProductSku() + " - $" + item.getUnitPrice().toString() + " each");
                detailLabel.setStyle("-fx-text-fill: #666; -fx-font-size: 11px;");
                productInfo.getChildren().addAll(nameLabel, detailLabel);

                javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
                javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

                // Quantity controls
                javafx.scene.control.Button decreaseBtn = new javafx.scene.control.Button("-");
                decreaseBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
                decreaseBtn.setOnAction(e -> updateItemQuantity(item, item.getQuantity() - 1));

                javafx.scene.control.Label quantityLabel = new javafx.scene.control.Label(String.valueOf(item.getQuantity()));
                quantityLabel.setStyle("-fx-font-weight: bold; -fx-min-width: 20; -fx-alignment: center;");

                javafx.scene.control.Button increaseBtn = new javafx.scene.control.Button("+");
                increaseBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
                increaseBtn.setOnAction(e -> updateItemQuantity(item, item.getQuantity() + 1));

                // Line total
                javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("$" + item.getLineTotal().toString());
                totalLabel.setStyle("-fx-font-weight: bold; -fx-min-width: 60; -fx-alignment: center-right;");

                // Remove button
                javafx.scene.control.Button removeBtn = new javafx.scene.control.Button("✖");
                removeBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 2 6;");
                removeBtn.setOnAction(e -> removeItemFromCart(item));

                container.getChildren().addAll(productInfo, spacer, decreaseBtn, quantityLabel, increaseBtn, totalLabel, removeBtn);
                setGraphic(container);
                setText(null);
            }
        }
    }

    private void createSimpleReportsInterface() {
        try {
            // Create main layout
            javafx.scene.layout.VBox layout = new javafx.scene.layout.VBox(15);
            layout.setPadding(new javafx.geometry.Insets(20));

            // Header
            javafx.scene.layout.HBox header = new javafx.scene.layout.HBox(10);
            header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

            javafx.scene.control.Label title = new javafx.scene.control.Label("Sales Reports & Analytics");
            title.setFont(javafx.scene.text.Font.font("System", javafx.scene.text.FontWeight.BOLD, 18));

            javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
            javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

            javafx.scene.control.Button refreshBtn = new javafx.scene.control.Button("🔄 Refresh");
            refreshBtn.setOnAction(e -> {
                createSimpleReportsInterface(); // Refresh the entire interface
                setStatus("Reports data refreshed");
            });

            javafx.scene.control.Button exportBtn = new javafx.scene.control.Button("📊 Export All");
            exportBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-weight: bold;");
            exportBtn.setOnAction(e -> exportAllReports());

            header.getChildren().addAll(title, spacer, refreshBtn, exportBtn);

            // Date range selection
            javafx.scene.layout.HBox dateRange = new javafx.scene.layout.HBox(10);
            dateRange.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

            javafx.scene.control.Label dateLabel = new javafx.scene.control.Label("Date Range:");
            javafx.scene.control.DatePicker startDate = new javafx.scene.control.DatePicker();
            startDate.setValue(java.time.LocalDate.now().withDayOfMonth(1));
            startDate.setPromptText("Start Date");

            javafx.scene.control.Label toLabel = new javafx.scene.control.Label("to");
            javafx.scene.control.DatePicker endDate = new javafx.scene.control.DatePicker();
            endDate.setValue(java.time.LocalDate.now());
            endDate.setPromptText("End Date");

            javafx.scene.control.Button applyBtn = new javafx.scene.control.Button("Apply");
            applyBtn.setOnAction(e -> generateDateRangeReport(startDate.getValue(), endDate.getValue()));

            javafx.scene.control.Button todayBtn = new javafx.scene.control.Button("Today");
            todayBtn.setOnAction(e -> {
                java.time.LocalDate today = java.time.LocalDate.now();
                startDate.setValue(today);
                endDate.setValue(today);
                generateDateRangeReport(today, today);
            });

            javafx.scene.control.Button thisMonthBtn = new javafx.scene.control.Button("This Month");
            thisMonthBtn.setOnAction(e -> {
                java.time.LocalDate today = java.time.LocalDate.now();
                java.time.LocalDate firstOfMonth = today.withDayOfMonth(1);
                startDate.setValue(firstOfMonth);
                endDate.setValue(today);
                generateDateRangeReport(firstOfMonth, today);
            });

            dateRange.getChildren().addAll(dateLabel, startDate, toLabel, endDate, applyBtn,
                    new javafx.scene.control.Separator(javafx.geometry.Orientation.VERTICAL),
                    todayBtn, thisMonthBtn);

            // Key metrics dashboard
            javafx.scene.layout.GridPane metricsGrid = new javafx.scene.layout.GridPane();
            metricsGrid.setHgap(20);
            metricsGrid.setVgap(20);

            // Create metric cards with real data
            ReportMetrics metrics = calculateReportMetrics(startDate.getValue(), endDate.getValue());
            javafx.scene.layout.VBox salesCard = createMetricCard("Total Sales", "$" + String.format("%.2f", metrics.totalSales), "in selected period", "#2196F3");
            javafx.scene.layout.VBox transactionsCard = createMetricCard("Transactions", String.valueOf(metrics.transactionCount), "completed", "#4CAF50");
            javafx.scene.layout.VBox itemsCard = createMetricCard("Items Sold", String.valueOf(metrics.itemsSold), "total quantity", "#FF9800");
            javafx.scene.layout.VBox avgCard = createMetricCard("Avg Transaction", "$" + String.format("%.2f", metrics.averageTransaction), "per transaction", "#9C27B0");

            metricsGrid.add(salesCard, 0, 0);
            metricsGrid.add(transactionsCard, 1, 0);
            metricsGrid.add(itemsCard, 2, 0);
            metricsGrid.add(avgCard, 3, 0);

            // Reports table with real transaction data
            javafx.scene.control.TableView<com.clothingstore.model.Transaction> reportsTable = new javafx.scene.control.TableView<>();

            javafx.scene.control.TableColumn<com.clothingstore.model.Transaction, String> dateCol = new javafx.scene.control.TableColumn<>("Date");
            dateCol.setCellValueFactory(cellData
                    -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getTransactionDate().toLocalDate().toString()));

            javafx.scene.control.TableColumn<com.clothingstore.model.Transaction, String> transactionCol = new javafx.scene.control.TableColumn<>("Transaction ID");
            transactionCol.setCellValueFactory(cellData
                    -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getTransactionNumber()));

            javafx.scene.control.TableColumn<com.clothingstore.model.Transaction, String> customerCol = new javafx.scene.control.TableColumn<>("Customer");
            customerCol.setCellValueFactory(cellData
                    -> new javafx.beans.property.SimpleStringProperty(cellData.getValue().getCustomerName() != null
                            ? cellData.getValue().getCustomerName() : "Walk-in"));

            javafx.scene.control.TableColumn<com.clothingstore.model.Transaction, String> itemsCol = new javafx.scene.control.TableColumn<>("Items");
            itemsCol.setCellValueFactory(cellData
                    -> new javafx.beans.property.SimpleStringProperty(String.valueOf(cellData.getValue().getItems().size())));

            javafx.scene.control.TableColumn<com.clothingstore.model.Transaction, String> totalCol = new javafx.scene.control.TableColumn<>("Total");
            totalCol.setCellValueFactory(cellData
                    -> new javafx.beans.property.SimpleStringProperty("$" + cellData.getValue().getTotalAmount().toString()));

            dateCol.setPrefWidth(100);
            transactionCol.setPrefWidth(120);
            customerCol.setPrefWidth(150);
            itemsCol.setPrefWidth(80);
            totalCol.setPrefWidth(100);

            reportsTable.getColumns().addAll(dateCol, transactionCol, customerCol, itemsCol, totalCol);
            reportsTable.setPlaceholder(new javafx.scene.control.Label("No transaction data available for selected period"));
            reportsTable.setPrefHeight(300);

            // Load initial transaction data
            loadTransactionData(reportsTable, startDate.getValue(), endDate.getValue());

            // Bottom section with real report buttons
            javafx.scene.layout.HBox bottomSection = new javafx.scene.layout.HBox(10);
            bottomSection.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

            javafx.scene.control.Label summaryLabel = new javafx.scene.control.Label("Summary: " + metrics.transactionCount + " transactions, $" + String.format("%.2f", metrics.totalSales) + " total sales");
            javafx.scene.layout.Region bottomSpacer = new javafx.scene.layout.Region();
            javafx.scene.layout.HBox.setHgrow(bottomSpacer, javafx.scene.layout.Priority.ALWAYS);

            javafx.scene.control.Button customerReportBtn = new javafx.scene.control.Button("👥 Customer Report");
            customerReportBtn.setOnAction(e -> showCustomerDetailedReport());

            javafx.scene.control.Button inventoryReportBtn = new javafx.scene.control.Button("📦 Inventory Report");
            inventoryReportBtn.setOnAction(e -> showInventoryReport());

            javafx.scene.control.Button inventoryMovementBtn = new javafx.scene.control.Button("📊 Inventory Movement");
            inventoryMovementBtn.setOnAction(e -> showInventoryMovementReport());
            inventoryMovementBtn.setStyle("-fx-background-color: #9b59b6; -fx-text-fill: white; -fx-font-weight: bold;");

            javafx.scene.control.Button lowStockBtn = new javafx.scene.control.Button("! Low Stock");
            lowStockBtn.setOnAction(e -> showLowStockReport());

            javafx.scene.control.Button missingItemsBtn = new javafx.scene.control.Button("Missing Items");
            missingItemsBtn.setStyle("-fx-background-color: #f44336; -fx-text-fill: white; -fx-font-weight: bold;");
            missingItemsBtn.setOnAction(e -> showMissingItemsReport());

            bottomSection.getChildren().addAll(summaryLabel, bottomSpacer, customerReportBtn, inventoryReportBtn, inventoryMovementBtn, lowStockBtn, missingItemsBtn);

            layout.getChildren().addAll(header, new javafx.scene.control.Separator(), dateRange,
                    new javafx.scene.control.Separator(), metricsGrid, reportsTable, bottomSection);

            // Add to content area
            contentArea.getChildren().clear();
            contentArea.getChildren().add(layout);

        } catch (Exception e) {
            AlertUtil.showError("Reports Interface Error", "Failed to create reports interface: " + e.getMessage());
        }
    }

    private void createSettingsInterface() {
        try {
            // Create main layout
            javafx.scene.layout.VBox layout = new javafx.scene.layout.VBox(20);
            layout.setPadding(new javafx.geometry.Insets(20));

            // Header
            javafx.scene.layout.HBox header = new javafx.scene.layout.HBox(10);
            header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

            javafx.scene.control.Label title = new javafx.scene.control.Label("System Settings");
            title.setFont(javafx.scene.text.Font.font("System", javafx.scene.text.FontWeight.BOLD, 18));

            javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
            javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

            javafx.scene.control.Button saveBtn = new javafx.scene.control.Button("💾 Save Settings");
            saveBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold;");

            javafx.scene.control.Button resetBtn = new javafx.scene.control.Button("🔄 Reset to Defaults");
            resetBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white;");

            javafx.scene.control.Button dbResetBtn = new javafx.scene.control.Button("Reset Database");
            dbResetBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold;");

            header.getChildren().addAll(title, spacer, saveBtn, resetBtn, dbResetBtn);

            // Create settings form
            javafx.scene.control.ScrollPane scrollPane = new javafx.scene.control.ScrollPane();
            scrollPane.setFitToWidth(true);
            scrollPane.setStyle("-fx-background-color: transparent;");

            javafx.scene.layout.VBox settingsForm = createSettingsForm();
            scrollPane.setContent(settingsForm);

            // Save button action
            saveBtn.setOnAction(e -> saveSettings(settingsForm));

            // Reset button action
            resetBtn.setOnAction(e -> {
                if (AlertUtil.showConfirmation("Reset Settings", "Are you sure you want to reset all settings to defaults?")) {
                    resetSettingsToDefaults(settingsForm);
                }
            });

            // Database reset button action
            dbResetBtn.setOnAction(e -> showDatabaseResetDialog());

            layout.getChildren().addAll(header, new javafx.scene.control.Separator(), scrollPane);

            // Add to content area
            contentArea.getChildren().clear();
            contentArea.getChildren().add(layout);

        } catch (Exception e) {
            AlertUtil.showError("Settings Interface Error", "Failed to create settings interface: " + e.getMessage());
        }
    }

    private javafx.scene.layout.VBox createSettingsForm() {
        javafx.scene.layout.VBox form = new javafx.scene.layout.VBox(15);
        form.setPadding(new javafx.geometry.Insets(20));

        try {
            com.clothingstore.dao.SettingsDAO settingsDAO = com.clothingstore.dao.SettingsDAO.getInstance();

            // POS Settings Section
            form.getChildren().add(createSettingsSection("Point of Sale Settings", "POS", settingsDAO));

            // Store Information Section
            form.getChildren().add(createSettingsSection("Store Information", "Store", settingsDAO));

            // Display Settings Section
            form.getChildren().add(createSettingsSection("Display Settings", "Display", settingsDAO));

            // Inventory Settings Section
            form.getChildren().add(createSettingsSection("Inventory Settings", "Inventory", settingsDAO));

            // Customer Settings Section
            form.getChildren().add(createSettingsSection("Customer Settings", "Customer", settingsDAO));

        } catch (Exception e) {
            AlertUtil.showError("Settings Error", "Failed to load settings: " + e.getMessage());
        }

        return form;
    }

    private javafx.scene.layout.VBox createSettingsSection(String sectionTitle, String category,
            com.clothingstore.dao.SettingsDAO settingsDAO) throws Exception {
        javafx.scene.layout.VBox section = new javafx.scene.layout.VBox(10);
        section.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5;");

        // Section header
        javafx.scene.control.Label sectionLabel = new javafx.scene.control.Label(sectionTitle);
        sectionLabel.setFont(javafx.scene.text.Font.font("System", javafx.scene.text.FontWeight.BOLD, 14));
        sectionLabel.setStyle("-fx-text-fill: #495057;");

        section.getChildren().add(sectionLabel);
        section.getChildren().add(new javafx.scene.control.Separator());

        // Get settings for this category
        java.util.List<com.clothingstore.model.Setting> categorySettings = settingsDAO.findByCategory(category);

        for (com.clothingstore.model.Setting setting : categorySettings) {
            javafx.scene.layout.HBox settingRow = createSettingRow(setting);
            section.getChildren().add(settingRow);
        }

        return section;
    }

    private javafx.scene.layout.HBox createSettingRow(com.clothingstore.model.Setting setting) {
        javafx.scene.layout.HBox row = new javafx.scene.layout.HBox(10);
        row.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Setting label and description
        javafx.scene.layout.VBox labelBox = new javafx.scene.layout.VBox(2);
        javafx.scene.control.Label nameLabel = new javafx.scene.control.Label(setting.getKey().replace("_", " ").toUpperCase());
        nameLabel.setStyle("-fx-font-weight: bold;");

        javafx.scene.control.Label descLabel = new javafx.scene.control.Label(setting.getDescription());
        descLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-size: 11px;");

        labelBox.getChildren().addAll(nameLabel, descLabel);
        labelBox.setPrefWidth(200);

        // Setting input field
        javafx.scene.control.Control inputField = createInputField(setting);
        inputField.setUserData(setting); // Store setting reference

        javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        row.getChildren().addAll(labelBox, spacer, inputField);
        return row;
    }

    private javafx.scene.control.Control createInputField(com.clothingstore.model.Setting setting) {
        switch (setting.getDataType().toUpperCase()) {
            case "BOOLEAN":
                javafx.scene.control.CheckBox checkBox = new javafx.scene.control.CheckBox();
                checkBox.setSelected(setting.getBooleanValue());
                return checkBox;

            case "INTEGER":
                javafx.scene.control.Spinner<Integer> intSpinner = new javafx.scene.control.Spinner<>(0, 999999, setting.getIntValue());
                intSpinner.setEditable(true);
                intSpinner.setPrefWidth(120);
                return intSpinner;

            case "DECIMAL":
                javafx.scene.control.TextField decimalField = new javafx.scene.control.TextField();
                decimalField.setText(setting.getValue());
                decimalField.setPrefWidth(120);
                // Add validation for decimal input
                decimalField.textProperty().addListener((obs, oldVal, newVal) -> {
                    if (!newVal.matches("\\d*\\.?\\d*")) {
                        decimalField.setText(oldVal);
                    }
                });
                return decimalField;

            case "STRING":
            default:
                javafx.scene.control.TextField textField = new javafx.scene.control.TextField();
                textField.setText(setting.getStringValue());
                textField.setPrefWidth(200);
                return textField;
        }
    }

    private void saveSettings(javafx.scene.layout.VBox settingsForm) {
        try {
            com.clothingstore.dao.SettingsDAO settingsDAO = com.clothingstore.dao.SettingsDAO.getInstance();
            java.util.List<String> errors = new java.util.ArrayList<>();
            int savedCount = 0;

            // Traverse the form to find all input fields
            for (javafx.scene.Node sectionNode : settingsForm.getChildren()) {
                if (sectionNode instanceof javafx.scene.layout.VBox) {
                    javafx.scene.layout.VBox section = (javafx.scene.layout.VBox) sectionNode;

                    for (javafx.scene.Node rowNode : section.getChildren()) {
                        if (rowNode instanceof javafx.scene.layout.HBox) {
                            javafx.scene.layout.HBox row = (javafx.scene.layout.HBox) rowNode;

                            // Find the input field in this row
                            for (javafx.scene.Node node : row.getChildren()) {
                                if (node instanceof javafx.scene.control.Control && node.getUserData() instanceof com.clothingstore.model.Setting) {
                                    com.clothingstore.model.Setting setting = (com.clothingstore.model.Setting) node.getUserData();
                                    String newValue = extractValueFromControl((javafx.scene.control.Control) node);

                                    // Validate the new value
                                    setting.setValue(newValue);
                                    if (!setting.isValid()) {
                                        errors.add(setting.getKey() + ": " + setting.getValidationError());
                                        continue;
                                    }

                                    // Save the setting
                                    settingsDAO.setValue(setting.getKey(), newValue);
                                    savedCount++;
                                }
                            }
                        }
                    }
                }
            }

            if (!errors.isEmpty()) {
                AlertUtil.showWarning("Validation Errors", "Some settings could not be saved:\n\n" + String.join("\n", errors));
            }

            if (savedCount > 0) {
                // Clear cache to force reload
                settingsDAO.clearCache();
                AlertUtil.showSuccess("Settings Saved", savedCount + " settings saved successfully!");
                setStatus("Settings saved successfully");
            }

        } catch (Exception e) {
            AlertUtil.showError("Save Error", "Failed to save settings: " + e.getMessage());
        }
    }

    private String extractValueFromControl(javafx.scene.control.Control control) {
        if (control instanceof javafx.scene.control.TextField) {
            return ((javafx.scene.control.TextField) control).getText();
        } else if (control instanceof javafx.scene.control.CheckBox) {
            return String.valueOf(((javafx.scene.control.CheckBox) control).isSelected());
        } else if (control instanceof javafx.scene.control.Spinner) {
            return String.valueOf(((javafx.scene.control.Spinner<?>) control).getValue());
        }
        return "";
    }

    private void resetSettingsToDefaults(javafx.scene.layout.VBox settingsForm) {
        try {
            com.clothingstore.dao.SettingsDAO settingsDAO = com.clothingstore.dao.SettingsDAO.getInstance();
            java.util.List<com.clothingstore.model.Setting> allSettings = settingsDAO.findAll();

            for (com.clothingstore.model.Setting setting : allSettings) {
                if (setting.getDefaultValue() != null) {
                    settingsDAO.setValue(setting.getKey(), setting.getDefaultValue());
                }
            }

            // Clear cache and refresh the form
            settingsDAO.clearCache();

            // Recreate the settings interface to show updated values
            createSettingsInterface();

            AlertUtil.showSuccess("Settings Reset", "All settings have been reset to their default values!");
            setStatus("Settings reset to defaults");

        } catch (Exception e) {
            AlertUtil.showError("Reset Error", "Failed to reset settings: " + e.getMessage());
        }
    }

    private void showDatabaseResetDialog() {
        try {
            // Load the database reset dialog
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/DatabaseResetDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            DatabaseResetDialogController controller = loader.getController();

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Database Reset - DANGER ZONE");
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(contentArea.getScene().getWindow());
            stage.setResizable(false);

            stage.showAndWait();

            // Check if reset was completed
            if (controller.isResetCompleted()) {
                // Refresh any loaded content
                showDashboardPage();
                AlertUtil.showInfo("Database Reset Complete",
                        "Database has been reset successfully!\n\n"
                        + "The application has been refreshed to reflect the changes.");
            }

        } catch (Exception e) {
            AlertUtil.showError("Database Reset Error", "Failed to show database reset dialog: " + e.getMessage());
        }
    }

    // Report metrics helper class
    private static class ReportMetrics {

        double totalSales = 0.0;
        int transactionCount = 0;
        int itemsSold = 0;
        double averageTransaction = 0.0;
    }

    private ReportMetrics calculateReportMetrics(java.time.LocalDate startDate, java.time.LocalDate endDate) {
        ReportMetrics metrics = new ReportMetrics();

        try {
            com.clothingstore.dao.TransactionDAO transactionDAO = com.clothingstore.dao.TransactionDAO.getInstance();
            java.time.LocalDateTime startDateTime = startDate.atStartOfDay();
            java.time.LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

            java.util.List<com.clothingstore.model.Transaction> transactions
                    = transactionDAO.findByDateRange(startDateTime, endDateTime);

            metrics.transactionCount = transactions.size();

            for (com.clothingstore.model.Transaction transaction : transactions) {
                metrics.totalSales += transaction.getTotalAmount().doubleValue();
                metrics.itemsSold += transaction.getItems().size();
            }

            if (metrics.transactionCount > 0) {
                metrics.averageTransaction = metrics.totalSales / metrics.transactionCount;
            }

        } catch (Exception e) {
            System.err.println("Error calculating report metrics: " + e.getMessage());
        }

        return metrics;
    }

    private void loadTransactionData(javafx.scene.control.TableView<com.clothingstore.model.Transaction> table,
            java.time.LocalDate startDate, java.time.LocalDate endDate) {
        try {
            com.clothingstore.dao.TransactionDAO transactionDAO = com.clothingstore.dao.TransactionDAO.getInstance();
            java.time.LocalDateTime startDateTime = startDate.atStartOfDay();
            java.time.LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

            java.util.List<com.clothingstore.model.Transaction> transactions
                    = transactionDAO.findByDateRange(startDateTime, endDateTime);

            table.getItems().clear();
            table.getItems().addAll(transactions);

        } catch (Exception e) {
            AlertUtil.showError("Data Loading Error", "Failed to load transaction data: " + e.getMessage());
        }
    }

    private void generateDateRangeReport(java.time.LocalDate startDate, java.time.LocalDate endDate) {
        // This will refresh the entire reports interface with new date range
        createSimpleReportsInterface();
        setStatus("Report generated for " + startDate + " to " + endDate);
    }

    private void exportAllReports() {
        try {
            // Create export dialog
            javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
            fileChooser.setTitle("Export Reports");
            fileChooser.getExtensionFilters().add(
                    new javafx.stage.FileChooser.ExtensionFilter("CSV Files", "*.csv"));
            fileChooser.setInitialFileName("sales_report_" + java.time.LocalDate.now() + ".csv");

            java.io.File file = fileChooser.showSaveDialog(contentArea.getScene().getWindow());
            if (file != null) {
                exportReportsToCSV(file);
                AlertUtil.showSuccess("Export Complete", "Reports exported successfully to: " + file.getName());
            }

        } catch (Exception e) {
            AlertUtil.showError("Export Error", "Failed to export reports: " + e.getMessage());
        }
    }

    private void exportReportsToCSV(java.io.File file) throws Exception {
        try (java.io.PrintWriter writer = new java.io.PrintWriter(file)) {
            // Export transaction data
            writer.println("Transaction Reports");
            writer.println("Date,Transaction ID,Customer,Items,Total");

            com.clothingstore.dao.TransactionDAO transactionDAO = com.clothingstore.dao.TransactionDAO.getInstance();
            java.util.List<com.clothingstore.model.Transaction> transactions = transactionDAO.findAll();

            for (com.clothingstore.model.Transaction transaction : transactions) {
                writer.printf("%s,%s,%s,%d,$%.2f%n",
                        transaction.getTransactionDate().toLocalDate(),
                        transaction.getTransactionNumber(),
                        transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in",
                        transaction.getItems().size(),
                        transaction.getTotalAmount().doubleValue());
            }

            writer.println();
            writer.println("Summary");
            ReportMetrics metrics = calculateReportMetrics(
                    java.time.LocalDate.now().minusDays(30),
                    java.time.LocalDate.now());
            writer.printf("Total Sales,$%.2f%n", metrics.totalSales);
            writer.printf("Total Transactions,%d%n", metrics.transactionCount);
            writer.printf("Items Sold,%d%n", metrics.itemsSold);
            writer.printf("Average Transaction,$%.2f%n", metrics.averageTransaction);
        }
    }

    private void showCustomerDetailedReport() {
        try {
            // Create customer detailed report dialog
            javafx.scene.control.Dialog<Void> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Customer Detailed Report");
            dialog.setHeaderText("Select a customer to view detailed purchase history");

            // Create customer selection
            com.clothingstore.dao.CustomerDAO customerDAO = com.clothingstore.dao.CustomerDAO.getInstance();
            java.util.List<com.clothingstore.model.Customer> customers = customerDAO.findAll();

            javafx.scene.control.ComboBox<com.clothingstore.model.Customer> customerCombo
                    = new javafx.scene.control.ComboBox<>();
            customerCombo.getItems().addAll(customers);
            customerCombo.setConverter(new javafx.util.StringConverter<com.clothingstore.model.Customer>() {
                @Override
                public String toString(com.clothingstore.model.Customer customer) {
                    return customer != null ? customer.getFullName() + " (Phone: " + customer.getPhone() + ")" : "";
                }

                @Override
                public com.clothingstore.model.Customer fromString(String string) {
                    return null;
                }
            });

            javafx.scene.layout.VBox content = new javafx.scene.layout.VBox(10);
            content.setPadding(new javafx.geometry.Insets(20));
            content.getChildren().addAll(
                    new javafx.scene.control.Label("Select Customer:"),
                    customerCombo
            );

            // Report area
            javafx.scene.control.TextArea reportArea = new javafx.scene.control.TextArea();
            reportArea.setPrefRowCount(20);
            reportArea.setPrefColumnCount(60);
            reportArea.setEditable(false);

            javafx.scene.control.Button generateBtn = new javafx.scene.control.Button("Generate Report");
            generateBtn.setOnAction(e -> {
                com.clothingstore.model.Customer selectedCustomer = customerCombo.getValue();
                if (selectedCustomer != null) {
                    generateCustomerReport(selectedCustomer, reportArea);
                }
            });

            content.getChildren().addAll(generateBtn, reportArea);
            dialog.getDialogPane().setContent(content);
            dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);
            dialog.showAndWait();

        } catch (Exception e) {
            AlertUtil.showError("Report Error", "Failed to show customer report: " + e.getMessage());
        }
    }

    private void generateCustomerReport(com.clothingstore.model.Customer customer, javafx.scene.control.TextArea reportArea) {
        try {
            StringBuilder report = new StringBuilder();
            report.append("CUSTOMER DETAILED REPORT\n");
            report.append("========================\n\n");

            report.append("Customer Information:\n");
            report.append("Name: ").append(customer.getFullName()).append("\n");
            report.append("Phone: ").append(customer.getPhone()).append("\n");
            report.append("Phone: ").append(customer.getPhone()).append("\n");
            report.append("Registration Date: ").append(customer.getRegistrationDate().toLocalDate()).append("\n");
            report.append("Loyalty Points: ").append(customer.getLoyaltyPoints()).append("\n");
            report.append("Total Spent: $").append(String.format("%.2f", customer.getTotalSpent())).append("\n");
            report.append("Total Purchases: ").append(customer.getTotalPurchases()).append("\n\n");

            // Get customer transactions
            com.clothingstore.dao.TransactionDAO transactionDAO = com.clothingstore.dao.TransactionDAO.getInstance();
            java.util.List<com.clothingstore.model.Transaction> transactions = transactionDAO.findByCustomerId(customer.getId());

            report.append("PURCHASE HISTORY:\n");
            report.append("-----------------\n");

            if (transactions.isEmpty()) {
                report.append("No transactions found for this customer.\n");
            } else {
                double totalFromTransactions = 0.0;
                for (com.clothingstore.model.Transaction transaction : transactions) {
                    report.append("\nTransaction: ").append(transaction.getTransactionNumber()).append("\n");
                    report.append("Date: ").append(transaction.getTransactionDate().toLocalDate()).append("\n");
                    report.append("Payment Method: ").append(transaction.getPaymentMethod()).append("\n");
                    report.append("Total: $").append(transaction.getTotalAmount()).append("\n");

                    report.append("Items:\n");
                    for (com.clothingstore.model.TransactionItem item : transaction.getItems()) {
                        report.append("  - ").append(item.getProductName())
                                .append(" (Qty: ").append(item.getQuantity())
                                .append(", Price: $").append(item.getUnitPrice())
                                .append(", Total: $").append(item.getLineTotal()).append(")\n");
                    }

                    totalFromTransactions += transaction.getTotalAmount().doubleValue();
                }

                report.append("\nSUMMARY:\n");
                report.append("--------\n");
                report.append("Total Transactions: ").append(transactions.size()).append("\n");
                report.append("Total Amount from Transactions: $").append(String.format("%.2f", totalFromTransactions)).append("\n");
                report.append("Average Transaction: $").append(String.format("%.2f", totalFromTransactions / transactions.size())).append("\n");
            }

            reportArea.setText(report.toString());

        } catch (Exception e) {
            reportArea.setText("Error generating customer report: " + e.getMessage());
        }
    }

    @FXML
    private void showInventoryReport() {
        try {
            // Create inventory report dialog
            javafx.scene.control.Dialog<Void> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Inventory Report");
            dialog.setHeaderText("Current Inventory Status");

            javafx.scene.control.TextArea reportArea = new javafx.scene.control.TextArea();
            reportArea.setPrefRowCount(25);
            reportArea.setPrefColumnCount(80);
            reportArea.setEditable(false);

            generateInventoryReport(reportArea);

            dialog.getDialogPane().setContent(reportArea);
            dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);
            dialog.showAndWait();

        } catch (Exception e) {
            AlertUtil.showError("Report Error", "Failed to show inventory report: " + e.getMessage());
        }
    }

    @FXML
    private void showInventoryMovementReport() {
        try {
            System.out.println("Creating programmatic Inventory Movement Report...");
            createProgrammaticInventoryMovementReport();
            System.out.println("Successfully created programmatic version!");
        } catch (Exception e) {
            System.err.println("Failed to create programmatic version: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Loading Error",
                    "Failed to load Inventory Movement Report.\n\n"
                    + "Error details:\n" + e.getMessage() + "\n\n"
                    + "This appears to be a JavaFX FXML loading issue.\n"
                    + "The report functionality is working, but there's a UI loading problem.");
        }
    }

    @FXML
    private void showMissingItemsReport() {
        try {
            System.out.println("Loading Missing Items Report...");
            loadContent("MissingItems.fxml", "Missing Items Report");
            System.out.println("Successfully loaded Missing Items Report!");
        } catch (Exception e) {
            System.err.println("Failed to load Missing Items Report: " + e.getMessage());
            e.printStackTrace();
            AlertUtil.showError("Loading Error",
                    "Failed to load Missing Items Report.\n\n"
                    + "Error details:\n" + e.getMessage());
        }
    }

    private void createProgrammaticInventoryMovementReport() {
        try {
            // Create main layout
            javafx.scene.layout.VBox layout = new javafx.scene.layout.VBox(15);
            layout.setPadding(new javafx.geometry.Insets(20));

            // Header
            javafx.scene.layout.HBox header = new javafx.scene.layout.HBox(10);
            header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

            javafx.scene.control.Label title = new javafx.scene.control.Label("Inventory Movement Report");
            title.setFont(javafx.scene.text.Font.font("System", javafx.scene.text.FontWeight.BOLD, 18));

            javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
            javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

            javafx.scene.control.Button refreshBtn = new javafx.scene.control.Button("Refresh");
            refreshBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white;");
            refreshBtn.setOnAction(e -> createProgrammaticInventoryMovementReport());

            javafx.scene.control.Button exportBtn = new javafx.scene.control.Button("Export");
            exportBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white;");
            exportBtn.setOnAction(e -> AlertUtil.showInfo("Export", "Export functionality will be implemented here."));

            header.getChildren().addAll(title, spacer, refreshBtn, exportBtn);

            // Date Range Section
            javafx.scene.layout.HBox dateSection = new javafx.scene.layout.HBox(15);
            dateSection.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

            javafx.scene.control.Label dateLabel = new javafx.scene.control.Label("Date Range:");
            javafx.scene.control.DatePicker startDate = new javafx.scene.control.DatePicker();
            startDate.setValue(java.time.LocalDate.now().minusDays(30));
            javafx.scene.control.Label toLabel = new javafx.scene.control.Label("to");
            javafx.scene.control.DatePicker endDate = new javafx.scene.control.DatePicker();
            endDate.setValue(java.time.LocalDate.now());

            javafx.scene.control.Button generateBtn = new javafx.scene.control.Button("Generate Report");
            generateBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white;");
            generateBtn.setOnAction(e -> generateInventoryMovementData(layout, startDate.getValue(), endDate.getValue()));

            dateSection.getChildren().addAll(dateLabel, startDate, toLabel, endDate, generateBtn);

            // Summary Statistics Section
            javafx.scene.layout.HBox summarySection = createSummarySection();

            // Report Content Area
            javafx.scene.control.TextArea reportArea = new javafx.scene.control.TextArea();
            reportArea.setPrefRowCount(20);
            reportArea.setEditable(false);
            reportArea.setStyle("-fx-font-family: monospace;");

            // Initial data load
            generateInventoryMovementData(reportArea, startDate.getValue(), endDate.getValue());

            layout.getChildren().addAll(
                    header,
                    new javafx.scene.control.Separator(),
                    dateSection,
                    new javafx.scene.control.Separator(),
                    summarySection,
                    new javafx.scene.control.Separator(),
                    new javafx.scene.control.Label("Inventory Movement Details:"),
                    reportArea
            );

            // Add to content area
            contentArea.getChildren().clear();
            contentArea.getChildren().add(layout);

        } catch (Exception e) {
            AlertUtil.showError("Report Creation Error", "Failed to create inventory movement report: " + e.getMessage());
        }
    }

    private javafx.scene.layout.HBox createSummarySection() {
        javafx.scene.layout.HBox summarySection = new javafx.scene.layout.HBox(20);
        summarySection.setAlignment(javafx.geometry.Pos.CENTER);
        summarySection.setPadding(new javafx.geometry.Insets(15));
        summarySection.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1;");

        // Items Sold Card
        javafx.scene.layout.VBox soldCard = createMetricCard("Items Sold", "0", "Completed transactions", "#27ae60");

        // Items Returned Card
        javafx.scene.layout.VBox returnedCard = createMetricCard("Items Returned", "0", "Refunds & cancellations", "#e74c3c");

        // Net Movement Card
        javafx.scene.layout.VBox netCard = createMetricCard("Net Movement", "0", "Overall inventory change", "#34495e");

        summarySection.getChildren().addAll(soldCard, returnedCard, netCard);
        return summarySection;
    }

    private void generateInventoryMovementData(Object target, java.time.LocalDate startDate, java.time.LocalDate endDate) {
        try {
            com.clothingstore.dao.InventoryMovementDAO movementDAO = com.clothingstore.dao.InventoryMovementDAO.getInstance();

            java.time.LocalDateTime startDateTime = startDate.atStartOfDay();
            java.time.LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

            // Get movement data
            java.util.List<com.clothingstore.model.InventoryMovement> soldItems
                    = movementDAO.getItemsSoldProcessed(startDateTime, endDateTime);
            java.util.List<com.clothingstore.model.InventoryMovement> returnedItems
                    = movementDAO.getItemsReturnedRefunded(startDateTime, endDateTime);

            // Generate report text
            StringBuilder report = new StringBuilder();
            report.append("INVENTORY MOVEMENT REPORT\n");
            report.append("========================\n\n");
            report.append("Period: ").append(startDate).append(" to ").append(endDate).append("\n\n");

            // Items Sold Section
            report.append("ITEMS SOLD/PROCESSED (").append(soldItems.size()).append(" movements)\n");
            report.append("-".repeat(80)).append("\n");
            report.append(String.format("%-15s %-20s %-10s %-6s %-10s %-12s %-15s\n",
                    "Date", "Product", "SKU", "Qty", "Price", "Total", "Customer"));
            report.append("-".repeat(80)).append("\n");

            double totalSoldValue = 0;
            int totalSoldQty = 0;
            for (com.clothingstore.model.InventoryMovement item : soldItems) {
                report.append(String.format("%-15s %-20s %-10s %-6d $%-9.2f $%-11.2f %-15s\n",
                        item.getFormattedMovementDate(),
                        truncateString(item.getProductName(), 20),
                        item.getProductSku(),
                        item.getQuantity(),
                        item.getUnitPrice().doubleValue(),
                        item.getLineTotal().doubleValue(),
                        truncateString(item.getCustomerName(), 15)));

                totalSoldValue += item.getLineTotal().doubleValue();
                totalSoldQty += item.getQuantity();
            }

            report.append("-".repeat(80)).append("\n");
            report.append(String.format("SOLD TOTALS: %d movements, %d units, $%.2f value\n\n",
                    soldItems.size(), totalSoldQty, totalSoldValue));

            // Items Returned Section
            report.append("ITEMS RETURNED/REFUNDED (").append(returnedItems.size()).append(" movements)\n");
            report.append("-".repeat(90)).append("\n");
            report.append(String.format("%-15s %-20s %-10s %-6s %-10s %-12s %-12s %-15s\n",
                    "Date", "Product", "SKU", "Qty", "Price", "Total", "Type", "Reason"));
            report.append("-".repeat(90)).append("\n");

            double totalReturnedValue = 0;
            int totalReturnedQty = 0;
            for (com.clothingstore.model.InventoryMovement item : returnedItems) {
                report.append(String.format("%-15s %-20s %-10s %-6d $%-9.2f $%-11.2f %-12s %-15s\n",
                        item.getFormattedMovementDate(),
                        truncateString(item.getProductName(), 20),
                        item.getProductSku(),
                        item.getQuantity(),
                        item.getUnitPrice().doubleValue(),
                        item.getLineTotal().doubleValue(),
                        item.getMovementType(),
                        truncateString(item.getReason() != null ? item.getReason() : "N/A", 15)));

                totalReturnedValue += item.getLineTotal().doubleValue();
                totalReturnedQty += item.getQuantity();
            }

            report.append("-".repeat(90)).append("\n");
            report.append(String.format("RETURNED TOTALS: %d movements, %d units, $%.2f value\n\n",
                    returnedItems.size(), totalReturnedQty, totalReturnedValue));

            // Summary
            int netQty = totalSoldQty - totalReturnedQty;
            double netValue = totalSoldValue - totalReturnedValue;
            double returnRate = totalSoldQty > 0 ? (totalReturnedQty * 100.0 / totalSoldQty) : 0;

            report.append("BUSINESS INTELLIGENCE SUMMARY\n");
            report.append("=============================\n");
            report.append("Net Units: ").append(netQty).append(" units\n");
            report.append("Net Value: $").append(String.format("%.2f", netValue)).append("\n");
            report.append("Return Rate: ").append(String.format("%.2f%%", returnRate)).append("\n\n");

            if (returnRate > 30) {
                report.append("WARNING: Very high return rate - immediate action required\n");
            } else if (returnRate > 15) {
                report.append("CAUTION: High return rate - monitor closely\n");
            } else {
                report.append("INFO: Return rate within normal range\n");
            }

            // Set the report text
            if (target instanceof javafx.scene.control.TextArea) {
                ((javafx.scene.control.TextArea) target).setText(report.toString());
            }

        } catch (Exception e) {
            String errorMsg = "Error generating inventory movement data: " + e.getMessage();
            if (target instanceof javafx.scene.control.TextArea) {
                ((javafx.scene.control.TextArea) target).setText(errorMsg);
            }
            AlertUtil.showError("Report Error", errorMsg);
        }
    }

    private String truncateString(String text, int maxLength) {
        if (text == null) {
            return "N/A";
        }
        return text.length() > maxLength ? text.substring(0, maxLength - 3) + "..." : text;
    }

    private void generateInventoryReport(javafx.scene.control.TextArea reportArea) {
        try {
            StringBuilder report = new StringBuilder();
            report.append("INVENTORY REPORT\n");
            report.append("================\n\n");

            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            java.util.List<com.clothingstore.model.Product> products = productDAO.findAll();

            // Get low stock threshold from settings
            com.clothingstore.dao.SettingsDAO settingsDAO = com.clothingstore.dao.SettingsDAO.getInstance();
            int lowStockThreshold = settingsDAO.getIntValue("low_stock_threshold", 10);

            int totalProducts = products.size();
            int lowStockCount = 0;
            int outOfStockCount = 0;
            double totalInventoryValue = 0.0;

            report.append("PRODUCT INVENTORY:\n");
            report.append("------------------\n");
            report.append(String.format("%-20s %-10s %-10s %-12s %-15s %s\n",
                    "Product", "SKU", "Stock", "Price", "Value", "Status"));
            report.append("-".repeat(80)).append("\n");

            for (com.clothingstore.model.Product product : products) {
                String status = "OK";
                if (product.getStockQuantity() == 0) {
                    status = "OUT OF STOCK";
                    outOfStockCount++;
                } else if (product.getStockQuantity() <= lowStockThreshold) {
                    status = "LOW STOCK";
                    lowStockCount++;
                }

                double productValue = product.getPrice().doubleValue() * product.getStockQuantity();
                totalInventoryValue += productValue;

                report.append(String.format("%-20s %-10s %-10d $%-11.2f $%-14.2f %s\n",
                        product.getName().length() > 20 ? product.getName().substring(0, 17) + "..." : product.getName(),
                        product.getSku(),
                        product.getStockQuantity(),
                        product.getPrice().doubleValue(),
                        productValue,
                        status));
            }

            report.append("\nINVENTORY SUMMARY:\n");
            report.append("------------------\n");
            report.append("Total Products: ").append(totalProducts).append("\n");
            report.append("Products in Stock: ").append(totalProducts - outOfStockCount).append("\n");
            report.append("Low Stock Items: ").append(lowStockCount).append("\n");
            report.append("Out of Stock Items: ").append(outOfStockCount).append("\n");
            report.append("Total Inventory Value: $").append(String.format("%.2f", totalInventoryValue)).append("\n");
            report.append("Low Stock Threshold: ").append(lowStockThreshold).append(" units\n");

            reportArea.setText(report.toString());

        } catch (Exception e) {
            reportArea.setText("Error generating inventory report: " + e.getMessage());
        }
    }

    @FXML
    private void showLowStockReport() {
        try {
            // Create low stock report dialog
            javafx.scene.control.Dialog<Void> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Low Stock Alert");
            dialog.setHeaderText("Products with Low Stock Levels");

            javafx.scene.control.TextArea reportArea = new javafx.scene.control.TextArea();
            reportArea.setPrefRowCount(20);
            reportArea.setPrefColumnCount(60);
            reportArea.setEditable(false);

            generateLowStockReport(reportArea);

            dialog.getDialogPane().setContent(reportArea);
            dialog.getDialogPane().getButtonTypes().add(javafx.scene.control.ButtonType.CLOSE);
            dialog.showAndWait();

        } catch (Exception e) {
            AlertUtil.showError("Report Error", "Failed to show low stock report: " + e.getMessage());
        }
    }

    private void generateLowStockReport(javafx.scene.control.TextArea reportArea) {
        try {
            StringBuilder report = new StringBuilder();
            report.append("LOW STOCK ALERT REPORT\n");
            report.append("======================\n\n");

            com.clothingstore.dao.ProductDAO productDAO = com.clothingstore.dao.ProductDAO.getInstance();
            com.clothingstore.dao.SettingsDAO settingsDAO = com.clothingstore.dao.SettingsDAO.getInstance();

            int lowStockThreshold = settingsDAO.getIntValue("low_stock_threshold", 10);
            java.util.List<com.clothingstore.model.Product> allProducts = productDAO.findAll();
            java.util.List<com.clothingstore.model.Product> lowStockProducts = new java.util.ArrayList<>();

            for (com.clothingstore.model.Product product : allProducts) {
                if (product.getStockQuantity() <= lowStockThreshold) {
                    lowStockProducts.add(product);
                }
            }

            report.append("Low Stock Threshold: ").append(lowStockThreshold).append(" units\n");
            report.append("Date: ").append(java.time.LocalDate.now()).append("\n\n");

            if (lowStockProducts.isEmpty()) {
                report.append("OK - No products are currently below the low stock threshold.\n");
                report.append("All inventory levels are adequate.\n");
            } else {
                report.append("WARNING - PRODUCTS REQUIRING ATTENTION:\n");
                report.append("-".repeat(50)).append("\n");
                report.append(String.format("%-20s %-10s %-10s %s\n", "Product", "SKU", "Stock", "Status"));
                report.append("-".repeat(50)).append("\n");

                for (com.clothingstore.model.Product product : lowStockProducts) {
                    String status = product.getStockQuantity() == 0 ? "OUT OF STOCK" : "LOW STOCK";
                    report.append(String.format("%-20s %-10s %-10d %s\n",
                            product.getName().length() > 20 ? product.getName().substring(0, 17) + "..." : product.getName(),
                            product.getSku(),
                            product.getStockQuantity(),
                            status));
                }

                report.append("\nSUMMARY:\n");
                report.append("--------\n");
                report.append("Total Low Stock Items: ").append(lowStockProducts.size()).append("\n");

                long outOfStock = lowStockProducts.stream().filter(p -> p.getStockQuantity() == 0).count();
                report.append("Out of Stock: ").append(outOfStock).append("\n");
                report.append("Low Stock: ").append(lowStockProducts.size() - outOfStock).append("\n");

                report.append("\nRECOMMENDATIONS:\n");
                report.append("---------------\n");
                report.append("• Review and reorder low stock items\n");
                report.append("• Consider adjusting reorder points\n");
                report.append("• Monitor sales velocity for these products\n");
            }

            reportArea.setText(report.toString());

        } catch (Exception e) {
            reportArea.setText("Error generating low stock report: " + e.getMessage());
        }
    }

    private javafx.scene.layout.VBox createMetricCard(String title, String value, String subtitle, String color) {
        javafx.scene.layout.VBox card = new javafx.scene.layout.VBox(5);
        card.setAlignment(javafx.geometry.Pos.CENTER);
        card.setPadding(new javafx.geometry.Insets(15));
        card.setStyle("-fx-background-color: white; -fx-border-color: " + color + "; -fx-border-width: 2; -fx-border-radius: 5; -fx-background-radius: 5;");
        card.setPrefWidth(150);

        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label(title);
        titleLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666;");

        javafx.scene.control.Label valueLabel = new javafx.scene.control.Label(value);
        valueLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");

        javafx.scene.control.Label subtitleLabel = new javafx.scene.control.Label(subtitle);
        subtitleLabel.setStyle("-fx-font-size: 10px; -fx-text-fill: #999;");

        card.getChildren().addAll(titleLabel, valueLabel, subtitleLabel);
        return card;
    }

    public void shutdown() {
        if (clockTimer != null) {
            clockTimer.cancel();
        }
    }

    public void showPointOfSaleAndInitiateRefund(com.clothingstore.model.Transaction transaction) {
        try {
            // Check if this is an installment transaction and show appropriate dialog
            if (transaction.isInstallmentTransaction()) {
                showSmartInstallmentRefundDialog(transaction);
            } else {
                // Load the standard refund dialog for regular transactions
                javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/RefundDialog.fxml"));
                javafx.scene.Parent root = loader.load();

                com.clothingstore.view.RefundDialogController controller = loader.getController();
                controller.setTransaction(transaction);

                // Create and show the dialog
                javafx.stage.Stage stage = new javafx.stage.Stage();
                stage.setTitle("Process Refund - " + transaction.getTransactionNumber());
                stage.setScene(new javafx.scene.Scene(root));
                stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
                stage.initOwner(contentArea.getScene().getWindow());
                stage.setResizable(false);

                stage.showAndWait();

                // Check if refund was processed and refresh displays if needed
                if (controller.isRefundProcessed()) {
                    // Refresh transaction history or other relevant displays
                    AlertUtil.showInfo("Refund Complete", "Refund has been processed successfully.");
                }
            }
        } catch (Exception e) {
            AlertUtil.showError("Refund Error", "Failed to open refund dialog: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Show smart refund dialog specifically for installment transactions
     */
    private void showSmartInstallmentRefundDialog(com.clothingstore.model.Transaction transaction) {
        try {
            // Create installment refund dialog
            javafx.scene.control.Dialog<InstallmentRefundRequest> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Smart Installment Refund");
            dialog.setHeaderText("Processing refund for installment transaction: " + transaction.getTransactionNumber());

            // Create dialog content
            javafx.scene.layout.GridPane grid = new javafx.scene.layout.GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));

            // Transaction info (read-only)
            javafx.scene.control.Label transactionInfoLabel = new javafx.scene.control.Label(
                    String.format("Transaction: %s\nTotal Amount: $%.2f\nAmount Paid: $%.2f\nInstallment Status: %s",
                            transaction.getTransactionNumber(),
                            transaction.getTotalAmount().doubleValue(),
                            transaction.getAmountPaid() != null ? transaction.getAmountPaid().doubleValue() : 0.0,
                            transaction.getInstallmentStatus()));
            transactionInfoLabel.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6;");

            // Refund amount field
            javafx.scene.control.TextField refundAmountField = new javafx.scene.control.TextField();
            refundAmountField.setPromptText("Refund amount (leave empty for full refund of paid amount)");

            // Reason field
            javafx.scene.control.TextArea reasonArea = new javafx.scene.control.TextArea();
            reasonArea.setPromptText("Refund reason");
            reasonArea.setPrefRowCount(3);

            // Cashier field
            javafx.scene.control.TextField cashierField = new javafx.scene.control.TextField();
            cashierField.setPromptText("Cashier name");
            cashierField.setText(getCurrentUser());

            grid.add(new javafx.scene.control.Label("Transaction Info:"), 0, 0);
            grid.add(transactionInfoLabel, 1, 0);
            grid.add(new javafx.scene.control.Label("Refund Amount:"), 0, 1);
            grid.add(refundAmountField, 1, 1);
            grid.add(new javafx.scene.control.Label("Reason:"), 0, 2);
            grid.add(reasonArea, 1, 2);
            grid.add(new javafx.scene.control.Label("Cashier:"), 0, 3);
            grid.add(cashierField, 1, 3);

            dialog.getDialogPane().setContent(grid);

            // Add buttons
            javafx.scene.control.ButtonType processButtonType = new javafx.scene.control.ButtonType("Process Refund", javafx.scene.control.ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(processButtonType, javafx.scene.control.ButtonType.CANCEL);

            // Convert result
            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == processButtonType) {
                    try {
                        java.math.BigDecimal refundAmount = null;
                        if (!refundAmountField.getText().trim().isEmpty()) {
                            refundAmount = new java.math.BigDecimal(refundAmountField.getText().trim());
                        }
                        String reason = reasonArea.getText().trim();
                        String cashier = cashierField.getText().trim();

                        if (reason.isEmpty()) {
                            AlertUtil.showError("Validation Error", "Please provide a reason for the refund.");
                            return null;
                        }

                        return new InstallmentRefundRequest(refundAmount, reason, cashier);
                    } catch (NumberFormatException e) {
                        AlertUtil.showError("Validation Error", "Invalid refund amount format.");
                        return null;
                    }
                }
                return null;
            });

            java.util.Optional<InstallmentRefundRequest> result = dialog.showAndWait();
            if (result.isPresent()) {
                processSmartInstallmentRefund(transaction, result.get());
            }

        } catch (Exception e) {
            AlertUtil.showError("Error", "Failed to show installment refund dialog: " + e.getMessage());
        }
    }

    /**
     * Process smart installment refund
     */
    private void processSmartInstallmentRefund(com.clothingstore.model.Transaction transaction, InstallmentRefundRequest request) {
        try {
            // Determine refund amount if not specified
            java.math.BigDecimal refundAmount = request.getRefundAmount();
            if (refundAmount == null) {
                // Full refund of amount paid
                refundAmount = transaction.getAmountPaid() != null ? transaction.getAmountPaid() : java.math.BigDecimal.ZERO;
            }

            // Use smart refund service
            com.clothingstore.service.RefundService refundService = com.clothingstore.service.RefundService.getInstance();
            com.clothingstore.model.RefundResult result = refundService.processSmartRefund(
                    transaction, refundAmount, request.getReason(), request.getCashier());

            if (result.isSuccess()) {
                AlertUtil.showSuccess("Refund Successful", "Installment refund processed successfully!\n\n" + result.getMessage());

                // Refresh any displayed data
                refreshCurrentView();
            } else {
                AlertUtil.showError("Refund Failed", "Failed to process installment refund:\n\n" + result.getMessage());
            }

        } catch (Exception e) {
            AlertUtil.showError("Error", "Error processing installment refund: " + e.getMessage());
        }
    }

    /**
     * Get current user name (placeholder - implement based on your
     * authentication system)
     */
    private String getCurrentUser() {
        // TODO: Implement based on your authentication system
        return "System User";
    }

    /**
     * Refresh current view after refund processing
     */
    private void refreshCurrentView() {
        // TODO: Implement view refresh logic based on current displayed content
        System.out.println("Refreshing view after refund processing...");
    }

    /**
     * Inner class for installment refund request data
     */
    private static class InstallmentRefundRequest {

        private final java.math.BigDecimal refundAmount;
        private final String reason;
        private final String cashier;

        public InstallmentRefundRequest(java.math.BigDecimal refundAmount, String reason, String cashier) {
            this.refundAmount = refundAmount;
            this.reason = reason;
            this.cashier = cashier;
        }

        public java.math.BigDecimal getRefundAmount() {
            return refundAmount;
        }

        public String getReason() {
            return reason;
        }

        public String getCashier() {
            return cashier;
        }
    }

    private void processRefund() {
        if (originalTransaction == null) {
            AlertUtil.showError("Refund Error", "No original transaction specified for refund.");
            return;
        }

        com.clothingstore.model.Transaction refundTransaction = new com.clothingstore.model.Transaction();
        refundTransaction.setItems(new java.util.ArrayList<>(currentCartItems)); // items with negative quantity
        refundTransaction.setCustomer(selectedCustomer);
        refundTransaction.setCustomerId(selectedCustomer != null ? selectedCustomer.getId() : null);
        refundTransaction.recalculateAmounts(); // totals will be negative
        refundTransaction.setPaymentMethod("REFUND"); // This indicates how the money was returned

        try {
            com.clothingstore.service.TransactionService transactionService = com.clothingstore.service.TransactionService.getInstance();
            transactionService.processRefund(refundTransaction, originalTransaction);
            AlertUtil.showSuccess("Refund Processed", "Refund for transaction " + originalTransaction.getTransactionNumber() + " completed.");
            startNewTransaction(); // Reset the POS screen
        } catch (Exception e) {
            AlertUtil.showError("Refund Error", "Failed to process refund: " + e.getMessage());
        } finally {
            isRefundMode = false;
            originalTransaction = null;
        }
    }

    // ## 3. Complete Payment Processing Functions
    private void processPayment() {
        if (currentCartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Please add items to cart before processing payment.");
            return;
        }

        try {
            // Create transaction from cart items
            com.clothingstore.model.Transaction transaction = createTransactionFromCart();

            // Show single payment dialog
            showSinglePaymentDialog(transaction);

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process payment: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void processMultiplePayments() {
        if (currentCartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Please add items to cart before processing payment.");
            return;
        }

        try {
            // Create transaction from cart items
            com.clothingstore.model.Transaction transaction = createTransactionFromCart();

            // Show programmatic multiple payment dialog
            showMultiplePaymentDialog(transaction);

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process multiple payments: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void processInstallmentPayment() {
        if (currentCartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Please add items to cart before processing installment payment.");
            return;
        }

        try {
            // Create transaction from cart items
            com.clothingstore.model.Transaction transaction = createTransactionFromCart();

            // Show installment payment dialog with partial payment pre-selected
            showInstallmentPaymentDialog(transaction);

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process installment payment: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showMultiplePaymentDialog(com.clothingstore.model.Transaction transaction) {
        // Create multiple payment dialog
        javafx.stage.Stage dialog = new javafx.stage.Stage();
        dialog.setTitle("Multiple Payment Methods");
        dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
        dialog.initOwner(contentArea.getScene().getWindow());

        javafx.scene.layout.VBox root = new javafx.scene.layout.VBox(15);
        root.setPadding(new javafx.geometry.Insets(20));
        root.setStyle("-fx-background-color: #f8f9fa;");

        // Header
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Multiple Payment Methods");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");

        javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("Total Amount: $" + transaction.getTotalAmount().toString());
        totalLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");

        // Payment tracking
        java.util.List<PaymentEntry> payments = new java.util.ArrayList<>();
        java.math.BigDecimal[] remainingAmount = {transaction.getTotalAmount()};

        javafx.scene.control.Label remainingLabel = new javafx.scene.control.Label("Remaining: $" + remainingAmount[0].toString());
        remainingLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;");

        // Payment entries list
        javafx.scene.control.ListView<PaymentEntry> paymentsList = new javafx.scene.control.ListView<>();
        paymentsList.setPrefHeight(200);
        paymentsList.setCellFactory(listView -> new PaymentEntryCell());

        // Add payment section
        javafx.scene.layout.HBox addPaymentBox = new javafx.scene.layout.HBox(10);
        addPaymentBox.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.ComboBox<String> methodCombo = new javafx.scene.control.ComboBox<>();
        methodCombo.getItems().addAll("Cash", "Credit Card", "Debit Card", "Store Credit", "Gift Card", "Check", "Mobile Payment");
        methodCombo.setValue("Cash");

        javafx.scene.control.TextField amountField = new javafx.scene.control.TextField();
        amountField.setPromptText("Amount");
        amountField.setPrefWidth(100);

        javafx.scene.control.Button addPaymentBtn = new javafx.scene.control.Button("Add Payment");
        addPaymentBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold;");

        addPaymentBtn.setOnAction(e -> {
            try {
                java.math.BigDecimal amount = new java.math.BigDecimal(amountField.getText());
                if (amount.compareTo(java.math.BigDecimal.ZERO) <= 0) {
                    AlertUtil.showWarning("Invalid Amount", "Please enter a positive amount.");
                    return;
                }
                if (amount.compareTo(remainingAmount[0]) > 0) {
                    AlertUtil.showWarning("Excessive Amount", "Amount cannot exceed remaining balance.");
                    return;
                }

                PaymentEntry payment = new PaymentEntry(methodCombo.getValue(), amount);
                payments.add(payment);
                paymentsList.getItems().add(payment);

                remainingAmount[0] = remainingAmount[0].subtract(amount);
                remainingLabel.setText("Remaining: $" + remainingAmount[0].setScale(2, java.math.RoundingMode.HALF_UP).toString());

                amountField.clear();

                if (remainingAmount[0].compareTo(java.math.BigDecimal.ZERO) == 0) {
                    remainingLabel.setText("PAID IN FULL");
                    remainingLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #28a745;");
                }

            } catch (NumberFormatException ex) {
                AlertUtil.showWarning("Invalid Amount", "Please enter a valid amount.");
            }
        });

        addPaymentBox.getChildren().addAll(new javafx.scene.control.Label("Method:"), methodCombo,
                new javafx.scene.control.Label("Amount:"), amountField, addPaymentBtn);

        // Buttons
        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(10);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        javafx.scene.control.Button completeBtn = new javafx.scene.control.Button("Complete Transaction");
        completeBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 8 16;");

        javafx.scene.control.Button cancelBtn = new javafx.scene.control.Button("Cancel");
        cancelBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 8 16;");

        completeBtn.setOnAction(e -> {
            if (remainingAmount[0].compareTo(java.math.BigDecimal.ZERO) > 0) {
                AlertUtil.showWarning("Incomplete Payment", "Please complete all payments before finishing transaction.");
                return;
            }

            try {
                // Process the transaction with multiple payments
                transaction.setPaymentMethod("MULTIPLE"); // Set as multiple payment
                com.clothingstore.service.TransactionService transactionService = com.clothingstore.service.TransactionService.getInstance();
                com.clothingstore.model.Transaction processedTransaction = transactionService.completeTransaction(transaction, false);

                if (processedTransaction != null) {
                    // Record payment history for multiple payments
                    try {
                        PaymentHistoryService paymentHistoryService = PaymentHistoryService.getInstance();
                        String cashierName = "System"; // In a real system, this would be the logged-in user

                        for (PaymentEntry entry : payments) {
                            String notes = "Multiple payment method: " + entry.getMethod();
                            paymentHistoryService.recordPayment(
                                    processedTransaction.getId(),
                                    entry.getAmount(),
                                    entry.getMethod(),
                                    cashierName,
                                    notes
                            );
                        }
                        System.out.println("DEBUG: Multiple payment history recorded for transaction " + processedTransaction.getId());
                    } catch (Exception historyError) {
                        System.err.println("WARNING: Failed to record multiple payment history: " + historyError.getMessage());
                        // Don't fail the entire process if history recording fails
                    }

                    // Show detailed receipt with multiple payment methods
                    showMultiplePaymentReceipt(processedTransaction, payments);
                    // Show transaction complete dialog with invoice option
                    showTransactionCompleteDialog(processedTransaction);
                    dialog.close();
                    startNewTransaction();
                }
            } catch (Exception ex) {
                AlertUtil.showError("Payment Error", "Failed to process transaction: " + ex.getMessage());
            }
        });

        cancelBtn.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(cancelBtn, completeBtn);

        root.getChildren().addAll(titleLabel, totalLabel, remainingLabel,
                new javafx.scene.control.Label("Payment Methods:"), paymentsList,
                new javafx.scene.control.Separator(), addPaymentBox, buttonBox);

        dialog.setScene(new javafx.scene.Scene(root, 600, 500));
        dialog.showAndWait();
    }

    // Payment Entry class for multiple payments
    private static class PaymentEntry {

        private String method;
        private java.math.BigDecimal amount;

        public PaymentEntry(String method, java.math.BigDecimal amount) {
            this.method = method;
            this.amount = amount;
        }

        public String getMethod() {
            return method;
        }

        public java.math.BigDecimal getAmount() {
            return amount;
        }

        @Override
        public String toString() {
            return method + ": $" + amount.setScale(2, java.math.RoundingMode.HALF_UP).toString();
        }
    }

    // Custom cell for payment entries
    private class PaymentEntryCell extends javafx.scene.control.ListCell<PaymentEntry> {

        @Override
        protected void updateItem(PaymentEntry payment, boolean empty) {
            super.updateItem(payment, empty);

            if (empty || payment == null) {
                setGraphic(null);
                setText(null);
            } else {
                javafx.scene.layout.HBox container = new javafx.scene.layout.HBox(10);
                container.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

                javafx.scene.control.Label methodLabel = new javafx.scene.control.Label(payment.getMethod());
                methodLabel.setStyle("-fx-font-weight: bold; -fx-min-width: 100;");

                javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
                javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

                javafx.scene.control.Label amountLabel = new javafx.scene.control.Label("$" + payment.getAmount().setScale(2, java.math.RoundingMode.HALF_UP).toString());
                amountLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #27ae60;");

                javafx.scene.control.Button removeBtn = new javafx.scene.control.Button("✖");
                removeBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 2 6;");
                removeBtn.setOnAction(e -> {
                    getListView().getItems().remove(payment);
                    // Note: In a full implementation, this would also update the remaining amount
                });

                container.getChildren().addAll(methodLabel, spacer, amountLabel, removeBtn);
                setGraphic(container);
                setText(null);
            }
        }
    }

    private void showMultiplePaymentReceipt(com.clothingstore.model.Transaction transaction, java.util.List<PaymentEntry> payments) {
        try {
            // Generate receipt content with multiple payment methods
            StringBuilder receipt = new StringBuilder();
            receipt.append("=".repeat(40)).append("\n");
            receipt.append("        CLOTHING STORE RECEIPT\n");
            receipt.append("=".repeat(40)).append("\n");
            receipt.append("Transaction: ").append(transaction.getTransactionNumber()).append("\n");
            receipt.append("Date: ").append(transaction.getTransactionDate().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");

            if (transaction.getCustomer() != null) {
                receipt.append("Customer: ").append(transaction.getCustomer().getFullName()).append("\n");
                receipt.append("Phone: ").append(transaction.getCustomer().getPhone()).append("\n");
            } else {
                receipt.append("Customer: Walk-in Customer\n");
            }

            receipt.append("-".repeat(40)).append("\n");
            receipt.append("ITEMS:\n");

            for (com.clothingstore.model.TransactionItem item : transaction.getItems()) {
                receipt.append(String.format("%-20s %2d x $%8.2f = $%8.2f\n",
                        item.getProductName(),
                        item.getQuantity(),
                        item.getUnitPrice(),
                        item.getLineTotal()));
            }

            receipt.append("-".repeat(40)).append("\n");
            receipt.append(String.format("Subtotal: %25s $%8.2f\n", "", transaction.getSubtotal()));
            receipt.append(String.format("Tax: %29s $%8.2f\n", "", transaction.getTaxAmount()));
            receipt.append("=".repeat(40)).append("\n");
            receipt.append(String.format("TOTAL: %27s $%8.2f\n", "", transaction.getTotalAmount()));
            receipt.append("=".repeat(40)).append("\n");

            // Multiple payment methods section
            receipt.append("PAYMENT METHODS:\n");
            for (PaymentEntry payment : payments) {
                receipt.append(String.format("%-20s %15s $%8.2f\n", payment.getMethod(), "", payment.getAmount()));
            }
            receipt.append("=".repeat(40)).append("\n");

            receipt.append("\n");
            receipt.append("Thank you for your business!\n");
            receipt.append("=".repeat(40)).append("\n");

            // Show receipt in dialog
            showReceiptDialog(receipt.toString(), transaction);

        } catch (Exception e) {
            AlertUtil.showError("Receipt Error", "Failed to generate multiple payment receipt: " + e.getMessage());
        }
    }

    private com.clothingstore.model.Transaction createTransactionFromCart() {
        com.clothingstore.model.Transaction transaction = new com.clothingstore.model.Transaction();
        transaction.setTransactionNumber(currentTransactionNumber);
        transaction.setCustomer(selectedCustomer);
        transaction.setCustomerId(selectedCustomer != null ? selectedCustomer.getId() : null);
        transaction.setTransactionDate(java.time.LocalDateTime.now());

        // Set transaction items
        transaction.setItems(new java.util.ArrayList<>(currentCartItems));

        // Calculate amounts
        transaction.recalculateAmounts();

        return transaction;
    }

    private void showSinglePaymentDialog(com.clothingstore.model.Transaction transaction) {
        try {
            System.out.println("DEBUG: Starting NEW single payment dialog for transaction: " + transaction.getTransactionNumber());
            System.out.println("DEBUG: Transaction total: " + transaction.getTotalAmount());

            // Create a programmatic JavaFX dialog (no FXML dependencies)
            javafx.scene.control.Dialog<javafx.scene.control.ButtonType> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Process Single Payment - " + transaction.getTransactionNumber());
            dialog.setHeaderText("Complete Transaction Payment");

            // Create dialog content using programmatic JavaFX components
            javafx.scene.layout.GridPane grid = new javafx.scene.layout.GridPane();
            grid.setHgap(15);
            grid.setVgap(15);
            grid.setPadding(new javafx.geometry.Insets(20, 20, 10, 10));

            // Transaction summary
            javafx.scene.control.Label summaryLabel = new javafx.scene.control.Label(String.format("Total Amount: $%.2f", transaction.getTotalAmount().doubleValue()));
            summaryLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            // Payment method selection
            javafx.scene.control.ComboBox<String> paymentMethodCombo = new javafx.scene.control.ComboBox<>();
            paymentMethodCombo.getItems().addAll("CASH", "CREDIT_CARD", "DEBIT_CARD", "GIFT_CARD");
            paymentMethodCombo.setValue("CASH");
            paymentMethodCombo.setPrefWidth(200);

            // Payment amount (pre-filled with total amount)
            javafx.scene.control.TextField amountField = new javafx.scene.control.TextField();
            amountField.setText(transaction.getTotalAmount().toString());
            amountField.setStyle("-fx-font-size: 14px;");
            amountField.setPrefWidth(200);

            // Notes field
            javafx.scene.control.TextArea notesArea = new javafx.scene.control.TextArea();
            notesArea.setPromptText("Payment notes (optional)");
            notesArea.setPrefRowCount(3);
            notesArea.setPrefWidth(200);

            // Add components to grid
            grid.add(new javafx.scene.control.Label("Transaction Summary:"), 0, 0);
            grid.add(summaryLabel, 1, 0);
            grid.add(new javafx.scene.control.Label("Payment Method:"), 0, 1);
            grid.add(paymentMethodCombo, 1, 1);
            grid.add(new javafx.scene.control.Label("Payment Amount:"), 0, 2);
            grid.add(amountField, 1, 2);
            grid.add(new javafx.scene.control.Label("Notes:"), 0, 3);
            grid.add(notesArea, 1, 3);

            dialog.getDialogPane().setContent(grid);

            // Add buttons
            javafx.scene.control.ButtonType processButtonType = new javafx.scene.control.ButtonType("Process Payment", javafx.scene.control.ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(processButtonType, javafx.scene.control.ButtonType.CANCEL);

            // Show dialog and handle result
            java.util.Optional<javafx.scene.control.ButtonType> result = dialog.showAndWait();
            if (result.isPresent() && result.get() == processButtonType) {
                processMainWindowSinglePayment(transaction, amountField.getText().trim(), paymentMethodCombo.getValue(), notesArea.getText().trim());
            }

        } catch (Exception e) {
            System.err.println("ERROR: Failed to show new single payment dialog:");
            e.printStackTrace();
            AlertUtil.showError("Payment Dialog Error", "Failed to show payment dialog: " + e.getMessage());
        }
    }

    /**
     * Process single payment from MainWindow using verified
     * Transaction.processFullPayment() method
     */
    private void processMainWindowSinglePayment(com.clothingstore.model.Transaction transaction, String amountText, String paymentMethod, String notes) {
        try {
            System.out.println("DEBUG: Processing MainWindow single payment - Amount: " + amountText + ", Method: " + paymentMethod);

            // Validate payment amount
            java.math.BigDecimal paymentAmount;
            try {
                paymentAmount = new java.math.BigDecimal(amountText);
            } catch (NumberFormatException e) {
                AlertUtil.showError("Invalid Amount", "Please enter a valid payment amount.");
                return;
            }

            if (paymentAmount.compareTo(java.math.BigDecimal.ZERO) <= 0) {
                AlertUtil.showError("Invalid Amount", "Payment amount must be greater than zero.");
                return;
            }

            java.math.BigDecimal totalAmount = transaction.getTotalAmount();
            if (paymentAmount.compareTo(totalAmount) < 0) {
                AlertUtil.showError("Insufficient Payment",
                        String.format("Payment amount ($%.2f) is less than total amount ($%.2f).",
                                paymentAmount.doubleValue(), totalAmount.doubleValue()));
                return;
            }

            // Set transaction details
            transaction.setPaymentMethod(paymentMethod);
            if (!notes.isEmpty()) {
                transaction.setNotes(notes);
            }

            // Use verified Transaction.processFullPayment() method
            transaction.processFullPayment(paymentAmount);

            System.out.println("DEBUG: Transaction status after processFullPayment: " + transaction.getStatus());

            // Save transaction using TransactionService
            com.clothingstore.service.TransactionService transactionService = com.clothingstore.service.TransactionService.getInstance();
            com.clothingstore.model.Transaction savedTransaction = transactionService.processTransaction(transaction);

            System.out.println("DEBUG: Transaction saved with ID: " + savedTransaction.getId());

            // Calculate change if overpayment
            java.math.BigDecimal changeAmount = paymentAmount.subtract(totalAmount);
            if (changeAmount.compareTo(java.math.BigDecimal.ZERO) < 0) {
                changeAmount = java.math.BigDecimal.ZERO;
            }

            // Show transaction complete dialog
            showTransactionCompleteDialog(savedTransaction);

            // Start new transaction
            startNewTransaction();
            setStatus("Single payment processed successfully");

        } catch (Exception e) {
            System.err.println("ERROR: Failed to process MainWindow single payment:");
            e.printStackTrace();
            AlertUtil.showError("Payment Processing Error", "Failed to process single payment: " + e.getMessage());
        }
    }

    private void showInstallmentPaymentDialog(com.clothingstore.model.Transaction transaction) {
        try {
            // Create programmatic payment dialog to bypass FXML loading issues
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Installment Payment - " + transaction.getTransactionNumber());
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(contentArea.getScene().getWindow());
            stage.setResizable(false);

            // Create the dialog content programmatically
            javafx.scene.layout.VBox root = createInstallmentPaymentDialogContent(transaction, stage);

            javafx.scene.Scene scene = new javafx.scene.Scene(root, 500, 400);
            stage.setScene(scene);
            stage.showAndWait();

            // Check if payment was processed
            // if (controller.isPaymentProcessed()) {
            //     try {
            //         // Save the transaction with payment information
            //         com.clothingstore.service.TransactionService transactionService = com.clothingstore.service.TransactionService.getInstance();
            //         com.clothingstore.model.Transaction processedTransaction = transactionService.completeTransaction(transaction, false);
            //         if (processedTransaction != null) {
            //             // Show installment payment confirmation
            //             AlertUtil.showSuccess("Installment Payment Processed",
            //                     String.format("Installment payment of %s processed successfully.\nRemaining balance: %s\n\nCustomer can make additional payments through Outstanding Balances.",
            //                             java.text.NumberFormat.getCurrencyInstance().format(controller.getPaymentAmount()),
            //                             java.text.NumberFormat.getCurrencyInstance().format(processedTransaction.getRemainingBalance())));
            //             startNewTransaction();
            //         }
            //     } catch (Exception ex) {
            //         AlertUtil.showError("Transaction Error", "Failed to complete installment transaction: " + ex.getMessage());
            //     }
            // }
            // Show success message
            AlertUtil.showSuccess("Installment Payment", "Installment payment dialog opened successfully!");

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to create installment payment dialog: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create programmatic installment payment dialog content
     */
    private javafx.scene.layout.VBox createInstallmentPaymentDialogContent(com.clothingstore.model.Transaction transaction, javafx.stage.Stage stage) {
        javafx.scene.layout.VBox root = new javafx.scene.layout.VBox(15);
        root.setPadding(new javafx.geometry.Insets(20));
        root.setStyle("-fx-background-color: #f8f9fa;");

        // Header
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Installment Payment");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        javafx.scene.control.Label transactionLabel = new javafx.scene.control.Label("Transaction: " + transaction.getTransactionNumber());
        transactionLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d;");

        // Transaction Summary
        javafx.scene.layout.VBox summaryBox = new javafx.scene.layout.VBox(8);
        summaryBox.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-color: #dee2e6; -fx-border-radius: 5;");

        javafx.scene.control.Label summaryTitle = new javafx.scene.control.Label("Transaction Summary");
        summaryTitle.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        javafx.scene.control.Label subtotalLabel = new javafx.scene.control.Label("Subtotal: "
                + java.text.NumberFormat.getCurrencyInstance().format(transaction.getSubtotal()));
        javafx.scene.control.Label totalLabel = new javafx.scene.control.Label("Total Amount: "
                + java.text.NumberFormat.getCurrencyInstance().format(transaction.getTotalAmount()));
        totalLabel.setStyle("-fx-font-weight: bold;");

        summaryBox.getChildren().addAll(summaryTitle, subtotalLabel, totalLabel);

        // Payment Method Selection
        javafx.scene.layout.HBox paymentMethodBox = new javafx.scene.layout.HBox(10);
        paymentMethodBox.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label methodLabel = new javafx.scene.control.Label("Payment Method:");
        javafx.scene.control.ComboBox<String> methodCombo = new javafx.scene.control.ComboBox<>();
        methodCombo.getItems().addAll("CASH", "CREDIT_CARD", "DEBIT_CARD", "CHECK");
        methodCombo.setValue("CASH");
        methodCombo.setPrefWidth(150);

        paymentMethodBox.getChildren().addAll(methodLabel, methodCombo);

        // Payment Amount
        javafx.scene.layout.HBox amountBox = new javafx.scene.layout.HBox(10);
        amountBox.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        javafx.scene.control.Label amountLabel = new javafx.scene.control.Label("Payment Amount:");
        javafx.scene.control.TextField amountField = new javafx.scene.control.TextField();
        amountField.setPromptText("Enter amount");
        amountField.setPrefWidth(150);

        // Set default to 50% of total
        java.math.BigDecimal defaultAmount = transaction.getTotalAmount().multiply(new java.math.BigDecimal("0.5"));
        amountField.setText(defaultAmount.setScale(2, java.math.RoundingMode.HALF_UP).toString());

        amountBox.getChildren().addAll(amountLabel, amountField);

        // Buttons
        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(10);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        javafx.scene.control.Button cancelBtn = new javafx.scene.control.Button("Cancel");
        cancelBtn.setOnAction(e -> stage.close());

        javafx.scene.control.Button processBtn = new javafx.scene.control.Button("Process Payment");
        processBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold;");
        processBtn.setOnAction(e -> {
            try {
                String amountText = amountField.getText().trim();
                if (amountText.isEmpty()) {
                    AlertUtil.showWarning("Invalid Amount", "Please enter a payment amount.");
                    return;
                }

                java.math.BigDecimal paymentAmount = new java.math.BigDecimal(amountText);
                if (paymentAmount.compareTo(java.math.BigDecimal.ZERO) <= 0) {
                    AlertUtil.showWarning("Invalid Amount", "Payment amount must be greater than zero.");
                    return;
                }

                if (paymentAmount.compareTo(transaction.getTotalAmount()) > 0) {
                    AlertUtil.showWarning("Invalid Amount", "Payment amount cannot exceed total amount.");
                    return;
                }

                // Initialize transaction for installment payments if not already done
                com.clothingstore.service.InstallmentPaymentService installmentService
                        = com.clothingstore.service.InstallmentPaymentService.getInstance();

                // If this is a new transaction, initialize it for installments
                com.clothingstore.model.Transaction finalTransaction;
                if (transaction.getId() == null) {
                    // Set the payment method before initializing (required for database NOT NULL constraint)
                    transaction.setPaymentMethod(methodCombo.getValue());
                    finalTransaction = installmentService.initializeInstallmentTransaction(transaction);
                } else {
                    finalTransaction = transaction;
                }

                // Process the installment payment (this will NOT complete the transaction until fully paid)
                com.clothingstore.model.InstallmentPayment payment = installmentService.processInstallmentPayment(
                        finalTransaction.getId(),
                        paymentAmount,
                        methodCombo.getValue(),
                        "System User", // TODO: Get actual cashier name
                        "Installment payment via POS"
                );

                stage.close();

                // Reload the transaction to get updated status
                com.clothingstore.dao.TransactionDAO transactionDAO = com.clothingstore.dao.TransactionDAO.getInstance();
                com.clothingstore.model.Transaction updatedTransaction = transactionDAO.findById(finalTransaction.getId()).orElse(finalTransaction);

                // Show appropriate success message based on transaction status
                if (updatedTransaction.isFullyPaid()) {
                    AlertUtil.showSuccess("Transaction Completed",
                            String.format("Final installment payment of %s processed successfully.\nTransaction is now complete and moved to Transaction History.",
                                    java.text.NumberFormat.getCurrencyInstance().format(paymentAmount)));
                } else {
                    AlertUtil.showSuccess("Installment Payment Processed",
                            String.format("Installment payment of %s processed successfully.\nRemaining balance: %s\n\nCustomer can make additional payments through Outstanding Balances.",
                                    java.text.NumberFormat.getCurrencyInstance().format(paymentAmount),
                                    java.text.NumberFormat.getCurrencyInstance().format(updatedTransaction.getRemainingBalance())));
                }

                startNewTransaction();

            } catch (NumberFormatException ex) {
                AlertUtil.showWarning("Invalid Amount", "Please enter a valid numeric amount.");
            } catch (Exception ex) {
                AlertUtil.showError("Payment Error", "Failed to process installment payment: " + ex.getMessage());
            }
        });

        buttonBox.getChildren().addAll(cancelBtn, processBtn);

        root.getChildren().addAll(titleLabel, transactionLabel, summaryBox, paymentMethodBox, amountBox, buttonBox);
        return root;
    }

    /**
     * Show enhanced partial payment completion dialog with Outstanding Balances
     * integration
     */
    private void showEnhancedPartialPaymentDialog(com.clothingstore.model.Transaction transaction, PaymentDialogController controller) {
        // Create enhanced partial payment completion dialog
        javafx.stage.Stage dialog = new javafx.stage.Stage();
        dialog.setTitle("Partial Payment Processed");
        dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
        dialog.initOwner(contentArea.getScene().getWindow());

        javafx.scene.layout.VBox root = new javafx.scene.layout.VBox(20);
        root.setPadding(new javafx.geometry.Insets(25));
        root.setStyle("-fx-background-color: #f8f9fa;");

        // Success header
        javafx.scene.layout.HBox headerBox = new javafx.scene.layout.HBox(15);
        headerBox.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        headerBox.setStyle("-fx-background-color: #d4edda; -fx-padding: 15; -fx-border-color: #c3e6cb; -fx-border-width: 1; -fx-border-radius: 5; -fx-background-radius: 5;");

        javafx.scene.control.Label successIcon = new javafx.scene.control.Label("✓");
        successIcon.setStyle("-fx-font-size: 24px; -fx-text-fill: #155724; -fx-font-weight: bold;");

        javafx.scene.control.Label headerLabel = new javafx.scene.control.Label("Partial Payment Processed Successfully");
        headerLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #155724;");

        headerBox.getChildren().addAll(successIcon, headerLabel);

        // Payment details
        javafx.scene.layout.VBox detailsBox = new javafx.scene.layout.VBox(10);
        detailsBox.setStyle("-fx-background-color: white; -fx-padding: 20; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 5; -fx-background-radius: 5;");

        javafx.scene.control.Label transactionLabel = new javafx.scene.control.Label("Transaction: " + transaction.getTransactionNumber());
        transactionLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        javafx.scene.control.Label paymentLabel = new javafx.scene.control.Label("Payment Amount: "
                + java.text.NumberFormat.getCurrencyInstance().format(controller.getPaymentAmount()));
        paymentLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #28a745;");

        javafx.scene.control.Label remainingLabel = new javafx.scene.control.Label("Remaining Balance: "
                + java.text.NumberFormat.getCurrencyInstance().format(transaction.getRemainingBalance()));
        remainingLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #dc3545; -fx-font-weight: bold;");

        javafx.scene.control.Label methodLabel = new javafx.scene.control.Label("Payment Method: " + controller.getSelectedPaymentMethod());
        methodLabel.setStyle("-fx-font-size: 14px;");

        detailsBox.getChildren().addAll(transactionLabel, paymentLabel, remainingLabel, methodLabel);

        // Outstanding balance info
        javafx.scene.layout.VBox infoBox = new javafx.scene.layout.VBox(10);
        infoBox.setStyle("-fx-background-color: #fff3cd; -fx-padding: 15; -fx-border-color: #ffeaa7; -fx-border-width: 1; -fx-border-radius: 5; -fx-background-radius: 5;");

        javafx.scene.control.Label infoLabel = new javafx.scene.control.Label("Next Steps:");
        infoLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #856404;");

        javafx.scene.control.Label step1 = new javafx.scene.control.Label("• Customer can make additional payments anytime");
        javafx.scene.control.Label step2 = new javafx.scene.control.Label("• View all outstanding balances in 'Outstanding Balances' page");
        javafx.scene.control.Label step3 = new javafx.scene.control.Label("• Transaction will be marked complete when fully paid");

        step1.setStyle("-fx-font-size: 12px; -fx-text-fill: #856404;");
        step2.setStyle("-fx-font-size: 12px; -fx-text-fill: #856404;");
        step3.setStyle("-fx-font-size: 12px; -fx-text-fill: #856404;");

        infoBox.getChildren().addAll(infoLabel, step1, step2, step3);

        // Buttons
        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(15);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER);

        javafx.scene.control.Button viewOutstandingBtn = new javafx.scene.control.Button("View Outstanding Balances");
        viewOutstandingBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20; -fx-background-radius: 5;");
        viewOutstandingBtn.setOnAction(e -> {
            dialog.close();
            showOutstandingBalances();
        });

        javafx.scene.control.Button okBtn = new javafx.scene.control.Button("OK");
        okBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20; -fx-background-radius: 5;");
        okBtn.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(viewOutstandingBtn, okBtn);

        root.getChildren().addAll(headerBox, detailsBox, infoBox, buttonBox);

        dialog.setScene(new javafx.scene.Scene(root, 500, 450));
        dialog.showAndWait();
    }

    /**
     * Shows transaction complete dialog with invoice option using custom Stage
     */
    private void showTransactionCompleteDialog(com.clothingstore.model.Transaction transaction) {
        // Create custom Stage instead of Alert for better control
        javafx.stage.Stage dialog = new javafx.stage.Stage();
        dialog.setTitle("Transaction Complete");
        dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
        dialog.initOwner(contentArea.getScene().getWindow());
        dialog.setResizable(false);

        // Create main container
        javafx.scene.layout.VBox mainContent = new javafx.scene.layout.VBox();
        mainContent.setSpacing(20);
        mainContent.setPadding(new javafx.geometry.Insets(30));
        mainContent.setStyle("-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 8;");

        // Header with icon and title
        javafx.scene.layout.HBox header = new javafx.scene.layout.HBox();
        header.setSpacing(15);
        header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // Success icon
        javafx.scene.control.Label iconLabel = new javafx.scene.control.Label("✅");
        iconLabel.setStyle("-fx-font-size: 24px;");

        // Title
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Transaction Processed Successfully!");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #28a745; -fx-font-family: 'Segoe UI', sans-serif;");

        header.getChildren().addAll(iconLabel, titleLabel);

        // Transaction details
        javafx.scene.control.Label detailsLabel = new javafx.scene.control.Label(
                String.format("Transaction Number: %s\nTotal Amount: $%.2f\nPayment Method: %s\nCustomer: %s",
                        transaction.getTransactionNumber(),
                        transaction.getTotalAmount(),
                        transaction.getPaymentMethod(),
                        selectedCustomer != null ? selectedCustomer.getName() : "Walk-in Customer"
                )
        );
        detailsLabel.setStyle("-fx-font-size: 14px; -fx-font-family: 'Segoe UI', sans-serif; -fx-text-fill: #495057;");

        // Action buttons
        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox();
        buttonBox.setSpacing(10);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER);

        javafx.scene.control.Button invoiceBtn = new javafx.scene.control.Button("📄 Generate Invoice");
        invoiceBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 160;");
        invoiceBtn.setOnAction(e -> {
            System.out.println("Generate Invoice button clicked");
            dialog.close();
            // Show professional invoice
            showProfessionalInvoice(transaction, selectedCustomer);
        });

        javafx.scene.control.Button receiptBtn = new javafx.scene.control.Button("🧾 Print Receipt");
        receiptBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 160;");
        receiptBtn.setOnAction(e -> {
            System.out.println("Print Receipt button clicked");
            dialog.close();
            printReceipt(transaction);
        });

        javafx.scene.control.Button closeBtn = new javafx.scene.control.Button("✓ Close");
        closeBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 120;");
        closeBtn.setOnAction(e -> {
            try {
                System.out.println("Transaction Complete dialog - Close button clicked");
                dialog.close();
                System.out.println("Transaction Complete dialog closed successfully");
            } catch (Exception ex) {
                System.err.println("Error closing transaction complete dialog: " + ex.getMessage());
                ex.printStackTrace();
                AlertUtil.showError("Close Error", "Failed to close dialog: " + ex.getMessage());
            }
        });

        // Add hover effects for all buttons
        String invoiceBaseStyle = "-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 160;";
        String invoiceHoverStyle = "-fx-background-color: #0056b3; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 160;";

        String receiptBaseStyle = "-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 160;";
        String receiptHoverStyle = "-fx-background-color: #1e7e34; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 160;";

        String closeBaseStyle = "-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 120;";
        String closeHoverStyle = "-fx-background-color: #5a6268; -fx-text-fill: white; -fx-font-weight: bold; "
                + "-fx-background-radius: 5; -fx-padding: 10 20; -fx-font-size: 14px; -fx-pref-width: 120;";

        // Add hover effects
        invoiceBtn.setOnMouseEntered(e -> invoiceBtn.setStyle(invoiceHoverStyle));
        invoiceBtn.setOnMouseExited(e -> invoiceBtn.setStyle(invoiceBaseStyle));

        receiptBtn.setOnMouseEntered(e -> receiptBtn.setStyle(receiptHoverStyle));
        receiptBtn.setOnMouseExited(e -> receiptBtn.setStyle(receiptBaseStyle));

        closeBtn.setOnMouseEntered(e -> closeBtn.setStyle(closeHoverStyle));
        closeBtn.setOnMouseExited(e -> closeBtn.setStyle(closeBaseStyle));

        buttonBox.getChildren().addAll(invoiceBtn, receiptBtn, closeBtn);

        // Add all components to main content
        mainContent.getChildren().addAll(header, detailsLabel, buttonBox);

        // Create scene and set up dialog
        javafx.scene.Scene scene = new javafx.scene.Scene(mainContent, 500, 300);
        dialog.setScene(scene);

        // Add keyboard shortcut to close dialog (ESC key)
        scene.setOnKeyPressed(event -> {
            if (event.getCode() == javafx.scene.input.KeyCode.ESCAPE) {
                System.out.println("ESC key pressed - closing transaction complete dialog");
                dialog.close();
            }
        });

        // Set close request handler
        dialog.setOnCloseRequest(event -> {
            System.out.println("Transaction complete dialog close requested");
            // Allow the dialog to close normally
        });

        // Center the dialog on screen
        dialog.centerOnScreen();

        // Show dialog and wait
        dialog.showAndWait();
    }

    // ## 4. Add Receipt/Invoice Generation
    private void printReceipt(com.clothingstore.model.Transaction transaction) {
        try {
            // Generate receipt content
            StringBuilder receipt = new StringBuilder();
            receipt.append("=".repeat(40)).append("\n");
            receipt.append("        CLOTHING STORE RECEIPT\n");
            receipt.append("=".repeat(40)).append("\n");
            receipt.append("Transaction: ").append(transaction.getTransactionNumber()).append("\n");
            receipt.append("Date: ").append(transaction.getTransactionDate().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");

            if (transaction.getCustomer() != null) {
                receipt.append("Customer: ").append(transaction.getCustomer().getFullName()).append("\n");
                receipt.append("Phone: ").append(transaction.getCustomer().getPhone()).append("\n");
            } else {
                receipt.append("Customer: Walk-in Customer\n");
            }

            receipt.append("-".repeat(40)).append("\n");
            receipt.append("ITEMS:\n");

            for (com.clothingstore.model.TransactionItem item : transaction.getItems()) {
                receipt.append(String.format("%-20s %2d x $%8.2f = $%8.2f\n",
                        item.getProductName(),
                        item.getQuantity(),
                        item.getUnitPrice(),
                        item.getLineTotal()));
            }

            receipt.append("-".repeat(40)).append("\n");
            receipt.append(String.format("Subtotal: %25s $%8.2f\n", "", transaction.getSubtotal()));
            receipt.append(String.format("Tax: %29s $%8.2f\n", "", transaction.getTaxAmount()));
            receipt.append("=".repeat(40)).append("\n");
            receipt.append(String.format("TOTAL: %27s $%8.2f\n", "", transaction.getTotalAmount()));
            receipt.append("=".repeat(40)).append("\n");
            receipt.append("Payment Method: ").append(transaction.getPaymentMethod()).append("\n");
            receipt.append("\n");
            receipt.append("Thank you for your business!\n");
            receipt.append("=".repeat(40)).append("\n");

            // Show receipt in dialog
            showReceiptDialog(receipt.toString(), transaction);

        } catch (Exception e) {
            AlertUtil.showError("Receipt Error", "Failed to generate receipt: " + e.getMessage());
        }
    }

    private void showReceiptDialog(String receiptContent, com.clothingstore.model.Transaction transaction) {
        javafx.stage.Stage dialog = new javafx.stage.Stage();
        dialog.setTitle("Transaction Receipt");
        dialog.initModality(javafx.stage.Modality.APPLICATION_MODAL);
        dialog.initOwner(contentArea.getScene().getWindow());

        javafx.scene.layout.VBox root = new javafx.scene.layout.VBox(15);
        root.setPadding(new javafx.geometry.Insets(20));

        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("Transaction Receipt");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");

        javafx.scene.control.TextArea receiptArea = new javafx.scene.control.TextArea(receiptContent);
        receiptArea.setEditable(false);
        receiptArea.setStyle("-fx-font-family: 'Courier New'; -fx-font-size: 12px;");
        receiptArea.setPrefRowCount(20);

        javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(10);
        buttonBox.setAlignment(javafx.geometry.Pos.CENTER);

        javafx.scene.control.Button printBtn = new javafx.scene.control.Button("Print Receipt");
        printBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold;");
        printBtn.setOnAction(e -> {
            // In a real implementation, this would send to printer
            AlertUtil.showInfo("Print", "Receipt sent to printer!");
        });

        javafx.scene.control.Button emailBtn = new javafx.scene.control.Button("Email Receipt");
        emailBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold;");
        emailBtn.setOnAction(e -> {
            AlertUtil.showInfo("Email Feature", "Email functionality has been removed from the system.");
        });

        javafx.scene.control.Button closeBtn = new javafx.scene.control.Button("Close");
        closeBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white;");
        closeBtn.setOnAction(e -> dialog.close());

        buttonBox.getChildren().addAll(printBtn, emailBtn, closeBtn);

        root.getChildren().addAll(titleLabel, receiptArea, buttonBox);

        dialog.setScene(new javafx.scene.Scene(root, 500, 600));
        dialog.showAndWait();
    }

    // ## 5. Enhance Transaction Management
    private void holdTransaction() {
        if (currentCartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Cannot hold an empty transaction.");
            return;
        }

        if (AlertUtil.showConfirmation("Hold Transaction", "Hold current transaction for later completion?")) {
            // In a real implementation, this would save the transaction state
            AlertUtil.showInfo("Transaction Held", "Transaction " + currentTransactionNumber + " has been held.");
            startNewTransaction();
        }
    }

    private void voidTransaction() {
        if (currentCartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "No transaction to void.");
            return;
        }

        if (AlertUtil.showConfirmation("Void Transaction", "Are you sure you want to void this transaction? This action cannot be undone.")) {
            startNewTransaction();
            AlertUtil.showInfo("Transaction Voided", "Transaction has been voided.");
        }
    }

    // ## 6. Improve User Experience - Enhanced Button States
    private void updateButtonStates() {
        boolean hasItems = !currentCartItems.isEmpty();

        if (processPaymentButton != null) {
            processPaymentButton.setDisable(!hasItems);
            // Make buttons more visible when disabled
            if (!hasItems) {
                processPaymentButton.setStyle(processPaymentButton.getStyle() + "-fx-opacity: 0.6;");
                processPaymentButton.setText("Add Items to Enable Payment");
            } else {
                processPaymentButton.setText("Single Payment Method");
                processPaymentButton.setStyle(processPaymentButton.getStyle().replace("-fx-opacity: 0.6;", ""));
            }
        }
        if (multiplePaymentButton != null) {
            multiplePaymentButton.setDisable(!hasItems);
            // Make buttons more visible when disabled
            if (!hasItems) {
                multiplePaymentButton.setStyle(multiplePaymentButton.getStyle() + "-fx-opacity: 0.6;");
                multiplePaymentButton.setText("Add Items for Multiple Payments");
            } else {
                multiplePaymentButton.setText("Multiple Payment Methods");
                multiplePaymentButton.setStyle(multiplePaymentButton.getStyle().replace("-fx-opacity: 0.6;", ""));
            }
        }

        if (installmentPaymentButton != null) {
            installmentPaymentButton.setDisable(!hasItems);
            if (!hasItems) {
                installmentPaymentButton.setStyle(installmentPaymentButton.getStyle() + "-fx-opacity: 0.6;");
                installmentPaymentButton.setText("Add Items to Enable Payment");
            } else {
                installmentPaymentButton.setText("Installment Payment");
                installmentPaymentButton.setStyle(installmentPaymentButton.getStyle().replace("-fx-opacity: 0.6;", ""));
            }
        }
    }

    // Override updateCartDisplay to include button state updates
    private void updateCartDisplayWithButtonStates() {
        updateCartDisplay();
        updateButtonStates();
    }

    /**
     * Shows a professional invoice window for the given transaction
     */
    private void showProfessionalInvoice(com.clothingstore.model.Transaction transaction, com.clothingstore.model.Customer customer) {
        try {
            // Create invoice window
            javafx.stage.Stage invoiceStage = new javafx.stage.Stage();
            invoiceStage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            invoiceStage.setTitle("Professional Invoice - " + transaction.getTransactionNumber());
            invoiceStage.setResizable(true);

            // Create main container
            javafx.scene.layout.VBox mainContainer = new javafx.scene.layout.VBox();
            mainContainer.setSpacing(0);
            mainContainer.setStyle("-fx-background-color: #f8f9fa;");

            // Create invoice content
            javafx.scene.layout.VBox invoiceContent = createInvoiceContent(transaction, customer);

            // Create scrollable container
            javafx.scene.control.ScrollPane scrollPane = new javafx.scene.control.ScrollPane(invoiceContent);
            scrollPane.setFitToWidth(true);
            scrollPane.setFitToHeight(true);
            scrollPane.setHbarPolicy(javafx.scene.control.ScrollPane.ScrollBarPolicy.AS_NEEDED);
            scrollPane.setVbarPolicy(javafx.scene.control.ScrollPane.ScrollBarPolicy.AS_NEEDED);
            scrollPane.setStyle("-fx-background-color: transparent; -fx-background: transparent;");

            // Create action buttons
            javafx.scene.layout.HBox actionButtons = createInvoiceActionButtons(invoiceStage, invoiceContent);

            mainContainer.getChildren().addAll(scrollPane, actionButtons);
            javafx.scene.layout.VBox.setVgrow(scrollPane, javafx.scene.layout.Priority.ALWAYS);

            // Create scene
            double screenWidth = javafx.stage.Screen.getPrimary().getVisualBounds().getWidth();
            double screenHeight = javafx.stage.Screen.getPrimary().getVisualBounds().getHeight();
            double windowWidth = Math.min(1000, screenWidth * 0.8);
            double windowHeight = Math.min(800, screenHeight * 0.9);

            javafx.scene.Scene scene = new javafx.scene.Scene(mainContainer, windowWidth, windowHeight);
            invoiceStage.setScene(scene);

            // Add keyboard shortcut to close window (ESC key)
            scene.setOnKeyPressed(event -> {
                if (event.getCode() == javafx.scene.input.KeyCode.ESCAPE) {
                    System.out.println("ESC key pressed - closing invoice window");
                    invoiceStage.close();
                }
            });

            // Set minimum size
            invoiceStage.setMinWidth(800);
            invoiceStage.setMinHeight(600);

            // Set window close request handler
            invoiceStage.setOnCloseRequest(event -> {
                System.out.println("Invoice window close requested");
                // Allow the window to close normally
            });

            invoiceStage.show();

        } catch (Exception e) {
            AlertUtil.showError("Invoice Error", "Failed to show professional invoice: " + e.getMessage());
        }
    }

    /**
     * Creates the complete invoice content with professional layout
     */
    private javafx.scene.layout.VBox createInvoiceContent(com.clothingstore.model.Transaction transaction, com.clothingstore.model.Customer customer) {
        javafx.scene.layout.VBox content = new javafx.scene.layout.VBox();
        content.setSpacing(0);
        content.setPadding(new javafx.geometry.Insets(40, 40, 40, 40));
        content.setStyle("-fx-background-color: white; -fx-border-color: #e1e5e9; -fx-border-width: 1;");
        content.setMaxWidth(Double.MAX_VALUE);

        // Invoice sections
        javafx.scene.layout.VBox header = createInvoiceHeader(transaction);
        javafx.scene.layout.VBox customerInfo = createInvoiceCustomerSection(transaction, customer);
        javafx.scene.layout.VBox itemsTable = createInvoiceItemsSection(transaction);
        javafx.scene.layout.VBox totalsSection = createInvoiceTotalsSection(transaction);
        javafx.scene.layout.VBox footer = createInvoiceFooter();

        content.getChildren().addAll(
                header,
                createInvoiceSeparator(),
                customerInfo,
                createInvoiceSeparator(),
                itemsTable,
                createInvoiceSeparator(),
                totalsSection,
                createInvoiceSeparator(),
                footer
        );

        return content;
    }

    /**
     * Creates the professional invoice header
     */
    private javafx.scene.layout.VBox createInvoiceHeader(com.clothingstore.model.Transaction transaction) {
        javafx.scene.layout.VBox header = new javafx.scene.layout.VBox();
        header.setSpacing(20);
        header.setPadding(new javafx.geometry.Insets(0, 0, 20, 0));

        // Company header row
        javafx.scene.layout.HBox companyRow = new javafx.scene.layout.HBox();
        companyRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        companyRow.setSpacing(20);

        // Company logo area
        javafx.scene.layout.VBox logoArea = new javafx.scene.layout.VBox();
        logoArea.setAlignment(javafx.geometry.Pos.CENTER);
        logoArea.setPrefWidth(100);
        logoArea.setPrefHeight(80);
        logoArea.setStyle("-fx-background-color: #007bff; -fx-background-radius: 8;");

        javafx.scene.control.Label logoText = new javafx.scene.control.Label("LOGO");
        logoText.setStyle("-fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;");
        logoArea.getChildren().add(logoText);

        // Company information
        javafx.scene.layout.VBox companyInfo = new javafx.scene.layout.VBox();
        companyInfo.setSpacing(5);

        javafx.scene.control.Label companyName = new javafx.scene.control.Label("FASHION BOUTIQUE");
        companyName.setStyle("-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.Label companyAddress = new javafx.scene.control.Label("123 Fashion Street, Style City, SC 12345");
        companyAddress.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.Label companyContact = new javafx.scene.control.Label("Phone: (************* | Email: <EMAIL>");
        companyContact.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d; -fx-font-family: 'Segoe UI', sans-serif;");

        companyInfo.getChildren().addAll(companyName, companyAddress, companyContact);

        javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        // Invoice details
        javafx.scene.layout.VBox invoiceDetails = createInvoiceDetailsBox(transaction);

        companyRow.getChildren().addAll(logoArea, companyInfo, spacer, invoiceDetails);

        // Invoice title
        javafx.scene.control.Label invoiceTitle = new javafx.scene.control.Label("INVOICE");
        invoiceTitle.setStyle("-fx-font-size: 36px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");
        invoiceTitle.setAlignment(javafx.geometry.Pos.CENTER);

        header.getChildren().addAll(companyRow, invoiceTitle);
        return header;
    }

    /**
     * Creates the invoice details box
     */
    private javafx.scene.layout.VBox createInvoiceDetailsBox(com.clothingstore.model.Transaction transaction) {
        javafx.scene.layout.VBox details = new javafx.scene.layout.VBox();
        details.setSpacing(8);
        details.setAlignment(javafx.geometry.Pos.TOP_RIGHT);
        details.setPadding(new javafx.geometry.Insets(10));
        details.setStyle("-fx-background-color: #f8f9fa; -fx-background-radius: 8; "
                + "-fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-border-width: 1;");

        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy");

        javafx.scene.control.Label invoiceNumLabel = new javafx.scene.control.Label("Invoice #: INV-" + transaction.getTransactionNumber());
        invoiceNumLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        javafx.scene.control.Label invoiceDateLabel = new javafx.scene.control.Label("Invoice Date: " + java.time.LocalDate.now().format(formatter));
        invoiceDateLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d;");

        javafx.scene.control.Label dueDateLabel = new javafx.scene.control.Label("Due Date: " + java.time.LocalDate.now().plusDays(30).format(formatter));
        dueDateLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #dc3545; -fx-font-weight: bold;");

        details.getChildren().addAll(invoiceNumLabel, invoiceDateLabel, dueDateLabel);
        return details;
    }

    /**
     * Creates invoice separator
     */
    private javafx.scene.control.Separator createInvoiceSeparator() {
        javafx.scene.control.Separator separator = new javafx.scene.control.Separator();
        separator.setStyle("-fx-background-color: #dee2e6; -fx-pref-height: 1; -fx-max-height: 1;");
        javafx.scene.layout.VBox.setMargin(separator, new javafx.geometry.Insets(15, 0, 15, 0));
        return separator;
    }

    /**
     * Creates the customer section for invoice
     */
    private javafx.scene.layout.VBox createInvoiceCustomerSection(com.clothingstore.model.Transaction transaction, com.clothingstore.model.Customer customer) {
        javafx.scene.layout.VBox customerSection = new javafx.scene.layout.VBox();
        customerSection.setSpacing(15);

        javafx.scene.control.Label sectionTitle = new javafx.scene.control.Label("BILL TO:");
        sectionTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.layout.HBox customerContainer = new javafx.scene.layout.HBox();
        customerContainer.setSpacing(40);

        // Customer information
        javafx.scene.layout.VBox customerInfo = new javafx.scene.layout.VBox();
        customerInfo.setSpacing(5);

        String customerName = (customer != null) ? customer.getName() : "Walk-in Customer";
        String customerPhone = (customer != null) ? customer.getPhone() : "N/A";
        String customerAddress = (customer != null) ? customer.getAddress() : "N/A";

        javafx.scene.control.Label nameLabel = new javafx.scene.control.Label(customerName);
        nameLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.Label phoneLabel = new javafx.scene.control.Label("Phone: " + customerPhone);
        phoneLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d; -fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.Label addressLabel = new javafx.scene.control.Label("Address: " + customerAddress);
        addressLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d; -fx-font-family: 'Segoe UI', sans-serif;");

        customerInfo.getChildren().addAll(nameLabel, phoneLabel, addressLabel);
        customerContainer.getChildren().add(customerInfo);
        customerSection.getChildren().addAll(sectionTitle, customerContainer);

        return customerSection;
    }

    /**
     * Creates the items section for invoice
     */
    private javafx.scene.layout.VBox createInvoiceItemsSection(com.clothingstore.model.Transaction transaction) {
        javafx.scene.layout.VBox itemsSection = new javafx.scene.layout.VBox();
        itemsSection.setSpacing(10);

        javafx.scene.control.Label sectionTitle = new javafx.scene.control.Label("ITEMS:");
        sectionTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");

        // Create simple items display
        javafx.scene.layout.VBox itemsList = new javafx.scene.layout.VBox();
        itemsList.setSpacing(8);
        itemsList.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 15; -fx-background-radius: 8; "
                + "-fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-border-width: 1;");

        // Add transaction items
        if (transaction.getItems() != null) {
            for (com.clothingstore.model.TransactionItem item : transaction.getItems()) {
                javafx.scene.layout.HBox itemRow = new javafx.scene.layout.HBox();
                itemRow.setSpacing(20);
                itemRow.setStyle("-fx-padding: 8; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");

                javafx.scene.control.Label itemInfo = new javafx.scene.control.Label(
                        item.getProductName() + " (SKU: " + item.getProductSku() + ") - Qty: " + item.getQuantity()
                        + " @ $" + item.getUnitPrice().setScale(2, java.math.RoundingMode.HALF_UP)
                        + " = $" + item.getLineTotal().setScale(2, java.math.RoundingMode.HALF_UP)
                );
                itemInfo.setStyle("-fx-font-size: 14px; -fx-font-family: 'Segoe UI', sans-serif;");

                itemRow.getChildren().add(itemInfo);
                itemsList.getChildren().add(itemRow);
            }
        }

        itemsSection.getChildren().addAll(sectionTitle, itemsList);
        return itemsSection;
    }

    /**
     * Creates the totals section for invoice
     */
    private javafx.scene.layout.VBox createInvoiceTotalsSection(com.clothingstore.model.Transaction transaction) {
        javafx.scene.layout.VBox totalsSection = new javafx.scene.layout.VBox();
        totalsSection.setSpacing(10);

        javafx.scene.layout.HBox totalsContainer = new javafx.scene.layout.HBox();
        totalsContainer.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        javafx.scene.layout.VBox totalsBox = new javafx.scene.layout.VBox();
        totalsBox.setSpacing(8);
        totalsBox.setPadding(new javafx.geometry.Insets(20));
        totalsBox.setStyle("-fx-background-color: #f8f9fa; -fx-background-radius: 8; "
                + "-fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-border-width: 1;");
        totalsBox.setPrefWidth(300);

        // Subtotal
        javafx.scene.layout.HBox subtotalRow = createInvoiceTotalRow("Subtotal:", "$" + transaction.getSubtotal().setScale(2, java.math.RoundingMode.HALF_UP), false);

        // Tax
        javafx.scene.layout.HBox taxRow = createInvoiceTotalRow("Tax (8.5%):", "$" + transaction.getTaxAmount().setScale(2, java.math.RoundingMode.HALF_UP), false);

        // Total
        javafx.scene.control.Separator totalSeparator = new javafx.scene.control.Separator();
        totalSeparator.setStyle("-fx-background-color: #dee2e6; -fx-pref-height: 2; -fx-max-height: 2;");

        javafx.scene.layout.HBox totalRow = createInvoiceTotalRow("TOTAL:", "$" + transaction.getTotalAmount().setScale(2, java.math.RoundingMode.HALF_UP), true);

        totalsBox.getChildren().addAll(subtotalRow, taxRow, totalSeparator, totalRow);
        totalsContainer.getChildren().addAll(spacer, totalsBox);
        totalsSection.getChildren().add(totalsContainer);

        return totalsSection;
    }

    /**
     * Creates a total calculation row for invoice
     */
    private javafx.scene.layout.HBox createInvoiceTotalRow(String label, String amount, boolean isTotal) {
        javafx.scene.layout.HBox row = new javafx.scene.layout.HBox();
        row.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        row.setSpacing(20);

        javafx.scene.control.Label labelText = new javafx.scene.control.Label(label);
        if (isTotal) {
            labelText.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                    + "-fx-font-family: 'Segoe UI', sans-serif;");
        } else {
            labelText.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d; -fx-font-family: 'Segoe UI', sans-serif;");
        }

        javafx.scene.layout.Region spacer = new javafx.scene.layout.Region();
        javafx.scene.layout.HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        javafx.scene.control.Label amountText = new javafx.scene.control.Label(amount);
        if (isTotal) {
            amountText.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #28a745; "
                    + "-fx-font-family: 'Segoe UI', sans-serif;");
        } else {
            amountText.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50; "
                    + "-fx-font-family: 'Segoe UI', sans-serif;");
        }

        row.getChildren().addAll(labelText, spacer, amountText);
        return row;
    }

    /**
     * Creates the invoice footer
     */
    private javafx.scene.layout.VBox createInvoiceFooter() {
        javafx.scene.layout.VBox footer = new javafx.scene.layout.VBox();
        footer.setSpacing(20);
        footer.setPadding(new javafx.geometry.Insets(20, 0, 0, 0));

        // Thank you message
        javafx.scene.layout.VBox thankYou = new javafx.scene.layout.VBox();
        thankYou.setAlignment(javafx.geometry.Pos.CENTER);
        thankYou.setSpacing(5);

        javafx.scene.control.Label thankYouMsg = new javafx.scene.control.Label("Thank you for your business!");
        thankYouMsg.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #007bff; "
                + "-fx-font-family: 'Segoe UI', sans-serif;");

        javafx.scene.control.Label contactMsg = new javafx.scene.control.Label("For questions about this invoice, please contact us at (*************");
        contactMsg.setStyle("-fx-font-size: 12px; -fx-text-fill: #6c757d; -fx-font-family: 'Segoe UI', sans-serif;");

        thankYou.getChildren().addAll(thankYouMsg, contactMsg);

        // Business footer
        javafx.scene.layout.VBox businessFooter = new javafx.scene.layout.VBox();
        businessFooter.setAlignment(javafx.geometry.Pos.CENTER);
        businessFooter.setSpacing(5);
        businessFooter.setPadding(new javafx.geometry.Insets(20, 0, 0, 0));
        businessFooter.setStyle("-fx-border-color: #dee2e6; -fx-border-width: 1 0 0 0;");

        javafx.scene.control.Label businessInfo = new javafx.scene.control.Label("Fashion Boutique | Business License #12345 | Tax ID: 98-7654321");
        businessInfo.setStyle("-fx-font-size: 10px; -fx-text-fill: #adb5bd; -fx-font-family: 'Segoe UI', sans-serif;");

        businessFooter.getChildren().add(businessInfo);

        footer.getChildren().addAll(thankYou, businessFooter);
        return footer;
    }

    /**
     * Creates action buttons for the invoice
     */
    private javafx.scene.layout.HBox createInvoiceActionButtons(javafx.stage.Stage invoiceStage, javafx.scene.layout.VBox invoiceContent) {
        javafx.scene.layout.HBox actionButtons = new javafx.scene.layout.HBox();
        actionButtons.setSpacing(15);
        actionButtons.setAlignment(javafx.geometry.Pos.CENTER);
        actionButtons.setPadding(new javafx.geometry.Insets(20));
        actionButtons.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1 0 0 0;");

        // Print button
        javafx.scene.control.Button printBtn = new javafx.scene.control.Button("🖨 Print Invoice");
        printBtn.setStyle(MODERN_BUTTON_PRIMARY + "-fx-pref-width: 150; -fx-pref-height: 40; -fx-font-size: 14px;");
        printBtn.setOnAction(e -> printInvoice(invoiceContent));

        // Close button with improved action handling
        javafx.scene.control.Button closeBtn = new javafx.scene.control.Button("✖ Close");
        closeBtn.setStyle(MODERN_BUTTON_SECONDARY + "-fx-pref-width: 100; -fx-pref-height: 40; -fx-font-size: 14px;");
        closeBtn.setOnAction(e -> {
            try {
                System.out.println("Close button clicked - attempting to close invoice window");
                invoiceStage.close();
                System.out.println("Invoice window closed successfully");
            } catch (Exception ex) {
                System.err.println("Error closing invoice window: " + ex.getMessage());
                ex.printStackTrace();
                AlertUtil.showError("Close Error", "Failed to close invoice window: " + ex.getMessage());
            }
        });

        // Add hover effect for better user feedback
        closeBtn.setOnMouseEntered(e -> {
            closeBtn.setStyle(MODERN_BUTTON_SECONDARY + "-fx-pref-width: 100; -fx-pref-height: 40; -fx-font-size: 14px; "
                    + "-fx-scale-x: 1.05; -fx-scale-y: 1.05;");
        });
        closeBtn.setOnMouseExited(e -> {
            closeBtn.setStyle(MODERN_BUTTON_SECONDARY + "-fx-pref-width: 100; -fx-pref-height: 40; -fx-font-size: 14px;");
        });

        actionButtons.getChildren().addAll(printBtn, closeBtn);
        return actionButtons;
    }

    /**
     * Prints the invoice content
     */
    private void printInvoice(javafx.scene.layout.VBox invoiceContent) {
        try {
            javafx.print.PrinterJob printerJob = javafx.print.PrinterJob.createPrinterJob();
            if (printerJob != null && printerJob.showPrintDialog(null)) {
                boolean success = printerJob.printPage(invoiceContent);
                if (success) {
                    printerJob.endJob();
                    AlertUtil.showSuccess("Print Success", "Invoice printed successfully!");
                } else {
                    AlertUtil.showError("Print Error", "Failed to print invoice.");
                }
            }
        } catch (Exception e) {
            AlertUtil.showError("Print Error", "Failed to print invoice: " + e.getMessage());
        }
    }
}
