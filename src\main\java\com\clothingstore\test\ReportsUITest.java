package com.clothingstore.test;

import com.clothingstore.view.ReportsController;
import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Test application for the new Reports UI
 */
public class ReportsUITest extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            System.out.println("Loading Reports.fxml...");
            
            // Load the Reports FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/Reports.fxml"));
            VBox root = loader.load();
            
            System.out.println("Reports.fxml loaded successfully!");
            
            // Get the controller
            ReportsController controller = loader.getController();
            System.out.println("ReportsController initialized: " + (controller != null ? "YES" : "NO"));

            // Create scene
            Scene scene = new Scene(root, 1000, 700);
            
            primaryStage.setTitle("Profit Analysis Reports - UI Test");
            primaryStage.setScene(scene);
            primaryStage.show();
            
            System.out.println("Reports UI Test launched successfully!");
            System.out.println("Test the following features:");
            System.out.println("1. Date range selection (default: last 30 days)");
            System.out.println("2. Generate Report button functionality");
            System.out.println("3. Progress indicator during report generation");
            System.out.println("4. Profit metrics display in cards");
            System.out.println("5. Status messages and error handling");
            
        } catch (Exception e) {
            System.err.println("Failed to load Reports UI: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        System.out.println("=== REPORTS UI TEST ===");
        System.out.println("Testing the new clean Reports interface...\n");
        
        launch(args);
    }
}
