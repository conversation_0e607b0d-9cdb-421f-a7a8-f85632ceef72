<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.CashDrawerController">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="header-section">
         <children>
            <Label styleClass="page-title" text="Cash Drawer Management" />
            <Region HBox.hgrow="ALWAYS" />
            <Button onAction="#handleGenerateShiftReport" styleClass="secondary-button" text="📋 Shift Report" />
            <Button onAction="#handleRefreshData" styleClass="primary-button" text="🔄 Refresh" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <!-- Current Drawer Status Section -->
      <VBox spacing="15.0" styleClass="current-drawer-section">
         <children>
            <Label styleClass="section-title" text="Current Drawer Status" />
            
            <!-- Status Cards -->
            <HBox spacing="15.0">
               <children>
                  <!-- Drawer Info Card -->
                  <VBox spacing="10.0" styleClass="status-card" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="card-title" text="💰 Drawer Information" />
                        <GridPane hgap="10.0" vgap="8.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           
                           <Label styleClass="info-label" text="Drawer:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                           <Label fx:id="currentDrawerLabel" styleClass="info-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                           
                           <Label styleClass="info-label" text="Cashier:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                           <Label fx:id="currentCashierLabel" styleClass="info-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                           
                           <Label styleClass="info-label" text="Status:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                           <Label fx:id="drawerStatusLabel" styleClass="info-value status-badge" text="--" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                           
                           <Label styleClass="info-label" text="Opened:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                           <Label fx:id="openedAtLabel" styleClass="info-value" text="--" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        </GridPane>
                     </children>
                  </VBox>
                  
                  <!-- Financial Summary Card -->
                  <VBox spacing="10.0" styleClass="status-card" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="card-title" text="💵 Financial Summary" />
                        <GridPane hgap="10.0" vgap="8.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           
                           <Label styleClass="info-label" text="Opening Amount:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                           <Label fx:id="openingAmountLabel" styleClass="info-value money" text="--" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                           
                           <Label styleClass="info-label" text="Total Sales:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                           <Label fx:id="totalSalesLabel" styleClass="info-value money" text="--" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                           
                           <Label styleClass="info-label" text="Cash Drops:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                           <Label fx:id="totalCashDropsLabel" styleClass="info-value money" text="--" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                           
                           <Label styleClass="info-label" text="Expected:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                           <Label fx:id="currentExpectedLabel" styleClass="info-value money expected" text="--" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        </GridPane>
                     </children>
                  </VBox>
                  
                  <!-- Transaction Summary Card -->
                  <VBox spacing="10.0" styleClass="status-card" HBox.hgrow="ALWAYS">
                     <children>
                        <Label styleClass="card-title" text="📊 Transaction Summary" />
                        <VBox alignment="CENTER" spacing="15.0">
                           <children>
                              <VBox alignment="CENTER" spacing="5.0">
                                 <children>
                                    <Label styleClass="metric-label" text="Total Transactions" />
                                    <Label fx:id="transactionCountLabel" styleClass="metric-value" text="--" />
                                 </children>
                              </VBox>
                           </children>
                        </VBox>
                     </children>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Action Buttons -->
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <Button fx:id="openDrawerButton" onAction="#handleOpenDrawer" styleClass="action-button open" text="🔓 Open Drawer" />
                  <Button fx:id="closeDrawerButton" onAction="#handleCloseDrawer" styleClass="action-button close" text="🔒 Close Drawer" />
                  <Button fx:id="cashDropButton" onAction="#handleCashDrop" styleClass="action-button drop" text="💸 Cash Drop" />
                  <Button fx:id="payoutButton" onAction="#handlePayout" styleClass="action-button payout" text="💰 Payout" />
                  <Button fx:id="reconcileButton" onAction="#handleReconcile" styleClass="action-button reconcile" text="✅ Reconcile" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </VBox>

      <!-- History and Reports Section -->
      <TabPane VBox.vgrow="ALWAYS">
         <tabs>
            <!-- Drawer History Tab -->
            <Tab text="📈 Drawer History" closable="false">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <!-- Filter Controls -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-row">
                           <children>
                              <Label styleClass="filter-label" text="📅 From:" />
                              <DatePicker fx:id="startDatePicker" prefWidth="130.0" styleClass="form-field" />
                              <Label styleClass="filter-label" text="To:" />
                              <DatePicker fx:id="endDatePicker" prefWidth="130.0" styleClass="form-field" />
                              <Label styleClass="filter-label" text="Status:" />
                              <ComboBox fx:id="statusFilter" prefWidth="120.0" styleClass="combo-box" />
                              <Label styleClass="filter-label" text="Cashier:" />
                              <TextField fx:id="cashierFilter" prefWidth="150.0" promptText="Filter by cashier" styleClass="search-box" />
                           </children>
                        </HBox>
                        
                        <!-- Drawer History Table -->
                        <TableView fx:id="drawerHistoryTable" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="drawerNumberColumn" prefWidth="100.0" text="Drawer" />
                              <TableColumn fx:id="cashierColumn" prefWidth="150.0" text="Cashier" />
                              <TableColumn fx:id="openedColumn" prefWidth="140.0" text="Opened" />
                              <TableColumn fx:id="closedColumn" prefWidth="140.0" text="Closed" />
                              <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Status" />
                              <TableColumn fx:id="varianceColumn" prefWidth="120.0" text="Variance" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>

            <!-- Cash Drops Tab -->
            <Tab text="💸 Cash Drops" closable="false">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <Label styleClass="section-title" text="Cash Drop History" />
                        
                        <!-- Cash Drops Table -->
                        <TableView fx:id="cashDropsTable" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="dropTimeColumn" prefWidth="140.0" text="Time" />
                              <TableColumn fx:id="dropAmountColumn" prefWidth="120.0" text="Amount" />
                              <TableColumn fx:id="dropReasonColumn" prefWidth="150.0" text="Reason" />
                              <TableColumn fx:id="dropStatusColumn" prefWidth="100.0" text="Status" />
                           </columns>
                        </TableView>
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </children>
</VBox>
