package com.clothingstore.util;

import com.clothingstore.database.DatabaseManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Logger;

/**
 * Database schema updater for refund tracking enhancements
 */
public class RefundTrackingSchemaUpdater {

    private static final Logger LOGGER = Logger.getLogger(RefundTrackingSchemaUpdater.class.getName());

    /**
     * Update database schema to support refund amount tracking
     */
    public static void updateSchemaForRefundTracking() throws SQLException {
        LOGGER.info("Starting refund tracking database schema updates...");

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Add refunded_amount column to transactions table
            addRefundedAmountColumn(conn);

            // Update existing refunded transactions
            updateExistingRefundedTransactions(conn);

            LOGGER.info("Refund tracking database schema updates completed successfully");
        }
    }

    /**
     * Add refunded_amount column to transactions table
     */
    private static void addRefundedAmountColumn(Connection conn) throws SQLException {
        // Check if column already exists
        boolean columnExists = false;
        try (PreparedStatement stmt = conn.prepareStatement("PRAGMA table_info(transactions)"); ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                String columnName = rs.getString("name");
                if ("refunded_amount".equals(columnName)) {
                    columnExists = true;
                    break;
                }
            }
        }

        if (!columnExists) {
            LOGGER.info("Adding refunded_amount column to transactions table");
            try (PreparedStatement stmt = conn.prepareStatement(
                    "ALTER TABLE transactions ADD COLUMN refunded_amount DECIMAL(10,2) DEFAULT 0.00")) {
                stmt.executeUpdate();
                LOGGER.info("Successfully added refunded_amount column");
            }
        } else {
            LOGGER.info("refunded_amount column already exists");
        }
    }

    /**
     * Update existing refunded transactions to set proper refunded_amount
     */
    private static void updateExistingRefundedTransactions(Connection conn) throws SQLException {
        LOGGER.info("Updating existing refunded transactions");

        // Update fully refunded transactions
        String updateFullRefunds = "UPDATE transactions "
                + "SET refunded_amount = total_amount "
                + "WHERE status = 'REFUNDED' AND (refunded_amount IS NULL OR refunded_amount = 0)";

        try (PreparedStatement stmt = conn.prepareStatement(updateFullRefunds)) {
            int updated = stmt.executeUpdate();
            LOGGER.info("Updated " + updated + " fully refunded transactions");
        }

        // For partially refunded transactions, we can't determine the exact amount
        // so we'll leave them as 0 and they'll be updated when new partial refunds occur
        String updatePartialRefunds = "UPDATE transactions "
                + "SET refunded_amount = 0.00 "
                + "WHERE status = 'PARTIALLY_REFUNDED' AND refunded_amount IS NULL";

        try (PreparedStatement stmt = conn.prepareStatement(updatePartialRefunds)) {
            int updated = stmt.executeUpdate();
            LOGGER.info("Initialized " + updated + " partially refunded transactions (amount tracking will start from next refund)");
        }
    }

    /**
     * Run all refund tracking schema updates
     */
    public static void runAllUpdates() {
        try {
            updateSchemaForRefundTracking();
        } catch (SQLException e) {
            LOGGER.severe("Failed to update refund tracking schema: " + e.getMessage());
            throw new RuntimeException("Database schema update failed", e);
        }
    }

    /**
     * Main method for running schema updates
     */
    public static void main(String[] args) {
        System.out.println("Running refund tracking schema updates...");
        runAllUpdates();
        System.out.println("Schema updates completed successfully!");
    }
}
