package com.clothingstore;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.util.PaymentHistorySchemaUpdater;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

/**
 * Main Application Class for Clothing Store POS & Inventory Management System
 *
 * This JavaFX application provides a comprehensive solution for managing
 * clothing store operations including inventory, sales, and customer
 * management.
 *
 * <AUTHOR> Store Management System
 * @version 1.0
 */
public class ClothingStoreApp extends Application {

    private static final String APP_TITLE = "Clothing Store Management System";
    private static final String MAIN_FXML = "/fxml/MainWindow.fxml";
    private static final int MIN_WIDTH = 1200;
    private static final int MIN_HEIGHT = 800;

    @Override
    public void start(Stage primaryStage) {
        try {
            // Initialize database
            DatabaseManager.getInstance().initializeDatabase();

            // Initialize payment history schema (critical fix for Outstanding Balances)
            System.out.println("Initializing payment history database schema...");
            PaymentHistorySchemaUpdater.runAllUpdates();
            System.out.println("Payment history schema initialization completed.");

            // Initialize low stock alert service
            com.clothingstore.service.LowStockAlertService.getInstance();

            // Load main FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource(MAIN_FXML));
            Parent root = loader.load();

            // Create scene with modern styling
            Scene scene = new Scene(root);
            scene.getStylesheets().add(getClass().getResource("/css/main-style.css").toExternalForm());

            // Configure primary stage
            primaryStage.setTitle(APP_TITLE);
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(MIN_WIDTH);
            primaryStage.setMinHeight(MIN_HEIGHT);
            primaryStage.setMaximized(true);

            // Show the application
            primaryStage.show();

        } catch (Exception e) {
            System.err.println("Application Startup Error: Failed to start the application: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void stop() {
        try {
            // Shutdown low stock alert service
            com.clothingstore.service.LowStockAlertService.getInstance().shutdown();

            // Clean up database connections
            DatabaseManager.getInstance().closeConnection();
        } catch (Exception e) {
            System.err.println("Error during application shutdown: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}
