<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.InventoryMovementReportController">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label text="Inventory Movement Report" textFill="#2c3e50">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnRefresh" mnemonicParsing="false" onAction="#handleRefresh" text="Refresh" />
            <Button fx:id="btnExport" mnemonicParsing="false" onAction="#handleExport" text="Export" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <Separator />

      <!-- Date Range Section -->
      <HBox alignment="CENTER_LEFT" spacing="15.0">
         <children>
            <Label text="Date Range:" />
            <DatePicker fx:id="dpStartDate" promptText="Start Date" />
            <Label text="to" />
            <DatePicker fx:id="dpEndDate" promptText="End Date" />
            <Button fx:id="btnGenerateReport" mnemonicParsing="false" onAction="#handleGenerateReport" text="Generate Report" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <Separator />

      <!-- Summary Statistics Section -->
      <HBox spacing="20.0">
         <children>
            <!-- Items Sold/Processed Statistics -->
            <VBox alignment="CENTER" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
               <children>
                  <Label text="Items Sold/Processed" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Label text="Count:" />
                  <Label fx:id="lblSoldItemCount" text="0" textFill="#27ae60">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Quantity:" />
                  <Label fx:id="lblSoldQuantity" text="0" textFill="#27ae60">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Value:" />
                  <Label fx:id="lblSoldValue" text="$0.00" textFill="#27ae60">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
               </children>
            </VBox>

            <!-- Items Returned/Refunded Statistics -->
            <VBox alignment="CENTER" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
               <children>
                  <Label text="Items Returned/Refunded" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Label text="Count:" />
                  <Label fx:id="lblReturnedItemCount" text="0" textFill="#e74c3c">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Quantity:" />
                  <Label fx:id="lblReturnedQuantity" text="0" textFill="#e74c3c">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Value:" />
                  <Label fx:id="lblReturnedValue" text="$0.00" textFill="#e74c3c">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
               </children>
            </VBox>

            <!-- Net Movement Statistics -->
            <VBox alignment="CENTER" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
               <children>
                  <Label text="Net Movement" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Label text="Net Quantity:" />
                  <Label fx:id="lblNetQuantity" text="0" textFill="#34495e">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Net Value:" />
                  <Label fx:id="lblNetValue" text="$0.00" textFill="#34495e">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Return Rate:" />
                  <Label fx:id="lblReturnRate" text="0.00%" textFill="#f39c12">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <Separator />

      <!-- Main Content - Tabbed Interface -->
      <TabPane tabClosingPolicy="UNAVAILABLE" VBox.vgrow="ALWAYS">
         <tabs>
            <!-- Items Sold/Processed Tab -->
            <Tab text="Items Sold/Processed">
               <content>
                  <VBox>
                     <children>
                        <Label text="All items sold through completed transactions" textFill="#27ae60">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                           <VBox.margin>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </VBox.margin>
                        </Label>
                        <TableView fx:id="tblSoldItems" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="colSoldDate" prefWidth="80.0" text="Date" />
                              <TableColumn fx:id="colSoldTransaction" prefWidth="120.0" text="Transaction" />
                              <TableColumn fx:id="colSoldProduct" prefWidth="150.0" text="Product" />
                              <TableColumn fx:id="colSoldSku" prefWidth="80.0" text="SKU" />
                              <TableColumn fx:id="colSoldCategory" prefWidth="100.0" text="Category" />
                              <TableColumn fx:id="colSoldQuantity" prefWidth="60.0" text="Qty" />
                              <TableColumn fx:id="colSoldUnitPrice" prefWidth="80.0" text="Unit Price" />
                              <TableColumn fx:id="colSoldLineTotal" prefWidth="90.0" text="Line Total" />
                              <TableColumn fx:id="colSoldCustomer" prefWidth="120.0" text="Customer" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>

            <!-- Items Returned/Refunded Tab -->
            <Tab text="Items Returned/Refunded">
               <content>
                  <VBox>
                     <children>
                        <Label text="All items returned to inventory through refunds and cancellations" textFill="#e74c3c">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                           <VBox.margin>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </VBox.margin>
                        </Label>
                        <TableView fx:id="tblReturnedItems" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="colReturnedDate" prefWidth="80.0" text="Date" />
                              <TableColumn fx:id="colReturnedTransaction" prefWidth="120.0" text="Transaction" />
                              <TableColumn fx:id="colReturnedProduct" prefWidth="130.0" text="Product" />
                              <TableColumn fx:id="colReturnedSku" prefWidth="80.0" text="SKU" />
                              <TableColumn fx:id="colReturnedCategory" prefWidth="90.0" text="Category" />
                              <TableColumn fx:id="colReturnedQuantity" prefWidth="60.0" text="Qty" />
                              <TableColumn fx:id="colReturnedUnitPrice" prefWidth="80.0" text="Unit Price" />
                              <TableColumn fx:id="colReturnedLineTotal" prefWidth="90.0" text="Line Total" />
                              <TableColumn fx:id="colReturnedType" prefWidth="80.0" text="Type" />
                              <TableColumn fx:id="colReturnedReason" prefWidth="150.0" text="Reason" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </children>
</VBox>
