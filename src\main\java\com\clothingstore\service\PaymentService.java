package com.clothingstore.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.clothingstore.model.Payment;
import com.clothingstore.model.PaymentMethod;
import com.clothingstore.model.PaymentStatus;
import com.clothingstore.model.Transaction;

/**
 * Service for handling payment processing including split payments
 */
public class PaymentService {

    private static final Logger LOGGER = Logger.getLogger(PaymentService.class.getName());
    private static PaymentService instance;

    private PaymentService() {
    }

    public static synchronized PaymentService getInstance() {
        if (instance == null) {
            instance = new PaymentService();
        }
        return instance;
    }

    /**
     * Process a single payment for a transaction
     */
    public PaymentResult processSinglePayment(Transaction transaction, PaymentMethod method, BigDecimal amount, String reference) {
        try {
            Payment payment = new Payment(method, amount);
            payment.setTransactionId(transaction.getId());
            payment.setReference(reference);

            // Validate payment amount
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return new PaymentResult(false, "Payment amount must be greater than zero", null);
            }

            if (amount.compareTo(transaction.getTotalAmount()) > 0 && !method.requiresChangeCalculation()) {
                return new PaymentResult(false, "Payment amount exceeds transaction total", null);
            }

            // Process payment based on method
            boolean success = processPaymentByMethod(payment);

            if (success) {
                payment.setStatus(PaymentStatus.COMPLETED);

                // Calculate change if needed
                BigDecimal change = BigDecimal.ZERO;
                if (method.requiresChangeCalculation()) {
                    change = amount.subtract(transaction.getTotalAmount());
                    if (change.compareTo(BigDecimal.ZERO) < 0) {
                        change = BigDecimal.ZERO;
                    }
                }

                return new PaymentResult(true, "Payment processed successfully", payment, change);
            } else {
                payment.setStatus(PaymentStatus.FAILED);
                return new PaymentResult(false, "Payment processing failed", payment);
            }

        } catch (Exception e) {
            LOGGER.severe("Error processing payment: " + e.getMessage());
            return new PaymentResult(false, "Payment processing error: " + e.getMessage(), null);
        }
    }

    /**
     * Process split payments for a transaction
     */
    public SplitPaymentResult processSplitPayments(Transaction transaction, List<Payment> payments) {
        try {
            List<Payment> processedPayments = new ArrayList<>();
            BigDecimal totalPaid = BigDecimal.ZERO;
            BigDecimal transactionTotal = transaction.getTotalAmount();

            // Validate total payment amount
            BigDecimal totalPaymentAmount = payments.stream()
                    .map(Payment::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalPaymentAmount.compareTo(transactionTotal) < 0) {
                return new SplitPaymentResult(false, "Total payment amount is less than transaction total",
                        processedPayments, totalPaid, transactionTotal.subtract(totalPaymentAmount));
            }

            // Process each payment
            for (Payment payment : payments) {
                payment.setTransactionId(transaction.getId());

                boolean success = processPaymentByMethod(payment);
                if (success) {
                    payment.setStatus(PaymentStatus.COMPLETED);
                    totalPaid = totalPaid.add(payment.getAmount());
                } else {
                    payment.setStatus(PaymentStatus.FAILED);
                }

                processedPayments.add(payment);
            }

            // Calculate remaining balance
            BigDecimal remainingBalance = transactionTotal.subtract(totalPaid);

            // Calculate change (only for cash payments)
            BigDecimal change = BigDecimal.ZERO;
            if (totalPaid.compareTo(transactionTotal) > 0) {
                // Check if any cash payment can provide change
                boolean hasCashPayment = payments.stream()
                        .anyMatch(p -> p.getPaymentMethod().requiresChangeCalculation());

                if (hasCashPayment) {
                    change = totalPaid.subtract(transactionTotal);
                }
            }

            boolean isFullyPaid = remainingBalance.compareTo(BigDecimal.ZERO) <= 0;
            String message = isFullyPaid ? "All payments processed successfully"
                    : "Partial payment processed. Remaining balance: $" + remainingBalance;

            return new SplitPaymentResult(isFullyPaid, message, processedPayments, totalPaid, remainingBalance, change);

        } catch (Exception e) {
            LOGGER.severe("Error processing split payments: " + e.getMessage());
            return new SplitPaymentResult(false, "Split payment processing error: " + e.getMessage(),
                    new ArrayList<>(), BigDecimal.ZERO, transaction.getTotalAmount());
        }
    }

    /**
     * Process payment based on payment method
     */
    private boolean processPaymentByMethod(Payment payment) {
        try {
            switch (payment.getPaymentMethod()) {
                case CASH:
                    return processCashPayment(payment);
                case CREDIT_CARD:
                case DEBIT_CARD:
                    return processCardPayment(payment);
                case MOBILE_PAYMENT:
                    return processMobilePayment(payment);
                case STORE_CREDIT:
                    return processStoreCreditPayment(payment);
                case CHECK:
                    return processCheckPayment(payment);
                case BANK_TRANSFER:
                    return processBankTransferPayment(payment);
                case LAYAWAY:
                    return processLayawayPayment(payment);
                default:
                    LOGGER.warning("Unknown payment method: " + payment.getPaymentMethod());
                    return false;
            }
        } catch (Exception e) {
            LOGGER.severe("Error processing payment by method: " + e.getMessage());
            return false;
        }
    }

    // Payment method specific processors
    private boolean processCashPayment(Payment payment) {
        // Cash payments are always successful (assuming correct amount)
        LOGGER.info("Processing cash payment: $" + payment.getAmount());
        return true;
    }

    private boolean processCardPayment(Payment payment) {
        // Simulate card processing
        LOGGER.info("Processing card payment: $" + payment.getAmount());

        // In a real implementation, this would integrate with a payment processor
        // For now, we'll simulate a successful transaction
        payment.setAuthorizationCode("AUTH" + System.currentTimeMillis());
        return true;
    }

    private boolean processMobilePayment(Payment payment) {
        // Simulate mobile payment processing
        LOGGER.info("Processing mobile payment: $" + payment.getAmount());
        payment.setAuthorizationCode("MOBILE" + System.currentTimeMillis());
        return true;
    }

    private boolean processStoreCreditPayment(Payment payment) {
        // In a real implementation, this would check store credit balance
        LOGGER.info("Processing store credit payment: $" + payment.getAmount());
        return true;
    }

    private boolean processCheckPayment(Payment payment) {
        // Check payments require manual verification
        LOGGER.info("Processing check payment: $" + payment.getAmount());
        return true;
    }

    private boolean processBankTransferPayment(Payment payment) {
        // Bank transfers require external verification
        LOGGER.info("Processing bank transfer payment: $" + payment.getAmount());
        return true;
    }

    private boolean processLayawayPayment(Payment payment) {
        // Layaway payments are partial payments
        LOGGER.info("Processing layaway payment: $" + payment.getAmount());
        return true;
    }

    /**
     * Calculate change for cash payments
     */
    public BigDecimal calculateChange(BigDecimal amountPaid, BigDecimal totalDue) {
        BigDecimal change = amountPaid.subtract(totalDue);
        return change.compareTo(BigDecimal.ZERO) > 0 ? change.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
    }

    /**
     * Validate payment amount
     */
    public boolean isValidPaymentAmount(BigDecimal amount, PaymentMethod method, BigDecimal transactionTotal) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // For non-cash payments, amount shouldn't exceed transaction total
        if (!method.requiresChangeCalculation() && amount.compareTo(transactionTotal) > 0) {
            return false;
        }

        return true;
    }
}
