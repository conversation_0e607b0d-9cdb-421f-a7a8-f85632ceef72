package com.clothingstore.service;

import com.clothingstore.model.Supplier;

/**
 * Result class for supplier operations
 */
public class SupplierOperationResult {

    private final boolean success;
    private final String message;
    private final Supplier supplier;

    public SupplierOperationResult(boolean success, String message, Supplier supplier) {
        this.success = success;
        this.message = message;
        this.supplier = supplier;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    @Override
    public String toString() {
        return String.format("SupplierOperationResult{success=%s, message='%s', supplier=%s}",
                success, message, supplier != null ? supplier.getDisplayName() : "null");
    }
}
