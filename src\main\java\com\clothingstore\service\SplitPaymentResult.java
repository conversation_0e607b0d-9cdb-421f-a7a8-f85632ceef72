package com.clothingstore.service;

import com.clothingstore.model.Payment;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Result class for split payment processing
 */
public class SplitPaymentResult {
    
    private final boolean success;
    private final String message;
    private final List<Payment> payments;
    private final BigDecimal totalPaid;
    private final BigDecimal remainingBalance;
    private final BigDecimal change;
    
    public SplitPaymentResult(boolean success, String message, List<Payment> payments, 
                             BigDecimal totalPaid, BigDecimal remainingBalance) {
        this(success, message, payments, totalPaid, remainingBalance, BigDecimal.ZERO);
    }
    
    public SplitPaymentResult(boolean success, String message, List<Payment> payments, 
                             BigDecimal totalPaid, BigDecimal remainingBalance, BigDecimal change) {
        this.success = success;
        this.message = message;
        this.payments = payments != null ? payments : new ArrayList<>();
        this.totalPaid = totalPaid != null ? totalPaid : BigDecimal.ZERO;
        this.remainingBalance = remainingBalance != null ? remainingBalance : BigDecimal.ZERO;
        this.change = change != null ? change : BigDecimal.ZERO;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public List<Payment> getPayments() {
        return payments;
    }
    
    public BigDecimal getTotalPaid() {
        return totalPaid;
    }
    
    public BigDecimal getRemainingBalance() {
        return remainingBalance;
    }
    
    public BigDecimal getChange() {
        return change;
    }
    
    public boolean hasChange() {
        return change.compareTo(BigDecimal.ZERO) > 0;
    }
    
    public boolean isFullyPaid() {
        return remainingBalance.compareTo(BigDecimal.ZERO) <= 0;
    }
}
