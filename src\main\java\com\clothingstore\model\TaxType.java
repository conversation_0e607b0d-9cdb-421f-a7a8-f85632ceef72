package com.clothingstore.model;

/**
 * Enumeration for tax types
 */
public enum TaxType {
    SALES_TAX("Sales Tax"),
    VAT("Value Added Tax"),
    EXCISE_TAX("Excise Tax"),
    LUXURY_TAX("Luxury Tax"),
    ENVIRONMENTAL_TAX("Environmental Tax"),
    IMPORT_DUTY("Import Duty");
    
    private final String displayName;
    
    TaxType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
