package com.clothingstore.database;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * Optimized database queries for profit analysis with performance enhancements
 */
public class OptimizedProfitQueries {

    private static final Logger LOGGER = Logger.getLogger(OptimizedProfitQueries.class.getName());
    private static final DateTimeFormatter DB_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final ConnectionPool connectionPool;

    public OptimizedProfitQueries() {
        this.connectionPool = ConnectionPool.getInstance();
        createOptimizedIndexes();
    }

    /**
     * Create database indexes for optimal profit query performance
     */
    private void createOptimizedIndexes() {
        Connection conn = null;
        try {
            conn = connectionPool.getConnection();

            // Index recommendations for profit analysis queries
            String[] indexQueries = {
                // Transaction table indexes
                "CREATE INDEX IF NOT EXISTS idx_transactions_date_status ON transactions(transaction_date, status)",
                "CREATE INDEX IF NOT EXISTS idx_transactions_status_refunded ON transactions(status, refunded_amount)",
                "CREATE INDEX IF NOT EXISTS idx_transactions_date_range ON transactions(transaction_date)",
                // Transaction items indexes
                "CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction_id ON transaction_items(transaction_id)",
                "CREATE INDEX IF NOT EXISTS idx_transaction_items_product_id ON transaction_items(product_id)",
                "CREATE INDEX IF NOT EXISTS idx_transaction_items_composite ON transaction_items(transaction_id, product_id)",
                // Products table indexes
                "CREATE INDEX IF NOT EXISTS idx_products_cost_price ON products(cost_price)",
                "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
                "CREATE INDEX IF NOT EXISTS idx_products_category_cost ON products(category, cost_price)",
                // Customers table index
                "CREATE INDEX IF NOT EXISTS idx_customers_id ON customers(id)"
            };

            for (String indexQuery : indexQueries) {
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute(indexQuery);
                }
            }

            // Analyze tables for query optimization
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ANALYZE");
            }

            LOGGER.info("Database indexes created and analyzed for optimal profit query performance");

        } catch (SQLException e) {
            LOGGER.warning("Error creating database indexes: " + e.getMessage());
        } finally {
            if (conn != null) {
                connectionPool.returnConnection(conn);
            }
        }
    }

    /**
     * Optimized query for profit metrics calculation
     */
    public ProfitQueryResult calculateProfitMetrics(LocalDateTime startDate, LocalDateTime endDate) {
        String sql = "SELECT "
                + "COUNT(DISTINCT t.id) as transaction_count, "
                + "SUM(ti.quantity) as total_items_sold, "
                + "SUM(ti.line_total) as total_revenue, "
                + "SUM(p.cost_price * ti.quantity) as total_cost "
                + "FROM transactions t "
                + "INNER JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "INNER JOIN products p ON ti.product_id = p.id "
                + "WHERE t.status = 'COMPLETED' "
                + "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) "
                + "AND CAST(t.transaction_date AS INTEGER) BETWEEN ? AND ? "
                + "AND p.cost_price IS NOT NULL";

        Connection conn = null;
        try {
            conn = connectionPool.getConnection();

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                // Convert LocalDateTime to epoch milliseconds for comparison
                long startEpoch = startDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                long endEpoch = endDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();

                stmt.setLong(1, startEpoch);
                stmt.setLong(2, endEpoch);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return new ProfitQueryResult(
                                rs.getInt("transaction_count"),
                                rs.getInt("total_items_sold"),
                                rs.getDouble("total_revenue"),
                                rs.getDouble("total_cost")
                        );
                    }
                }
            }

        } catch (SQLException e) {
            LOGGER.severe(String.format("Error calculating profit metrics: %s", e.getMessage()));
        } finally {
            if (conn != null) {
                connectionPool.returnConnection(conn);
            }
        }

        return new ProfitQueryResult(0, 0, 0.0, 0.0);
    }

    /**
     * Optimized query for category profit breakdown
     */
    public List<CategoryProfitResult> calculateCategoryProfitBreakdown(LocalDateTime startDate, LocalDateTime endDate) {
        String sql = "SELECT "
                + "COALESCE(p.category, 'Uncategorized') as category, "
                + "COUNT(DISTINCT t.id) as transaction_count, "
                + "SUM(ti.quantity) as items_sold, "
                + "SUM(ti.line_total) as revenue, "
                + "SUM(p.cost_price * ti.quantity) as cost "
                + "FROM transactions t "
                + "INNER JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "INNER JOIN products p ON ti.product_id = p.id "
                + "WHERE t.status = 'COMPLETED' "
                + "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) "
                + "AND CAST(t.transaction_date AS INTEGER) BETWEEN ? AND ? "
                + "AND p.cost_price IS NOT NULL "
                + "GROUP BY p.category "
                + "ORDER BY (SUM(ti.line_total) - SUM(p.cost_price * ti.quantity)) DESC";

        List<CategoryProfitResult> results = new ArrayList<>();
        Connection conn = null;

        try {
            conn = connectionPool.getConnection();

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                // Convert LocalDateTime to epoch milliseconds for comparison
                long startEpoch = startDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                long endEpoch = endDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();

                stmt.setLong(1, startEpoch);
                stmt.setLong(2, endEpoch);

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        results.add(new CategoryProfitResult(
                                rs.getString("category"),
                                rs.getInt("transaction_count"),
                                rs.getInt("items_sold"),
                                rs.getDouble("revenue"),
                                rs.getDouble("cost")
                        ));
                    }
                }
            }

        } catch (SQLException e) {
            LOGGER.severe(String.format("Error calculating category profit breakdown: %s", e.getMessage()));
        } finally {
            if (conn != null) {
                connectionPool.returnConnection(conn);
            }
        }

        return results;
    }

    /**
     * Optimized query for product profit breakdown with pagination
     */
    public List<ProductProfitResult> calculateProductProfitBreakdown(LocalDateTime startDate, LocalDateTime endDate,
            int limit, int offset) {
        String sql = "SELECT "
                + "p.id as product_id, "
                + "p.name as product_name, "
                + "p.sku, "
                + "COALESCE(p.category, 'Uncategorized') as category, "
                + "COUNT(DISTINCT t.id) as transaction_count, "
                + "SUM(ti.quantity) as quantity_sold, "
                + "SUM(ti.line_total) as revenue, "
                + "SUM(p.cost_price * ti.quantity) as cost "
                + "FROM transactions t "
                + "INNER JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "INNER JOIN products p ON ti.product_id = p.id "
                + "WHERE t.status = 'COMPLETED' "
                + "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) "
                + "AND CAST(t.transaction_date AS INTEGER) BETWEEN ? AND ? "
                + "AND p.cost_price IS NOT NULL "
                + "GROUP BY p.id, p.name, p.sku, p.category "
                + "ORDER BY (SUM(ti.line_total) - SUM(p.cost_price * ti.quantity)) DESC "
                + "LIMIT ? OFFSET ?";

        List<ProductProfitResult> results = new ArrayList<>();
        Connection conn = null;

        try {
            conn = connectionPool.getConnection();

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                // Convert LocalDateTime to epoch milliseconds for comparison
                long startEpoch = startDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                long endEpoch = endDate.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();

                stmt.setLong(1, startEpoch);
                stmt.setLong(2, endEpoch);
                stmt.setInt(3, limit);
                stmt.setInt(4, offset);

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        results.add(new ProductProfitResult(
                                rs.getLong("product_id"),
                                rs.getString("product_name"),
                                rs.getString("sku"),
                                rs.getString("category"),
                                rs.getInt("transaction_count"),
                                rs.getInt("quantity_sold"),
                                rs.getDouble("revenue"),
                                rs.getDouble("cost")
                        ));
                    }
                }
            }

        } catch (SQLException e) {
            LOGGER.severe(String.format("Error calculating product profit breakdown: %s", e.getMessage()));
        } finally {
            if (conn != null) {
                connectionPool.returnConnection(conn);
            }
        }

        return results;
    }

    /**
     * Get profit trend data for forecasting
     */
    public List<ProfitTrendResult> getProfitTrendData(LocalDateTime startDate, LocalDateTime endDate, String interval) {
        String dateFormat;
        switch (interval.toLowerCase()) {
            case "daily":
                dateFormat = "%Y-%m-%d";
                break;
            case "weekly":
                dateFormat = "%Y-%W";
                break;
            case "monthly":
                dateFormat = "%Y-%m";
                break;
            default:
                dateFormat = "%Y-%m-%d";
                break;
        }

        String sql = String.format("SELECT "
                + "strftime('%s', t.transaction_date) as period, "
                + "COUNT(DISTINCT t.id) as transaction_count, "
                + "SUM(ti.line_total) as revenue, "
                + "SUM(p.cost_price * ti.quantity) as cost "
                + "FROM transactions t "
                + "INNER JOIN transaction_items ti ON t.id = ti.transaction_id "
                + "INNER JOIN products p ON ti.product_id = p.id "
                + "WHERE t.status = 'COMPLETED' "
                + "AND (t.refunded_amount IS NULL OR t.refunded_amount = 0) "
                + "AND t.transaction_date BETWEEN ? AND ? "
                + "AND p.cost_price IS NOT NULL "
                + "GROUP BY strftime('%s', t.transaction_date) "
                + "ORDER BY period", dateFormat, dateFormat);

        List<ProfitTrendResult> results = new ArrayList<>();
        Connection conn = null;

        try {
            conn = connectionPool.getConnection();

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, startDate.format(DB_FORMATTER));
                stmt.setString(2, endDate.format(DB_FORMATTER));

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        results.add(new ProfitTrendResult(
                                rs.getString("period"),
                                rs.getInt("transaction_count"),
                                rs.getDouble("revenue"),
                                rs.getDouble("cost")
                        ));
                    }
                }
            }

        } catch (SQLException e) {
            LOGGER.severe(String.format("Error getting profit trend data: %s", e.getMessage()));
        } finally {
            if (conn != null) {
                connectionPool.returnConnection(conn);
            }
        }

        return results;
    }

    // Result data classes
    public static class ProfitQueryResult {

        public final int transactionCount;
        public final int totalItemsSold;
        public final double totalRevenue;
        public final double totalCost;

        public ProfitQueryResult(int transactionCount, int totalItemsSold, double totalRevenue, double totalCost) {
            this.transactionCount = transactionCount;
            this.totalItemsSold = totalItemsSold;
            this.totalRevenue = totalRevenue;
            this.totalCost = totalCost;
        }
    }

    public static class CategoryProfitResult {

        public final String category;
        public final int transactionCount;
        public final int itemsSold;
        public final double revenue;
        public final double cost;

        public CategoryProfitResult(String category, int transactionCount, int itemsSold, double revenue, double cost) {
            this.category = category;
            this.transactionCount = transactionCount;
            this.itemsSold = itemsSold;
            this.revenue = revenue;
            this.cost = cost;
        }
    }

    public static class ProductProfitResult {

        public final Long productId;
        public final String productName;
        public final String sku;
        public final String category;
        public final int transactionCount;
        public final int quantitySold;
        public final double revenue;
        public final double cost;

        public ProductProfitResult(Long productId, String productName, String sku, String category,
                int transactionCount, int quantitySold, double revenue, double cost) {
            this.productId = productId;
            this.productName = productName;
            this.sku = sku;
            this.category = category;
            this.transactionCount = transactionCount;
            this.quantitySold = quantitySold;
            this.revenue = revenue;
            this.cost = cost;
        }
    }

    public static class ProfitTrendResult {

        public final String period;
        public final int transactionCount;
        public final double revenue;
        public final double cost;

        public ProfitTrendResult(String period, int transactionCount, double revenue, double cost) {
            this.period = period;
            this.transactionCount = transactionCount;
            this.revenue = revenue;
            this.cost = cost;
        }
    }
}
