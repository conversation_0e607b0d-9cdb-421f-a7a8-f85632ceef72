package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class representing a tax rate configuration
 */
public class TaxRate {

    private Long id;
    private String name;
    private String description;
    private BigDecimal rate; // Tax rate as percentage (e.g., 8.5 for 8.5%)
    private TaxType type;
    private String jurisdiction; // State, county, city, etc.
    private String applicableCategories; // Comma-separated list of product categories
    private String exemptCategories; // Comma-separated list of exempt categories
    private BigDecimal minimumAmount; // Minimum purchase amount for tax to apply
    private BigDecimal maximumAmount; // Maximum taxable amount
    private boolean active;
    private LocalDateTime effectiveDate;
    private LocalDateTime expirationDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public TaxRate() {
        this.active = true;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public TaxRate(String name, BigDecimal rate, TaxType type) {
        this();
        this.name = name;
        this.rate = rate;
        this.type = type;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public TaxType getType() {
        return type;
    }

    public void setType(TaxType type) {
        this.type = type;
    }

    public String getJurisdiction() {
        return jurisdiction;
    }

    public void setJurisdiction(String jurisdiction) {
        this.jurisdiction = jurisdiction;
    }

    public String getApplicableCategories() {
        return applicableCategories;
    }

    public void setApplicableCategories(String applicableCategories) {
        this.applicableCategories = applicableCategories;
    }

    public String getExemptCategories() {
        return exemptCategories;
    }

    public void setExemptCategories(String exemptCategories) {
        this.exemptCategories = exemptCategories;
    }

    public BigDecimal getMinimumAmount() {
        return minimumAmount;
    }

    public void setMinimumAmount(BigDecimal minimumAmount) {
        this.minimumAmount = minimumAmount;
    }

    public BigDecimal getMaximumAmount() {
        return maximumAmount;
    }

    public void setMaximumAmount(BigDecimal maximumAmount) {
        this.maximumAmount = maximumAmount;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public LocalDateTime getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDateTime effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Utility methods
    public boolean isValid() {
        LocalDateTime now = LocalDateTime.now();

        if (!active) {
            return false;
        }

        if (effectiveDate != null && now.isBefore(effectiveDate)) {
            return false;
        }

        if (expirationDate != null && now.isAfter(expirationDate)) {
            return false;
        }

        return true;
    }

    public boolean isApplicableToCategory(String category) {
        if (category == null || category.trim().isEmpty()) {
            return false;
        }

        // Check if category is exempt
        if (exemptCategories != null && !exemptCategories.trim().isEmpty()) {
            String[] exempt = exemptCategories.split(",");
            for (String exemptCategory : exempt) {
                if (exemptCategory.trim().equalsIgnoreCase(category.trim())) {
                    return false;
                }
            }
        }

        // Check if category is specifically applicable
        if (applicableCategories != null && !applicableCategories.trim().isEmpty()) {
            String[] applicable = applicableCategories.split(",");
            for (String applicableCategory : applicable) {
                if (applicableCategory.trim().equalsIgnoreCase(category.trim())) {
                    return true;
                }
            }
            return false; // Category not in applicable list
        }

        return true; // No specific categories defined, applies to all
    }

    public boolean isApplicableToAmount(BigDecimal amount) {
        if (amount == null) {
            return false;
        }

        if (minimumAmount != null && amount.compareTo(minimumAmount) < 0) {
            return false;
        }

        if (maximumAmount != null && amount.compareTo(maximumAmount) > 0) {
            return false;
        }

        return true;
    }

    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        sb.append(name);
        sb.append(" (").append(rate).append("%)");

        if (jurisdiction != null && !jurisdiction.trim().isEmpty()) {
            sb.append(" - ").append(jurisdiction);
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return String.format("TaxRate{id=%d, name='%s', rate=%s, type=%s}",
                id, name, rate, type);
    }
}
