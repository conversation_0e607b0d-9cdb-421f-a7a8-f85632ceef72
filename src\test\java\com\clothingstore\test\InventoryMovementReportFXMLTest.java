package com.clothingstore.test;

import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;

/**
 * Test to verify FXML loading for InventoryMovementReport
 */
public class InventoryMovementReportFXMLTest {

    public static void main(String[] args) {
        System.out.println("=== Inventory Movement Report FXML Loading Test ===");

        try {
            System.out.println("Testing FXML loading...");

            // Test FXML loading
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(InventoryMovementReportFXMLTest.class.getResource("/fxml/InventoryMovementReport.fxml"));

            System.out.println("FXML file found at: " + loader.getLocation());

            VBox root = loader.load();
            System.out.println("✓ FXML loaded successfully");
            System.out.println("✓ Root node created: " + root.getClass().getSimpleName());

            // Check controller
            Object controller = loader.getController();
            if (controller != null) {
                System.out.println("PASSED: Controller loaded: " + controller.getClass().getSimpleName());
            } else {
                System.out.println("FAILED: Controller is null");
            }

            System.out.println("\n=== FXML Loading Test PASSED ===");

        } catch (Exception e) {
            System.err.println("FAILED: FXML Loading Test FAILED");
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();

            System.err.println("\nTroubleshooting steps:");
            System.err.println("1. Check if InventoryMovementReport.fxml exists in src/main/resources/fxml/");
            System.err.println("2. Verify controller class is compiled and in classpath");
            System.err.println("3. Check for syntax errors in FXML file");
            System.err.println("4. Ensure all fx:id attributes match @FXML fields in controller");
        }
    }
}
