package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Model class representing a purchase order
 */
public class PurchaseOrder {
    
    private Long id;
    private String orderNumber;
    private Long supplierId;
    private Supplier supplier;
    private PurchaseOrderStatus status;
    private LocalDateTime orderDate;
    private LocalDateTime expectedDeliveryDate;
    private LocalDateTime actualDeliveryDate;
    private String deliveryAddress;
    private String notes;
    private String terms;
    private BigDecimal subtotal;
    private BigDecimal taxAmount;
    private BigDecimal shippingCost;
    private BigDecimal totalAmount;
    private String createdBy;
    private String approvedBy;
    private LocalDateTime approvedDate;
    private String receivedBy;
    private LocalDateTime receivedDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Purchase order items
    private List<PurchaseOrderItem> items;
    
    // Constructors
    public PurchaseOrder() {
        this.status = PurchaseOrderStatus.DRAFT;
        this.orderDate = LocalDateTime.now();
        this.subtotal = BigDecimal.ZERO;
        this.taxAmount = BigDecimal.ZERO;
        this.shippingCost = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
        this.items = new ArrayList<>();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public PurchaseOrder(Long supplierId) {
        this();
        this.supplierId = supplierId;
        this.orderNumber = generateOrderNumber();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getOrderNumber() {
        return orderNumber;
    }
    
    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }
    
    public Long getSupplierId() {
        return supplierId;
    }
    
    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }
    
    public Supplier getSupplier() {
        return supplier;
    }
    
    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }
    
    public PurchaseOrderStatus getStatus() {
        return status;
    }
    
    public void setStatus(PurchaseOrderStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getOrderDate() {
        return orderDate;
    }
    
    public void setOrderDate(LocalDateTime orderDate) {
        this.orderDate = orderDate;
    }
    
    public LocalDateTime getExpectedDeliveryDate() {
        return expectedDeliveryDate;
    }
    
    public void setExpectedDeliveryDate(LocalDateTime expectedDeliveryDate) {
        this.expectedDeliveryDate = expectedDeliveryDate;
    }
    
    public LocalDateTime getActualDeliveryDate() {
        return actualDeliveryDate;
    }
    
    public void setActualDeliveryDate(LocalDateTime actualDeliveryDate) {
        this.actualDeliveryDate = actualDeliveryDate;
    }
    
    public String getDeliveryAddress() {
        return deliveryAddress;
    }
    
    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public String getTerms() {
        return terms;
    }
    
    public void setTerms(String terms) {
        this.terms = terms;
    }
    
    public BigDecimal getSubtotal() {
        return subtotal;
    }
    
    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }
    
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }
    
    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }
    
    public BigDecimal getShippingCost() {
        return shippingCost;
    }
    
    public void setShippingCost(BigDecimal shippingCost) {
        this.shippingCost = shippingCost;
    }
    
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public String getApprovedBy() {
        return approvedBy;
    }
    
    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }
    
    public LocalDateTime getApprovedDate() {
        return approvedDate;
    }
    
    public void setApprovedDate(LocalDateTime approvedDate) {
        this.approvedDate = approvedDate;
    }
    
    public String getReceivedBy() {
        return receivedBy;
    }
    
    public void setReceivedBy(String receivedBy) {
        this.receivedBy = receivedBy;
    }
    
    public LocalDateTime getReceivedDate() {
        return receivedDate;
    }
    
    public void setReceivedDate(LocalDateTime receivedDate) {
        this.receivedDate = receivedDate;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public List<PurchaseOrderItem> getItems() {
        return items;
    }
    
    public void setItems(List<PurchaseOrderItem> items) {
        this.items = items;
    }
    
    // Business methods
    
    /**
     * Add item to purchase order
     */
    public void addItem(PurchaseOrderItem item) {
        if (items == null) {
            items = new ArrayList<>();
        }
        
        item.setPurchaseOrderId(this.id);
        items.add(item);
        recalculateTotals();
    }
    
    /**
     * Remove item from purchase order
     */
    public boolean removeItem(PurchaseOrderItem item) {
        if (items != null && items.remove(item)) {
            recalculateTotals();
            return true;
        }
        return false;
    }
    
    /**
     * Recalculate order totals
     */
    public void recalculateTotals() {
        if (items == null || items.isEmpty()) {
            this.subtotal = BigDecimal.ZERO;
            this.totalAmount = BigDecimal.ZERO;
            return;
        }
        
        this.subtotal = items.stream()
                .map(PurchaseOrderItem::getLineTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.totalAmount = subtotal.add(taxAmount != null ? taxAmount : BigDecimal.ZERO)
                .add(shippingCost != null ? shippingCost : BigDecimal.ZERO);
        
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Get total quantity of items
     */
    public int getTotalQuantity() {
        if (items == null) {
            return 0;
        }
        
        return items.stream()
                .mapToInt(PurchaseOrderItem::getQuantityOrdered)
                .sum();
    }
    
    /**
     * Check if order can be modified
     */
    public boolean canBeModified() {
        return status == PurchaseOrderStatus.DRAFT || status == PurchaseOrderStatus.PENDING_APPROVAL;
    }
    
    /**
     * Check if order can be approved
     */
    public boolean canBeApproved() {
        return status == PurchaseOrderStatus.PENDING_APPROVAL && !items.isEmpty();
    }
    
    /**
     * Check if order can be sent
     */
    public boolean canBeSent() {
        return status == PurchaseOrderStatus.APPROVED;
    }
    
    /**
     * Check if order can be received
     */
    public boolean canBeReceived() {
        return status == PurchaseOrderStatus.SENT;
    }
    
    /**
     * Check if order is overdue
     */
    public boolean isOverdue() {
        if (expectedDeliveryDate == null || status == PurchaseOrderStatus.RECEIVED || 
            status == PurchaseOrderStatus.CANCELLED) {
            return false;
        }
        
        return LocalDateTime.now().isAfter(expectedDeliveryDate);
    }
    
    /**
     * Generate order number
     */
    private String generateOrderNumber() {
        return "PO" + System.currentTimeMillis();
    }
    
    /**
     * Get order summary
     */
    public String getOrderSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("PO: ").append(orderNumber).append("\n");
        summary.append("Supplier: ").append(supplier != null ? supplier.getDisplayName() : "Unknown").append("\n");
        summary.append("Status: ").append(status.getDisplayName()).append("\n");
        summary.append("Items: ").append(items != null ? items.size() : 0).append("\n");
        summary.append("Total: $").append(totalAmount).append("\n");
        
        if (expectedDeliveryDate != null) {
            summary.append("Expected: ").append(expectedDeliveryDate.toLocalDate()).append("\n");
        }
        
        if (isOverdue()) {
            summary.append("STATUS: OVERDUE!\n");
        }
        
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return String.format("PurchaseOrder{id=%d, orderNumber='%s', status=%s, total=%s}", 
                id, orderNumber, status, totalAmount);
    }
}
