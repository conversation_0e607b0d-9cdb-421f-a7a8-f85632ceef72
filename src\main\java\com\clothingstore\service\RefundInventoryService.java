package com.clothingstore.service;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.*;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Dedicated service for handling inventory adjustments during refund operations
 * Provides comprehensive validation, rollback capabilities, and detailed tracking
 */
public class RefundInventoryService {
    
    private static RefundInventoryService instance;
    private final ProductDAO productDAO;
    
    private RefundInventoryService() {
        this.productDAO = ProductDAO.getInstance();
    }
    
    public static synchronized RefundInventoryService getInstance() {
        if (instance == null) {
            instance = new RefundInventoryService();
        }
        return instance;
    }
    
    /**
     * Process inventory adjustments for refunded items with comprehensive validation
     */
    public InventoryAdjustmentResult processRefundInventoryAdjustments(List<TransactionItem> refundedItems) {
        InventoryAdjustmentResult result = new InventoryAdjustmentResult();
        List<InventoryAdjustmentResult.InventoryAdjustmentDetail> adjustmentDetails = new ArrayList<>();
        
        try {
            // Phase 1: Pre-validation - Check all items can be processed
            ValidationResult validationResult = validateRefundAdjustments(refundedItems);
            if (!validationResult.isValid()) {
                result.setSuccess(false);
                result.setErrorMessage(validationResult.getErrorMessage());
                return result;
            }
            
            // Add any warnings from validation
            result.getWarnings().addAll(validationResult.getWarnings());
            
            // Phase 2: Execute adjustments with transaction-like behavior
            for (TransactionItem item : refundedItems) {
                if (item.getProduct() == null) {
                    result.addWarning("Skipping item with null product reference");
                    continue;
                }
                
                InventoryAdjustmentResult.InventoryAdjustmentDetail detail = 
                    processIndividualAdjustment(item);
                adjustmentDetails.add(detail);
                
                if (!detail.isSuccessful()) {
                    // If any adjustment fails, we should ideally rollback previous adjustments
                    // For now, we'll mark the overall operation as failed
                    result.setSuccess(false);
                    result.setErrorMessage("Failed to adjust inventory for product: " + detail.getProductName() + 
                                         ". Error: " + detail.getErrorMessage());
                    result.setAdjustmentDetails(adjustmentDetails);
                    return result;
                }
            }
            
            result.setSuccess(true);
            result.setAdjustmentDetails(adjustmentDetails);
            return result;
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("Unexpected error during inventory adjustment: " + e.getMessage());
            result.setAdjustmentDetails(adjustmentDetails);
            return result;
        }
    }
    
    /**
     * Validate that all refund adjustments can be processed
     */
    private ValidationResult validateRefundAdjustments(List<TransactionItem> refundedItems) {
        ValidationResult result = new ValidationResult();
        List<String> warnings = new ArrayList<>();
        
        try {
            for (TransactionItem item : refundedItems) {
                if (item.getProduct() == null) {
                    warnings.add("Item with null product reference will be skipped");
                    continue;
                }
                
                // Check if product still exists
                Optional<Product> productOpt = productDAO.findById(item.getProduct().getId());
                if (productOpt.isEmpty()) {
                    result.setValid(false);
                    result.setErrorMessage("Product not found for inventory adjustment: ID " + item.getProduct().getId());
                    return result;
                }
                
                Product product = productOpt.get();
                int adjustmentQuantity = Math.abs(item.getQuantity());
                int newStock = product.getStockQuantity() + adjustmentQuantity;
                
                // Validate new stock level is reasonable
                if (newStock < 0) {
                    result.setValid(false);
                    result.setErrorMessage("Invalid stock adjustment would result in negative stock for product: " + product.getName());
                    return result;
                }
                
                // Check for maximum stock limits (configurable business rule)
                final int MAX_STOCK_LIMIT = 10000;
                if (newStock > MAX_STOCK_LIMIT) {
                    warnings.add("Stock adjustment for " + product.getName() + 
                               " will exceed recommended maximum (" + MAX_STOCK_LIMIT + ")");
                }
            }
            
            result.setValid(true);
            result.setWarnings(warnings);
            return result;
            
        } catch (SQLException e) {
            result.setValid(false);
            result.setErrorMessage("Database error during validation: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * Process inventory adjustment for a single item
     */
    private InventoryAdjustmentResult.InventoryAdjustmentDetail processIndividualAdjustment(TransactionItem item) {
        try {
            Optional<Product> productOpt = productDAO.findById(item.getProduct().getId());
            if (productOpt.isEmpty()) {
                InventoryAdjustmentResult.InventoryAdjustmentDetail detail = 
                    new InventoryAdjustmentResult.InventoryAdjustmentDetail();
                detail.setProductId(item.getProduct().getId());
                detail.setProductName("Unknown Product");
                detail.setSuccessful(false);
                detail.setErrorMessage("Product not found");
                return detail;
            }
            
            Product product = productOpt.get();
            int previousStock = product.getStockQuantity();
            int adjustmentQuantity = Math.abs(item.getQuantity());
            int newStock = previousStock + adjustmentQuantity;
            
            try {
                // Update stock using DAO method for consistency
                productDAO.updateStock(item.getProduct().getId(), newStock);
                
                // Create successful adjustment detail
                InventoryAdjustmentResult.InventoryAdjustmentDetail detail = 
                    new InventoryAdjustmentResult.InventoryAdjustmentDetail(
                        product.getId(), product.getName(), previousStock, 
                        adjustmentQuantity, newStock, true);
                
                System.out.println("DEBUG: Successfully restored " + adjustmentQuantity + 
                                 " units to inventory for product: " + product.getName() + 
                                 " (Previous: " + previousStock + ", New: " + newStock + ")");
                
                return detail;
                
            } catch (SQLException e) {
                // Create failed adjustment detail
                InventoryAdjustmentResult.InventoryAdjustmentDetail detail = 
                    new InventoryAdjustmentResult.InventoryAdjustmentDetail(
                        product.getId(), product.getName(), previousStock, 
                        adjustmentQuantity, previousStock, false);
                detail.setErrorMessage("Database error: " + e.getMessage());
                return detail;
            }
            
        } catch (SQLException e) {
            InventoryAdjustmentResult.InventoryAdjustmentDetail detail = 
                new InventoryAdjustmentResult.InventoryAdjustmentDetail();
            detail.setProductId(item.getProduct().getId());
            detail.setProductName("Unknown Product");
            detail.setSuccessful(false);
            detail.setErrorMessage("Database error during product lookup: " + e.getMessage());
            return detail;
        }
    }
    
    /**
     * Inner class for validation results
     */
    private static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private List<String> warnings = new ArrayList<>();
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings != null ? warnings : new ArrayList<>(); }
    }
}
