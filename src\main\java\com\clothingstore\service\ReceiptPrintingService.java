package com.clothingstore.service;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

// import com.clothingstore.dialog.WhatsAppPhoneDialog; // Commented out - WhatsApp integration disabled
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.util.AlertUtil;

import javafx.print.Printer;
import javafx.print.PrinterJob;
import javafx.scene.Node;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.TextArea;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.scene.text.Text;

/**
 * Service for generating and printing receipts
 */
public class ReceiptPrintingService {

    private static ReceiptPrintingService instance;
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");

    // Store information (could be loaded from settings)
    private String storeName = "Clothing Store Management System";
    private String storeAddress = "123 Main Street";
    private String storeCityState = "Anytown, ST 12345";
    private String storePhone = "(*************";
    private String storeFooter = "Thank you for your business!";

    private ReceiptPrintingService() {
    }

    public static synchronized ReceiptPrintingService getInstance() {
        if (instance == null) {
            instance = new ReceiptPrintingService();
        }
        return instance;
    }

    /**
     * Generate receipt text for a transaction
     */
    public String generateReceiptText(Transaction transaction) {
        return generateReceiptText(transaction, null, null);
    }

    /**
     * Generate receipt text with payment details
     */
    public String generateReceiptText(Transaction transaction, BigDecimal amountReceived, BigDecimal change) {
        StringBuilder receipt = new StringBuilder();

        // Store header
        receipt.append(centerText(storeName, 40)).append("\n");
        receipt.append(centerText(storeAddress, 40)).append("\n");
        receipt.append(centerText(storeCityState, 40)).append("\n");
        receipt.append(centerText(storePhone, 40)).append("\n");
        receipt.append(repeatChar('=', 40)).append("\n\n");

        // Transaction details
        receipt.append("Transaction: ").append(transaction.getTransactionNumber()).append("\n");
        receipt.append("Date: ").append(transaction.getTransactionDate().format(dateFormatter)).append("\n");
        receipt.append("Cashier: ").append(transaction.getCashierName() != null ? transaction.getCashierName() : "Admin").append("\n");
        receipt.append("Customer: ").append(transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in Customer").append("\n");
        receipt.append(repeatChar('-', 40)).append("\n\n");

        // Items
        receipt.append("ITEMS:\n");
        receipt.append(repeatChar('-', 40)).append("\n");

        for (TransactionItem item : transaction.getItems()) {
            String productName = truncateText(item.getProductName(), 20);
            receipt.append(String.format("%-20s %2d x $%6.2f = $%7.2f\n",
                    productName,
                    item.getQuantity(),
                    item.getUnitPrice().doubleValue(),
                    item.getLineTotal().doubleValue()));

            // Add discount if applicable
            if (item.getDiscountAmount() != null && item.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                receipt.append(String.format("  Discount: %26s -$%7.2f\n", "", item.getDiscountAmount().doubleValue()));
            }
        }

        receipt.append(repeatChar('-', 40)).append("\n");

        // Totals
        receipt.append(String.format("Subtotal: %26s $%7.2f\n", "", transaction.getSubtotal().doubleValue()));

        if (transaction.getDiscountAmount() != null && transaction.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            receipt.append(String.format("Discount: %26s -$%7.2f\n", "", transaction.getDiscountAmount().doubleValue()));
        }

        if (transaction.getTaxAmount() != null && transaction.getTaxAmount().compareTo(BigDecimal.ZERO) > 0) {
            receipt.append(String.format("Tax: %30s $%7.2f\n", "", transaction.getTaxAmount().doubleValue()));
        }

        receipt.append(repeatChar('=', 40)).append("\n");
        receipt.append(String.format("TOTAL: %28s $%7.2f\n", "", transaction.getTotalAmount().doubleValue()));
        receipt.append(repeatChar('=', 40)).append("\n\n");

        // Payment details
        receipt.append("Payment Method: ").append(transaction.getPaymentMethod()).append("\n");

        if (amountReceived != null) {
            receipt.append(String.format("Amount Received: %20s $%7.2f\n", "", amountReceived.doubleValue()));
        }

        if (change != null && change.compareTo(BigDecimal.ZERO) > 0) {
            receipt.append(String.format("Change: %29s $%7.2f\n", "", change.doubleValue()));
        }

        receipt.append("\n");

        // Notes
        if (transaction.getNotes() != null && !transaction.getNotes().trim().isEmpty()) {
            receipt.append("Notes: ").append(transaction.getNotes()).append("\n\n");
        }

        // Footer
        receipt.append(centerText(storeFooter, 40)).append("\n");
        receipt.append(centerText("Visit us again soon!", 40)).append("\n\n");

        return receipt.toString();
    }

    /**
     * Show receipt preview dialog
     */
    public void showReceiptPreview(Transaction transaction, BigDecimal amountReceived, BigDecimal change) {
        showReceiptPreview(transaction, amountReceived, change, null);
    }

    /**
     * Show receipt preview dialog with optional customer context for WhatsApp
     */
    public void showReceiptPreview(Transaction transaction, BigDecimal amountReceived, BigDecimal change, Customer customer) {
        String receiptText = generateReceiptText(transaction, amountReceived, change);

        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Receipt Preview");
        alert.setHeaderText("Transaction Receipt - " + transaction.getTransactionNumber());

        TextArea textArea = new TextArea(receiptText);
        textArea.setEditable(false);
        textArea.setFont(Font.font("Courier New", 12));
        textArea.setPrefRowCount(25);
        textArea.setPrefColumnCount(50);

        alert.getDialogPane().setContent(textArea);

        // Create custom button types
        ButtonType printButton = new ButtonType("Print");
        ButtonType whatsappButton = new ButtonType("Send via WhatsApp");
        ButtonType closeButton = new ButtonType("Close", ButtonBar.ButtonData.CANCEL_CLOSE);

        // WhatsApp integration disabled
        alert.getButtonTypes().setAll(printButton, closeButton);

        // Handle button actions
        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent()) {
            ButtonType selectedButton = result.get();

            if (selectedButton == printButton) {
                printReceipt(transaction, amountReceived, change);
            }
            // WhatsApp functionality disabled
        }
    }

    /**
     * Handle WhatsApp receipt sending - DISABLED
     */
    /*
    private void handleWhatsAppReceipt(Transaction transaction, Customer customer) {
        try {
            WhatsAppService whatsAppService = WhatsAppService.getInstance();

            // Check if WhatsApp is enabled
            if (!whatsAppService.getConfiguration().isEnabled()) {
                AlertUtil.showWarning("WhatsApp Disabled",
                        "WhatsApp service is currently disabled. Please enable it in Settings.");
                return;
            }

            // Determine phone number to use
            String phoneNumber = getPhoneNumberForWhatsApp(whatsAppService, customer);

            if (phoneNumber == null) {
                // User cancelled or chose to skip
                return;
            }

            // Show sending progress
            Alert progressAlert = new Alert(Alert.AlertType.INFORMATION);
            progressAlert.setTitle("Sending Receipt");
            progressAlert.setHeaderText("Sending receipt via WhatsApp...");
            progressAlert.setContentText("Please wait while we send your receipt to " + phoneNumber);
            progressAlert.getButtonTypes().clear(); // Remove buttons to prevent closing
            progressAlert.show();

            // Send receipt asynchronously
            whatsAppService.sendReceiptAsync(transaction, customer, phoneNumber)
                    .thenAccept(success -> {
                        javafx.application.Platform.runLater(() -> {
                            progressAlert.close();

                            if (success) {
                                AlertUtil.showSuccess("Receipt Sent",
                                        "Receipt has been sent successfully via WhatsApp to " + phoneNumber);
                            } else {
                                AlertUtil.showError("Send Failed",
                                        "Failed to send receipt via WhatsApp. Please check the phone number and try again.");
                            }
                        });
                    })
                    .exceptionally(throwable -> {
                        javafx.application.Platform.runLater(() -> {
                            progressAlert.close();
                            AlertUtil.showError("Send Error",
                                    "An error occurred while sending the receipt: " + throwable.getMessage());
                        });
                        return null;
                    });

        } catch (Exception e) {
            AlertUtil.showError("WhatsApp Error",
                    "An error occurred while preparing to send the receipt: " + e.getMessage());
        }
    }
    */

    /**
     * Print receipt using JavaFX printing
     */
    public void printReceipt(Transaction transaction, BigDecimal amountReceived, BigDecimal change) {
        try {
            Printer printer = Printer.getDefaultPrinter();
            if (printer == null) {
                showError("No default printer found. Please configure a printer.");
                return;
            }

            PrinterJob job = PrinterJob.createPrinterJob();
            if (job == null) {
                showError("Unable to create print job.");
                return;
            }

            // Create printable content
            Node printNode = createPrintableReceipt(transaction, amountReceived, change);

            // Show print dialog
            if (job.showPrintDialog(null)) {
                boolean success = job.printPage(printNode);
                if (success) {
                    job.endJob();
                    showInfo("Receipt printed successfully!");
                } else {
                    showError("Failed to print receipt.");
                }
            }

        } catch (Exception e) {
            showError("Print error: " + e.getMessage());
        }
    }

    /**
     * Create printable JavaFX node for receipt
     */
    private Node createPrintableReceipt(Transaction transaction, BigDecimal amountReceived, BigDecimal change) {
        VBox receiptBox = new VBox(5);

        // Store header
        Text storeNameText = new Text(storeName);
        storeNameText.setFont(Font.font("Arial", FontWeight.BOLD, 14));
        receiptBox.getChildren().add(storeNameText);

        receiptBox.getChildren().add(new Text(storeAddress));
        receiptBox.getChildren().add(new Text(storeCityState));
        receiptBox.getChildren().add(new Text(storePhone));
        receiptBox.getChildren().add(new Text(""));

        // Transaction details
        receiptBox.getChildren().add(new Text("Transaction: " + transaction.getTransactionNumber()));
        receiptBox.getChildren().add(new Text("Date: " + transaction.getTransactionDate().format(dateFormatter)));
        receiptBox.getChildren().add(new Text("Cashier: " + (transaction.getCashierName() != null ? transaction.getCashierName() : "Admin")));
        receiptBox.getChildren().add(new Text("Customer: " + (transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in Customer")));
        receiptBox.getChildren().add(new Text(""));

        // Items
        Text itemsHeader = new Text("ITEMS:");
        itemsHeader.setFont(Font.font("Arial", FontWeight.BOLD, 12));
        receiptBox.getChildren().add(itemsHeader);

        for (TransactionItem item : transaction.getItems()) {
            String itemLine = String.format("%s - %d x $%.2f = $%.2f",
                    item.getProductName(),
                    item.getQuantity(),
                    item.getUnitPrice().doubleValue(),
                    item.getLineTotal().doubleValue());
            receiptBox.getChildren().add(new Text(itemLine));
        }

        receiptBox.getChildren().add(new Text(""));

        // Totals
        receiptBox.getChildren().add(new Text(String.format("Subtotal: $%.2f", transaction.getSubtotal().doubleValue())));

        if (transaction.getDiscountAmount() != null && transaction.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            receiptBox.getChildren().add(new Text(String.format("Discount: -$%.2f", transaction.getDiscountAmount().doubleValue())));
        }

        Text totalText = new Text(String.format("TOTAL: $%.2f", transaction.getTotalAmount().doubleValue()));
        totalText.setFont(Font.font("Arial", FontWeight.BOLD, 12));
        receiptBox.getChildren().add(totalText);

        receiptBox.getChildren().add(new Text(""));

        // Payment details
        receiptBox.getChildren().add(new Text("Payment Method: " + transaction.getPaymentMethod()));

        if (amountReceived != null) {
            receiptBox.getChildren().add(new Text(String.format("Amount Received: $%.2f", amountReceived.doubleValue())));
        }

        if (change != null && change.compareTo(BigDecimal.ZERO) > 0) {
            receiptBox.getChildren().add(new Text(String.format("Change: $%.2f", change.doubleValue())));
        }

        receiptBox.getChildren().add(new Text(""));
        receiptBox.getChildren().add(new Text(storeFooter));

        return receiptBox;
    }

    /**
     * Utility methods
     */
    private String centerText(String text, int width) {
        if (text.length() >= width) {
            return text.substring(0, width);
        }

        int padding = (width - text.length()) / 2;
        return " ".repeat(padding) + text;
    }

    private String repeatChar(char ch, int count) {
        return String.valueOf(ch).repeat(count);
    }

    private String truncateText(String text, int maxLength) {
        if (text == null) {
            return "";
        }
        return text.length() > maxLength ? text.substring(0, maxLength - 3) + "..." : text;
    }

    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Print Error");
        alert.setHeaderText("Printing Failed");
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showInfo(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Print Success");
        alert.setHeaderText("Printing Complete");
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * Get phone number for WhatsApp, prompting user if necessary - DISABLED
     */
    /*
    private String getPhoneNumberForWhatsApp(WhatsAppService whatsAppService, Customer customer) {
        // WhatsApp integration disabled
        return null;
    }
    */

    // Setters for store information
    public void setStoreInfo(String name, String address, String cityState, String phone, String footer) {
        this.storeName = name;
        this.storeAddress = address;
        this.storeCityState = cityState;
        this.storePhone = phone;
        this.storeFooter = footer;
    }
}
