<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.ProfitReportController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="form-title" text="💎 Profit Analysis Report">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <DatePicker fx:id="dateFrom" promptText="From Date" />
            <DatePicker fx:id="dateTo" promptText="To Date" />
            <ComboBox fx:id="cmbPeriod" onAction="#handlePeriodChange" promptText="Period" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" text="🔄 Refresh" />
            <Button fx:id="btnExport" onAction="#handleExport" text="📊 Export" />
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </HBox>

      <!-- Profit Summary -->
      <GridPane hgap="15.0" vgap="15.0" styleClass="form-container">
         <columnConstraints>
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
            <ColumnConstraints hgrow="ALWAYS" />
         </columnConstraints>
         <rowConstraints>
            <RowConstraints />
            <RowConstraints />
         </rowConstraints>
         <children>
            <VBox spacing="5.0" styleClass="metric-card success" GridPane.columnIndex="0" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="metric-title" text="💰 Total Revenue" />
                  <Label fx:id="lblTotalRevenue" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card warning" GridPane.columnIndex="1" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="metric-title" text="💸 Total Cost" />
                  <Label fx:id="lblTotalCost" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card primary" GridPane.columnIndex="2" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="metric-title" text="💎 Gross Profit" />
                  <Label fx:id="lblGrossProfit" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card info" GridPane.columnIndex="3" GridPane.rowIndex="0">
               <children>
                  <Label styleClass="metric-title" text="📊 Profit Margin" />
                  <Label fx:id="lblProfitMargin" styleClass="metric-value" text="0%" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card secondary" GridPane.columnIndex="0" GridPane.rowIndex="1">
               <children>
                  <Label styleClass="metric-title" text="📈 Avg Daily Profit" />
                  <Label fx:id="lblAvgDailyProfit" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="1" GridPane.rowIndex="1">
               <children>
                  <Label styleClass="metric-title" text="🏆 Best Product Profit" />
                  <Label fx:id="lblBestProductProfit" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="2" GridPane.rowIndex="1">
               <children>
                  <Label styleClass="metric-title" text="📦 Units Sold" />
                  <Label fx:id="lblUnitsSold" styleClass="metric-value" text="0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            <VBox spacing="5.0" styleClass="metric-card" GridPane.columnIndex="3" GridPane.rowIndex="1">
               <children>
                  <Label styleClass="metric-title" text="💵 Avg Profit per Unit" />
                  <Label fx:id="lblAvgProfitPerUnit" styleClass="metric-value" text="$0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </GridPane>

      <!-- Product Profitability -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="📊 Product Profitability Analysis">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <TextField fx:id="txtSearch" onKeyReleased="#handleSearch" promptText="Search products..." />
                  <ComboBox fx:id="cmbCategoryFilter" onAction="#handleCategoryFilter" promptText="All Categories" />
               </children>
            </HBox>

            <TableView fx:id="tblProductProfitability" prefHeight="300.0">
               <columns>
                  <TableColumn fx:id="colProductSku" prefWidth="100.0" text="SKU" />
                  <TableColumn fx:id="colProductName" prefWidth="180.0" text="Product Name" />
                  <TableColumn fx:id="colCategory" prefWidth="120.0" text="Category" />
                  <TableColumn fx:id="colUnitsSold" prefWidth="80.0" text="Units Sold" />
                  <TableColumn fx:id="colRevenue" prefWidth="100.0" text="Revenue" />
                  <TableColumn fx:id="colCost" prefWidth="100.0" text="Cost" />
                  <TableColumn fx:id="colProfit" prefWidth="100.0" text="Profit" />
                  <TableColumn fx:id="colMargin" prefWidth="80.0" text="Margin %" />
                  <TableColumn fx:id="colProfitPerUnit" prefWidth="100.0" text="Profit/Unit" />
               </columns>
            </TableView>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Category Profitability -->
      <HBox spacing="15.0" styleClass="form-container">
         <children>
            <VBox spacing="10.0" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="section-title" text="📈 Category Profitability">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <TableView fx:id="tblCategoryProfitability" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="colCategoryRank" prefWidth="50.0" text="Rank" />
                        <TableColumn fx:id="colCategoryNameProfit" prefWidth="120.0" text="Category" />
                        <TableColumn fx:id="colCategoryRevenue" prefWidth="100.0" text="Revenue" />
                        <TableColumn fx:id="colCategoryProfit" prefWidth="100.0" text="Profit" />
                        <TableColumn fx:id="colCategoryMargin" prefWidth="80.0" text="Margin %" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
            <VBox spacing="10.0" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="section-title" text="📊 Profit Trends">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <TableView fx:id="tblProfitTrends" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="colPeriod" prefWidth="100.0" text="Period" />
                        <TableColumn fx:id="colPeriodRevenue" prefWidth="100.0" text="Revenue" />
                        <TableColumn fx:id="colPeriodProfit" prefWidth="100.0" text="Profit" />
                        <TableColumn fx:id="colPeriodMargin" prefWidth="80.0" text="Margin %" />
                        <TableColumn fx:id="colGrowth" prefWidth="80.0" text="Growth %" />
                     </columns>
                  </TableView>
               </children>
            </VBox>
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
