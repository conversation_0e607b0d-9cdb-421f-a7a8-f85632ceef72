package com.clothingstore.test;

import com.clothingstore.dao.InventoryMovementDAO;

/**
 * Test to verify the Inventory Movement Report is accessible through the UI
 */
public class InventoryMovementReportUITest {

    public static void main(String[] args) {
        System.out.println("=== Inventory Movement Report UI Test ===");
        
        try {
            // Test 1: Verify DAO is accessible
            System.out.println("1. Testing DAO accessibility...");
            InventoryMovementDAO dao = InventoryMovementDAO.getInstance();
            System.out.println("   PASSED: InventoryMovementDAO instance created");
            
            // Test 2: Verify controller class exists
            System.out.println("\n2. Testing controller class...");
            Class<?> controllerClass = Class.forName("com.clothingstore.view.InventoryMovementReportController");
            System.out.println("   PASSED: InventoryMovementReportController class found");
            
            // Test 3: Verify FXML file exists
            System.out.println("\n3. Testing FXML file...");
            java.net.URL fxmlUrl = InventoryMovementReportUITest.class.getResource("/fxml/InventoryMovementReport.fxml");
            if (fxmlUrl != null) {
                System.out.println("   PASSED: InventoryMovementReport.fxml found at: " + fxmlUrl);
            } else {
                System.out.println("   FAILED: InventoryMovementReport.fxml not found");
                return;
            }
            
            // Test 4: Verify MainWindowController integration
            System.out.println("\n4. Testing MainWindowController integration...");
            Class<?> mainControllerClass = Class.forName("com.clothingstore.view.MainWindowController");
            
            // Check if showInventoryMovementReport method exists
            try {
                mainControllerClass.getDeclaredMethod("showInventoryMovementReport");
                System.out.println("   PASSED: showInventoryMovementReport method found in MainWindowController");
            } catch (NoSuchMethodException e) {
                System.out.println("   FAILED: showInventoryMovementReport method not found");
                return;
            }
            
            System.out.println("\n=== UI Integration Test Results ===");
            System.out.println("PASSED: All components are properly integrated");
            System.out.println("PASSED: DAO layer is functional");
            System.out.println("PASSED: Controller class is available");
            System.out.println("PASSED: FXML file is in correct location");
            System.out.println("PASSED: MainWindow integration is complete");
            
            System.out.println("\n=== MANUAL TESTING INSTRUCTIONS ===");
            System.out.println("To test the Inventory Movement Report in the application:");
            System.out.println("1. Launch the main application: java -cp \"lib\\sqlite-jdbc-3.50.1.0.jar;javafx-sdk-17.0.2\\lib\\*;target\\classes\" --module-path javafx-sdk-17.0.2\\lib --add-modules javafx.controls,javafx.fxml com.clothingstore.ClothingStoreApp");
            System.out.println("2. Navigate to the Reports section");
            System.out.println("3. Click on 'Inventory Movement' button");
            System.out.println("4. Verify the report loads with two tabs: 'Items Sold/Processed' and 'Items Returned/Refunded'");
            System.out.println("5. Test date range selection and report generation");
            System.out.println("6. Test export functionality");
            
            System.out.println("\nIf the report fails to load, check:");
            System.out.println("- JavaFX runtime is properly initialized");
            System.out.println("- All dependencies are in classpath");
            System.out.println("- Database connection is working");
            System.out.println("- No compilation errors in controller");
            
        } catch (ClassNotFoundException e) {
            System.err.println("FAILED: Required class not found: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("FAILED: Test error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
