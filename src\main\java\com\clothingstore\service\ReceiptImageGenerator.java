package com.clothingstore.service;

import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import javafx.embed.swing.SwingFXUtils;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.image.WritableImage;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.scene.text.Text;
import javafx.scene.text.TextAlignment;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;

/**
 * Service for generating visual receipts as images for WhatsApp sharing
 */
public class ReceiptImageGenerator {
    
    private static final int RECEIPT_WIDTH = 400;
    private static final int RECEIPT_PADDING = 20;
    private static final String FONT_FAMILY = "Courier New";
    
    private final NumberFormat currencyFormat;
    private final DateTimeFormatter dateFormatter;
    
    public ReceiptImageGenerator() {
        this.currencyFormat = NumberFormat.getCurrencyInstance();
        this.dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");
    }
    
    /**
     * Generate receipt image as PNG byte array for WhatsApp sending
     */
    public byte[] generateReceiptImage(Transaction transaction) throws IOException {
        VBox receiptLayout = createReceiptLayout(transaction);
        
        // Create a scene to render the receipt
        Scene scene = new Scene(receiptLayout, RECEIPT_WIDTH, calculateHeight(transaction));
        scene.getStylesheets().add(getClass().getResource("/css/receipt.css").toExternalForm());
        
        // Take a snapshot of the scene
        WritableImage writableImage = receiptLayout.snapshot(null, null);
        
        // Convert to BufferedImage
        BufferedImage bufferedImage = SwingFXUtils.fromFXImage(writableImage, null);
        
        // Convert to byte array
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "PNG", baos);
        return baos.toByteArray();
    }
    
    /**
     * Save receipt image to file
     */
    public File saveReceiptImage(Transaction transaction, String filePath) throws IOException {
        byte[] imageData = generateReceiptImage(transaction);
        File file = new File(filePath);
        
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(file)) {
            fos.write(imageData);
        }
        
        return file;
    }
    
    /**
     * Create the visual layout for the receipt
     */
    private VBox createReceiptLayout(Transaction transaction) {
        VBox layout = new VBox(5);
        layout.setPadding(new javafx.geometry.Insets(RECEIPT_PADDING));
        layout.setStyle("-fx-background-color: white; -fx-border-color: #cccccc; -fx-border-width: 1;");
        
        // Header
        Text header = new Text("CLOTHING STORE RECEIPT");
        header.setFont(Font.font(FONT_FAMILY, FontWeight.BOLD, 16));
        header.setTextAlignment(TextAlignment.CENTER);
        layout.getChildren().add(header);
        
        // Separator
        Text separator1 = new Text("----------------------------------------");
        separator1.setFont(Font.font(FONT_FAMILY, 12));
        layout.getChildren().add(separator1);
        
        // Transaction details
        layout.getChildren().add(createDetailLine("Transaction #:", transaction.getTransactionNumber()));
        layout.getChildren().add(createDetailLine("Date:", transaction.getTransactionDate().format(dateFormatter)));
        layout.getChildren().add(createDetailLine("Customer:", 
            transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in"));
        layout.getChildren().add(createDetailLine("Status:", transaction.getStatus()));
        
        // Another separator
        Text separator2 = new Text("----------------------------------------");
        separator2.setFont(Font.font(FONT_FAMILY, 12));
        layout.getChildren().add(separator2);
        
        // Items header
        Text itemsHeader = new Text("ITEMS:");
        itemsHeader.setFont(Font.font(FONT_FAMILY, FontWeight.BOLD, 12));
        layout.getChildren().add(itemsHeader);
        
        // Items
        for (TransactionItem item : transaction.getItems()) {
            VBox itemBox = new VBox(2);
            
            Text itemName = new Text(item.getProductName() + " (" + item.getProductSku() + ")");
            itemName.setFont(Font.font(FONT_FAMILY, 11));
            
            Text itemDetails = new Text(String.format("  %d x %s = %s", 
                item.getQuantity(),
                currencyFormat.format(item.getUnitPrice()),
                currencyFormat.format(item.getLineTotal())));
            itemDetails.setFont(Font.font(FONT_FAMILY, 11));
            
            itemBox.getChildren().addAll(itemName, itemDetails);
            layout.getChildren().add(itemBox);
        }
        
        // Final separator
        Text separator3 = new Text("----------------------------------------");
        separator3.setFont(Font.font(FONT_FAMILY, 12));
        layout.getChildren().add(separator3);
        
        // Totals
        layout.getChildren().add(createDetailLine("Subtotal:", currencyFormat.format(transaction.getSubtotal())));
        layout.getChildren().add(createDetailLine("Discount:", currencyFormat.format(transaction.getDiscount())));
        layout.getChildren().add(createDetailLine("Tax:", currencyFormat.format(transaction.getTax())));
        
        Text totalLine = new Text(String.format("TOTAL: %s", currencyFormat.format(transaction.getTotal())));
        totalLine.setFont(Font.font(FONT_FAMILY, FontWeight.BOLD, 14));
        layout.getChildren().add(totalLine);
        
        // Footer
        Text separator4 = new Text("----------------------------------------");
        separator4.setFont(Font.font(FONT_FAMILY, 12));
        layout.getChildren().add(separator4);
        
        Text footer = new Text("Thank you for shopping with us!");
        footer.setFont(Font.font(FONT_FAMILY, FontWeight.NORMAL, 11));
        footer.setTextAlignment(TextAlignment.CENTER);
        layout.getChildren().add(footer);
        
        return layout;
    }
    
    /**
     * Create a detail line with label and value
     */
    private Text createDetailLine(String label, String value) {
        Text line = new Text(String.format("%-15s %s", label, value));
        line.setFont(Font.font(FONT_FAMILY, 11));
        return line;
    }
    
    /**
     * Calculate the approximate height needed for the receipt
     */
    private double calculateHeight(Transaction transaction) {
        // Base height for header, details, and footer
        double baseHeight = 300;
        
        // Add height for each item (approximately 40 pixels per item)
        double itemsHeight = transaction.getItems().size() * 40;
        
        return baseHeight + itemsHeight + (RECEIPT_PADDING * 2);
    }
    
    /**
     * Generate simple text receipt for fallback
     */
    public String generateTextReceipt(Transaction transaction) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("      CLOTHING STORE RECEIPT\n");
        sb.append("----------------------------------------\n");
        sb.append("Transaction #: ").append(transaction.getTransactionNumber()).append("\n");
        sb.append("Date: ").append(transaction.getTransactionDate().format(dateFormatter)).append("\n");
        sb.append("Customer: ").append(transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in").append("\n");
        sb.append("Status: ").append(transaction.getStatus()).append("\n");
        sb.append("----------------------------------------\n");
        sb.append("ITEMS:\n");
        
        for (TransactionItem item : transaction.getItems()) {
            sb.append(item.getProductName()).append(" (").append(item.getProductSku()).append(")\n");
            sb.append("  ").append(item.getQuantity()).append(" x ")
              .append(currencyFormat.format(item.getUnitPrice())).append(" = ")
              .append(currencyFormat.format(item.getLineTotal())).append("\n");
        }
        
        sb.append("----------------------------------------\n");
        sb.append("Subtotal: ").append(currencyFormat.format(transaction.getSubtotal())).append("\n");
        sb.append("Discount: ").append(currencyFormat.format(transaction.getDiscount())).append("\n");
        sb.append("Tax: ").append(currencyFormat.format(transaction.getTax())).append("\n");
        sb.append("TOTAL: ").append(currencyFormat.format(transaction.getTotal())).append("\n");
        sb.append("----------------------------------------\n");
        sb.append("Thank you for shopping with us!\n");
        
        return sb.toString();
    }
}
