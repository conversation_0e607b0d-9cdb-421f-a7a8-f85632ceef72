package com.clothingstore.service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.InventoryAdjustmentResult;
import com.clothingstore.model.OutstandingBalanceUpdateResult;
import com.clothingstore.model.Product;
import com.clothingstore.model.RefundItem;
import com.clothingstore.model.RefundResult;
import com.clothingstore.model.RefundValidationResult;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

/**
 * Service class for handling comprehensive refund operations Manages inventory
 * adjustments, transaction status updates, and audit trails
 */
public class RefundService {

    private static RefundService instance;
    private final TransactionDAO transactionDAO;
    private final ProductDAO productDAO;
    private final CustomerDAO customerDAO;
    private final RefundInventoryService inventoryService;
    private final OutstandingBalanceRefundService outstandingBalanceService;
    private InstallmentRefundService installmentRefundService; // Lazy initialization to break circular dependency

    private RefundService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.productDAO = ProductDAO.getInstance();
        this.customerDAO = CustomerDAO.getInstance();
        this.inventoryService = RefundInventoryService.getInstance();
        this.outstandingBalanceService = OutstandingBalanceRefundService.getInstance();
        // Don't initialize installmentRefundService here to break circular dependency
        this.installmentRefundService = null;
    }

    /**
     * Get InstallmentRefundService with lazy initialization to break circular
     * dependency
     */
    private InstallmentRefundService getInstallmentRefundService() {
        if (this.installmentRefundService == null) {
            this.installmentRefundService = InstallmentRefundService.getInstance();
        }
        return this.installmentRefundService;
    }

    public static synchronized RefundService getInstance() {
        if (instance == null) {
            instance = new RefundService();
        }
        return instance;
    }

    /**
     * Smart refund processing that automatically detects transaction type and
     * applies appropriate logic
     */
    public RefundResult processSmartRefund(Transaction transaction, BigDecimal refundAmount,
            String refundReason, String cashierName) {
        try {
            // Check if this is an installment transaction
            if (transaction.isInstallmentTransaction()) {
                // Use specialized installment refund logic
                InstallmentRefundService.InstallmentRefundResult installmentResult
                        = getInstallmentRefundService().processInstallmentRefund(
                                transaction.getId(), refundAmount, refundReason, cashierName);

                // Convert to standard RefundResult
                return new RefundResult(
                        installmentResult.isSuccess(),
                        installmentResult.getMessage(),
                        installmentResult.getRefundTransaction()
                );
            } else {
                // Use standard refund logic for regular transactions
                BigDecimal totalAmount = transaction.getTotalAmount();

                if (refundAmount.compareTo(totalAmount) == 0) {
                    // Full refund
                    return processFullRefund(transaction, refundReason, cashierName);
                } else {
                    // Partial refund - convert to RefundItem list
                    List<RefundItem> refundItems = createRefundItemsFromAmount(transaction, refundAmount);
                    return processPartialRefund(transaction, refundItems, refundReason, cashierName);
                }
            }

        } catch (Exception e) {
            return new RefundResult(false, "Error processing smart refund: " + e.getMessage(), null);
        }
    }

    /**
     * Create RefundItem list from a refund amount (for partial refunds)
     */
    private List<RefundItem> createRefundItemsFromAmount(Transaction transaction, BigDecimal refundAmount) {
        List<RefundItem> refundItems = new ArrayList<>();
        BigDecimal remainingRefund = refundAmount;

        // Distribute refund amount proportionally across transaction items
        for (TransactionItem item : transaction.getItems()) {
            if (remainingRefund.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal itemTotal = item.getSubtotal();
            BigDecimal itemRefundAmount = remainingRefund.min(itemTotal);

            if (itemRefundAmount.compareTo(BigDecimal.ZERO) > 0) {
                RefundItem refundItem = new RefundItem(item);

                // Calculate refund quantity proportionally
                int refundQuantity = itemRefundAmount.divide(item.getUnitPrice(), 0, java.math.RoundingMode.DOWN).intValue();
                refundQuantity = Math.max(1, Math.min(refundQuantity, item.getQuantity()));

                refundItem.setRefundQuantity(refundQuantity);
                refundItem.setRefundAmount(itemRefundAmount);
                refundItem.setSelected(true);

                refundItems.add(refundItem);
                remainingRefund = remainingRefund.subtract(itemRefundAmount);
            }
        }

        return refundItems;
    }

    /**
     * Process a full refund for a transaction
     */
    public RefundResult processFullRefund(Transaction originalTransaction, String refundReason, String cashierName) {
        try {
            // Validate transaction can be refunded
            RefundValidationResult validation = validateRefund(originalTransaction);
            if (!validation.isValid()) {
                return new RefundResult(false, validation.getErrorMessage(), null);
            }

            // Create refund transaction record
            Transaction refundTransaction = createRefundTransaction(originalTransaction, refundReason, cashierName);

            // Process inventory adjustments with enhanced validation using dedicated service
            InventoryAdjustmentResult inventoryResult = inventoryService.processRefundInventoryAdjustments(originalTransaction.getItems());
            if (!inventoryResult.isSuccess()) {
                return new RefundResult(false, "Failed to adjust inventory for refund: " + inventoryResult.getErrorMessage(), null);
            }

            // Process outstanding balance updates
            OutstandingBalanceUpdateResult balanceResult = outstandingBalanceService.processFullRefundBalanceUpdate(
                    originalTransaction.getId(),
                    originalTransaction.getTotalAmount(),
                    "REFUND",
                    cashierName,
                    refundReason
            );

            if (!balanceResult.isSuccess()) {
                return new RefundResult(false, "Refund processed but failed to update outstanding balance: " + balanceResult.getMessage(), null);
            }

            // Update original transaction status
            originalTransaction.setStatus("REFUNDED");
            originalTransaction.setRefundedAmount(originalTransaction.getTotalAmount());
            originalTransaction.setUpdatedAt(LocalDateTime.now());

            // Update customer data if applicable
            if (originalTransaction.getCustomer() != null) {
                updateCustomerDataForRefund(originalTransaction.getCustomer(), originalTransaction.getTotalAmount());
            }

            // Save all changes
            transactionDAO.save(originalTransaction);
            Transaction savedRefundTransaction = transactionDAO.save(refundTransaction);

            RefundResult result = new RefundResult(true, "Full refund processed successfully", savedRefundTransaction);

            // Add balance update information to result
            if (balanceResult.hasWarnings()) {
                result.addWarnings(balanceResult.getWarnings());
            }

            return result;

        } catch (SQLException e) {
            return new RefundResult(false, "Database error during refund processing: " + e.getMessage(), null);
        } catch (Exception e) {
            return new RefundResult(false, "Unexpected error during refund processing: " + e.getMessage(), null);
        }
    }

    /**
     * Process a partial refund for specific items
     */
    public RefundResult processPartialRefund(Transaction originalTransaction, List<RefundItem> refundItems,
            String refundReason, String cashierName) {
        try {
            // Validate partial refund
            RefundValidationResult validation = validatePartialRefund(originalTransaction, refundItems);
            if (!validation.isValid()) {
                return new RefundResult(false, validation.getErrorMessage(), null);
            }

            // Calculate refund amounts
            BigDecimal refundSubtotal = calculateRefundSubtotal(refundItems);
            BigDecimal refundTax = calculateRefundTax(refundSubtotal, originalTransaction);
            BigDecimal refundTotal = refundSubtotal.add(refundTax);

            // Create partial refund transaction
            Transaction refundTransaction = createPartialRefundTransaction(originalTransaction, refundItems,
                    refundSubtotal, refundTax, refundTotal,
                    refundReason, cashierName);

            // Process inventory adjustments for refunded items with enhanced validation using dedicated service
            List<TransactionItem> itemsToAdjust = convertRefundItemsToTransactionItems(refundItems);
            InventoryAdjustmentResult inventoryResult = inventoryService.processRefundInventoryAdjustments(itemsToAdjust);
            if (!inventoryResult.isSuccess()) {
                return new RefundResult(false, "Failed to adjust inventory for partial refund: " + inventoryResult.getErrorMessage(), null);
            }

            // Process outstanding balance updates
            OutstandingBalanceUpdateResult balanceResult = outstandingBalanceService.processPartialRefundBalanceUpdate(
                    originalTransaction.getId(),
                    refundTotal,
                    "REFUND",
                    cashierName,
                    refundReason
            );

            if (!balanceResult.isSuccess()) {
                return new RefundResult(false, "Partial refund processed but failed to update outstanding balance: " + balanceResult.getMessage(), null);
            }

            // Update original transaction
            BigDecimal currentRefundedAmount = originalTransaction.getRefundedAmount() != null
                    ? originalTransaction.getRefundedAmount() : BigDecimal.ZERO;
            BigDecimal newRefundedAmount = currentRefundedAmount.add(refundTotal);

            originalTransaction.setRefundedAmount(newRefundedAmount);

            // Determine new status
            if (newRefundedAmount.compareTo(originalTransaction.getTotalAmount()) >= 0) {
                originalTransaction.setStatus("REFUNDED");
            } else {
                originalTransaction.setStatus("PARTIALLY_REFUNDED");
            }
            originalTransaction.setUpdatedAt(LocalDateTime.now());

            // Update customer data if applicable
            if (originalTransaction.getCustomer() != null) {
                updateCustomerDataForRefund(originalTransaction.getCustomer(), refundTotal);
            }

            // Save all changes
            transactionDAO.save(originalTransaction);
            Transaction savedRefundTransaction = transactionDAO.save(refundTransaction);

            RefundResult result = new RefundResult(true, "Partial refund processed successfully", savedRefundTransaction);

            // Add balance update information to result
            if (balanceResult.hasWarnings()) {
                result.addWarnings(balanceResult.getWarnings());
            }

            return result;

        } catch (SQLException e) {
            return new RefundResult(false, "Database error during partial refund processing: " + e.getMessage(), null);
        } catch (Exception e) {
            return new RefundResult(false, "Unexpected error during partial refund processing: " + e.getMessage(), null);
        }
    }

    /**
     * Validate if a transaction can be refunded
     */
    private RefundValidationResult validateRefund(Transaction transaction) {
        if (transaction == null) {
            return new RefundValidationResult(false, "Transaction not found");
        }

        // Allow refunds for completed transactions and installment transactions with payments
        String status = transaction.getStatus();
        boolean canRefund = "COMPLETED".equals(status)
                || "PARTIAL_PAYMENT".equals(status)
                || "PENDING_COMPLETION".equals(status)
                || (transaction.getAmountPaid() != null
                && transaction.getAmountPaid().compareTo(BigDecimal.ZERO) > 0);

        if (!canRefund) {
            return new RefundValidationResult(false,
                    "Only completed transactions or transactions with payments can be refunded. Current status: " + status);
        }

        if (transaction.getRefundedAmount() != null
                && transaction.getRefundedAmount().compareTo(transaction.getTotalAmount()) >= 0) {
            return new RefundValidationResult(false, "Transaction has already been fully refunded");
        }

        // Check if refund is within allowed time period (e.g., 30 days)
        if (transaction.getTransactionDate().isBefore(LocalDateTime.now().minusDays(30))) {
            return new RefundValidationResult(false, "Refund period has expired (30 days limit)");
        }

        return new RefundValidationResult(true, "Transaction can be refunded");
    }

    /**
     * Validate partial refund request
     */
    private RefundValidationResult validatePartialRefund(Transaction transaction, List<RefundItem> refundItems) {
        RefundValidationResult basicValidation = validateRefund(transaction);
        if (!basicValidation.isValid()) {
            return basicValidation;
        }

        if (refundItems == null || refundItems.isEmpty()) {
            return new RefundValidationResult(false, "No items specified for refund");
        }

        // Validate each refund item
        for (RefundItem refundItem : refundItems) {
            if (!refundItem.canRefund()) {
                return new RefundValidationResult(false,
                        "Invalid refund quantity for product: " + refundItem.getProductName());
            }

            if (refundItem.getRefundQuantity() > refundItem.getMaxRefundQuantity()) {
                return new RefundValidationResult(false,
                        "Cannot refund more items than originally purchased for product: "
                        + refundItem.getProductName());
            }
        }

        return new RefundValidationResult(true, "Partial refund is valid");
    }

    /**
     * Enhanced validation for partial refund with detailed error reporting
     */
    private RefundValidationResult validatePartialRefundEnhanced(Transaction transaction, List<RefundItem> refundItems) {
        RefundValidationResult basicValidation = validatePartialRefund(transaction, refundItems);
        if (!basicValidation.isValid()) {
            return basicValidation;
        }

        // Additional enhanced validations
        BigDecimal totalRefundAmount = calculateRefundSubtotal(refundItems);
        BigDecimal alreadyRefunded = transaction.getRefundedAmount() != null
                ? transaction.getRefundedAmount() : BigDecimal.ZERO;
        BigDecimal maxRefundable = transaction.getTotalAmount().subtract(alreadyRefunded);

        if (totalRefundAmount.compareTo(maxRefundable) > 0) {
            return new RefundValidationResult(false,
                    String.format("Total refund amount ($%.2f) exceeds maximum refundable amount ($%.2f). Already refunded: $%.2f",
                            totalRefundAmount.doubleValue(), maxRefundable.doubleValue(), alreadyRefunded.doubleValue()));
        }

        // Validate individual item refund limits
        for (RefundItem refundItem : refundItems) {
            if (!refundItem.isSelected()) {
                continue;
            }

            // Check if item has already been partially refunded
            // This would require tracking individual item refund history
            // For now, we'll validate against original quantities
            if (refundItem.getRefundQuantity() <= 0) {
                return new RefundValidationResult(false,
                        "Invalid refund quantity for " + refundItem.getProductName() + ": must be greater than 0");
            }

            if (refundItem.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return new RefundValidationResult(false,
                        "Invalid refund amount for " + refundItem.getProductName() + ": must be greater than $0.00");
            }
        }

        return new RefundValidationResult(true, "Enhanced partial refund validation passed");
    }

    /**
     * Enhanced inventory adjustment with validation and rollback capability
     */
    private boolean adjustInventoryForRefundWithValidation(List<TransactionItem> refundItems) {
        try {
            // First pass: validate all inventory adjustments are possible
            for (TransactionItem item : refundItems) {
                if (item.getProduct() == null) {
                    System.err.println("WARNING: Cannot adjust inventory for item with null product");
                    continue;
                }

                // Check if product still exists
                Optional<Product> productOpt = productDAO.findById(item.getProduct().getId());
                if (productOpt.isEmpty()) {
                    System.err.println("ERROR: Product not found for inventory adjustment: " + item.getProduct().getId());
                    return false;
                }

                Product product = productOpt.get();
                int newStock = product.getStockQuantity() + item.getQuantity();

                // Validate new stock level is reasonable (not negative, not exceeding max)
                if (newStock < 0) {
                    System.err.println("ERROR: Invalid stock adjustment would result in negative stock for product: " + product.getName());
                    return false;
                }

                // Optional: Check for maximum stock limits
                // if (newStock > MAX_STOCK_LIMIT) { ... }
            }

            // Second pass: perform all inventory adjustments
            for (TransactionItem item : refundItems) {
                if (item.getProduct() == null) {
                    continue;
                }

                Optional<Product> productOpt = productDAO.findById(item.getProduct().getId());
                if (productOpt.isPresent()) {
                    Product product = productOpt.get();
                    int newStock = product.getStockQuantity() + item.getQuantity();

                    productDAO.updateStock(item.getProduct().getId(), newStock);
                    // updateStock throws SQLException if it fails, so if we reach here it succeeded

                    System.out.println("DEBUG: Restored " + item.getQuantity() + " units to inventory for product: " + product.getName()
                            + " (New stock: " + newStock + ")");
                }
            }

            return true;

        } catch (Exception e) {
            System.err.println("ERROR: Exception during inventory adjustment: " + e.getMessage());
            return false;
        }
    }

    /**
     * Enhanced partial refund transaction creation with detailed tracking
     */
    private Transaction createPartialRefundTransactionEnhanced(Transaction originalTransaction, List<RefundItem> refundItems,
            BigDecimal refundSubtotal, BigDecimal refundTax, BigDecimal refundTotal,
            String refundReason, String cashierName) throws SQLException {

        Transaction refundTransaction = new Transaction();

        // Generate unique refund transaction number
        String refundTransactionNumber = "REF-" + originalTransaction.getTransactionNumber() + "-"
                + System.currentTimeMillis() % 10000;
        refundTransaction.setTransactionNumber(refundTransactionNumber);

        // Copy customer information
        refundTransaction.setCustomerId(originalTransaction.getCustomerId());
        refundTransaction.setCustomer(originalTransaction.getCustomer());

        // Set refund amounts (negative values to indicate refund)
        refundTransaction.setSubtotal(refundSubtotal.negate());
        refundTransaction.setTaxAmount(refundTax.negate());
        refundTransaction.setTotalAmount(refundTotal.negate());
        refundTransaction.setAmountPaid(refundTotal.negate());

        // Set transaction details
        refundTransaction.setTransactionDate(LocalDateTime.now());
        refundTransaction.setPaymentMethod("REFUND");
        refundTransaction.setStatus("REFUNDED");
        refundTransaction.setCashierName(cashierName);

        // Enhanced notes with detailed information
        StringBuilder notesBuilder = new StringBuilder();
        notesBuilder.append("PARTIAL REFUND - Original Transaction: ").append(originalTransaction.getTransactionNumber());
        notesBuilder.append(" | Reason: ").append(refundReason);
        notesBuilder.append(" | Items Refunded: ").append(refundItems.size());
        notesBuilder.append(" | Refund Amount: $").append(refundTotal);
        notesBuilder.append(" | Processed by: ").append(cashierName);
        notesBuilder.append(" | Date: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        refundTransaction.setNotes(notesBuilder.toString());

        // Convert refund items to transaction items (negative quantities)
        List<TransactionItem> refundTransactionItems = new ArrayList<>();
        for (RefundItem refundItem : refundItems) {
            if (!refundItem.isSelected() || refundItem.getRefundQuantity() <= 0) {
                continue;
            }

            TransactionItem transactionItem = new TransactionItem();
            transactionItem.setProduct(refundItem.getOriginalItem().getProduct());
            transactionItem.setQuantity(-refundItem.getRefundQuantity()); // Negative for refund
            transactionItem.setUnitPrice(refundItem.getUnitPrice());
            transactionItem.setDiscount(BigDecimal.ZERO);
            transactionItem.setLineTotal(refundItem.getRefundAmount().negate()); // Negative for refund

            refundTransactionItems.add(transactionItem);
        }

        refundTransaction.setItems(refundTransactionItems);
        refundTransaction.setCreatedAt(LocalDateTime.now());
        refundTransaction.setUpdatedAt(LocalDateTime.now());

        // Save the refund transaction
        return transactionDAO.save(refundTransaction);
    }

    /**
     * Create a refund transaction record
     */
    private Transaction createRefundTransaction(Transaction originalTransaction, String refundReason, String cashierName) {
        Transaction refundTransaction = new Transaction();
        refundTransaction.setTransactionNumber("REF-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase());
        refundTransaction.setCustomerId(originalTransaction.getCustomerId());
        refundTransaction.setCustomer(originalTransaction.getCustomer());
        refundTransaction.setTransactionDate(LocalDateTime.now());
        refundTransaction.setSubtotal(originalTransaction.getSubtotal().negate());
        refundTransaction.setTaxAmount(originalTransaction.getTaxAmount().negate());
        refundTransaction.setDiscountAmount(BigDecimal.ZERO);
        refundTransaction.setTotalAmount(originalTransaction.getTotalAmount().negate());
        refundTransaction.setRefundedAmount(BigDecimal.ZERO);
        refundTransaction.setAmountPaid(originalTransaction.getTotalAmount().negate());
        refundTransaction.setPaymentMethod("REFUND");
        refundTransaction.setStatus("REFUND");
        refundTransaction.setNotes("REFUND for transaction: " + originalTransaction.getTransactionNumber()
                + ". Reason: " + refundReason);
        refundTransaction.setCashierName(cashierName);

        // Copy items with negative quantities
        List<TransactionItem> refundItems = new ArrayList<>();
        for (TransactionItem originalItem : originalTransaction.getItems()) {
            TransactionItem refundItem = new TransactionItem();
            refundItem.setProduct(originalItem.getProduct());
            refundItem.setQuantity(-originalItem.getQuantity()); // Negative quantity for refund
            refundItem.setUnitPrice(originalItem.getUnitPrice());
            refundItem.setDiscount(originalItem.getDiscount());
            refundItem.setLineTotal(originalItem.getLineTotal().negate());
            refundItems.add(refundItem);
        }
        refundTransaction.setItems(refundItems);

        return refundTransaction;
    }

    /**
     * Create a partial refund transaction record
     */
    private Transaction createPartialRefundTransaction(Transaction originalTransaction, List<RefundItem> refundItems,
            BigDecimal refundSubtotal, BigDecimal refundTax,
            BigDecimal refundTotal, String refundReason, String cashierName) {
        Transaction refundTransaction = new Transaction();
        refundTransaction.setTransactionNumber("PREF-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase());
        refundTransaction.setCustomerId(originalTransaction.getCustomerId());
        refundTransaction.setCustomer(originalTransaction.getCustomer());
        refundTransaction.setTransactionDate(LocalDateTime.now());
        refundTransaction.setSubtotal(refundSubtotal.negate());
        refundTransaction.setTaxAmount(refundTax.negate());
        refundTransaction.setDiscountAmount(BigDecimal.ZERO);
        refundTransaction.setTotalAmount(refundTotal.negate());
        refundTransaction.setRefundedAmount(BigDecimal.ZERO);
        refundTransaction.setAmountPaid(refundTotal.negate());
        refundTransaction.setPaymentMethod("REFUND");
        refundTransaction.setStatus("REFUND");
        refundTransaction.setNotes("PARTIAL REFUND for transaction: " + originalTransaction.getTransactionNumber()
                + ". Reason: " + refundReason);
        refundTransaction.setCashierName(cashierName);

        // Convert refund items to transaction items with negative quantities
        List<TransactionItem> transactionItems = convertRefundItemsToTransactionItems(refundItems);
        for (TransactionItem item : transactionItems) {
            item.setQuantity(-item.getQuantity()); // Make quantities negative for refund
            item.setLineTotal(item.getLineTotal().negate());
        }
        refundTransaction.setItems(transactionItems);

        return refundTransaction;
    }

    /**
     * Adjust inventory quantities for refunded items
     */
    private boolean adjustInventoryForRefund(List<TransactionItem> refundedItems) {
        try {
            for (TransactionItem item : refundedItems) {
                java.util.Optional<Product> productOpt = productDAO.findById(item.getProduct().getId());
                if (productOpt.isPresent()) {
                    Product product = productOpt.get();
                    // Add back the refunded quantity to inventory
                    int newStockQuantity = product.getStockQuantity() + Math.abs(item.getQuantity());
                    product.setStockQuantity(newStockQuantity);
                    productDAO.save(product);
                }
            }
            return true;
        } catch (SQLException e) {
            System.err.println("Error adjusting inventory for refund: " + e.getMessage());
            return false;
        }
    }

    /**
     * Enhanced inventory adjustment with comprehensive validation and detailed
     * tracking
     */
    private InventoryAdjustmentResult adjustInventoryForRefundEnhanced(List<TransactionItem> refundedItems) {
        InventoryAdjustmentResult result = new InventoryAdjustmentResult();
        List<InventoryAdjustmentResult.InventoryAdjustmentDetail> adjustmentDetails = new ArrayList<>();

        try {
            // Phase 1: Validation - Check all items can be processed
            for (TransactionItem item : refundedItems) {
                if (item.getProduct() == null) {
                    result.addWarning("Skipping item with null product reference");
                    continue;
                }

                Optional<Product> productOpt = productDAO.findById(item.getProduct().getId());
                if (productOpt.isEmpty()) {
                    result.setSuccess(false);
                    result.setErrorMessage("Product not found for inventory adjustment: ID " + item.getProduct().getId());
                    return result;
                }

                Product product = productOpt.get();
                int adjustmentQuantity = Math.abs(item.getQuantity());
                int newStock = product.getStockQuantity() + adjustmentQuantity;

                // Validate new stock level is reasonable
                if (newStock < 0) {
                    result.setSuccess(false);
                    result.setErrorMessage("Invalid stock adjustment would result in negative stock for product: " + product.getName());
                    return result;
                }

                // Check for maximum stock limits (optional business rule)
                final int MAX_STOCK_LIMIT = 10000; // Configurable limit
                if (newStock > MAX_STOCK_LIMIT) {
                    result.addWarning("Stock adjustment for " + product.getName() + " exceeds recommended maximum (" + MAX_STOCK_LIMIT + ")");
                }
            }

            // Phase 2: Execute adjustments
            for (TransactionItem item : refundedItems) {
                if (item.getProduct() == null) {
                    continue;
                }

                Optional<Product> productOpt = productDAO.findById(item.getProduct().getId());
                if (productOpt.isPresent()) {
                    Product product = productOpt.get();
                    int previousStock = product.getStockQuantity();
                    int adjustmentQuantity = Math.abs(item.getQuantity());
                    int newStock = previousStock + adjustmentQuantity;

                    try {
                        // Update stock using DAO method for consistency
                        productDAO.updateStock(item.getProduct().getId(), newStock);

                        // Create adjustment detail record
                        InventoryAdjustmentResult.InventoryAdjustmentDetail detail
                                = new InventoryAdjustmentResult.InventoryAdjustmentDetail(
                                        product.getId(), product.getName(), previousStock,
                                        adjustmentQuantity, newStock, true);
                        adjustmentDetails.add(detail);

                        System.out.println("DEBUG: Successfully restored " + adjustmentQuantity
                                + " units to inventory for product: " + product.getName()
                                + " (Previous: " + previousStock + ", New: " + newStock + ")");

                    } catch (SQLException e) {
                        // Create failed adjustment detail
                        InventoryAdjustmentResult.InventoryAdjustmentDetail detail
                                = new InventoryAdjustmentResult.InventoryAdjustmentDetail(
                                        product.getId(), product.getName(), previousStock,
                                        adjustmentQuantity, previousStock, false);
                        detail.setErrorMessage("Database error: " + e.getMessage());
                        adjustmentDetails.add(detail);

                        result.setSuccess(false);
                        result.setErrorMessage("Failed to update stock for product " + product.getName() + ": " + e.getMessage());
                        result.setAdjustmentDetails(adjustmentDetails);
                        return result;
                    }
                }
            }

            result.setSuccess(true);
            result.setAdjustmentDetails(adjustmentDetails);
            return result;

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("Unexpected error during inventory adjustment: " + e.getMessage());
            result.setAdjustmentDetails(adjustmentDetails);
            return result;
        }
    }

    /**
     * Update customer data for refund (reduce total spent, adjust loyalty
     * points)
     */
    private void updateCustomerDataForRefund(Customer customer, BigDecimal refundAmount) {
        try {
            // Reduce total spent
            double newTotalSpent = customer.getTotalSpent() - refundAmount.doubleValue();
            customer.setTotalSpent(Math.max(0, newTotalSpent)); // Don't go below 0

            // Reduce loyalty points (assuming 1 point per dollar spent)
            int pointsToDeduct = refundAmount.intValue();
            int newLoyaltyPoints = Math.max(0, customer.getLoyaltyPoints() - pointsToDeduct);
            customer.setLoyaltyPoints(newLoyaltyPoints);

            // Reduce total purchases count if full refund
            if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                customer.setTotalPurchases(Math.max(0, customer.getTotalPurchases() - 1));
            }

            customerDAO.save(customer);
        } catch (SQLException e) {
            System.err.println("Error updating customer data for refund: " + e.getMessage());
        }
    }

    /**
     * Calculate refund subtotal from refund items
     */
    private BigDecimal calculateRefundSubtotal(List<RefundItem> refundItems) {
        return refundItems.stream()
                .map(item -> item.getUnitPrice().multiply(BigDecimal.valueOf(item.getRefundQuantity())))
                .reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
    }

    /**
     * Calculate refund tax amount
     */
    private BigDecimal calculateRefundTax(BigDecimal refundSubtotal, Transaction originalTransaction) {
        if (originalTransaction.getSubtotal().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        // Calculate tax rate from original transaction
        BigDecimal taxRate = originalTransaction.getTaxAmount().divide(originalTransaction.getSubtotal(), 4, java.math.RoundingMode.HALF_UP);
        return refundSubtotal.multiply(taxRate);
    }

    /**
     * Convert RefundItem list to TransactionItem list
     */
    private List<TransactionItem> convertRefundItemsToTransactionItems(List<RefundItem> refundItems) {
        List<TransactionItem> transactionItems = new ArrayList<>();

        for (RefundItem refundItem : refundItems) {
            TransactionItem originalItem = refundItem.getOriginalItem();
            if (originalItem != null) {
                TransactionItem transactionItem = new TransactionItem();
                transactionItem.setProduct(originalItem.getProduct());
                transactionItem.setQuantity(refundItem.getRefundQuantity());
                transactionItem.setUnitPrice(refundItem.getUnitPrice());
                transactionItem.setDiscount(BigDecimal.ZERO);

                BigDecimal lineTotal = refundItem.getUnitPrice().multiply(BigDecimal.valueOf(refundItem.getRefundQuantity()));
                transactionItem.setLineTotal(lineTotal);

                transactionItems.add(transactionItem);
            }
        }

        return transactionItems;
    }

    /**
     * Get all refunds for a specific transaction
     */
    public List<Transaction> getRefundsForTransaction(String originalTransactionNumber) {
        try {
            return transactionDAO.findRefundsForTransaction(originalTransactionNumber);
        } catch (SQLException e) {
            System.err.println("Error retrieving refunds for transaction: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Check if a transaction has any refunds
     */
    public boolean hasRefunds(Transaction transaction) {
        return transaction.getRefundedAmount() != null
                && transaction.getRefundedAmount().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Get net amount for a transaction (total - refunded)
     */
    public BigDecimal getNetAmount(Transaction transaction) {
        BigDecimal refundedAmount = transaction.getRefundedAmount() != null
                ? transaction.getRefundedAmount() : BigDecimal.ZERO;
        return transaction.getTotalAmount().subtract(refundedAmount);
    }
}
