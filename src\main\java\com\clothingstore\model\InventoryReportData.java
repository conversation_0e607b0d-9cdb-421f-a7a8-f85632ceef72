package com.clothingstore.model;

/**
 * Data model for inventory report entries
 */
public class InventoryReportData {
    private String sku;
    private String productName;
    private int stockQuantity;
    private double value;
    private String status;
    private String category;

    public InventoryReportData() {}

    public InventoryReportData(String sku, String productName, int stockQuantity, double value, String status) {
        this.sku = sku;
        this.productName = productName;
        this.stockQuantity = stockQuantity;
        this.value = value;
        this.status = status;
    }

    public InventoryReportData(String sku, String productName, int stockQuantity, double value, String status, String category) {
        this.sku = sku;
        this.productName = productName;
        this.stockQuantity = stockQuantity;
        this.value = value;
        this.status = status;
        this.category = category;
    }

    // Getters and Setters
    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public int getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(int stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public double getValue() {
        return value;
    }

    public void setValue(double value) {
        this.value = value;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    @Override
    public String toString() {
        return String.format("InventoryReportData{sku='%s', productName='%s', stockQuantity=%d, value=%.2f, status='%s'}", 
                           sku, productName, stockQuantity, value, status);
    }
}
