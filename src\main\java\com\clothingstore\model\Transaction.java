package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Transaction model class representing sales transactions
 */
public class Transaction {

    private Long id;
    private String transactionNumber;
    private Long customerId;
    private Customer customer;
    private LocalDateTime transactionDate;
    private BigDecimal subtotal;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalAmount;
    private BigDecimal refundedAmount; // Track total amount refunded for partial refunds
    private BigDecimal amountPaid; // Track amount paid for partial payments
    private String paymentMethod; // CASH, CREDIT_CARD, DEBIT_CARD, GIFT_CARD
    private String status; // COMPLETED, REFUNDED, PARTIALLY_REFUNDED, CANCELLED, PENDING, PARTIAL_PAYMENT
    private String notes;
    private List<TransactionItem> items;
    private String cashierName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Installment payment tracking fields
    private Boolean isInstallment; // Whether this is an installment transaction
    private String installmentStatus; // NONE, PENDING, IN_PROGRESS, COMPLETED
    private Integer totalInstallments; // Total number of planned installments
    private Integer completedInstallments; // Number of completed installments

    // Constructors
    public Transaction() {
        this.transactionDate = LocalDateTime.now();
        this.status = "PENDING"; // Default status for a new transaction
        this.items = new ArrayList<>();
        this.totalAmount = BigDecimal.ZERO;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.subtotal = BigDecimal.ZERO;
        this.taxAmount = BigDecimal.ZERO;
        this.discountAmount = BigDecimal.ZERO;
    }

    public Transaction(String transactionNumber, Customer customer) {
        this();
        this.transactionNumber = transactionNumber;
        this.customer = customer;
        this.customerId = customer != null ? customer.getId() : null;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
        this.customerId = customer != null ? customer.getId() : null;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRefundedAmount() {
        return refundedAmount != null ? refundedAmount : BigDecimal.ZERO;
    }

    public void setRefundedAmount(BigDecimal refundedAmount) {
        this.refundedAmount = refundedAmount;
    }

    public BigDecimal getAmountPaid() {
        return amountPaid != null ? amountPaid : BigDecimal.ZERO;
    }

    public void setAmountPaid(BigDecimal amountPaid) {
        this.amountPaid = amountPaid;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<TransactionItem> getItems() {
        return items;
    }

    public void setItems(List<TransactionItem> items) {
        this.items = items;
    }

    public void setDiscount(BigDecimal discountPercentage) {
        if (discountPercentage.compareTo(BigDecimal.ZERO) >= 0 && discountPercentage.compareTo(BigDecimal.ONE) <= 0) {
            this.discountAmount = subtotal.multiply(discountPercentage);
            recalculateAmounts();
        }
    }

    public String getCashierName() {
        return cashierName;
    }

    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Installment payment getters and setters
    public Boolean getIsInstallment() {
        return isInstallment != null ? isInstallment : false;
    }

    public void setIsInstallment(Boolean isInstallment) {
        this.isInstallment = isInstallment;
    }

    public String getInstallmentStatus() {
        return installmentStatus != null ? installmentStatus : "NONE";
    }

    public void setInstallmentStatus(String installmentStatus) {
        this.installmentStatus = installmentStatus;
    }

    public Integer getTotalInstallments() {
        return totalInstallments != null ? totalInstallments : 0;
    }

    public void setTotalInstallments(Integer totalInstallments) {
        this.totalInstallments = totalInstallments;
    }

    public Integer getCompletedInstallments() {
        return completedInstallments != null ? completedInstallments : 0;
    }

    public void setCompletedInstallments(Integer completedInstallments) {
        this.completedInstallments = completedInstallments;
    }

    // Business methods
    public void addItem(TransactionItem item) {
        items.add(item);
        item.setTransaction(this);
        recalculateAmounts();
    }

    public void removeItem(TransactionItem item) {
        items.remove(item);
        recalculateAmounts();
    }

    public void recalculateAmounts() {
        subtotal = items.stream()
                .map(TransactionItem::getLineTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // No automatic discount application - discounts must be manually set
        // discountAmount is only applied if manually set via setDiscountAmount()
        // No tax calculation - set tax to zero
        taxAmount = BigDecimal.ZERO;

        // Calculate total (subtotal minus manually applied discount, no tax)
        totalAmount = subtotal.subtract(discountAmount != null ? discountAmount : BigDecimal.ZERO);

        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Manually apply a discount amount
     */
    public void applyManualDiscount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount != null ? discountAmount : BigDecimal.ZERO;
        recalculateAmounts();
    }

    /**
     * Manually apply a discount percentage
     */
    public void applyManualDiscountPercentage(double discountPercentage) {
        if (discountPercentage >= 0.0 && discountPercentage <= 1.0) {
            this.discountAmount = subtotal.multiply(BigDecimal.valueOf(discountPercentage));
            recalculateAmounts();
        }
    }

    /**
     * Clear any applied discount
     */
    public void clearDiscount() {
        this.discountAmount = BigDecimal.ZERO;
        recalculateAmounts();
    }

    public int getTotalItemCount() {
        return items.stream().mapToInt(TransactionItem::getQuantity).sum();
    }

    // Convenience methods for UI compatibility
    public BigDecimal getTotal() {
        return getTotalAmount();
    }

    public BigDecimal getTax() {
        return getTaxAmount();
    }

    public BigDecimal getDiscount() {
        return getDiscountAmount();
    }

    public int getTotalItems() {
        return getTotalItemCount();
    }

    public String getCustomerName() {
        return customer != null ? customer.getFullName() : null;
    }

    /**
     * Checks if a transaction is eligible for a refund. Only completed
     * transactions can be refunded.
     *
     * @return true if the transaction can be refunded, false otherwise.
     */
    public boolean canBeRefunded() {
        return "COMPLETED".equalsIgnoreCase(status) || "PARTIALLY_REFUNDED".equalsIgnoreCase(status);
    }

    /**
     * Checks if a transaction has been fully refunded.
     *
     * @return true if the transaction is fully refunded, false otherwise.
     */
    public boolean isRefunded() {
        return "REFUNDED".equalsIgnoreCase(status);
    }

    /**
     * Mark transaction as fully refunded
     */
    public void processRefund() {
        if (!canBeRefunded()) {
            throw new IllegalStateException("Transaction cannot be refunded. Current status: " + status);
        }
        this.status = "REFUNDED";
        this.refundedAmount = this.totalAmount; // Full refund
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Mark transaction as partially refunded
     *
     * @param refundAmount The amount that was refunded
     */
    public void processPartialRefund(BigDecimal refundAmount) {
        if (!canBeRefunded()) {
            throw new IllegalStateException("Transaction cannot be partially refunded. Current status: " + status);
        }
        this.status = "PARTIALLY_REFUNDED";
        this.refundedAmount = (this.refundedAmount != null ? this.refundedAmount : BigDecimal.ZERO).add(refundAmount);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Process a partial payment
     *
     * @param paymentAmount The amount being paid
     * @throws IllegalArgumentException if payment amount is invalid
     */
    public void processPartialPayment(BigDecimal paymentAmount) {
        if (paymentAmount == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Payment amount must be greater than zero");
        }

        BigDecimal currentPaid = getAmountPaid();
        BigDecimal remainingBalance = getRemainingBalance();

        // Enhanced validation for partial payments
        if (paymentAmount.compareTo(remainingBalance) > 0) {
            throw new IllegalArgumentException(String.format(
                    "Payment amount $%.2f exceeds remaining balance of $%.2f",
                    paymentAmount.doubleValue(), remainingBalance.doubleValue()));
        }

        BigDecimal newAmountPaid = currentPaid.add(paymentAmount);
        this.amountPaid = newAmountPaid;

        // Update status based on payment completion
        if (newAmountPaid.compareTo(totalAmount) == 0) {
            this.status = "COMPLETED";
            // Add completion note
            String completionNote = String.format("Transaction completed with final payment of $%.2f",
                    paymentAmount.doubleValue());
            addPaymentNote(completionNote);
        } else {
            this.status = "PARTIAL_PAYMENT";
            // Add partial payment note
            String partialNote = String.format("Partial payment of $%.2f received. Remaining balance: $%.2f",
                    paymentAmount.doubleValue(), getRemainingBalance().doubleValue());
            addPaymentNote(partialNote);
        }

        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Add a payment note to the transaction
     */
    private void addPaymentNote(String note) {
        String timestamp = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm"));
        String paymentNote = String.format("[%s] %s", timestamp, note);

        if (this.notes == null || this.notes.trim().isEmpty()) {
            this.notes = paymentNote;
        } else {
            this.notes = this.notes + "\n" + paymentNote;
        }
    }

    /**
     * Process a full payment
     *
     * @param paymentAmount The amount being paid
     */
    public void processFullPayment(BigDecimal paymentAmount) {
        if (paymentAmount == null || paymentAmount.compareTo(totalAmount) < 0) {
            throw new IllegalArgumentException("Payment amount must be at least the total amount");
        }

        this.amountPaid = totalAmount;
        this.status = "COMPLETED";
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Get the remaining balance owed
     *
     * @return The remaining balance
     */
    public BigDecimal getRemainingBalance() {
        // For COMPLETED or PARTIALLY_REFUNDED transactions without payment history (amountPaid = 0),
        // the remaining balance after refunds should be: totalAmount - refundedAmount
        if (("COMPLETED".equals(status) || "PARTIALLY_REFUNDED".equals(status))
                && getAmountPaid().compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal refunded = refundedAmount != null ? refundedAmount : BigDecimal.ZERO;
            return totalAmount.subtract(refunded);
        }

        // For transactions with payment history, calculate as: totalAmount - amountPaid
        // (refunds are already accounted for in the amountPaid through payment history)
        return totalAmount.subtract(getAmountPaid());
    }

    /**
     * Calculate actual profit based on amount actually paid (not listed price)
     * This accounts for discounts and partial payments
     *
     * @return The actual profit earned from this transaction
     */
    public BigDecimal calculateActualProfit() {
        BigDecimal actualRevenue = getAmountPaid(); // Use actual amount paid, not total amount
        BigDecimal totalCost = BigDecimal.ZERO;

        if (items != null && !items.isEmpty()) {
            for (TransactionItem item : items) {
                if (item.getProduct() != null && item.getProduct().getCostPrice() != null) {
                    // Calculate proportional cost based on actual payment received
                    BigDecimal itemCost = item.getProduct().getCostPrice()
                            .multiply(new BigDecimal(item.getQuantity()));
                    totalCost = totalCost.add(itemCost);
                }
            }
        }

        return actualRevenue.subtract(totalCost);
    }

    /**
     * Calculate actual profit margin based on amount actually paid
     *
     * @return The actual profit margin percentage
     */
    public BigDecimal calculateActualProfitMargin() {
        BigDecimal actualRevenue = getAmountPaid();
        if (actualRevenue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal actualProfit = calculateActualProfit();
        return actualProfit.divide(actualRevenue, 4, java.math.RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * Get the effective selling price per unit (accounting for discounts) This
     * is the actual amount received divided by total quantity
     *
     * @return The effective selling price per unit
     */
    public BigDecimal getEffectiveSellingPrice() {
        int totalQuantity = items.stream().mapToInt(TransactionItem::getQuantity).sum();
        if (totalQuantity == 0) {
            return BigDecimal.ZERO;
        }

        return getAmountPaid().divide(new BigDecimal(totalQuantity), 2, java.math.RoundingMode.HALF_UP);
    }

    /**
     * Check if the transaction has an outstanding balance
     *
     * @return true if there's a remaining balance
     */
    public boolean hasOutstandingBalance() {
        return getRemainingBalance().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Check if the transaction is fully paid
     *
     * @return true if fully paid
     */
    public boolean isFullyPaid() {
        return getAmountPaid().compareTo(totalAmount) >= 0;
    }

    // Installment payment business logic methods
    /**
     * Initialize this transaction as an installment transaction
     */
    public void initializeAsInstallment() {
        this.isInstallment = true;
        this.installmentStatus = "PENDING";
        this.totalInstallments = 0;
        this.completedInstallments = 0;
        this.status = "PENDING";
        this.amountPaid = BigDecimal.ZERO;

        // Set default payment method if not already set (required for database NOT NULL constraint)
        if (this.paymentMethod == null || this.paymentMethod.trim().isEmpty()) {
            this.paymentMethod = "INSTALLMENT";
        }
    }

    /**
     * Process an installment payment (does not update inventory or customer
     * data)
     */
    public void processInstallmentPayment(BigDecimal paymentAmount) {
        if (paymentAmount == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Payment amount must be greater than zero");
        }

        BigDecimal currentPaid = getAmountPaid();
        BigDecimal newAmountPaid = currentPaid.add(paymentAmount);

        if (newAmountPaid.compareTo(totalAmount) > 0) {
            throw new IllegalArgumentException("Payment amount exceeds remaining balance");
        }

        this.amountPaid = newAmountPaid;
        this.completedInstallments = getCompletedInstallments() + 1;

        // Update installment status
        if (newAmountPaid.compareTo(totalAmount) == 0) {
            this.installmentStatus = "COMPLETED";
            this.status = "PENDING_COMPLETION"; // Ready for final processing
        } else {
            this.installmentStatus = "IN_PROGRESS";
            this.status = "PARTIAL_PAYMENT";
        }

        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Complete the installment transaction (final processing with inventory and
     * customer updates)
     */
    public void completeInstallmentTransaction() {
        if (!isFullyPaid()) {
            throw new IllegalStateException("Cannot complete installment transaction - not fully paid");
        }

        this.status = "COMPLETED";
        this.installmentStatus = "COMPLETED";
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Check if this is an installment transaction
     */
    public boolean isInstallmentTransaction() {
        return getIsInstallment();
    }

    /**
     * Check if installment transaction is ready for completion
     */
    public boolean isReadyForCompletion() {
        return isInstallmentTransaction() && isFullyPaid() && "PENDING_COMPLETION".equals(status);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Transaction that = (Transaction) o;
        return Objects.equals(id, that.id) && Objects.equals(transactionNumber, that.transactionNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, transactionNumber);
    }

    @Override
    public String toString() {
        return String.format("Transaction{id=%d, number='%s', date=%s, total=%s, status='%s'}",
                id, transactionNumber, transactionDate, totalAmount, status);
    }
}
