package com.clothingstore.model;

/**
 * Data model for financial report entries
 */
public class FinancialReportData {
    private String category;
    private double amount;
    private double percentage;
    private String notes;
    private String period;

    public FinancialReportData() {}

    public FinancialReportData(String category, double amount, double percentage, String notes) {
        this.category = category;
        this.amount = amount;
        this.percentage = percentage;
        this.notes = notes;
    }

    public FinancialReportData(String category, double amount, double percentage, String notes, String period) {
        this.category = category;
        this.amount = amount;
        this.percentage = percentage;
        this.notes = notes;
        this.period = period;
    }

    // Getters and Setters
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public double getPercentage() {
        return percentage;
    }

    public void setPercentage(double percentage) {
        this.percentage = percentage;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    @Override
    public String toString() {
        return String.format("FinancialReportData{category='%s', amount=%.2f, percentage=%.1f%%, notes='%s'}", 
                           category, amount, percentage, notes);
    }
}
