<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Priority?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.EnhancedTransactionHistoryController" onKeyPressed="#handleKeyPressed" focusTraversable="true" style="-fx-background-color: #f8f9fa;">
   <children>
      <!-- Header Section -->
      <VBox style="-fx-background-color: #2c3e50; -fx-padding: 15;">
         <children>
            <Label text="Enhanced Transaction History" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Label fx:id="lblCustomerInfo" textFill="#ecf0f1" wrapText="true">
               <font>
                  <Font size="12.0" />
               </font>
               <VBox.margin>
                  <Insets top="5.0" />
               </VBox.margin>
            </Label>
         </children>
      </VBox>

      <!-- Navigation Controls Section -->
      <HBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #34495e; -fx-padding: 10;">
         <children>
            <!-- Transaction Counter -->
            <Label fx:id="lblTransactionCounter" text="Transaction 1 of 1" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            
            <!-- Spacer -->
            <HBox HBox.hgrow="ALWAYS" />
            
            <!-- Navigation Buttons -->
            <Button fx:id="btnFirst" mnemonicParsing="false" onAction="#handleFirst" text="⏮ First" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;">
               <font>
                  <Font size="11.0" />
               </font>
            </Button>
            <Button fx:id="btnPrevious" mnemonicParsing="false" onAction="#handlePrevious" text="◀ Previous" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;">
               <font>
                  <Font size="11.0" />
               </font>
            </Button>
            <Button fx:id="btnNext" mnemonicParsing="false" onAction="#handleNext" text="Next ▶" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;">
               <font>
                  <Font size="11.0" />
               </font>
            </Button>
            <Button fx:id="btnLast" mnemonicParsing="false" onAction="#handleLast" text="Last ⏭" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;">
               <font>
                  <Font size="11.0" />
               </font>
            </Button>
         </children>
      </HBox>

      <!-- Transaction Details Section -->
      <VBox VBox.vgrow="ALWAYS">
         <children>
            <TextArea fx:id="txtTransactionDetails" editable="false" wrapText="true" VBox.vgrow="ALWAYS" style="-fx-background-color: white; -fx-border-color: #bdc3c7; -fx-border-width: 1; -fx-font-family: 'Courier New', monospace;">
               <font>
                  <Font size="12.0" />
               </font>
               <VBox.margin>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </VBox.margin>
            </TextArea>
         </children>
      </VBox>

      <!-- Footer Section -->
      <Separator />
      <HBox alignment="CENTER_RIGHT" spacing="10.0" style="-fx-background-color: #ecf0f1; -fx-padding: 10;">
         <children>
            <!-- Help Text -->
            <Label text="Use arrow keys or buttons to navigate • ESC to close" textFill="#7f8c8d">
               <font>
                  <Font size="10.0" />
               </font>
            </Label>
            
            <!-- Spacer -->
            <HBox HBox.hgrow="ALWAYS" />
            
            <!-- Action Buttons -->
            <Button fx:id="btnListView" mnemonicParsing="false" onAction="#handleListView" text="📋 List View" style="-fx-background-color: #95a5a6; -fx-text-fill: white;">
               <font>
                  <Font size="11.0" />
               </font>
            </Button>
            <Button fx:id="btnClose" mnemonicParsing="false" onAction="#handleClose" text="✖ Close" style="-fx-background-color: #e74c3c; -fx-text-fill: white;">
               <font>
                  <Font size="11.0" />
               </font>
            </Button>
         </children>
      </HBox>
   </children>
</VBox>
