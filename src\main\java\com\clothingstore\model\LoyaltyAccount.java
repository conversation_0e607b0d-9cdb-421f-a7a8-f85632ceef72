package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Model class representing a customer's loyalty account
 */
public class LoyaltyAccount {
    
    private Long id;
    private Long customerId;
    private String accountNumber;
    private int currentPoints;
    private int lifetimePointsEarned;
    private int lifetimePointsRedeemed;
    private BigDecimal lifetimeSpend;
    private LoyaltyTier currentTier;
    private LocalDateTime tierAchievedDate;
    private LocalDateTime lastActivityDate;
    private boolean active;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Transient fields for calculations
    private List<LoyaltyTransaction> recentTransactions;
    
    // Constructors
    public LoyaltyAccount() {
        this.currentPoints = 0;
        this.lifetimePointsEarned = 0;
        this.lifetimePointsRedeemed = 0;
        this.lifetimeSpend = BigDecimal.ZERO;
        this.currentTier = LoyaltyTier.BRONZE;
        this.active = true;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.recentTransactions = new ArrayList<>();
    }
    
    public LoyaltyAccount(Long customerId) {
        this();
        this.customerId = customerId;
        this.accountNumber = generateAccountNumber();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }
    
    public String getAccountNumber() {
        return accountNumber;
    }
    
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }
    
    public int getCurrentPoints() {
        return currentPoints;
    }
    
    public void setCurrentPoints(int currentPoints) {
        this.currentPoints = currentPoints;
    }
    
    public int getLifetimePointsEarned() {
        return lifetimePointsEarned;
    }
    
    public void setLifetimePointsEarned(int lifetimePointsEarned) {
        this.lifetimePointsEarned = lifetimePointsEarned;
    }
    
    public int getLifetimePointsRedeemed() {
        return lifetimePointsRedeemed;
    }
    
    public void setLifetimePointsRedeemed(int lifetimePointsRedeemed) {
        this.lifetimePointsRedeemed = lifetimePointsRedeemed;
    }
    
    public BigDecimal getLifetimeSpend() {
        return lifetimeSpend;
    }
    
    public void setLifetimeSpend(BigDecimal lifetimeSpend) {
        this.lifetimeSpend = lifetimeSpend;
    }
    
    public LoyaltyTier getCurrentTier() {
        return currentTier;
    }
    
    public void setCurrentTier(LoyaltyTier currentTier) {
        this.currentTier = currentTier;
    }
    
    public LocalDateTime getTierAchievedDate() {
        return tierAchievedDate;
    }
    
    public void setTierAchievedDate(LocalDateTime tierAchievedDate) {
        this.tierAchievedDate = tierAchievedDate;
    }
    
    public LocalDateTime getLastActivityDate() {
        return lastActivityDate;
    }
    
    public void setLastActivityDate(LocalDateTime lastActivityDate) {
        this.lastActivityDate = lastActivityDate;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public List<LoyaltyTransaction> getRecentTransactions() {
        return recentTransactions;
    }
    
    public void setRecentTransactions(List<LoyaltyTransaction> recentTransactions) {
        this.recentTransactions = recentTransactions;
    }
    
    // Business methods
    
    /**
     * Add points to the account
     */
    public void addPoints(int points, String description) {
        if (points > 0) {
            this.currentPoints += points;
            this.lifetimePointsEarned += points;
            this.lastActivityDate = LocalDateTime.now();
            this.updatedAt = LocalDateTime.now();
            
            // Add transaction record
            LoyaltyTransaction transaction = new LoyaltyTransaction();
            transaction.setAccountId(this.id);
            transaction.setTransactionType(LoyaltyTransactionType.EARNED);
            transaction.setPoints(points);
            transaction.setDescription(description);
            transaction.setTransactionDate(LocalDateTime.now());
            
            if (recentTransactions == null) {
                recentTransactions = new ArrayList<>();
            }
            recentTransactions.add(transaction);
        }
    }
    
    /**
     * Redeem points from the account
     */
    public boolean redeemPoints(int points, String description) {
        if (points > 0 && this.currentPoints >= points) {
            this.currentPoints -= points;
            this.lifetimePointsRedeemed += points;
            this.lastActivityDate = LocalDateTime.now();
            this.updatedAt = LocalDateTime.now();
            
            // Add transaction record
            LoyaltyTransaction transaction = new LoyaltyTransaction();
            transaction.setAccountId(this.id);
            transaction.setTransactionType(LoyaltyTransactionType.REDEEMED);
            transaction.setPoints(-points); // Negative for redemption
            transaction.setDescription(description);
            transaction.setTransactionDate(LocalDateTime.now());
            
            if (recentTransactions == null) {
                recentTransactions = new ArrayList<>();
            }
            recentTransactions.add(transaction);
            
            return true;
        }
        return false;
    }
    
    /**
     * Add purchase amount and update tier if necessary
     */
    public void addPurchase(BigDecimal amount) {
        if (amount != null && amount.compareTo(BigDecimal.ZERO) > 0) {
            this.lifetimeSpend = this.lifetimeSpend.add(amount);
            
            // Check for tier upgrade
            LoyaltyTier newTier = LoyaltyTier.getTierBySpend(this.lifetimeSpend);
            if (newTier != this.currentTier) {
                this.currentTier = newTier;
                this.tierAchievedDate = LocalDateTime.now();
            }
            
            this.lastActivityDate = LocalDateTime.now();
            this.updatedAt = LocalDateTime.now();
        }
    }
    
    /**
     * Calculate points earned for a purchase amount
     */
    public int calculatePointsForPurchase(BigDecimal purchaseAmount) {
        if (purchaseAmount == null || purchaseAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }
        
        BigDecimal points = purchaseAmount.multiply(currentTier.getPointsMultiplier());
        return points.intValue();
    }
    
    /**
     * Get amount needed to reach next tier
     */
    public BigDecimal getAmountToNextTier() {
        return currentTier.getAmountToNextTier(lifetimeSpend);
    }
    
    /**
     * Get next tier
     */
    public LoyaltyTier getNextTier() {
        return currentTier.getNextTier();
    }
    
    /**
     * Check if account has sufficient points for redemption
     */
    public boolean hasSufficientPoints(int requiredPoints) {
        return currentPoints >= requiredPoints;
    }
    
    /**
     * Get points value in dollars (assuming 100 points = $1)
     */
    public BigDecimal getPointsValue() {
        return new BigDecimal(currentPoints).divide(new BigDecimal("100"));
    }
    
    /**
     * Generate account number
     */
    private String generateAccountNumber() {
        return "LOY" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }
    
    /**
     * Get account summary
     */
    public String getAccountSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Account: ").append(accountNumber).append("\n");
        summary.append("Tier: ").append(currentTier.getDisplayName()).append("\n");
        summary.append("Current Points: ").append(currentPoints).append("\n");
        summary.append("Points Value: $").append(getPointsValue()).append("\n");
        summary.append("Lifetime Spend: $").append(lifetimeSpend).append("\n");
        
        if (getNextTier() != null) {
            summary.append("Next Tier: ").append(getNextTier().getDisplayName()).append("\n");
            summary.append("Amount to Next Tier: $").append(getAmountToNextTier()).append("\n");
        } else {
            summary.append("Status: Maximum tier achieved!\n");
        }
        
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return String.format("LoyaltyAccount{id=%d, accountNumber='%s', tier=%s, points=%d}", 
                id, accountNumber, currentTier, currentPoints);
    }
}
