package com.clothingstore.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.clothingstore.dao.InstallmentPaymentDAO;
import com.clothingstore.dao.PaymentHistoryDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.InstallmentPayment;
import com.clothingstore.model.PaymentHistory;
import com.clothingstore.model.RefundResult;
import com.clothingstore.model.Transaction;

/**
 * Service for handling smart refund logic specifically for installment
 * transactions. This service provides intelligent refund processing that
 * considers payment history, installment status, and proper balance
 * adjustments.
 */
public class InstallmentRefundService {

    private static InstallmentRefundService instance;

    private final TransactionDAO transactionDAO;
    private final InstallmentPaymentDAO installmentPaymentDAO;
    private final PaymentHistoryDAO paymentHistoryDAO;
    private final RefundService refundService;
    private final OutstandingBalanceRefundService outstandingBalanceService;

    private InstallmentRefundService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.installmentPaymentDAO = InstallmentPaymentDAO.getInstance();
        this.paymentHistoryDAO = PaymentHistoryDAO.getInstance();
        this.refundService = RefundService.getInstance();
        this.outstandingBalanceService = OutstandingBalanceRefundService.getInstance();
    }

    public static synchronized InstallmentRefundService getInstance() {
        if (instance == null) {
            instance = new InstallmentRefundService();
        }
        return instance;
    }

    /**
     * Smart refund processing for installment transactions
     */
    public InstallmentRefundResult processInstallmentRefund(Long transactionId, BigDecimal refundAmount,
            String refundReason, String cashierName) {
        try {
            // Get the transaction
            Optional<Transaction> transactionOpt = transactionDAO.findById(transactionId);
            if (!transactionOpt.isPresent()) {
                return new InstallmentRefundResult(false, "Transaction not found", null);
            }

            Transaction transaction = transactionOpt.get();

            // Validate this is an installment transaction
            if (!transaction.isInstallmentTransaction()) {
                return new InstallmentRefundResult(false, "This is not an installment transaction", null);
            }

            // Validate refund amount
            InstallmentRefundValidation validation = validateInstallmentRefund(transaction, refundAmount);
            if (!validation.isValid()) {
                return new InstallmentRefundResult(false, validation.getErrorMessage(), null);
            }

            // Determine refund strategy based on installment status
            InstallmentRefundStrategy strategy = determineRefundStrategy(transaction, refundAmount);

            // Execute the refund based on strategy
            return executeInstallmentRefund(transaction, refundAmount, refundReason, cashierName, strategy);

        } catch (Exception e) {
            return new InstallmentRefundResult(false, "Error processing installment refund: " + e.getMessage(), null);
        }
    }

    /**
     * Validate installment refund request
     */
    private InstallmentRefundValidation validateInstallmentRefund(Transaction transaction, BigDecimal refundAmount) {
        // Check if refund amount is valid
        if (refundAmount == null || refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return new InstallmentRefundValidation(false, "Refund amount must be greater than zero");
        }

        // Get total amount paid so far
        BigDecimal totalPaid = transaction.getAmountPaid() != null ? transaction.getAmountPaid() : BigDecimal.ZERO;

        // Check if refund amount exceeds amount paid
        if (refundAmount.compareTo(totalPaid) > 0) {
            return new InstallmentRefundValidation(false,
                    String.format("Refund amount ($%.2f) cannot exceed amount paid ($%.2f)",
                            refundAmount.doubleValue(), totalPaid.doubleValue()));
        }

        // Check transaction status
        String status = transaction.getStatus();
        if ("REFUNDED".equals(status) || "CANCELLED".equals(status)) {
            return new InstallmentRefundValidation(false, "Transaction has already been refunded or cancelled");
        }

        return new InstallmentRefundValidation(true, null);
    }

    /**
     * Determine the best refund strategy based on transaction state
     */
    private InstallmentRefundStrategy determineRefundStrategy(Transaction transaction, BigDecimal refundAmount) {
        BigDecimal totalPaid = transaction.getAmountPaid() != null ? transaction.getAmountPaid() : BigDecimal.ZERO;
        String installmentStatus = transaction.getInstallmentStatus();

        // Strategy 1: Full refund of all payments made
        if (refundAmount.compareTo(totalPaid) == 0) {
            return new InstallmentRefundStrategy(
                    InstallmentRefundStrategy.StrategyType.FULL_REFUND,
                    "Refunding all payments made - transaction will be cancelled",
                    true // Cancel transaction
            );
        }

        // Strategy 2: Partial refund - reverse recent payments
        if ("IN_PROGRESS".equals(installmentStatus) || "COMPLETED".equals(installmentStatus)) {
            return new InstallmentRefundStrategy(
                    InstallmentRefundStrategy.StrategyType.REVERSE_PAYMENTS,
                    "Reversing recent installment payments",
                    false // Keep transaction active with adjusted balance
            );
        }

        // Strategy 3: Pending installment refund
        if ("PENDING".equals(installmentStatus)) {
            return new InstallmentRefundStrategy(
                    InstallmentRefundStrategy.StrategyType.CANCEL_PENDING,
                    "Cancelling pending installment transaction",
                    true // Cancel transaction
            );
        }

        // Default strategy: Standard partial refund
        return new InstallmentRefundStrategy(
                InstallmentRefundStrategy.StrategyType.STANDARD_PARTIAL,
                "Processing standard partial refund",
                false
        );
    }

    /**
     * Execute the installment refund based on the determined strategy
     */
    private InstallmentRefundResult executeInstallmentRefund(Transaction transaction, BigDecimal refundAmount,
            String refundReason, String cashierName,
            InstallmentRefundStrategy strategy) {
        try {
            InstallmentRefundResult result = new InstallmentRefundResult();
            result.setStrategy(strategy);

            switch (strategy.getType()) {
                case FULL_REFUND:
                    return executeFullInstallmentRefund(transaction, refundAmount, refundReason, cashierName);

                case REVERSE_PAYMENTS:
                    return executePaymentReversalRefund(transaction, refundAmount, refundReason, cashierName);

                case CANCEL_PENDING:
                    return executePendingCancellation(transaction, refundAmount, refundReason, cashierName);

                case STANDARD_PARTIAL:
                default:
                    return executeStandardPartialRefund(transaction, refundAmount, refundReason, cashierName);
            }

        } catch (Exception e) {
            return new InstallmentRefundResult(false, "Failed to execute refund strategy: " + e.getMessage(), null);
        }
    }

    /**
     * Execute full installment refund - refund all payments and cancel
     * transaction
     */
    private InstallmentRefundResult executeFullInstallmentRefund(Transaction transaction, BigDecimal refundAmount,
            String refundReason, String cashierName) {
        try {
            // Use the standard refund service for full refund
            RefundResult standardResult = refundService.processFullRefund(transaction, refundReason, cashierName);

            if (standardResult.isSuccess()) {
                // Update installment-specific fields
                transaction.setInstallmentStatus("CANCELLED");
                transaction.setStatus("REFUNDED");
                transaction.setUpdatedAt(LocalDateTime.now());
                transactionDAO.save(transaction);

                // Create installment refund record
                createInstallmentRefundRecord(transaction, refundAmount, refundReason, cashierName, "FULL_REFUND");

                return new InstallmentRefundResult(true,
                        "Full installment refund processed successfully. All payments have been refunded and transaction cancelled.",
                        standardResult.getRefundTransaction());
            } else {
                return new InstallmentRefundResult(false, standardResult.getMessage(), null);
            }

        } catch (Exception e) {
            return new InstallmentRefundResult(false, "Error processing full installment refund: " + e.getMessage(), null);
        }
    }

    /**
     * Execute payment reversal refund - reverse specific installment payments
     */
    private InstallmentRefundResult executePaymentReversalRefund(Transaction transaction, BigDecimal refundAmount,
            String refundReason, String cashierName) {
        try {
            // Get installment payment history
            List<InstallmentPayment> payments = installmentPaymentDAO.findByTransactionId(transaction.getId());

            // Reverse payments starting from the most recent
            BigDecimal remainingRefund = refundAmount;
            int paymentsReversed = 0;

            for (int i = payments.size() - 1; i >= 0 && remainingRefund.compareTo(BigDecimal.ZERO) > 0; i--) {
                InstallmentPayment payment = payments.get(i);

                if ("COMPLETED".equals(payment.getStatus())) {
                    BigDecimal paymentAmount = payment.getPaymentAmount();

                    if (remainingRefund.compareTo(paymentAmount) >= 0) {
                        // Reverse entire payment
                        payment.setStatus("REFUNDED");
                        payment.setNotes((payment.getNotes() != null ? payment.getNotes() + "; " : "")
                                + "Refunded: " + refundReason);
                        installmentPaymentDAO.save(payment);

                        remainingRefund = remainingRefund.subtract(paymentAmount);
                        paymentsReversed++;
                    } else {
                        // Partial reversal of this payment
                        payment.setPaymentAmount(paymentAmount.subtract(remainingRefund));
                        payment.setNotes((payment.getNotes() != null ? payment.getNotes() + "; " : "")
                                + "Partially refunded: " + refundReason + " (Amount: $" + remainingRefund + ")");
                        installmentPaymentDAO.save(payment);

                        remainingRefund = BigDecimal.ZERO;
                    }
                }
            }

            // Update transaction amounts and status
            BigDecimal newAmountPaid = transaction.getAmountPaid().subtract(refundAmount);
            transaction.setAmountPaid(newAmountPaid);
            transaction.setCompletedInstallments(Math.max(0, transaction.getCompletedInstallments() - paymentsReversed));

            // Update installment status
            if (newAmountPaid.compareTo(BigDecimal.ZERO) == 0) {
                transaction.setInstallmentStatus("PENDING");
                transaction.setStatus("PENDING");
            } else if (newAmountPaid.compareTo(transaction.getTotalAmount()) < 0) {
                transaction.setInstallmentStatus("IN_PROGRESS");
                transaction.setStatus("PARTIAL_PAYMENT");
            }

            transaction.setUpdatedAt(LocalDateTime.now());
            transactionDAO.save(transaction);

            // Create refund record
            createInstallmentRefundRecord(transaction, refundAmount, refundReason, cashierName, "PAYMENT_REVERSAL");

            // Process outstanding balance update
            outstandingBalanceService.processPartialRefundBalanceUpdate(
                    transaction.getId(), refundAmount, "REFUND", cashierName, refundReason);

            return new InstallmentRefundResult(true,
                    String.format("Payment reversal refund processed successfully. %d payment(s) reversed, $%.2f refunded.",
                            paymentsReversed, refundAmount.doubleValue()), null);

        } catch (Exception e) {
            return new InstallmentRefundResult(false, "Error processing payment reversal refund: " + e.getMessage(), null);
        }
    }

    /**
     * Execute pending cancellation - cancel a pending installment transaction
     */
    private InstallmentRefundResult executePendingCancellation(Transaction transaction, BigDecimal refundAmount,
            String refundReason, String cashierName) {
        try {
            // For pending transactions, we just cancel and refund any deposits made
            transaction.setInstallmentStatus("CANCELLED");
            transaction.setStatus("CANCELLED");
            transaction.setNotes((transaction.getNotes() != null ? transaction.getNotes() + "; " : "")
                    + "Cancelled: " + refundReason);
            transaction.setUpdatedAt(LocalDateTime.now());
            transactionDAO.save(transaction);

            // If there was any amount paid (deposit), process refund
            if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                createInstallmentRefundRecord(transaction, refundAmount, refundReason, cashierName, "CANCELLATION");

                // Process outstanding balance update
                outstandingBalanceService.processPartialRefundBalanceUpdate(
                        transaction.getId(), refundAmount, "REFUND", cashierName, refundReason);
            }

            return new InstallmentRefundResult(true,
                    "Pending installment transaction cancelled successfully"
                    + (refundAmount.compareTo(BigDecimal.ZERO) > 0 ? " and deposit refunded." : "."), null);

        } catch (Exception e) {
            return new InstallmentRefundResult(false, "Error cancelling pending installment: " + e.getMessage(), null);
        }
    }

    /**
     * Execute standard partial refund for installment transactions
     */
    private InstallmentRefundResult executeStandardPartialRefund(Transaction transaction, BigDecimal refundAmount,
            String refundReason, String cashierName) {
        try {
            // Adjust the amount paid
            BigDecimal newAmountPaid = transaction.getAmountPaid().subtract(refundAmount);
            transaction.setAmountPaid(newAmountPaid);

            // Update installment status based on new amount
            if (newAmountPaid.compareTo(BigDecimal.ZERO) == 0) {
                transaction.setInstallmentStatus("PENDING");
                transaction.setStatus("PENDING");
            } else if (newAmountPaid.compareTo(transaction.getTotalAmount()) < 0) {
                transaction.setInstallmentStatus("IN_PROGRESS");
                transaction.setStatus("PARTIAL_PAYMENT");
            }

            transaction.setUpdatedAt(LocalDateTime.now());
            transactionDAO.save(transaction);

            // Create refund record
            createInstallmentRefundRecord(transaction, refundAmount, refundReason, cashierName, "PARTIAL_REFUND");

            // Process outstanding balance update
            outstandingBalanceService.processPartialRefundBalanceUpdate(
                    transaction.getId(), refundAmount, "REFUND", cashierName, refundReason);

            return new InstallmentRefundResult(true,
                    String.format("Partial refund processed successfully. $%.2f refunded from installment transaction.",
                            refundAmount.doubleValue()), null);

        } catch (Exception e) {
            return new InstallmentRefundResult(false, "Error processing standard partial refund: " + e.getMessage(), null);
        }
    }

    /**
     * Create an installment refund record for tracking
     */
    private void createInstallmentRefundRecord(Transaction transaction, BigDecimal refundAmount,
            String refundReason, String cashierName, String refundType) {
        try {
            // Create a payment history record for the refund
            PaymentHistory refundRecord = new PaymentHistory();
            refundRecord.setTransactionId(transaction.getId());
            refundRecord.setPaymentAmount(refundAmount.negate()); // Negative for refund
            refundRecord.setPaymentMethod("REFUND");
            refundRecord.setPaymentDate(LocalDateTime.now());
            refundRecord.setPaymentType(PaymentHistory.PaymentType.REFUND);
            refundRecord.setPaymentReference("REF-" + System.currentTimeMillis());
            refundRecord.setNotes("Installment " + refundType + ": " + refundReason);
            refundRecord.setCashierName(cashierName);

            paymentHistoryDAO.create(refundRecord);

        } catch (Exception e) {
            System.err.println("Warning: Failed to create installment refund record: " + e.getMessage());
        }
    }

    // Inner classes for refund processing
    public static class InstallmentRefundResult {

        private boolean success;
        private String message;
        private Transaction refundTransaction;
        private InstallmentRefundStrategy strategy;

        public InstallmentRefundResult() {
        }

        public InstallmentRefundResult(boolean success, String message, Transaction refundTransaction) {
            this.success = success;
            this.message = message;
            this.refundTransaction = refundTransaction;
        }

        // Getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Transaction getRefundTransaction() {
            return refundTransaction;
        }

        public void setRefundTransaction(Transaction refundTransaction) {
            this.refundTransaction = refundTransaction;
        }

        public InstallmentRefundStrategy getStrategy() {
            return strategy;
        }

        public void setStrategy(InstallmentRefundStrategy strategy) {
            this.strategy = strategy;
        }
    }

    public static class InstallmentRefundValidation {

        private boolean valid;
        private String errorMessage;

        public InstallmentRefundValidation(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    public static class InstallmentRefundStrategy {

        public enum StrategyType {
            FULL_REFUND,
            REVERSE_PAYMENTS,
            CANCEL_PENDING,
            STANDARD_PARTIAL
        }

        private StrategyType type;
        private String description;
        private boolean cancelTransaction;

        public InstallmentRefundStrategy(StrategyType type, String description, boolean cancelTransaction) {
            this.type = type;
            this.description = description;
            this.cancelTransaction = cancelTransaction;
        }

        // Getters
        public StrategyType getType() {
            return type;
        }

        public String getDescription() {
            return description;
        }

        public boolean shouldCancelTransaction() {
            return cancelTransaction;
        }
    }
}
