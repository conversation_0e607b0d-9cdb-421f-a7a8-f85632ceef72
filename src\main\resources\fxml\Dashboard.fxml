<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.DashboardController" spacing="20.0">
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
   <children>
      <!-- Dashboard Header -->
      <VBox spacing="10.0">
         <children>
            <Label text="📊 Business Dashboard" />
            <Label fx:id="lblRefreshInterval" text="Real-time updates every 30 seconds" />
         </children>
      </VBox>

      <!-- Key Performance Metrics Row -->
      <HBox spacing="20.0">
         <children>
            <VBox spacing="8.0">
               <children>
                  <Label fx:id="lblTodaySales" text="$0.00" />
                  <Label text="Today's Sales" />
               </children>
            </VBox>
            <VBox spacing="8.0">
               <children>
                  <Label fx:id="lblTotalCustomers" text="0" />
                  <Label text="Total Customers" />
               </children>
            </VBox>
            <VBox spacing="8.0">
               <children>
                  <Label fx:id="lblLowStockItems" text="0" />
                  <Label text="Low Stock Alerts" />
               </children>
            </VBox>
            <VBox spacing="8.0">
               <children>
                  <Label fx:id="lblMonthlyProfit" text="$0.00" />
                  <Label text="Monthly Profit" />
               </children>
            </VBox>
         </children>
      </HBox>

      <!-- Recent Activity Section -->
      <VBox spacing="15.0">
         <children>
            <Label text="📈 Recent Activity" />
            <TextArea fx:id="txtRecentActivity" prefRowCount="6" editable="false" text="Loading recent activity..." />
         </children>
      </VBox>

      <!-- Quick Actions -->
      <HBox spacing="15.0">
         <children>
            <Button fx:id="btnRefreshDashboard" text="🔄 Refresh Data" onAction="#handleRefresh" />
            <Button text="💰 New Sale" onAction="#handleNewSale" />
            <Button text="👥 Manage Customers" onAction="#handleManageCustomers" />
            <Button text="📦 Manage Products" onAction="#handleManageProducts" />
            <Button text="📊 View Reports" onAction="#handleViewReports" />
         </children>
      </HBox>
   </children>
</VBox>
