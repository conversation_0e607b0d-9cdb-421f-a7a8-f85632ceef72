<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<?import java.net.URL?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.DashboardController" styleClass="dashboard-container">
   <stylesheets>
      <URL value="@../css/dashboard.css" />
   </stylesheets>
   <children>
      <!-- Modern Dashboard Header -->
      <VBox spacing="20.0" styleClass="dashboard-header">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <VBox spacing="5.0">
                     <children>
                        <Label styleClass="dashboard-title" text="Analytics Dashboard">
                           <font>
                              <Font name="System Bold" size="28.0" />
                           </font>
                        </Label>
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <Label fx:id="lblLiveStatus" text="LIVE DATA" style="-fx-text-fill: #27ae60; -fx-font-weight: bold; -fx-font-size: 12px;" />
                              <Label fx:id="lblLastRefresh" text="Last updated: --:--" style="-fx-text-fill: #7f8c8d; -fx-font-size: 11px;" />
                              <Label fx:id="lblRefreshInterval" text="Auto-refresh: 5s" style="-fx-text-fill: #3498db; -fx-font-size: 11px; -fx-font-weight: bold;" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnRefreshDashboard" onAction="#handleRefresh" text="Refresh Data" styleClass="btn-primary" />
                  <Button fx:id="btnQuickSale" onAction="#handleQuickSale" text="New Sale" styleClass="btn-success" />
                  <Button fx:id="btnAddProduct" onAction="#handleAddProduct" text="Add Product" styleClass="btn-secondary" />
               </children>
            </HBox>

            <!-- Key Performance Metrics Row with Live Updates -->
            <HBox spacing="20.0" alignment="CENTER_LEFT">
               <children>
                  <VBox styleClass="metric-card live-metric" spacing="8.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="8.0">
                           <children>
                              <Label fx:id="lblTodaySales" text="$0.00" styleClass="metric-value" />
                              <Label fx:id="lblTodaySalesIndicator" text="•" style="-fx-text-fill: #27ae60; -fx-font-size: 8px;" />
                           </children>
                        </HBox>
                        <Label text="Today's Sales" styleClass="metric-label" />
                        <Label fx:id="lblTodaySalesChange" text="--" style="-fx-text-fill: #7f8c8d; -fx-font-size: 10px;" />
                     </children>
                  </VBox>
                  <VBox styleClass="metric-card live-metric" spacing="8.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="8.0">
                           <children>
                              <Label fx:id="lblTotalCustomers" text="0" styleClass="metric-value" />
                              <Label fx:id="lblCustomersIndicator" text="•" style="-fx-text-fill: #27ae60; -fx-font-size: 8px;" />
                           </children>
                        </HBox>
                        <Label text="Total Customers" styleClass="metric-label" />
                        <Label fx:id="lblCustomersChange" text="--" style="-fx-text-fill: #7f8c8d; -fx-font-size: 10px;" />
                     </children>
                  </VBox>
                  <VBox styleClass="metric-card live-metric" spacing="8.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="8.0">
                           <children>
                              <Label fx:id="lblLowStockItems" text="0" styleClass="metric-value" />
                              <Label fx:id="lblStockIndicator" text="•" style="-fx-text-fill: #27ae60; -fx-font-size: 8px;" />
                           </children>
                        </HBox>
                        <Label text="Low Stock Alerts" styleClass="metric-label" />
                        <Label fx:id="lblStockChange" text="--" style="-fx-text-fill: #7f8c8d; -fx-font-size: 10px;" />
                     </children>
                  </VBox>
                  <VBox styleClass="metric-card live-metric" spacing="8.0">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="8.0">
                           <children>
                              <Label fx:id="lblMonthlyProfit" text="$0.00" styleClass="metric-value" />
                              <Label fx:id="lblProfitIndicator" text="•" style="-fx-text-fill: #27ae60; -fx-font-size: 8px;" />
                           </children>
                        </HBox>
                        <Label text="Monthly Profit" styleClass="metric-label" />
                        <Label fx:id="lblProfitChange" text="--" style="-fx-text-fill: #7f8c8d; -fx-font-size: 10px;" />
                     </children>
                  </VBox>
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Key Metrics Row -->
      <HBox spacing="15.0" styleClass="form-container">
         <children>
            <!-- Inventory Metrics -->
            <VBox spacing="5.0" styleClass="metric-card" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="metric-title" text="Inventory Overview" />
                  <Label fx:id="lblTotalProducts" styleClass="metric-value" text="Total Products: 0" />
                  <Label fx:id="lblTotalValue" styleClass="metric-value" text="Total Value: $0.00" />
                  <Label fx:id="lblLowStockCount" styleClass="metric-warning" text="Low Stock: 0" />
                  <Label fx:id="lblOutOfStockCount" styleClass="metric-danger" text="Out of Stock: 0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>

            <!-- Sales Metrics -->
            <VBox spacing="5.0" styleClass="metric-card" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="metric-title" text="Sales Overview" />
                  <Label fx:id="lblTotalCustomersCount" styleClass="metric-value" text="Total Customers: 0" />
                  <Label fx:id="lblActiveCustomers" styleClass="metric-value" text="Active Customers: 0" />
                  <Label fx:id="lblTotalTransactions" styleClass="metric-value" text="Total Transactions: 0" />
                  <Label fx:id="lblAverageTransaction" styleClass="metric-value" text="Avg Transaction: $0.00" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>

            <!-- Product Analytics -->
            <VBox spacing="5.0" styleClass="metric-card" HBox.hgrow="ALWAYS">
               <children>
                  <Label styleClass="metric-title" text="Product Analytics" />
                  <Label fx:id="lblAveragePrice" styleClass="metric-value" text="Avg Price: $0.00" />
                  <Label fx:id="lblTopCategory" styleClass="metric-value" text="Top Category: -" />
                  <Label fx:id="lblTopBrand" styleClass="metric-value" text="Top Brand: -" />
                  <Label fx:id="lblCategoryCount" styleClass="metric-value" text="Categories: 0" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </HBox>

      <!-- Top Products Section -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="🏆 Top Products (by Stock Quantity)">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnViewAllProducts" onAction="#handleViewAllProducts" styleClass="button info" text="View All Products" />
               </children>
            </HBox>

            <TableView fx:id="tblTopProducts" prefHeight="200.0">
               <columns>
                  <TableColumn fx:id="colRank" prefWidth="50.0" text="Rank" />
                  <TableColumn fx:id="colProductName" prefWidth="200.0" text="Product Name" />
                  <TableColumn fx:id="colProductSku" prefWidth="100.0" text="SKU" />
                  <TableColumn fx:id="colProductCategory" prefWidth="120.0" text="Category" />
                  <TableColumn fx:id="colProductPrice" prefWidth="80.0" text="Price" />
                  <TableColumn fx:id="colProductStock" prefWidth="80.0" text="Stock" />
                  <TableColumn fx:id="colProductValue" prefWidth="100.0" text="Total Value" />
               </columns>
            </TableView>
         </children>
         <VBox.margin>
            <Insets bottom="15.0" />
         </VBox.margin>
      </VBox>

      <!-- Quick Actions -->
      <HBox spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="section-title" text="Quick Actions:">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            <Button fx:id="btnLowStockReport" onAction="#handleLowStockReport" styleClass="button warning" text="Low Stock Report" />
            <Button fx:id="btnOutOfStockReport" onAction="#handleOutOfStockReport" styleClass="button danger" text="Out of Stock" />
            <Button fx:id="btnAddProductQuick" onAction="#handleAddProduct" styleClass="button success" text="+ Add Product" />
            <Button fx:id="btnManageProducts" onAction="#handleManageProducts" styleClass="button primary" text="Manage Products" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnExportData" onAction="#handleExportData" styleClass="button secondary" text="Export Data" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" top="10.0" />
         </VBox.margin>
      </HBox>

      <!-- Recent Activity Section -->
      <VBox spacing="10.0" styleClass="form-container">
         <children>
            <Label styleClass="section-title" text="Recent Activity">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            <TextArea fx:id="txtRecentActivity" editable="false" prefHeight="100.0" promptText="Recent system activity will be displayed here..." wrapText="true" />
         </children>
      </VBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
