<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.DashboardController" spacing="20.0">
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
   <children>
      <!-- Dashboard Header -->
      <VBox spacing="10.0">
         <children>
            <Label text="📊 Business Dashboard" />
            <Label fx:id="lblRefreshInterval" text="Real-time updates every 30 seconds" />
         </children>
      </VBox>

      <!-- Key Performance Metrics Row -->
      <HBox spacing="20.0">
         <children>
            <VBox spacing="8.0">
               <children>
                  <Label text="$0.00" />
                  <Label text="Today's Sales" />
               </children>
            </VBox>
            <VBox spacing="8.0">
               <children>
                  <Label text="0" />
                  <Label text="Total Customers" />
               </children>
            </VBox>
            <VBox spacing="8.0">
               <children>
                  <Label text="0" />
                  <Label text="Low Stock Alerts" />
               </children>
            </VBox>
            <VBox spacing="8.0">
               <children>
                  <Label text="$0.00" />
                  <Label text="Monthly Profit" />
               </children>
            </VBox>
         </children>
      </HBox>

      <!-- Quick Actions -->
      <HBox spacing="15.0">
         <children>
            <Button text="🔄 Refresh Data" />
            <Button text="💰 New Sale" />
            <Button text="👥 Manage Customers" />
            <Button text="📦 Manage Products" />
            <Button text="📊 View Reports" />
         </children>
      </HBox>
   </children>
</VBox>
