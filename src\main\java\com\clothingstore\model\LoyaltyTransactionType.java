package com.clothingstore.model;

/**
 * Enumeration for loyalty transaction types
 */
public enum LoyaltyTransactionType {
    EARNED("Points Earned", "Points earned from purchases or activities"),
    REDEEMED("Points Redeemed", "Points redeemed for rewards or discounts"),
    EXPIRED("Points Expired", "Points that have expired"),
    ADJUSTED("Points Adjusted", "Manual adjustment to points balance"),
    BONUS("Bonus Points", "Bonus points from promotions or special events");
    
    private final String displayName;
    private final String description;
    
    LoyaltyTransactionType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
