package com.clothingstore.dao;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;

import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * Test to verify that TransactionDAO properly loads complete customer information
 * when retrieving transactions from the database
 */
public class TransactionCustomerLoadingTest {

    public static void main(String[] args) {
        TransactionCustomerLoadingTest test = new TransactionCustomerLoadingTest();
        test.runCustomerLoadingTest();
    }

    public void runCustomerLoadingTest() {
        System.out.println("=== Transaction Customer Loading Test ===\n");

        try {
            TransactionDAO transactionDAO = TransactionDAO.getInstance();

            // Test 1: Load all transactions and check customer data
            System.out.println("1. Testing findAll() with customer data loading...");
            List<Transaction> allTransactions = transactionDAO.findAll();
            System.out.println("   ✓ Loaded " + allTransactions.size() + " transactions");

            int transactionsWithCustomers = 0;
            int transactionsWithCompleteCustomerData = 0;

            for (Transaction transaction : allTransactions) {
                System.out.println("\n   Transaction: " + transaction.getTransactionNumber());
                System.out.println("   - Customer ID: " + transaction.getCustomerId());
                
                if (transaction.getCustomer() != null) {
                    transactionsWithCustomers++;
                    Customer customer = transaction.getCustomer();
                    
                    System.out.println("   - Customer Name: " + customer.getFullName());
                    System.out.println("   - Customer Email: " + customer.getEmail());
                    System.out.println("   - Customer Phone: " + customer.getPhone());
                    System.out.println("   - Customer Address: " + customer.getAddress());
                    System.out.println("   - Loyalty Points: " + customer.getLoyaltyPoints());
                    System.out.println("   - Total Spent: $" + customer.getTotalSpent());
                    System.out.println("   - Total Purchases: " + customer.getTotalPurchases());
                    
                    // Check if customer data is complete
                    if (customer.getFullName() != null && !customer.getFullName().trim().isEmpty()) {
                        transactionsWithCompleteCustomerData++;
                    }
                } else if (transaction.getCustomerId() != null) {
                    System.out.println("   ⚠ Customer ID exists but Customer object is null");
                } else {
                    System.out.println("   - Walk-in customer (no customer ID)");
                }
                
                // Show transaction items count
                System.out.println("   - Items: " + transaction.getItems().size());
                System.out.println("   - Total: $" + transaction.getTotalAmount());
            }

            System.out.println("\n   Summary:");
            System.out.println("   - Total transactions: " + allTransactions.size());
            System.out.println("   - Transactions with customer objects: " + transactionsWithCustomers);
            System.out.println("   - Transactions with complete customer data: " + transactionsWithCompleteCustomerData);

            // Test 2: Test findById with customer loading
            if (!allTransactions.isEmpty()) {
                System.out.println("\n2. Testing findById() with customer data loading...");
                Transaction firstTransaction = allTransactions.get(0);
                
                Optional<Transaction> reloadedTransaction = transactionDAO.findById(firstTransaction.getId());
                if (reloadedTransaction.isPresent()) {
                    Transaction transaction = reloadedTransaction.get();
                    System.out.println("   ✓ Reloaded transaction: " + transaction.getTransactionNumber());
                    
                    if (transaction.getCustomer() != null) {
                        Customer customer = transaction.getCustomer();
                        System.out.println("   ✓ Customer data loaded: " + customer.getFullName());
                        System.out.println("   - Email: " + customer.getEmail());
                        System.out.println("   - Phone: " + customer.getPhone());
                    } else if (transaction.getCustomerId() != null) {
                        System.out.println("   ⚠ Customer ID exists but Customer object is null");
                    } else {
                        System.out.println("   ✓ Walk-in customer (no customer data expected)");
                    }
                } else {
                    System.out.println("   ✗ Failed to reload transaction");
                }
            }

            // Test 3: Test customer data consistency
            System.out.println("\n3. Testing customer data consistency...");
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            List<Customer> allCustomers = customerDAO.findAll();
            System.out.println("   ✓ Found " + allCustomers.size() + " customers in database");

            for (Customer customer : allCustomers) {
                System.out.println("\n   Customer: " + customer.getFullName() + " (ID: " + customer.getId() + ")");
                
                // Find transactions for this customer
                List<Transaction> customerTransactions = transactionDAO.findByCustomerId(customer.getId());
                System.out.println("   - Transactions: " + customerTransactions.size());
                
                for (Transaction transaction : customerTransactions) {
                    if (transaction.getCustomer() != null) {
                        System.out.println("     ✓ " + transaction.getTransactionNumber() + " has customer data");
                    } else {
                        System.out.println("     ✗ " + transaction.getTransactionNumber() + " missing customer data");
                    }
                }
            }

            System.out.println("\n=== Customer Loading Test Completed Successfully! ===");

        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Test failed with error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
