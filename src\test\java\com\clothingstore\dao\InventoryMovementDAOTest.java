package com.clothingstore.dao;

import com.clothingstore.model.InventoryMovement;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Test class for InventoryMovementDAO to verify enhanced database queries
 * for tracking inventory movements (sold vs returned items)
 */
public class InventoryMovementDAOTest {

    public static void main(String[] args) {
        InventoryMovementDAOTest test = new InventoryMovementDAOTest();
        test.runInventoryMovementTests();
    }

    public void runInventoryMovementTests() {
        System.out.println("=== Inventory Movement DAO Test ===\n");

        try {
            InventoryMovementDAO movementDAO = InventoryMovementDAO.getInstance();
            
            // Test date range - last 30 days
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(30);
            
            System.out.println("Testing inventory movements from " + startDate.toLocalDate() + " to " + endDate.toLocalDate());
            System.out.println("=" + "=".repeat(80) + "\n");

            // Test 1: Get Items Sold/Processed
            System.out.println("1. Testing Items Sold/Processed Report...");
            List<InventoryMovement> soldItems = movementDAO.getItemsSoldProcessed(startDate, endDate);
            System.out.println("   ✓ Found " + soldItems.size() + " sold item movements");
            
            if (!soldItems.isEmpty()) {
                System.out.println("\n   Sample Sold Items:");
                System.out.println("   " + "-".repeat(100));
                System.out.printf("   %-15s %-20s %-10s %-8s %-12s %-15s %s\n", 
                    "Transaction", "Product", "SKU", "Qty", "Unit Price", "Line Total", "Customer");
                System.out.println("   " + "-".repeat(100));
                
                int count = 0;
                for (InventoryMovement item : soldItems) {
                    if (count >= 10) break; // Show first 10 items
                    System.out.printf("   %-15s %-20s %-10s %-8d $%-11.2f $%-14.2f %s\n",
                        item.getTransactionNumber(),
                        item.getProductName().length() > 20 ? item.getProductName().substring(0, 17) + "..." : item.getProductName(),
                        item.getProductSku(),
                        item.getQuantity(),
                        item.getUnitPrice().doubleValue(),
                        item.getLineTotal().doubleValue(),
                        item.getCustomerName());
                    count++;
                }
                if (soldItems.size() > 10) {
                    System.out.println("   ... and " + (soldItems.size() - 10) + " more items");
                }
            }

            // Test 2: Get Items Returned/Refunded
            System.out.println("\n2. Testing Items Returned/Refunded Report...");
            List<InventoryMovement> returnedItems = movementDAO.getItemsReturnedRefunded(startDate, endDate);
            System.out.println("   ✓ Found " + returnedItems.size() + " returned item movements");
            
            if (!returnedItems.isEmpty()) {
                System.out.println("\n   Sample Returned Items:");
                System.out.println("   " + "-".repeat(110));
                System.out.printf("   %-15s %-20s %-10s %-8s %-12s %-15s %-12s %s\n", 
                    "Transaction", "Product", "SKU", "Qty", "Unit Price", "Line Total", "Type", "Reason");
                System.out.println("   " + "-".repeat(110));
                
                for (InventoryMovement item : returnedItems) {
                    System.out.printf("   %-15s %-20s %-10s %-8d $%-11.2f $%-14.2f %-12s %s\n",
                        item.getTransactionNumber(),
                        item.getProductName().length() > 20 ? item.getProductName().substring(0, 17) + "..." : item.getProductName(),
                        item.getProductSku(),
                        item.getQuantity(),
                        item.getUnitPrice().doubleValue(),
                        item.getLineTotal().doubleValue(),
                        item.getMovementType(),
                        item.getReason() != null ? item.getReason() : "N/A");
                }
            }

            // Test 3: Get Movement Summary
            System.out.println("\n3. Testing Inventory Movement Summary...");
            InventoryMovementDAO.InventoryMovementSummary summary = movementDAO.getMovementSummary(startDate, endDate);
            System.out.println("   ✓ Generated movement summary");
            
            System.out.println("\n   INVENTORY MOVEMENT SUMMARY");
            System.out.println("   " + "=".repeat(50));
            System.out.println("   Items Sold/Processed:");
            System.out.println("     - Item Count: " + summary.getSoldItemCount());
            System.out.println("     - Total Quantity: " + summary.getSoldQuantity());
            System.out.println("     - Total Value: $" + String.format("%.2f", summary.getSoldValue().doubleValue()));
            
            System.out.println("\n   Items Returned/Refunded:");
            System.out.println("     - Item Count: " + summary.getReturnedItemCount());
            System.out.println("     - Total Quantity: " + summary.getReturnedQuantity());
            System.out.println("     - Total Value: $" + String.format("%.2f", summary.getReturnedValue().doubleValue()));
            
            System.out.println("\n   Net Movement:");
            System.out.println("     - Net Quantity: " + summary.getNetQuantity());
            System.out.println("     - Net Value: $" + String.format("%.2f", summary.getNetValue().doubleValue()));
            System.out.println("     - Return Rate: " + String.format("%.2f%%", summary.getReturnRate()));

            // Test 4: Get Top Products by Movement
            System.out.println("\n4. Testing Top Products by Movement...");
            
            // Top sold products
            List<InventoryMovementDAO.ProductMovementSummary> topSold = 
                movementDAO.getTopProductsByMovement(startDate, endDate, "SOLD", 5);
            System.out.println("   ✓ Found " + topSold.size() + " top sold products");
            
            if (!topSold.isEmpty()) {
                System.out.println("\n   TOP 5 SOLD PRODUCTS:");
                System.out.println("   " + "-".repeat(80));
                System.out.printf("   %-25s %-12s %-8s %-12s %s\n", 
                    "Product", "SKU", "Qty", "Value", "Transactions");
                System.out.println("   " + "-".repeat(80));
                
                for (InventoryMovementDAO.ProductMovementSummary product : topSold) {
                    System.out.printf("   %-25s %-12s %-8d $%-11.2f %d\n",
                        product.getProductName().length() > 25 ? product.getProductName().substring(0, 22) + "..." : product.getProductName(),
                        product.getProductSku(),
                        product.getTotalQuantity(),
                        product.getTotalValue().doubleValue(),
                        product.getTransactionCount());
                }
            }
            
            // Top returned products
            List<InventoryMovementDAO.ProductMovementSummary> topReturned = 
                movementDAO.getTopProductsByMovement(startDate, endDate, "RETURNED", 5);
            System.out.println("\n   ✓ Found " + topReturned.size() + " top returned products");
            
            if (!topReturned.isEmpty()) {
                System.out.println("\n   TOP RETURNED PRODUCTS:");
                System.out.println("   " + "-".repeat(80));
                System.out.printf("   %-25s %-12s %-8s %-12s %s\n", 
                    "Product", "SKU", "Qty", "Value", "Transactions");
                System.out.println("   " + "-".repeat(80));
                
                for (InventoryMovementDAO.ProductMovementSummary product : topReturned) {
                    System.out.printf("   %-25s %-12s %-8d $%-11.2f %d\n",
                        product.getProductName().length() > 25 ? product.getProductName().substring(0, 22) + "..." : product.getProductName(),
                        product.getProductSku(),
                        product.getTotalQuantity(),
                        product.getTotalValue().doubleValue(),
                        product.getTransactionCount());
                }
            }

            // Test 5: Validate Data Accuracy
            System.out.println("\n5. Testing Data Accuracy and Validation...");
            
            // Check that sold items are only from completed transactions
            boolean soldDataValid = soldItems.stream().allMatch(item -> "SOLD".equals(item.getMovementType()));
            System.out.println("   ✓ Sold items data validation: " + (soldDataValid ? "PASSED" : "FAILED"));
            
            // Check that returned items are only from refunded/cancelled transactions
            boolean returnedDataValid = returnedItems.stream().allMatch(item -> 
                item.getMovementType().equals("REFUNDED") || item.getMovementType().equals("CANCELLED"));
            System.out.println("   ✓ Returned items data validation: " + (returnedDataValid ? "PASSED" : "FAILED"));
            
            // Check that all items have required fields
            boolean requiredFieldsValid = soldItems.stream().allMatch(item -> 
                item.getTransactionNumber() != null && 
                item.getProductName() != null && 
                item.getQuantity() != null && 
                item.getQuantity() > 0);
            System.out.println("   ✓ Required fields validation: " + (requiredFieldsValid ? "PASSED" : "FAILED"));

            System.out.println("\n=== Inventory Movement DAO Test Completed Successfully! ===");
            
            // Summary
            System.out.println("\nTEST SUMMARY:");
            System.out.println("- Items Sold/Processed: " + soldItems.size() + " movements");
            System.out.println("- Items Returned/Refunded: " + returnedItems.size() + " movements");
            System.out.println("- Net Inventory Movement: " + summary.getNetQuantity() + " units");
            System.out.println("- Return Rate: " + String.format("%.2f%%", summary.getReturnRate()));

        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Test failed with error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
