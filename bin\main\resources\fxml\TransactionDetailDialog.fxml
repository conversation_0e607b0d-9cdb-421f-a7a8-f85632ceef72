<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<ScrollPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="com.clothingstore.view.TransactionDetailDialogController"
            fitToWidth="true" fitToHeight="true" prefWidth="800" prefHeight="700">
    <content>
        <VBox spacing="15" padding="20">
            <children>
                <!-- Transaction Information Section -->
                <VBox spacing="10" style="-fx-border-color: #cccccc; -fx-border-width: 1; -fx-border-radius: 5; -fx-padding: 15;">
                    <children>
                        <Label text="Transaction Information" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
                        <GridPane hgap="10" vgap="5">
                            <columnConstraints>
                                <ColumnConstraints minWidth="120" prefWidth="120" />
                                <ColumnConstraints minWidth="200" prefWidth="200" />
                                <ColumnConstraints minWidth="120" prefWidth="120" />
                                <ColumnConstraints minWidth="200" prefWidth="200" />
                            </columnConstraints>
                            <children>
                                <Label text="Transaction #:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                <Label fx:id="lblTransactionNumber" text="TXN-001" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                                <Label text="Date:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                                <Label fx:id="lblTransactionDate" text="01/01/2024" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                                <Label text="Status:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                <Label fx:id="lblStatus" text="COMPLETED" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                <Label text="Payment Method:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                <Label fx:id="lblPaymentMethod" text="CASH" GridPane.columnIndex="3" GridPane.rowIndex="1" />

                                <Label text="Subtotal:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                <Label fx:id="lblSubtotal" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                                <Label text="Discount:" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                                <Label fx:id="lblDiscount" text="$0.00" GridPane.columnIndex="3" GridPane.rowIndex="2" />

                                <Label text="Total:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                                <Label fx:id="lblTotal" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                                <Label text="Cashier:" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                                <Label fx:id="lblCashierName" text="System" GridPane.columnIndex="3" GridPane.rowIndex="3" />
                            </children>
                        </GridPane>
                        <Label text="Notes:" />
                        <TextArea fx:id="txtNotes" prefRowCount="2" editable="false" />
                    </children>
                </VBox>

                <!-- Customer Information Section -->
                <VBox spacing="10" style="-fx-border-color: #cccccc; -fx-border-width: 1; -fx-border-radius: 5; -fx-padding: 15;">
                    <children>
                        <Label text="Customer Information" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
                        <GridPane hgap="10" vgap="5">
                            <columnConstraints>
                                <ColumnConstraints minWidth="120" prefWidth="120" />
                                <ColumnConstraints minWidth="200" prefWidth="200" />
                                <ColumnConstraints minWidth="120" prefWidth="120" />
                                <ColumnConstraints minWidth="200" prefWidth="200" />
                            </columnConstraints>
                            <children>
                                <Label text="Name:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                <Label fx:id="lblCustomerName" text="Walk-in Customer" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                                <Label text="Phone:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                                <Label fx:id="lblCustomerPhone" text="N/A" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                                <Label text="Email:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                <Label fx:id="lblCustomerEmail" text="N/A" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                <Label text="Type:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                <Label fx:id="lblCustomerType" text="Walk-in" GridPane.columnIndex="3" GridPane.rowIndex="1" />

                                <Label text="Address:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                <Label fx:id="lblCustomerAddress" text="N/A" GridPane.columnIndex="1" GridPane.rowIndex="2" GridPane.columnSpan="3" />

                                <Label text="Loyalty Points:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                                <Label fx:id="lblLoyaltyPoints" text="0" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                                <Label text="Total Spent:" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                                <Label fx:id="lblTotalSpent" text="$0.00" GridPane.columnIndex="3" GridPane.rowIndex="3" />

                                <Label text="Total Purchases:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                                <Label fx:id="lblTotalPurchases" text="0" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                            </children>
                        </GridPane>
                    </children>
                </VBox>

                <!-- Transaction Items Section -->
                <VBox spacing="10" style="-fx-border-color: #cccccc; -fx-border-width: 1; -fx-border-radius: 5; -fx-padding: 15;">
                    <children>
                        <Label text="Transaction Items" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
                        <TableView fx:id="tblTransactionItems" prefHeight="200">
                            <columns>
                                <TableColumn fx:id="colProductName" text="Product" prefWidth="200" />
                                <TableColumn fx:id="colSku" text="SKU" prefWidth="120" />
                                <TableColumn fx:id="colQuantity" text="Qty" prefWidth="60" />
                                <TableColumn fx:id="colUnitPrice" text="Unit Price" prefWidth="100" />
                                <TableColumn fx:id="colLineTotal" text="Line Total" prefWidth="100" />
                            </columns>
                        </TableView>
                    </children>
                </VBox>

                <!-- Payment History Section -->
                <VBox spacing="10" style="-fx-border-color: #cccccc; -fx-border-width: 1; -fx-border-radius: 5; -fx-padding: 15;">
                    <children>
                        <Label text="Payment History and Audit Trail" style="-fx-font-size: 16px; -fx-font-weight: bold;" />
                        <TableView fx:id="tblPaymentHistory" prefHeight="250">
                            <columns>
                                <TableColumn fx:id="colPaymentDate" text="Date/Time" prefWidth="130" />
                                <TableColumn fx:id="colPaymentAmount" text="Amount" prefWidth="100" />
                                <TableColumn fx:id="colPaymentMethod" text="Method" prefWidth="120" />
                                <TableColumn fx:id="colPaymentType" text="Type" prefWidth="80" />
                                <TableColumn fx:id="colRunningBalance" text="Running Balance" prefWidth="120" />
                                <TableColumn fx:id="colRemainingBalance" text="Remaining Balance" prefWidth="130" />
                            </columns>
                        </TableView>
                    </children>
                </VBox>
            </children>
        </VBox>
    </content>
</ScrollPane>
