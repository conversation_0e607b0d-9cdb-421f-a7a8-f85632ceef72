package com.clothingstore.view;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Product;
import com.clothingstore.util.AlertUtil;

/**
 * Controller for Inventory Report interface
 */
public class InventoryReportController implements Initializable {

    @FXML private ComboBox<String> cmbReportType;
    @FXML private Button btnRefresh;
    @FXML private Button btnExport;
    @FXML private TextField txtSearch;
    @FXML private ComboBox<String> cmbCategoryFilter;

    @FXML private Label lblTotalProducts;
    @FXML private Label lblTotalValue;
    @FXML private Label lblTotalCategories;
    @FXML private Label lblLowStockItems;

    @FXML private TableView<Product> tblInventory;
    @FXML private TableColumn<Product, String> colSku;
    @FXML private TableColumn<Product, String> colName;
    @FXML private TableColumn<Product, String> colCategory;
    @FXML private TableColumn<Product, String> colBrand;
    @FXML private TableColumn<Product, Integer> colStock;
    @FXML private TableColumn<Product, Integer> colMinStock;
    @FXML private TableColumn<Product, String> colPrice;
    @FXML private TableColumn<Product, String> colTotalValue;
    @FXML private TableColumn<Product, String> colStatus;

    @FXML private TableView<CategoryAnalysisItem> tblCategoryAnalysis;
    @FXML private TableColumn<CategoryAnalysisItem, String> colCategoryName;
    @FXML private TableColumn<CategoryAnalysisItem, String> colCategoryCount;
    @FXML private TableColumn<CategoryAnalysisItem, String> colCategoryValue;
    @FXML private TableColumn<CategoryAnalysisItem, String> colCategoryAvgPrice;

    private ObservableList<Product> inventoryData;
    private ObservableList<CategoryAnalysisItem> categoryData;
    private ProductDAO productDAO;
    private NumberFormat currencyFormat;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        productDAO = ProductDAO.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        inventoryData = FXCollections.observableArrayList();
        categoryData = FXCollections.observableArrayList();
        
        setupTables();
        setupFilters();
        loadInventoryData();
    }

    private void setupTables() {
        // Inventory table
        colSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colBrand.setCellValueFactory(new PropertyValueFactory<>("brand"));
        colStock.setCellValueFactory(new PropertyValueFactory<>("stockQuantity"));
        colMinStock.setCellValueFactory(new PropertyValueFactory<>("minStockLevel"));
        
        colPrice.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                currencyFormat.format(cellData.getValue().getPrice())));
        
        colTotalValue.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            BigDecimal totalValue = product.getPrice().multiply(BigDecimal.valueOf(product.getStockQuantity()));
            return new javafx.beans.property.SimpleStringProperty(currencyFormat.format(totalValue));
        });
        
        colStatus.setCellValueFactory(cellData -> {
            Product product = cellData.getValue();
            String status = product.isLowStock() ? "LOW STOCK" : "OK";
            return new javafx.beans.property.SimpleStringProperty(status);
        });
        
        tblInventory.setItems(inventoryData);

        // Category analysis table
        colCategoryName.setCellValueFactory(new PropertyValueFactory<>("categoryName"));
        colCategoryCount.setCellValueFactory(new PropertyValueFactory<>("productCount"));
        colCategoryValue.setCellValueFactory(new PropertyValueFactory<>("totalValue"));
        colCategoryAvgPrice.setCellValueFactory(new PropertyValueFactory<>("avgPrice"));
        
        tblCategoryAnalysis.setItems(categoryData);
    }

    private void setupFilters() {
        cmbReportType.setItems(FXCollections.observableArrayList(
            "All Products", "Low Stock Only", "Out of Stock", "High Value Items"));
        cmbReportType.setValue("All Products");
        
        try {
            List<String> categories = productDAO.getAllCategories();
            ObservableList<String> categoryItems = FXCollections.observableArrayList("All Categories");
            categoryItems.addAll(categories);
            cmbCategoryFilter.setItems(categoryItems);
            cmbCategoryFilter.setValue("All Categories");
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load categories: " + e.getMessage());
        }
    }

    private void loadInventoryData() {
        try {
            List<Product> allProducts = productDAO.findAll();
            inventoryData.setAll(allProducts);
            
            // Update summary metrics
            int totalProducts = allProducts.size();
            BigDecimal totalValue = productDAO.getTotalInventoryValue();
            long totalCategories = allProducts.stream()
                .map(Product::getCategory)
                .distinct()
                .count();
            long lowStockCount = allProducts.stream()
                .mapToLong(p -> p.isLowStock() ? 1 : 0)
                .sum();
            
            lblTotalProducts.setText(String.valueOf(totalProducts));
            lblTotalValue.setText(currencyFormat.format(totalValue));
            lblTotalCategories.setText(String.valueOf(totalCategories));
            lblLowStockItems.setText(String.valueOf(lowStockCount));
            
            // Load category analysis
            loadCategoryAnalysis(allProducts);
            
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load inventory data: " + e.getMessage());
        }
    }

    private void loadCategoryAnalysis(List<Product> products) {
        Map<String, List<Product>> categoryGroups = products.stream()
            .collect(Collectors.groupingBy(Product::getCategory));
        
        categoryData.clear();
        for (Map.Entry<String, List<Product>> entry : categoryGroups.entrySet()) {
            String categoryName = entry.getKey();
            List<Product> categoryProducts = entry.getValue();
            
            int productCount = categoryProducts.size();
            BigDecimal totalValue = categoryProducts.stream()
                .map(p -> p.getPrice().multiply(BigDecimal.valueOf(p.getStockQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal avgPrice = categoryProducts.stream()
                .map(Product::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(categoryProducts.size()), 2, BigDecimal.ROUND_HALF_UP);
            
            CategoryAnalysisItem item = new CategoryAnalysisItem();
            item.setCategoryName(categoryName);
            item.setProductCount(String.valueOf(productCount));
            item.setTotalValue(currencyFormat.format(totalValue));
            item.setAvgPrice(currencyFormat.format(avgPrice));
            
            categoryData.add(item);
        }
    }

    @FXML
    private void handleReportTypeChange() {
        // Filter based on report type
        String reportType = cmbReportType.getValue();
        if (reportType != null) {
            try {
                List<Product> allProducts = productDAO.findAll();
                List<Product> filteredProducts;
                
                switch (reportType) {
                    case "Low Stock Only":
                        filteredProducts = productDAO.findLowStockProducts();
                        break;
                    case "Out of Stock":
                        filteredProducts = productDAO.findOutOfStockProducts();
                        break;
                    case "High Value Items":
                        filteredProducts = allProducts.stream()
                            .filter(p -> p.getPrice().compareTo(new BigDecimal("100")) > 0)
                            .collect(Collectors.toList());
                        break;
                    default:
                        filteredProducts = allProducts;
                        break;
                }
                
                inventoryData.setAll(filteredProducts);
                
            } catch (SQLException e) {
                AlertUtil.showError("Database Error", "Failed to filter products: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleRefresh() {
        loadInventoryData();
        AlertUtil.showInfo("Refreshed", "Inventory report has been refreshed.");
    }

    @FXML
    private void handleExport() {
        AlertUtil.showInfo("Export", "Inventory report export functionality will be implemented in future version.");
    }

    @FXML
    private void handleSearch() {
        // Implement search functionality
        String searchTerm = txtSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            loadInventoryData();
            return;
        }
        
        try {
            List<Product> allProducts = productDAO.findAll();
            List<Product> filteredProducts = allProducts.stream()
                .filter(p -> p.getName().toLowerCase().contains(searchTerm.toLowerCase()) ||
                           p.getSku().toLowerCase().contains(searchTerm.toLowerCase()) ||
                           p.getCategory().toLowerCase().contains(searchTerm.toLowerCase()))
                .collect(Collectors.toList());
            
            inventoryData.setAll(filteredProducts);
            
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to search products: " + e.getMessage());
        }
    }

    @FXML
    private void handleCategoryFilter() {
        String selectedCategory = cmbCategoryFilter.getValue();
        if (selectedCategory == null || "All Categories".equals(selectedCategory)) {
            loadInventoryData();
            return;
        }
        
        try {
            List<Product> allProducts = productDAO.findAll();
            List<Product> filteredProducts = allProducts.stream()
                .filter(p -> selectedCategory.equals(p.getCategory()))
                .collect(Collectors.toList());
            
            inventoryData.setAll(filteredProducts);
            
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to filter by category: " + e.getMessage());
        }
    }

    // Inner class for category analysis
    public static class CategoryAnalysisItem {
        private String categoryName;
        private String productCount;
        private String totalValue;
        private String avgPrice;

        // Getters and setters
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }
        
        public String getProductCount() { return productCount; }
        public void setProductCount(String productCount) { this.productCount = productCount; }
        
        public String getTotalValue() { return totalValue; }
        public void setTotalValue(String totalValue) { this.totalValue = totalValue; }
        
        public String getAvgPrice() { return avgPrice; }
        public void setAvgPrice(String avgPrice) { this.avgPrice = avgPrice; }
    }
}
