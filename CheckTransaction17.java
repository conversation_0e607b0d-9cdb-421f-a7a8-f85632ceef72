import com.clothingstore.database.DatabaseManager;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class CheckTransaction17 {
    public static void main(String[] args) {
        try {
            System.out.println("=== CHECKING TRANSACTION 17 ===");
            
            try (Connection conn = DatabaseManager.getInstance().getConnection()) {
                
                // 1. Check if transaction 17 exists
                String txnSql = "SELECT id, transaction_number, total_amount, status FROM transactions WHERE id = 17";
                try (PreparedStatement stmt = conn.prepareStatement(txnSql); ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        System.out.println("Transaction 17 found:");
                        System.out.printf("  ID: %d, Number: %s, Amount: %.2f, Status: %s%n",
                            rs.getLong("id"),
                            rs.getString("transaction_number"),
                            rs.getDouble("total_amount"),
                            rs.getString("status")
                        );
                    } else {
                        System.out.println("Transaction 17 NOT FOUND!");
                        return;
                    }
                }
                
                // 2. Check current items for transaction 17
                String itemSql = "SELECT id, product_id, quantity FROM transaction_items WHERE transaction_id = 17";
                try (PreparedStatement stmt = conn.prepareStatement(itemSql); ResultSet rs = stmt.executeQuery()) {
                    System.out.println("\nCurrent items for transaction 17:");
                    System.out.println("Item_ID | Product_ID | Quantity");
                    System.out.println("---------------------------");
                    
                    boolean hasItems = false;
                    while (rs.next()) {
                        hasItems = true;
                        System.out.printf("%d | %d | %d%n",
                            rs.getLong("id"),
                            rs.getLong("product_id"),
                            rs.getInt("quantity")
                        );
                    }
                    
                    if (!hasItems) {
                        System.out.println("No items found for transaction 17!");
                    }
                }
                
                // 3. Check what refund tracking records point to transaction 17
                String refundSql = "SELECT id, original_transaction_item_id, refund_transaction_id, product_name, refunded_quantity " +
                                  "FROM refund_item_tracking WHERE original_transaction_id = 17";
                try (PreparedStatement stmt = conn.prepareStatement(refundSql); ResultSet rs = stmt.executeQuery()) {
                    System.out.println("\nRefund tracking records for transaction 17:");
                    System.out.println("Refund_ID | Orig_Item_ID | Refund_TXN | Product | Qty");
                    System.out.println("--------------------------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%d | %d | %d | %s | %d%n",
                            rs.getLong("id"),
                            rs.getLong("original_transaction_item_id"),
                            rs.getLong("refund_transaction_id"),
                            rs.getString("product_name"),
                            rs.getInt("refunded_quantity")
                        );
                    }
                }
                
                // 4. Check if the original item IDs (22, 168) exist anywhere
                System.out.println("\n=== CHECKING ORIGINAL ITEM IDs ===");
                String checkItemSql = "SELECT id, transaction_id, product_id, quantity FROM transaction_items WHERE id IN (22, 168)";
                try (PreparedStatement stmt = conn.prepareStatement(checkItemSql); ResultSet rs = stmt.executeQuery()) {
                    System.out.println("Items 22 and 168 in transaction_items table:");
                    System.out.println("Item_ID | TXN_ID | Product_ID | Quantity");
                    System.out.println("--------------------------------------");
                    
                    boolean found = false;
                    while (rs.next()) {
                        found = true;
                        System.out.printf("%d | %d | %d | %d%n",
                            rs.getLong("id"),
                            rs.getLong("transaction_id"),
                            rs.getLong("product_id"),
                            rs.getInt("quantity")
                        );
                    }
                    
                    if (!found) {
                        System.out.println("Items 22 and 168 NOT FOUND in transaction_items table!");
                    }
                }
                
            }
            
        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
