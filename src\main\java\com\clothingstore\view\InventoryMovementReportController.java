package com.clothingstore.view;

import com.clothingstore.dao.InventoryMovementDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.CategoryDAO;
import com.clothingstore.model.InventoryMovement;
import com.clothingstore.model.Product;
import com.clothingstore.util.AlertUtil;

import javafx.animation.Timeline;
import javafx.animation.KeyFrame;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.collections.transformation.SortedList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.FileChooser;
import javafx.util.Duration;

import java.io.File;
import java.io.PrintWriter;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;

/**
 * Controller for Enhanced Inventory Movement Report interface Provides separate
 * sections for Items Sold/Processed and Items Returned/Refunded
 */
public class InventoryMovementReportController implements Initializable {

    // Enhanced Header Controls
    @FXML
    private Label lblReportSubtitle;
    @FXML
    private Label lblRecordCount;
    @FXML
    private MenuButton mbExport;
    @FXML
    private MenuItem miExportCSV;
    @FXML
    private MenuItem miExportPDF;
    @FXML
    private MenuItem miExportExcel;
    @FXML
    private MenuItem miPrintReport;

    // Date Range Controls
    @FXML
    private DatePicker dpStartDate;
    @FXML
    private DatePicker dpEndDate;
    @FXML
    private Button btnGenerateReport;
    @FXML
    private Button btnExport;
    @FXML
    private Button btnRefresh;

    // Quick Filter Buttons
    @FXML
    private Button btnToday;
    @FXML
    private Button btnThisWeek;
    @FXML
    private Button btnThisMonth;
    @FXML
    private Button btnLast30Days;

    // Advanced Filter Controls
    @FXML
    private ComboBox<String> cbCategory;
    @FXML
    private ComboBox<String> cbBrand;
    @FXML
    private ComboBox<String> cbMovementType;
    @FXML
    private TextField txtProductSearch;
    @FXML
    private TextField txtMinValue;
    @FXML
    private TextField txtMaxValue;
    @FXML
    private Button btnClearFilters;
    @FXML
    private Button btnAutoRefresh;
    @FXML
    private Label lblLastUpdated;

    // Summary Statistics
    @FXML
    private Label lblSoldItemCount;
    @FXML
    private Label lblSoldQuantity;
    @FXML
    private Label lblSoldValue;
    @FXML
    private Label lblReturnedItemCount;
    @FXML
    private Label lblReturnedQuantity;
    @FXML
    private Label lblReturnedValue;
    @FXML
    private Label lblNetQuantity;
    @FXML
    private Label lblNetValue;
    @FXML
    private Label lblReturnRate;
    @FXML
    private Label lblReturnTrend;
    @FXML
    private Label lblSoldItemsCount;
    @FXML
    private Label lblReturnedItemsCount;

    // Items Sold/Processed Section
    @FXML
    private TableView<InventoryMovement> tblSoldItems;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldDate;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldTransaction;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldProduct;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldSku;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldCategory;
    @FXML
    private TableColumn<InventoryMovement, Integer> colSoldQuantity;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldUnitPrice;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldLineTotal;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldCustomer;

    // Items Returned/Refunded Section
    @FXML
    private TableView<InventoryMovement> tblReturnedItems;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedDate;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedTransaction;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedProduct;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedSku;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedCategory;
    @FXML
    private TableColumn<InventoryMovement, Integer> colReturnedQuantity;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedUnitPrice;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedLineTotal;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedType;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedReason;

    // New Enhanced Table Columns
    @FXML
    private TableColumn<InventoryMovement, String> colSoldTime;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldBrand;
    @FXML
    private TableColumn<InventoryMovement, String> colSoldPaymentMethod;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedTime;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedBrand;
    @FXML
    private TableColumn<InventoryMovement, String> colReturnedStatus;

    // Analytics Tables
    @FXML
    private TableView<?> tblTopProducts;
    @FXML
    private TableView<?> tblCategoryAnalysis;
    @FXML
    private TableView<?> tblReturnReasons;
    @FXML
    private TableColumn<?, String> colTopProductName;
    @FXML
    private TableColumn<?, Integer> colTopProductSold;
    @FXML
    private TableColumn<?, String> colTopProductRevenue;
    @FXML
    private TableColumn<?, Integer> colTopProductReturns;
    @FXML
    private TableColumn<?, String> colTopProductReturnRate;
    @FXML
    private TableColumn<?, String> colCategoryName;
    @FXML
    private TableColumn<?, Integer> colCategorySold;
    @FXML
    private TableColumn<?, String> colCategoryRevenue;
    @FXML
    private TableColumn<?, Integer> colCategoryReturns;
    @FXML
    private TableColumn<?, String> colCategoryReturnRate;
    @FXML
    private TableColumn<?, String> colCategoryProfit;
    @FXML
    private TableColumn<?, String> colReturnReason;
    @FXML
    private TableColumn<?, Integer> colReturnReasonCount;
    @FXML
    private TableColumn<?, String> colReturnReasonPercentage;
    @FXML
    private TableColumn<?, String> colReturnReasonValue;
    @FXML
    private Button btnRefreshAnalytics;

    // Data and Services
    private ObservableList<InventoryMovement> soldItemsData;
    private ObservableList<InventoryMovement> returnedItemsData;
    private FilteredList<InventoryMovement> filteredSoldItems;
    private FilteredList<InventoryMovement> filteredReturnedItems;
    private InventoryMovementDAO movementDAO;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateFormatter;
    private boolean autoRefreshEnabled = false;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            movementDAO = InventoryMovementDAO.getInstance();
            currencyFormat = NumberFormat.getCurrencyInstance();
            dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

            soldItemsData = FXCollections.observableArrayList();
            returnedItemsData = FXCollections.observableArrayList();

            setupTableColumns();
            setupFilters();
            setDefaultDates();
            initializeUI();

            // Load initial data
            generateReport();
        } catch (Exception e) {
            System.err.println("Error initializing InventoryMovementReportController: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void setupTableColumns() {
        // Setup Items Sold/Processed table columns
        colSoldDate.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getFormattedMovementDate()));
        colSoldTransaction.setCellValueFactory(new PropertyValueFactory<>("transactionNumber"));
        colSoldProduct.setCellValueFactory(new PropertyValueFactory<>("productName"));
        colSoldSku.setCellValueFactory(new PropertyValueFactory<>("productSku"));
        colSoldCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colSoldQuantity.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        colSoldUnitPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice())));
        colSoldLineTotal.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getLineTotal())));
        colSoldCustomer.setCellValueFactory(new PropertyValueFactory<>("customerName"));

        // Setup Items Returned/Refunded table columns
        colReturnedDate.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getFormattedMovementDate()));
        colReturnedTransaction.setCellValueFactory(new PropertyValueFactory<>("transactionNumber"));
        colReturnedProduct.setCellValueFactory(new PropertyValueFactory<>("productName"));
        colReturnedSku.setCellValueFactory(new PropertyValueFactory<>("productSku"));
        colReturnedCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colReturnedQuantity.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        colReturnedUnitPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice())));
        colReturnedLineTotal.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getLineTotal())));
        colReturnedType.setCellValueFactory(new PropertyValueFactory<>("movementType"));
        colReturnedReason.setCellValueFactory(new PropertyValueFactory<>("reason"));

        // Set table data
        tblSoldItems.setItems(soldItemsData);
        tblReturnedItems.setItems(returnedItemsData);
    }

    private void setDefaultDates() {
        LocalDate today = LocalDate.now();
        dpEndDate.setValue(today);
        dpStartDate.setValue(today.minusDays(30)); // Last 30 days
    }

    private void setupFilters() {
        // Setup filter ComboBoxes
        if (cbCategory != null) {
            cbCategory.getItems().addAll("All Categories", "Clothing", "Accessories", "Footwear", "Electronics");
            cbCategory.setValue("All Categories");
        }

        if (cbBrand != null) {
            cbBrand.getItems().addAll("All Brands", "Nike", "Adidas", "Puma", "Zara", "H&M");
            cbBrand.setValue("All Brands");
        }

        if (cbMovementType != null) {
            cbMovementType.getItems().addAll("All Types", "Sale", "Return", "Refund", "Exchange");
            cbMovementType.setValue("All Types");
        }
    }

    private void initializeUI() {
        // Initialize UI elements with default values
        if (lblReportSubtitle != null) {
            lblReportSubtitle.setText("Track items sold, returned, and inventory changes");
        }

        if (lblRecordCount != null) {
            lblRecordCount.setText("0 records found");
        }

        if (lblLastUpdated != null) {
            lblLastUpdated.setText("Last Updated: Never");
        }

        if (btnAutoRefresh != null) {
            btnAutoRefresh.setText("Auto Refresh: OFF");
        }
    }

    @FXML
    private void handleGenerateReport() {
        generateReport();
    }

    @FXML
    private void handleRefresh() {
        generateReport();
    }

    @FXML
    private void handleExport() {
        exportReportToCSV();
    }

    // Enhanced Export Methods
    @FXML
    private void handleExportCSV() {
        exportReportToCSV();
    }

    @FXML
    private void handleExportPDF() {
        AlertUtil.showInfo("Export PDF", "PDF export functionality will be implemented soon!");
    }

    @FXML
    private void handleExportExcel() {
        AlertUtil.showInfo("Export Excel", "Excel export functionality will be implemented soon!");
    }

    @FXML
    private void handlePrintReport() {
        AlertUtil.showInfo("Print Report", "Print functionality will be implemented soon!");
    }

    // Quick Filter Methods
    @FXML
    private void handleTodayFilter() {
        LocalDate today = LocalDate.now();
        dpStartDate.setValue(today);
        dpEndDate.setValue(today);
        generateReport();
    }

    @FXML
    private void handleThisWeekFilter() {
        LocalDate today = LocalDate.now();
        LocalDate startOfWeek = today.minusDays(today.getDayOfWeek().getValue() - 1);
        dpStartDate.setValue(startOfWeek);
        dpEndDate.setValue(today);
        generateReport();
    }

    @FXML
    private void handleThisMonthFilter() {
        LocalDate today = LocalDate.now();
        LocalDate startOfMonth = today.withDayOfMonth(1);
        dpStartDate.setValue(startOfMonth);
        dpEndDate.setValue(today);
        generateReport();
    }

    @FXML
    private void handleLast30DaysFilter() {
        LocalDate today = LocalDate.now();
        dpStartDate.setValue(today.minusDays(30));
        dpEndDate.setValue(today);
        generateReport();
    }

    @FXML
    private void handleClearFilters() {
        cbCategory.setValue(null);
        cbBrand.setValue(null);
        cbMovementType.setValue(null);
        txtProductSearch.clear();
        txtMinValue.clear();
        txtMaxValue.clear();
        generateReport();
    }

    @FXML
    private void handleToggleAutoRefresh() {
        autoRefreshEnabled = !autoRefreshEnabled;
        btnAutoRefresh.setText("Auto Refresh: " + (autoRefreshEnabled ? "ON" : "OFF"));
        btnAutoRefresh.setStyle(autoRefreshEnabled
                ? "-fx-background-color: #27ae60; -fx-text-fill: white;"
                : "-fx-background-color: #f39c12; -fx-text-fill: white;");

        if (autoRefreshEnabled) {
            // Start auto-refresh timer (every 30 seconds)
            AlertUtil.showInfo("Auto Refresh", "Auto refresh enabled - data will update every 30 seconds");
        }
    }

    // Table Sorting Methods
    @FXML
    private void handleSortSoldByDate() {
        soldItemsData.sort((a, b) -> b.getMovementDate().compareTo(a.getMovementDate()));
        updateRecordCounts();
    }

    @FXML
    private void handleSortSoldByValue() {
        soldItemsData.sort((a, b) -> b.getLineTotal().compareTo(a.getLineTotal()));
        updateRecordCounts();
    }

    @FXML
    private void handleSortReturnedByDate() {
        returnedItemsData.sort((a, b) -> b.getMovementDate().compareTo(a.getMovementDate()));
        updateRecordCounts();
    }

    @FXML
    private void handleSortReturnedByReason() {
        returnedItemsData.sort((a, b) -> {
            String reasonA = a.getReason() != null ? a.getReason() : "";
            String reasonB = b.getReason() != null ? b.getReason() : "";
            return reasonA.compareTo(reasonB);
        });
        updateRecordCounts();
    }

    @FXML
    private void handleExportSoldItems() {
        exportTableToCSV(soldItemsData, "sold_items_" + LocalDate.now() + ".csv", "SOLD ITEMS");
    }

    @FXML
    private void handleExportReturnedItems() {
        exportTableToCSV(returnedItemsData, "returned_items_" + LocalDate.now() + ".csv", "RETURNED ITEMS");
    }

    @FXML
    private void handleRefreshAnalytics() {
        generateAnalytics();
    }

    private void updateRecordCounts() {
        if (lblSoldItemsCount != null) {
            lblSoldItemsCount.setText(soldItemsData.size() + " items");
        }
        if (lblReturnedItemsCount != null) {
            lblReturnedItemsCount.setText(returnedItemsData.size() + " items");
        }
        if (lblRecordCount != null) {
            lblRecordCount.setText((soldItemsData.size() + returnedItemsData.size()) + " records found");
        }
        if (lblLastUpdated != null) {
            lblLastUpdated.setText("Last Updated: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        }
    }

    private void generateAnalytics() {
        // This method will generate analytics data for the Analytics tab
        // Implementation will be added based on requirements
        AlertUtil.showInfo("Analytics", "Analytics generation will be implemented with advanced reporting features!");
    }

    private void exportTableToCSV(ObservableList<InventoryMovement> data, String filename, String title) {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Export " + title);
            fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("CSV Files", "*.csv"));
            fileChooser.setInitialFileName(filename);

            File file = fileChooser.showSaveDialog(tblSoldItems.getScene().getWindow());
            if (file != null) {
                try (PrintWriter writer = new PrintWriter(file)) {
                    writer.println(title + " REPORT");
                    writer.println("Period: " + dpStartDate.getValue() + " to " + dpEndDate.getValue());
                    writer.println();
                    writer.println("Date,Time,Transaction,Product,SKU,Category,Brand,Quantity,Unit Price,Line Total,Customer");

                    for (InventoryMovement item : data) {
                        writer.printf("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",%d,%.2f,%.2f,\"%s\"%n",
                                item.getFormattedMovementDate(),
                                item.getFormattedMovementTime(),
                                item.getTransactionNumber(),
                                item.getProductName(),
                                item.getProductSku(),
                                item.getCategory() != null ? item.getCategory() : "",
                                item.getBrand() != null ? item.getBrand() : "",
                                item.getQuantity(),
                                item.getUnitPrice().doubleValue(),
                                item.getLineTotal().doubleValue(),
                                item.getCustomerName() != null ? item.getCustomerName() : "Walk-in");
                    }
                }
                AlertUtil.showSuccess("Export Complete", title + " exported successfully to: " + file.getName());
            }
        } catch (Exception e) {
            AlertUtil.showError("Export Error", "Failed to export " + title + ": " + e.getMessage());
        }
    }

    private void generateReport() {
        try {
            LocalDateTime startDate = dpStartDate.getValue().atStartOfDay();
            LocalDateTime endDate = dpEndDate.getValue().atTime(23, 59, 59);

            // Load sold items data
            List<InventoryMovement> soldItems = movementDAO.getItemsSoldProcessed(startDate, endDate);
            soldItemsData.setAll(soldItems);

            // Load returned items data
            List<InventoryMovement> returnedItems = movementDAO.getItemsReturnedRefunded(startDate, endDate);
            returnedItemsData.setAll(returnedItems);

            // Load and display summary statistics
            InventoryMovementDAO.InventoryMovementSummary summary = movementDAO.getMovementSummary(startDate, endDate);
            updateSummaryStatistics(summary);

            // Update record counts and UI elements
            updateRecordCounts();
            updateReturnTrend(summary);

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate inventory movement report: " + e.getMessage());
        }
    }

    private void updateSummaryStatistics(InventoryMovementDAO.InventoryMovementSummary summary) {
        // Items Sold/Processed statistics
        lblSoldItemCount.setText(String.valueOf(summary.getSoldItemCount()));
        lblSoldQuantity.setText(String.valueOf(summary.getSoldQuantity()));
        lblSoldValue.setText(currencyFormat.format(summary.getSoldValue()));

        // Items Returned/Refunded statistics
        lblReturnedItemCount.setText(String.valueOf(summary.getReturnedItemCount()));
        lblReturnedQuantity.setText(String.valueOf(summary.getReturnedQuantity()));
        lblReturnedValue.setText(currencyFormat.format(summary.getReturnedValue()));

        // Net movement statistics
        lblNetQuantity.setText(String.valueOf(summary.getNetQuantity()));
        lblNetValue.setText(currencyFormat.format(summary.getNetValue()));
        lblReturnRate.setText(String.format("%.2f%%", summary.getReturnRate()));
    }

    private void updateReturnTrend(InventoryMovementDAO.InventoryMovementSummary summary) {
        if (lblReturnTrend != null) {
            double returnRate = summary.getReturnRate();
            String trend;
            if (returnRate < 5.0) {
                trend = "Excellent";
            } else if (returnRate < 10.0) {
                trend = "Good";
            } else if (returnRate < 20.0) {
                trend = "Moderate";
            } else {
                trend = "High";
            }
            lblReturnTrend.setText(trend);
        }
    }

    private void exportReportToCSV() {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Export Inventory Movement Report");
            fileChooser.getExtensionFilters().add(
                    new FileChooser.ExtensionFilter("CSV Files", "*.csv"));
            fileChooser.setInitialFileName("inventory_movement_report_" + LocalDate.now() + ".csv");

            File file = fileChooser.showSaveDialog(tblSoldItems.getScene().getWindow());
            if (file != null) {
                try (PrintWriter writer = new PrintWriter(file)) {
                    // Write header
                    writer.println("INVENTORY MOVEMENT REPORT");
                    writer.println("Period: " + dpStartDate.getValue() + " to " + dpEndDate.getValue());
                    writer.println();

                    // Write Items Sold/Processed section
                    writer.println("ITEMS SOLD/PROCESSED");
                    writer.println("Date,Transaction,Product,SKU,Category,Quantity,Unit Price,Line Total,Customer");

                    for (InventoryMovement item : soldItemsData) {
                        writer.printf("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",%d,%.2f,%.2f,\"%s\"%n",
                                item.getFormattedMovementDate(),
                                item.getTransactionNumber(),
                                item.getProductName(),
                                item.getProductSku(),
                                item.getCategory() != null ? item.getCategory() : "",
                                item.getQuantity(),
                                item.getUnitPrice().doubleValue(),
                                item.getLineTotal().doubleValue(),
                                item.getCustomerName() != null ? item.getCustomerName() : "Walk-in");
                    }

                    writer.println();
                    writer.println("ITEMS RETURNED/REFUNDED");
                    writer.println("Date,Transaction,Product,SKU,Category,Quantity,Unit Price,Line Total,Type,Reason");

                    for (InventoryMovement item : returnedItemsData) {
                        writer.printf("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",%d,%.2f,%.2f,\"%s\",\"%s\"%n",
                                item.getFormattedMovementDate(),
                                item.getTransactionNumber(),
                                item.getProductName(),
                                item.getProductSku(),
                                item.getCategory() != null ? item.getCategory() : "",
                                item.getQuantity(),
                                item.getUnitPrice().doubleValue(),
                                item.getLineTotal().doubleValue(),
                                item.getMovementType(),
                                item.getReason() != null ? item.getReason() : "");
                    }

                    // Write summary
                    writer.println();
                    writer.println("SUMMARY");
                    writer.println("Items Sold Count," + lblSoldItemCount.getText());
                    writer.println("Items Sold Quantity," + lblSoldQuantity.getText());
                    writer.println("Items Sold Value," + lblSoldValue.getText());
                    writer.println("Items Returned Count," + lblReturnedItemCount.getText());
                    writer.println("Items Returned Quantity," + lblReturnedQuantity.getText());
                    writer.println("Items Returned Value," + lblReturnedValue.getText());
                    writer.println("Net Quantity," + lblNetQuantity.getText());
                    writer.println("Net Value," + lblNetValue.getText());
                    writer.println("Return Rate," + lblReturnRate.getText());
                }

                AlertUtil.showSuccess("Export Complete", "Inventory movement report exported successfully to: " + file.getName());
            }

        } catch (Exception e) {
            AlertUtil.showError("Export Error", "Failed to export report: " + e.getMessage());
        }
    }
}
