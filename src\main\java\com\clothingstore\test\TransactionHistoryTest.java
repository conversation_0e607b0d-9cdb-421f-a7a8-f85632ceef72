package com.clothingstore.test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.view.EnhancedTransactionHistoryController;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;

/**
 * Test application for Enhanced Transaction History functionality
 */
public class TransactionHistoryTest extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Create test data
            Customer testCustomer = createTestCustomer();
            List<Transaction> testTransactions = createTestTransactions();

            // Load the enhanced transaction history dialog
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/enhanced-transaction-history.fxml"));
            Parent root = loader.load();

            EnhancedTransactionHistoryController controller = loader.getController();
            controller.initializeData(testCustomer, testTransactions);

            Scene scene = new Scene(root, 900, 700);
            primaryStage.setTitle("Enhanced Transaction History - Test");
            primaryStage.setScene(scene);
            primaryStage.setResizable(true);
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(600);

            // Setup keyboard focus for navigation
            root.requestFocus();

            primaryStage.show();

        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("Failed to load Enhanced Transaction History Test: " + e.getMessage());
        }
    }

    /**
     * Create a test customer
     */
    private Customer createTestCustomer() {
        Customer customer = new Customer();
        customer.setId(1L);
        customer.setFirstName("John");
        customer.setLastName("Doe");
        customer.setPhone("555-0123");
        customer.setTotalPurchases(5);
        customer.setLoyaltyPoints(1250);
        customer.setActive(true);
        customer.setRegistrationDate(LocalDateTime.now().minusMonths(6));
        return customer;
    }

    /**
     * Create test transactions
     */
    private List<Transaction> createTestTransactions() {
        List<Transaction> transactions = new ArrayList<>();

        // Transaction 1 - Recent purchase
        Transaction t1 = new Transaction();
        t1.setId(1L);
        t1.setTransactionNumber("TXN001");
        t1.setTransactionDate(LocalDateTime.now().minusDays(2));
        t1.setTotalAmount(new BigDecimal("89.99"));
        t1.setStatus("COMPLETED");
        t1.setPaymentMethod("Credit Card");
        t1.setItems(createTransactionItems1());
        transactions.add(t1);

        // Transaction 2 - Last week
        Transaction t2 = new Transaction();
        t2.setId(2L);
        t2.setTransactionNumber("TXN002");
        t2.setTransactionDate(LocalDateTime.now().minusDays(7));
        t2.setTotalAmount(new BigDecimal("156.50"));
        t2.setStatus("COMPLETED");
        t2.setPaymentMethod("Cash");
        t2.setItems(createTransactionItems2());
        transactions.add(t2);

        // Transaction 3 - Last month
        Transaction t3 = new Transaction();
        t3.setId(3L);
        t3.setTransactionNumber("TXN003");
        t3.setTransactionDate(LocalDateTime.now().minusDays(30));
        t3.setTotalAmount(new BigDecimal("245.00"));
        t3.setStatus("COMPLETED");
        t3.setPaymentMethod("Debit Card");
        t3.setItems(createTransactionItems3());
        transactions.add(t3);

        // Transaction 4 - Refunded transaction
        Transaction t4 = new Transaction();
        t4.setId(4L);
        t4.setTransactionNumber("TXN004");
        t4.setTransactionDate(LocalDateTime.now().minusDays(45));
        t4.setTotalAmount(new BigDecimal("75.25"));
        t4.setRefundedAmount(new BigDecimal("75.25"));
        t4.setStatus("REFUNDED");
        t4.setPaymentMethod("Credit Card");
        t4.setItems(createTransactionItems4());
        transactions.add(t4);

        // Transaction 5 - Older transaction
        Transaction t5 = new Transaction();
        t5.setId(5L);
        t5.setTransactionNumber("TXN005");
        t5.setTransactionDate(LocalDateTime.now().minusDays(90));
        t5.setTotalAmount(new BigDecimal("684.01"));
        t5.setStatus("COMPLETED");
        t5.setPaymentMethod("Cash");
        t5.setItems(createTransactionItems5());
        transactions.add(t5);

        return transactions;
    }

    private List<TransactionItem> createTransactionItems1() {
        List<TransactionItem> items = new ArrayList<>();

        Product product1 = new Product();
        product1.setName("Blue Jeans");

        TransactionItem item1 = new TransactionItem();
        item1.setProduct(product1);
        item1.setQuantity(1);
        item1.setUnitPrice(new BigDecimal("89.99"));
        item1.setLineTotal(new BigDecimal("89.99"));
        items.add(item1);

        return items;
    }

    private List<TransactionItem> createTransactionItems2() {
        List<TransactionItem> items = new ArrayList<>();

        Product product1 = new Product();
        product1.setName("Cotton T-Shirt");

        Product product2 = new Product();
        product2.setName("Summer Dress");

        TransactionItem item1 = new TransactionItem();
        item1.setProduct(product1);
        item1.setQuantity(2);
        item1.setUnitPrice(new BigDecimal("25.00"));
        item1.setLineTotal(new BigDecimal("50.00"));
        items.add(item1);

        TransactionItem item2 = new TransactionItem();
        item2.setProduct(product2);
        item2.setQuantity(1);
        item2.setUnitPrice(new BigDecimal("106.50"));
        item2.setLineTotal(new BigDecimal("106.50"));
        items.add(item2);

        return items;
    }

    private List<TransactionItem> createTransactionItems3() {
        List<TransactionItem> items = new ArrayList<>();

        Product product1 = new Product();
        product1.setName("Leather Jacket");

        TransactionItem item1 = new TransactionItem();
        item1.setProduct(product1);
        item1.setQuantity(1);
        item1.setUnitPrice(new BigDecimal("245.00"));
        item1.setLineTotal(new BigDecimal("245.00"));
        items.add(item1);

        return items;
    }

    private List<TransactionItem> createTransactionItems4() {
        List<TransactionItem> items = new ArrayList<>();

        Product product1 = new Product();
        product1.setName("Sneakers");

        TransactionItem item1 = new TransactionItem();
        item1.setProduct(product1);
        item1.setQuantity(1);
        item1.setUnitPrice(new BigDecimal("75.25"));
        item1.setLineTotal(new BigDecimal("75.25"));
        items.add(item1);

        return items;
    }

    private List<TransactionItem> createTransactionItems5() {
        List<TransactionItem> items = new ArrayList<>();

        Product product1 = new Product();
        product1.setName("Winter Coat");

        Product product2 = new Product();
        product2.setName("Wool Scarf");

        Product product3 = new Product();
        product3.setName("Gloves");

        TransactionItem item1 = new TransactionItem();
        item1.setProduct(product1);
        item1.setQuantity(1);
        item1.setUnitPrice(new BigDecimal("599.99"));
        item1.setLineTotal(new BigDecimal("599.99"));
        items.add(item1);

        TransactionItem item2 = new TransactionItem();
        item2.setProduct(product2);
        item2.setQuantity(1);
        item2.setUnitPrice(new BigDecimal("45.00"));
        item2.setLineTotal(new BigDecimal("45.00"));
        items.add(item2);

        TransactionItem item3 = new TransactionItem();
        item3.setProduct(product3);
        item3.setQuantity(1);
        item3.setUnitPrice(new BigDecimal("39.02"));
        item3.setLineTotal(new BigDecimal("39.02"));
        items.add(item3);

        return items;
    }

    public static void main(String[] args) {
        launch(args);
    }
}
