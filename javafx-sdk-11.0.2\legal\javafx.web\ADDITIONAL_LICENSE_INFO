                      ADDITIONAL INFORMATION ABOUT LICENSING

Certain files distributed by Oracle America, Inc. and/or its affiliates are 
subject to the following clarification and special exception to the GPLv2, 
based on the GNU Project exception for its Classpath libraries, known as the 
GNU Classpath Exception.

Note that Oracle includes multiple, independent programs in this software 
package.  Some of those programs are provided under licenses deemed 
incompatible with the GPLv2 by the Free Software Foundation and others. 
For example, the package includes programs licensed under the Apache 
License, Version 2.0 and may include FreeType. Such programs are licensed 
to you under their original licenses. 

Oracle facilitates your further distribution of this package by adding the 
Classpath Exception to the necessary parts of its GPLv2 code, which permits 
you to use that code in combination with other independent modules not 
licensed under the GPLv2. However, note that this would not permit you to 
commingle code under an incompatible license with Oracle's GPLv2 licensed 
code by, for example, cutting and pasting such code into a file also 
containing Oracle's GPLv2 licensed code and then distributing the result. 

Additionally, if you were to remove the Classpath Exception from any of the 
files to which it applies and distribute the result, you would likely be 
required to license some or all of the other code in that distribution under 
the GPLv2 as well, and since the GPLv2 is incompatible with the license terms 
of some items included in the distribution by Oracle, removing the Classpath 
Exception could therefore effectively compromise your ability to further 
distribute the package. 

Failing to distribute notices associated with some files may also create 
unexpected legal consequences.
 
Proceed with caution and we recommend that you obtain the advice of a lawyer 
skilled in open source matters before removing the Classpath Exception or 
making modifications to this package which may subsequently be redistributed 
and/or involve the use of third party software.
