@echo off
echo ===============================================
echo CLOTHING STORE DATA RESET UTILITY
echo ===============================================
echo.

if "%1"=="" (
    echo Usage: reset-data.bat [command]
    echo.
    echo Commands:
    echo   empty   - Reset database to empty state
    echo   demo    - Reset database with demo data
    echo   stats   - Show current database statistics
    echo.
    echo Examples:
    echo   reset-data.bat empty
    echo   reset-data.bat demo
    echo   reset-data.bat stats
    echo.
    pause
    exit /b
)

java -cp "lib\sqlite-jdbc-3.50.1.0.jar;javafx-sdk-17.0.2\lib\*;target\classes" com.clothingstore.util.DataResetUtility %1

echo.
echo Operation completed!
pause
