/* Customer Management Enhanced Styles */

/* TYPOGRAPHY FOUNDATION */
.customer-management-root {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-text-fill: #2c3e50;
}

/* ROOT CONTAINER - UNIFIED LAYOUT */
.customer-management-root {
    -fx-background-color: #f8f9fa;
    -fx-spacing: 0;
    -fx-padding: 0;
    -fx-min-height: 600px;
    -fx-pref-height: -1;
}

/* SCROLLING FUNCTIONALITY - PAGE LEVEL ONLY */
.main-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-fit-to-width: true;
    -fx-fit-to-height: false;
    -fx-hbar-policy: never;
    -fx-vbar-policy: as-needed;
}

.main-scroll-pane .viewport {
    -fx-background-color: #f8f9fa;
}

.main-scroll-pane .content {
    -fx-padding: 0;
}

.main-scroll-pane .scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-pref-width: 12px;
    -fx-opacity: 0.7;
}

.main-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: rgba(0,0,0,0.1);
    -fx-background-radius: 8px;
    -fx-border-color: transparent;
}

.main-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: linear-gradient(to bottom, #667eea, #764ba2);
    -fx-background-radius: 8px;
    -fx-border-color: transparent;
}

.main-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: linear-gradient(to bottom, #5a6fd8, #6a4190);
}

.main-scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: linear-gradient(to bottom, #4e63d2, #5e3a84);
}

.main-scroll-pane .scroll-bar:vertical .increment-button,
.main-scroll-pane .scroll-bar:vertical .decrement-button {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-pref-height: 0;
    -fx-max-height: 0;
    -fx-min-height: 0;
}

.main-scroll-pane .corner {
    -fx-background-color: transparent;
}

/* Smooth scrolling behavior */
.main-scroll-pane {
    -fx-smooth: true;
    -fx-pannable: true;
}

/* Touch-friendly scrolling */
.main-scroll-pane .scroll-bar:vertical {
    -fx-min-width: 14px;
    -fx-pref-width: 14px;
    -fx-max-width: 14px;
}

/* Responsive scrolling for different screen sizes */
@media screen and (max-width: 1366px) {
    .main-scroll-pane .scroll-bar:vertical {
        -fx-pref-width: 16px;
        -fx-opacity: 0.8;
    }
}

@media screen and (min-width: 1920px) {
    .main-scroll-pane .scroll-bar:vertical {
        -fx-pref-width: 12px;
        -fx-opacity: 0.6;
    }
}

/* Main Container */
.customer-management-container {
    -fx-background-color: transparent;
    -fx-padding: 20px;
    -fx-spacing: 15px;
    -fx-min-height: -1;
    -fx-pref-height: -1;
    -fx-max-height: -1;
}

/* TEXT STYLING HIERARCHY */
.customer-management-container .label {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-text-fill: #2c3e50;
    -fx-font-size: 14px;
    -fx-font-weight: normal;
    -fx-alignment: center-left;
}

/* Page Title */
.customer-management-container > HBox .label {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #1a202c;
    -fx-alignment: center-left;
}

/* Section Headers */
.analytics-header .label,
.table-header .label {
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: #2d3748;
    -fx-alignment: center-left;
}

/* Metric Labels */
.metric-label {
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-text-fill: #4a5568;
    -fx-alignment: center;
}

/* Metric Values */
.metric-value {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #1a202c;
    -fx-alignment: center;
}

/* General Text Styling for Customer Management */
.customer-management-container .text,
.customer-management-container .text-field,
.customer-management-container .label {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-text-fill: #2c3e50;
}

/* Search Section Labels */
.search-filter-container .label {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-text-fill: #495057;
    -fx-alignment: center-left;
}

/* Table Section Labels */
.table-container .label {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #2d3748;
    -fx-alignment: center-left;
}

/* ENHANCED ANALYTICS DASHBOARD */
.enhanced-analytics-container {
    -fx-background-color: linear-gradient(to bottom right, #667eea, #764ba2);
    -fx-background-radius: 15px;
    -fx-border-radius: 15px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 12, 0, 0, 4);
    -fx-padding: 20px;
    -fx-max-height: 240px;
    -fx-pref-height: 220px;
    -fx-min-height: 200px;
}

.analytics-header-bar {
    -fx-background-color: rgba(255,255,255,0.1);
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px;
    -fx-border-color: rgba(255,255,255,0.2);
    -fx-border-width: 1px;
    -fx-border-radius: 10px;
}

.analytics-main-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-alignment: center-left;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 3, 0, 0, 2);
    -fx-letter-spacing: 0.5px;
}

.analytics-period-selector {
    -fx-background-color: rgba(255,255,255,0.9);
    -fx-border-color: transparent;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-font-weight: 600;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.analytics-refresh-btn, .analytics-export-btn {
    -fx-background-color: rgba(255,255,255,0.2);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: rgba(255,255,255,0.3);
    -fx-border-width: 1px;
    -fx-padding: 10px 20px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);
}

.analytics-refresh-btn:hover, .analytics-export-btn:hover {
    -fx-background-color: rgba(255,255,255,0.3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 8, 0, 0, 4);
}

/* ENHANCED METRIC CARDS */
.metrics-container {
    -fx-padding: 15px 0;
    -fx-spacing: 20px;
    -fx-alignment: center;
}

.enhanced-metric-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-padding: 16px;
    -fx-min-width: 160px;
    -fx-max-width: 200px;
    -fx-pref-width: 180px;
    -fx-min-height: 100px;
    -fx-max-height: 120px;
    -fx-pref-height: 110px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 8, 0, 0, 3);
    -fx-cursor: hand;
}

.enhanced-metric-card .label {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-alignment: center;
    -fx-text-alignment: center;
}

.enhanced-metric-card .metric-value {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #1a202c;
    -fx-alignment: center;
}

.enhanced-metric-card .metric-label {
    -fx-font-size: 11px;
    -fx-font-weight: 500;
    -fx-text-fill: #4a5568;
    -fx-alignment: center;
}

.enhanced-metric-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 20, 0, 0, 8);
    -fx-scale-x: 1.03;
    -fx-scale-y: 1.03;
    -fx-background-color: #fafbfc;
}

/* Individual Card Styling */
.total-customers-card {
    -fx-border-color: #3498db;
    -fx-border-width: 0 0 4 0;
    -fx-background-color: linear-gradient(to bottom, white, #f8fbff);
}

.active-customers-card {
    -fx-border-color: #2ecc71;
    -fx-border-width: 0 0 4 0;
    -fx-background-color: linear-gradient(to bottom, white, #f8fff8);
}

.top-spenders-card {
    -fx-border-color: #f39c12;
    -fx-border-width: 0 0 4 0;
    -fx-background-color: linear-gradient(to bottom, white, #fffcf8);
}

.new-customers-card {
    -fx-border-color: #9b59b6;
    -fx-border-width: 0 0 4 0;
    -fx-background-color: linear-gradient(to bottom, white, #faf8ff);
}

.lifetime-value-card {
    -fx-border-color: #e74c3c;
    -fx-border-width: 0 0 4 0;
    -fx-background-color: linear-gradient(to bottom, white, #fff8f8);
}

/* Metric Content Styling */
.metric-icon {
    -fx-font-size: 22px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 3, 0, 0, 2);
    -fx-font-weight: bold;
    -fx-font-family: "Segoe UI Symbol", "Arial", sans-serif;
}

.metric-value-large {
    -fx-font-size: 26px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 2, 0, 0, 1);
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
}

.metric-label-enhanced {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-text-fill: #34495e;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
}

.metric-subtext {
    -fx-font-size: 11px;
    -fx-text-fill: #7f8c8d;
    -fx-font-style: italic;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
}

.metric-trend {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
}

.metric-trend.positive {
    -fx-text-fill: #27ae60;
    -fx-effect: dropshadow(gaussian, rgba(39,174,96,0.3), 1, 0, 0, 1);
}

.metric-trend.negative {
    -fx-text-fill: #e74c3c;
    -fx-effect: dropshadow(gaussian, rgba(231,76,60,0.3), 1, 0, 0, 1);
}

.metric-trend.neutral {
    -fx-text-fill: #95a5a6;
}

/* Loading Indicator */
.loading-spinner {
    -fx-font-size: 16px;
    -fx-rotate: 0;
}

.loading-text {
    -fx-font-size: 14px;
    -fx-text-fill: white;
    -fx-font-weight: 600;
}

/* Animations */
@keyframes spin {
    from { -fx-rotate: 0deg; }
    to { -fx-rotate: 360deg; }
}

.loading-spinner {
    -fx-animation: spin 1s linear infinite;
}

/* ENHANCED SEARCH AND FILTER SECTION */
.search-filter-container {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.06), 6, 0, 0, 2);
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-max-height: 120px;
    -fx-pref-height: 100px;
}

.search-bar {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 6px;
    -fx-padding: 12px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
}

.search-label, .filter-label {
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-text-fill: #495057;
}

.enhanced-search-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 12px 16px;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-text-fill: #2c3e50;
    -fx-alignment: center-left;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
}

.enhanced-search-field:focused {
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.3), 8, 0, 0, 2);
}

.search-button {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 12px 16px;
    -fx-cursor: hand;
    -fx-alignment: center;
    -fx-effect: dropshadow(gaussian, rgba(102,126,234,0.3), 6, 0, 0, 2);
}

.search-button:hover {
    -fx-background-color: #5a6fd8;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.enhanced-filter-combo {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-font-size: 13px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
}

.clear-filters-btn {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-cursor: hand;
}

.clear-filters-btn:hover {
    -fx-background-color: #5a6268;
}

.export-btn {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-cursor: hand;
}

.export-btn:hover {
    -fx-background-color: #218838;
}

.results-summary {
    -fx-background-color: rgba(102,126,234,0.05);
    -fx-background-radius: 6px;
    -fx-padding: 12px 16px;
    -fx-border-color: rgba(102,126,234,0.2);
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
}

.results-count {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-text-fill: #495057;
}

.selection-info {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
}

/* ENHANCED TABLE STYLING */
.table-container {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 10, 0, 0, 3);
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-min-height: 500px;
    -fx-pref-height: 600px;
    -fx-max-height: -1;
}

.table-header {
    -fx-background-color: linear-gradient(to right, #f8f9fa, #e9ecef);
    -fx-background-radius: 12px 12px 0 0;
    -fx-padding: 20px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 2 0;
}

.table-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.refresh-table-btn {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(23,162,184,0.3), 4, 0, 0, 1);
}

.refresh-table-btn:hover {
    -fx-background-color: #138496;
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.scroll-top-btn {
    -fx-background-color: #6f42c1;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(111,66,193,0.3), 4, 0, 0, 1);
}

.scroll-top-btn:hover {
    -fx-background-color: #5a359a;
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.add-customer-btn {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(40,167,69,0.3), 4, 0, 0, 1);
}

.add-customer-btn:hover {
    -fx-background-color: #218838;
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.enhanced-customer-table {
    -fx-background-color: white;
    -fx-border-color: transparent;
    -fx-table-cell-border-color: #f1f3f4;
    -fx-background-radius: 0 0 12px 12px;
    -fx-min-height: 400px;
    -fx-pref-height: 500px;
    -fx-max-height: -1;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 13px;
}

/* Table Header Styling */
.enhanced-customer-table .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1px 0;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-text-fill: #2d3748;
    -fx-alignment: center-left;
    -fx-padding: 8px 12px;
}

.enhanced-customer-table .column-header .label {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-text-fill: #2d3748;
    -fx-alignment: center-left;
}

/* Table Cell Styling */
.enhanced-customer-table .table-cell {
    -fx-background-color: white;
    -fx-border-color: #f1f3f4;
    -fx-border-width: 0 0 1px 0;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 13px;
    -fx-text-fill: #2c3e50;
    -fx-alignment: center-left;
    -fx-padding: 8px 12px;
    -fx-text-overrun: ellipsis;
    -fx-wrap-text: false;
}

.enhanced-customer-table .table-row-cell:selected .table-cell {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1565c0;
}

.enhanced-customer-table .table-row-cell:hover .table-cell {
    -fx-background-color: #f5f5f5;
}

/* Column-Specific Text Alignment */
.name-column .table-cell,
.address-column .table-cell {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
}

.phone-column .table-cell {
    -fx-alignment: center;
    -fx-text-alignment: center;
}

.points-column .table-cell,
.spent-column .table-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
    -fx-font-weight: 500;
}

.status-column .table-cell {
    -fx-alignment: center;
    -fx-text-alignment: center;
}

.actions-column .table-cell {
    -fx-alignment: center;
    -fx-text-alignment: center;
}

/* Remove individual table scrolling - use page-level scrolling only */
.enhanced-customer-table .virtual-flow .scroll-bar:vertical {
    -fx-opacity: 0;
    -fx-pref-width: 0;
    -fx-max-width: 0;
}

.enhanced-customer-table .virtual-flow .scroll-bar:horizontal {
    -fx-opacity: 0;
    -fx-pref-height: 0;
    -fx-max-height: 0;
}

.enhanced-customer-table .column-header-background {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 2 0;
}

.enhanced-customer-table .column-header {
    -fx-background-color: transparent;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-text-fill: #495057;
    -fx-padding: 12px 8px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1 0 0;
}

.enhanced-customer-table .table-row-cell {
    -fx-background-color: white;
    -fx-border-color: #f1f3f4;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 4px 0;
}

.enhanced-customer-table .table-row-cell:hover {
    -fx-background-color: #f8f9fa;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
}

.enhanced-customer-table .table-row-cell:selected {
    -fx-background-color: rgba(102,126,234,0.1);
    -fx-border-color: #667eea;
    -fx-border-width: 0 0 2 0;
}

.enhanced-customer-table .table-cell {
    -fx-padding: 12px 8px;
    -fx-font-size: 13px;
    -fx-text-fill: #495057;
    -fx-border-color: transparent;
}

/* Column-specific styling */
.name-column .table-cell {
    -fx-font-weight: 600;
    -fx-text-fill: #2c3e50;
}



.phone-column .table-cell {
    -fx-font-family: "Courier New", monospace;
}

.points-column .table-cell {
    -fx-alignment: CENTER;
    -fx-font-weight: 600;
    -fx-text-fill: #f39c12;
}

.spent-column .table-cell {
    -fx-alignment: CENTER_RIGHT;
    -fx-font-weight: 600;
    -fx-text-fill: #28a745;
}

.status-column .table-cell {
    -fx-alignment: CENTER;
    -fx-font-weight: 600;
}

.actions-column .table-cell {
    -fx-alignment: CENTER;
}

/* Analytics Header */
.analytics-header {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
    -fx-border-color: transparent;
}

.analytics-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

/* Metric Cards */
.metric-card {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
    -fx-padding: 16px;
    -fx-min-width: 160px;
    -fx-border-color: transparent;
}

.metric-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 12, 0, 0, 4);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.metric-value {
    -fx-font-size: 22px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.metric-label {
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
}

/* Header Section */
.header-section {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
    -fx-border-color: transparent;
}

.page-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

/* Enhanced Buttons */
.primary-button {
    -fx-background-color: linear-gradient(to bottom, #28a745 0%, #218838 100%);
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10 20 10 20;
    -fx-alignment: center;
    -fx-effect: dropshadow(gaussian, rgba(40,167,69,0.3), 4, 0, 0, 2);
    -fx-cursor: hand;
}

.primary-button:hover {
    -fx-background-color: linear-gradient(to bottom, #218838 0%, #1e7e34 100%);
    -fx-effect: dropshadow(gaussian, rgba(40,167,69,0.4), 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.secondary-button {
    -fx-background-color: linear-gradient(to bottom, #6c757d 0%, #5a6268 100%);
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10 20 10 20;
    -fx-alignment: center;
    -fx-effect: dropshadow(gaussian, rgba(108,117,125,0.3), 4, 0, 0, 2);
    -fx-cursor: hand;
}

.secondary-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5a6268 0%, #495057 100%);
    -fx-effect: dropshadow(gaussian, rgba(108,117,125,0.4), 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.action-button {
    -fx-background-color: linear-gradient(to bottom, #3498db 0%, #2980b9 100%);
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10 20 10 20;
    -fx-alignment: center;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 4, 0, 0, 2);
    -fx-cursor: hand;
}

.action-button:hover {
    -fx-background-color: linear-gradient(to bottom, #2980b9 0%, #21618c 100%);
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.4), 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Filter Section */
.filter-section {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 20px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
}

.filter-row {
    -fx-padding: 5px 0;
}

.filter-label {
    -fx-font-weight: 600;
    -fx-text-fill: #495057;
    -fx-font-size: 13px;
}

.search-box {
    -fx-background-color: white;
    -fx-border-color: #3498db;
    -fx-border-width: 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-font-size: 13px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

.search-box:focused {
    -fx-border-color: #2980b9;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 6, 0, 0, 2);
}

.combo-box {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-text-fill: #2c3e50;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

.combo-box .list-cell {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-text-fill: #2c3e50;
    -fx-alignment: center-left;
}

.combo-box:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.2), 4, 0, 0, 2);
}

.filter-results {
    -fx-font-size: 12px;
    -fx-text-fill: #28a745;
    -fx-font-weight: bold;
    -fx-background-color: rgba(40,167,69,0.1);
    -fx-padding: 4px 8px;
    -fx-background-radius: 6px;
}

.clear-filters-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-cursor: hand;
}

.clear-filters-button:hover {
    -fx-background-color: #c82333;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Expandable Cell Styling */
.expandable-cell {
    -fx-cursor: hand;
    -fx-background-color: white;
    -fx-border-color: #f1f3f4;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 4px 8px;
}

.expandable-cell:hover,
.expandable-cell-hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.expandable-cell-text {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 13px;
    -fx-text-fill: #2c3e50;
    -fx-line-spacing: 2px;
}

/* Expand/Collapse Icon Styling */
.expand-icon {
    -fx-background-radius: 3px;
    -fx-min-width: 14px;
    -fx-min-height: 14px;
    -fx-max-width: 14px;
    -fx-max-height: 14px;
    -fx-cursor: hand;
    -fx-border-radius: 3px;
    -fx-border-width: 1px;
}

.expand-icon-collapsed {
    -fx-background-color: #e3f2fd;
    -fx-border-color: #2196f3;
    -fx-shape: "M 4 7 L 10 7 M 7 4 L 7 10";
}

.expand-icon-collapsed:hover {
    -fx-background-color: #bbdefb;
    -fx-border-color: #1976d2;
}

.expand-icon-expanded {
    -fx-background-color: #fff3e0;
    -fx-border-color: #ff9800;
    -fx-shape: "M 4 7 L 10 7";
}

.expand-icon-expanded:hover {
    -fx-background-color: #ffe0b2;
    -fx-border-color: #f57c00;
}

.expand-icon:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 3, 0, 0, 1);
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

/* Enhanced Table Row Height for Expandable Content */
.enhanced-customer-table .table-row-cell {
    -fx-cell-size: 40px;
    -fx-pref-height: 40px;
    -fx-max-height: -1;
}

.enhanced-customer-table .table-row-cell .table-cell {
    -fx-alignment: top-left;
    -fx-padding: 6px 8px;
    -fx-wrap-text: false;
}

/* Smooth Transitions for Cell Expansion */
.expandable-cell {
    -fx-transition: all 0.3s ease-in-out;
}

.expandable-cell-text {
    -fx-transition: all 0.2s ease-in-out;
    -fx-text-alignment: left;
}

/* Visual Feedback for Expandable Content */
.expandable-cell .expandable-cell-text {
    -fx-underline: false;
}

.expandable-cell:hover .expandable-cell-text {
    -fx-text-fill: #1976d2;
}

/* Improved Table Cell Spacing for Expanded Content */
.enhanced-customer-table .table-cell.expandable-cell {
    -fx-padding: 8px 12px;
    -fx-spacing: 4px;
}

/* Tooltip Styling for Expandable Cells */
.expandable-cell .tooltip {
    -fx-background-color: #2c3e50;
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 12px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 2);
    -fx-max-width: 300px;
    -fx-wrap-text: true;
}

/* Table Styling */
.table-view {
    -fx-background-color: white;
    -fx-border-color: transparent;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
}

.table-view .column-header {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    -fx-border-color: #dee2e6;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-font-size: 13px;
    -fx-padding: 12px 8px;
}

.table-row-cell {
    -fx-border-color: transparent transparent #f1f3f4 transparent;
    -fx-border-width: 0 0 1 0;
}

.table-row-cell:selected {
    -fx-background-color: linear-gradient(to right, #e3f2fd 0%, #bbdefb 100%);
    -fx-text-fill: #1565c0;
    -fx-effect: dropshadow(gaussian, rgba(21,101,192,0.2), 4, 0, 0, 1);
}

.table-row-cell:hover {
    -fx-background-color: #f8f9fa;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

/* Customer Analytics Panel */
.customer-analytics-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.customer-info-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

/* Action Buttons in Table */
.table-action-btn {
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 4 8 4 8;
    -fx-font-size: 10px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
}

.btn-view {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
    -fx-border-color: #1976d2;
    -fx-border-width: 1px;
}

.btn-edit {
    -fx-background-color: #fff3e0;
    -fx-text-fill: #f57c00;
    -fx-border-color: #f57c00;
    -fx-border-width: 1px;
}

.btn-delete {
    -fx-background-color: #ffebee;
    -fx-text-fill: #d32f2f;
    -fx-border-color: #d32f2f;
    -fx-border-width: 1px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .metric-card {
        -fx-min-width: 140px;
    }
    
    .analytics-title {
        -fx-font-size: 18px;
    }
}
