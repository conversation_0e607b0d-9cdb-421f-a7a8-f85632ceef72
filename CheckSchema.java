import java.sql.*;

public class CheckSchema {
    public static void main(String[] args) {
        try (Connection conn = DriverManager.getConnection("*****************************")) {
            System.out.println("=== TRANSACTION_ITEMS TABLE SCHEMA ===");
            
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("PRAGMA table_info(transaction_items)");
            
            while (rs.next()) {
                System.out.println(rs.getString("name") + " - " + rs.getString("type"));
            }
            
        } catch (SQLException e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}
