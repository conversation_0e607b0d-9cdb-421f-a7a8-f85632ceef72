package com.clothingstore.view;

import java.io.File;
import java.net.URL;
import java.sql.SQLException;
import java.util.ResourceBundle;

import com.clothingstore.dao.SettingsDAO;
import com.clothingstore.model.Setting;
import com.clothingstore.util.AlertUtil;

import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.stage.DirectoryChooser;
import javafx.stage.FileChooser;

/**
 * Controller for Application Settings interface
 */
public class SettingsController implements Initializable {

    // General Settings
    @FXML
    private TextField txtStoreName;
    @FXML
    private ComboBox<String> cmbCurrency;
    @FXML
    private TextField txtTaxRate;
    @FXML
    private TextField txtLowStockThreshold;

    // Receipt Settings
    @FXML
    private TextArea txtReceiptHeader;
    @FXML
    private TextArea txtReceiptFooter;
    @FXML
    private CheckBox chkAutoPrint;
    @FXML
    private ComboBox<String> cmbPrinter;

    // Loyalty Program Settings
    @FXML
    private TextField txtPointsPerDollar;
    @FXML
    private TextField txtBronzeThreshold;
    @FXML
    private TextField txtSilverThreshold;
    @FXML
    private TextField txtGoldThreshold;

    // Database Settings
    @FXML
    private TextField txtDatabasePath;
    @FXML
    private CheckBox chkAutoBackup;
    @FXML
    private TextField txtBackupPath;

    // Buttons
    @FXML
    private Button btnSave;
    @FXML
    private Button btnReset;
    @FXML
    private Button btnBrowseDatabase;
    @FXML
    private Button btnBrowseBackup;

    private SettingsDAO settingsDAO;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        settingsDAO = SettingsDAO.getInstance();

        setupControls();
        loadSettings();
    }

    private void setupControls() {
        // Currency options
        cmbCurrency.setItems(FXCollections.observableArrayList(
                "USD ($)", "EUR (€)", "GBP (£)", "CAD ($)", "AUD ($)", "JPY (¥)"
        ));

        // Printer options (mock data)
        cmbPrinter.setItems(FXCollections.observableArrayList(
                "Default Printer", "Receipt Printer 1", "Receipt Printer 2", "PDF Printer"
        ));

        // Set default values
        setDefaultValues();
    }

    private void setDefaultValues() {
        txtStoreName.setText("Clothing Store");
        cmbCurrency.setValue("USD ($)");
        txtTaxRate.setText("0.00");
        txtLowStockThreshold.setText("5");

        txtReceiptHeader.setText("Welcome to Clothing Store\nThank you for your business!");
        txtReceiptFooter.setText("Have a great day!\nVisit us again soon!");
        chkAutoPrint.setSelected(false);
        cmbPrinter.setValue("Default Printer");

        txtPointsPerDollar.setText("1");
        txtBronzeThreshold.setText("100");
        txtSilverThreshold.setText("500");
        txtGoldThreshold.setText("1000");

        txtDatabasePath.setText("clothing_store.db");
        chkAutoBackup.setSelected(true);
        txtBackupPath.setText("backups/");

    }

    private void loadSettings() {
        try {
            // Initialize cache for better performance
            settingsDAO.initializeCache();

            // Load General Settings
            txtStoreName.setText(settingsDAO.getValue("store_name", "Clothing Store"));
            cmbCurrency.setValue(settingsDAO.getValue("store_currency", "USD ($)"));
            txtTaxRate.setText(String.valueOf(settingsDAO.getDoubleValue("tax_rate", 8.5)));
            txtLowStockThreshold.setText(String.valueOf(settingsDAO.getIntValue("low_stock_threshold", 5)));

            // Load Receipt Settings
            txtReceiptHeader.setText(settingsDAO.getValue("receipt_header", "Welcome to Clothing Store\nThank you for your business!"));
            txtReceiptFooter.setText(settingsDAO.getValue("receipt_footer", "Have a great day!\nVisit us again soon!"));
            chkAutoPrint.setSelected(settingsDAO.getBooleanValue("receipt_auto_print", false));
            cmbPrinter.setValue(settingsDAO.getValue("receipt_printer", "Default Printer"));

            // Load Loyalty Program Settings (these may be deprecated but still load if present)
            txtPointsPerDollar.setText(String.valueOf(settingsDAO.getIntValue("loyalty_points_per_dollar", 1)));
            txtBronzeThreshold.setText(String.valueOf(settingsDAO.getIntValue("loyalty_bronze_threshold", 100)));
            txtSilverThreshold.setText(String.valueOf(settingsDAO.getIntValue("loyalty_silver_threshold", 500)));
            txtGoldThreshold.setText(String.valueOf(settingsDAO.getIntValue("loyalty_gold_threshold", 1000)));

            // Load Database Settings
            txtDatabasePath.setText(settingsDAO.getValue("database_path", "clothing_store.db"));
            chkAutoBackup.setSelected(settingsDAO.getBooleanValue("database_auto_backup", true));
            txtBackupPath.setText(settingsDAO.getValue("database_backup_path", "backups/"));

        } catch (SQLException e) {
            AlertUtil.showError("Settings Load Error", "Could not load settings from database: " + e.getMessage());
            // Fall back to default values
            setDefaultValues();
        }
    }

    @FXML
    private void handleSave() {
        if (validateSettings()) {
            try {
                saveSettings();
                AlertUtil.showInfo("Settings Saved", "All settings have been saved successfully.");
            } catch (Exception e) {
                AlertUtil.showError("Save Failed", "Failed to save settings: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleReset() {
        if (AlertUtil.showConfirmation("Reset Settings",
                "Are you sure you want to reset all settings to their default values?")) {
            setDefaultValues();
            AlertUtil.showInfo("Settings Reset", "All settings have been reset to default values.");
        }
    }

    @FXML
    private void handleBrowseDatabase() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Database File");
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Database Files", "*.db", "*.sqlite"));

        File selectedFile = fileChooser.showOpenDialog(txtDatabasePath.getScene().getWindow());
        if (selectedFile != null) {
            txtDatabasePath.setText(selectedFile.getAbsolutePath());
        }
    }

    @FXML
    private void handleBrowseBackup() {
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle("Select Backup Directory");

        File selectedDirectory = directoryChooser.showDialog(txtBackupPath.getScene().getWindow());
        if (selectedDirectory != null) {
            txtBackupPath.setText(selectedDirectory.getAbsolutePath());
        }
    }

    private boolean validateSettings() {
        // Validate tax rate
        try {
            double taxRate = Double.parseDouble(txtTaxRate.getText());
            if (taxRate < 0 || taxRate > 100) {
                AlertUtil.showError("Invalid Tax Rate", "Tax rate must be between 0 and 100.");
                return false;
            }
        } catch (NumberFormatException e) {
            AlertUtil.showError("Invalid Tax Rate", "Please enter a valid tax rate.");
            return false;
        }

        // Validate low stock threshold
        try {
            int threshold = Integer.parseInt(txtLowStockThreshold.getText());
            if (threshold < 0) {
                AlertUtil.showError("Invalid Threshold", "Low stock threshold must be a positive number.");
                return false;
            }
        } catch (NumberFormatException e) {
            AlertUtil.showError("Invalid Threshold", "Please enter a valid low stock threshold.");
            return false;
        }

        // Validate loyalty program settings
        try {
            int pointsPerDollar = Integer.parseInt(txtPointsPerDollar.getText());
            int bronzeThreshold = Integer.parseInt(txtBronzeThreshold.getText());
            int silverThreshold = Integer.parseInt(txtSilverThreshold.getText());
            int goldThreshold = Integer.parseInt(txtGoldThreshold.getText());

            if (pointsPerDollar <= 0 || bronzeThreshold <= 0 || silverThreshold <= 0 || goldThreshold <= 0) {
                AlertUtil.showError("Invalid Loyalty Settings", "All loyalty program values must be positive.");
                return false;
            }

            if (bronzeThreshold >= silverThreshold || silverThreshold >= goldThreshold) {
                AlertUtil.showError("Invalid Loyalty Thresholds",
                        "Loyalty thresholds must be in ascending order: Bronze < Silver < Gold.");
                return false;
            }
        } catch (NumberFormatException e) {
            AlertUtil.showError("Invalid Loyalty Settings", "Please enter valid numbers for loyalty program settings.");
            return false;
        }

        // Validate store name
        if (txtStoreName.getText().trim().isEmpty()) {
            AlertUtil.showError("Invalid Store Name", "Store name cannot be empty.");
            return false;
        }

        return true;
    }

    private void saveSettings() throws SQLException {
        // Save General Settings
        saveOrUpdateSetting("store_name", txtStoreName.getText(), "Store name for receipts and reports", "Store", "STRING");
        saveOrUpdateSetting("store_currency", cmbCurrency.getValue(), "Store currency for transactions", "Store", "STRING");
        saveOrUpdateSetting("tax_rate", txtTaxRate.getText(), "Default tax rate percentage", "POS", "DECIMAL");
        saveOrUpdateSetting("low_stock_threshold", txtLowStockThreshold.getText(), "Low stock alert threshold", "Inventory", "INTEGER");

        // Save Receipt Settings
        saveOrUpdateSetting("receipt_header", txtReceiptHeader.getText(), "Receipt header text", "Receipt", "STRING");
        saveOrUpdateSetting("receipt_footer", txtReceiptFooter.getText(), "Receipt footer text", "Receipt", "STRING");
        saveOrUpdateSetting("receipt_auto_print", String.valueOf(chkAutoPrint.isSelected()), "Auto print receipts", "Receipt", "BOOLEAN");
        saveOrUpdateSetting("receipt_printer", cmbPrinter.getValue(), "Selected receipt printer", "Receipt", "STRING");

        // Save Loyalty Program Settings (deprecated but maintained for compatibility)
        saveOrUpdateSetting("loyalty_points_per_dollar", txtPointsPerDollar.getText(), "Points earned per dollar spent", "Loyalty", "INTEGER");
        saveOrUpdateSetting("loyalty_bronze_threshold", txtBronzeThreshold.getText(), "Bronze tier threshold", "Loyalty", "INTEGER");
        saveOrUpdateSetting("loyalty_silver_threshold", txtSilverThreshold.getText(), "Silver tier threshold", "Loyalty", "INTEGER");
        saveOrUpdateSetting("loyalty_gold_threshold", txtGoldThreshold.getText(), "Gold tier threshold", "Loyalty", "INTEGER");

        // Save Database Settings
        saveOrUpdateSetting("database_path", txtDatabasePath.getText(), "Database file path", "Database", "STRING");
        saveOrUpdateSetting("database_auto_backup", String.valueOf(chkAutoBackup.isSelected()), "Enable automatic backups", "Database", "BOOLEAN");
        saveOrUpdateSetting("database_backup_path", txtBackupPath.getText(), "Backup directory path", "Database", "STRING");

    }

    private void saveOrUpdateSetting(String key, String value, String description, String category, String dataType) throws SQLException {
        try {
            var existingSetting = settingsDAO.findByKey(key);
            if (existingSetting.isPresent()) {
                // Update existing setting
                Setting setting = existingSetting.get();
                setting.setValue(value);
                settingsDAO.save(setting);
            } else {
                // Create new setting
                Setting newSetting = new Setting(key, value, description, category, dataType);
                settingsDAO.save(newSetting);
            }
        } catch (SQLException e) {
            throw new SQLException("Failed to save setting '" + key + "': " + e.getMessage(), e);
        }
    }

    // Getter methods for accessing settings from other parts of the application
    public String getStoreName() {
        return txtStoreName.getText();
    }

    public String getCurrency() {
        return cmbCurrency.getValue();
    }

    public double getTaxRate() {
        try {
            return Double.parseDouble(txtTaxRate.getText());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    public int getLowStockThreshold() {
        try {
            return Integer.parseInt(txtLowStockThreshold.getText());
        } catch (NumberFormatException e) {
            return 5;
        }
    }

    public String getReceiptHeader() {
        return txtReceiptHeader.getText();
    }

    public String getReceiptFooter() {
        return txtReceiptFooter.getText();
    }

    public boolean isAutoPrintEnabled() {
        return chkAutoPrint.isSelected();
    }

    public String getSelectedPrinter() {
        return cmbPrinter.getValue();
    }

    public int getPointsPerDollar() {
        try {
            return Integer.parseInt(txtPointsPerDollar.getText());
        } catch (NumberFormatException e) {
            return 1;
        }
    }

    public int getBronzeThreshold() {
        try {
            return Integer.parseInt(txtBronzeThreshold.getText());
        } catch (NumberFormatException e) {
            return 100;
        }
    }

    public int getSilverThreshold() {
        try {
            return Integer.parseInt(txtSilverThreshold.getText());
        } catch (NumberFormatException e) {
            return 500;
        }
    }

    public int getGoldThreshold() {
        try {
            return Integer.parseInt(txtGoldThreshold.getText());
        } catch (NumberFormatException e) {
            return 1000;
        }
    }

    public String getDatabasePath() {
        return txtDatabasePath.getText();
    }

    public boolean isAutoBackupEnabled() {
        return chkAutoBackup.isSelected();
    }

    public String getBackupPath() {
        return txtBackupPath.getText();
    }

}
