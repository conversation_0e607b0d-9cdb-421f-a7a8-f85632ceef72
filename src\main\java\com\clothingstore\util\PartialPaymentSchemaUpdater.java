package com.clothingstore.util;

import com.clothingstore.database.DatabaseManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Logger;

/**
 * Database schema updater for partial payment functionality
 */
public class PartialPaymentSchemaUpdater {
    
    private static final Logger LOGGER = Logger.getLogger(PartialPaymentSchemaUpdater.class.getName());
    
    /**
     * Update database schema to support partial payments
     */
    public static void updateSchemaForPartialPayments() throws SQLException {
        LOGGER.info("Starting partial payment database schema updates...");
        
        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Add amount_paid column to transactions table
            addAmountPaidColumn(conn);
            
            // Update existing transactions
            updateExistingTransactions(conn);
            
            LOGGER.info("Partial payment database schema updates completed successfully");
        }
    }
    
    /**
     * Add amount_paid column to transactions table
     */
    private static void addAmountPaidColumn(Connection conn) throws SQLException {
        // Check if column already exists
        boolean columnExists = false;
        try (PreparedStatement stmt = conn.prepareStatement("PRAGMA table_info(transactions)");
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String columnName = rs.getString("name");
                if ("amount_paid".equals(columnName)) {
                    columnExists = true;
                    break;
                }
            }
        }
        
        if (!columnExists) {
            LOGGER.info("Adding amount_paid column to transactions table");
            try (PreparedStatement stmt = conn.prepareStatement(
                    "ALTER TABLE transactions ADD COLUMN amount_paid DECIMAL(10,2) DEFAULT 0.00")) {
                stmt.executeUpdate();
                LOGGER.info("Successfully added amount_paid column");
            }
        } else {
            LOGGER.info("amount_paid column already exists");
        }
    }
    
    /**
     * Update existing transactions to set proper amount_paid values
     */
    private static void updateExistingTransactions(Connection conn) throws SQLException {
        LOGGER.info("Updating existing transactions with amount_paid values");
        
        // Update completed transactions - they should have amount_paid = total_amount
        String updateCompleted = "UPDATE transactions " +
                "SET amount_paid = total_amount " +
                "WHERE status = 'COMPLETED' AND (amount_paid IS NULL OR amount_paid = 0)";
        
        try (PreparedStatement stmt = conn.prepareStatement(updateCompleted)) {
            int updated = stmt.executeUpdate();
            LOGGER.info("Updated " + updated + " completed transactions");
        }
        
        // Update pending transactions - they should have amount_paid = 0
        String updatePending = "UPDATE transactions " +
                "SET amount_paid = 0.00 " +
                "WHERE status = 'PENDING' AND amount_paid IS NULL";
        
        try (PreparedStatement stmt = conn.prepareStatement(updatePending)) {
            int updated = stmt.executeUpdate();
            LOGGER.info("Updated " + updated + " pending transactions");
        }
        
        // Update other statuses
        String updateOthers = "UPDATE transactions " +
                "SET amount_paid = 0.00 " +
                "WHERE amount_paid IS NULL";
        
        try (PreparedStatement stmt = conn.prepareStatement(updateOthers)) {
            int updated = stmt.executeUpdate();
            LOGGER.info("Updated " + updated + " other transactions");
        }
    }
    
    /**
     * Run all partial payment schema updates
     */
    public static void runAllUpdates() {
        try {
            updateSchemaForPartialPayments();
        } catch (SQLException e) {
            LOGGER.severe("Failed to update partial payment schema: " + e.getMessage());
            throw new RuntimeException("Database schema update failed", e);
        }
    }
    
    /**
     * Main method for running schema updates
     */
    public static void main(String[] args) {
        System.out.println("Running partial payment schema updates...");
        runAllUpdates();
        System.out.println("Schema updates completed successfully!");
    }
}
