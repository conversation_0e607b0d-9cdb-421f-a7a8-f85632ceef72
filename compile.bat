@echo off
echo Compiling Clothing Store Management System...
echo.

REM Create target directory if it doesn't exist
if not exist "target\classes" mkdir target\classes

REM Check if JavaFX SDK exists
if not exist "javafx-sdk-17.0.2\lib" (
    echo ERROR: JavaFX SDK not found!
    echo Please ensure javafx-sdk-17.0.2 folder exists in the project directory.
    pause
    exit /b 1
)

REM Check if SQLite library exists
if not exist "lib\sqlite-jdbc-3.50.1.0.jar" (
    echo ERROR: SQLite JDBC library not found!
    echo Please ensure lib\sqlite-jdbc-3.50.1.0.jar exists.
    pause
    exit /b 1
)

echo Compiling Java source files...

REM Compile all Java files (excluding removed demo and test files)
javac -cp "lib\sqlite-jdbc-3.50.1.0.jar;javafx-sdk-17.0.2\lib\*" -d target\classes src\main\java\com\clothingstore\*.java src\main\java\com\clothingstore\dao\*.java src\main\java\com\clothingstore\database\*.java src\main\java\com\clothingstore\model\*.java src\main\java\com\clothingstore\service\*.java src\main\java\com\clothingstore\util\*.java src\main\java\com\clothingstore\view\*.java src\main\java\com\clothingstore\controller\*.java

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo Copying resource files...

REM Copy FXML files
if not exist "target\classes\fxml" mkdir target\classes\fxml
copy "src\main\resources\fxml\*.fxml" "target\classes\fxml\" >nul 2>&1

REM Copy CSS files
if not exist "target\classes\css" mkdir target\classes\css
copy "src\main\resources\css\*.css" "target\classes\css\" >nul 2>&1

echo.
echo Compilation completed successfully!
echo You can now run the application using run-app.bat
pause
